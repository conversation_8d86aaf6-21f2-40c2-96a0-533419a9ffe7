
> kapi-node-backend@1.0.0 build
> tsc -p tsconfig.json

src/admin/admin.ts(143,30): error TS6133: 'next' is declared but its value is never read.
src/admin/controllers/services.controller.ts(7,1): error TS6133: 'Router' is declared but its value is never read.
src/admin/controllers/services.controller.ts(8,1): error TS6133: 'fs' is declared but its value is never read.
src/admin/controllers/services.controller.ts(9,1): error TS6133: 'path' is declared but its value is never read.
src/admin/controllers/services.controller.ts(16,38): error TS6133: 'req' is declared but its value is never read.
src/admin/controllers/tables.controller.ts(398,33): error TS7053: Element implicitly has an 'any' type because expression of type '0' can't be used to index type 'Number'.
  Property '0' does not exist on type 'Number'.
src/admin/routes/api-health.routes.ts(206,28): error TS6133: 'req' is declared but its value is never read.
src/admin/routes/api-health.routes.ts(206,42): error TS6133: 'res' is declared but its value is never read.
src/admin/routes/db-health.routes.ts(11,34): error TS6133: 'req' is declared but its value is never read.
src/admin/routes/db-health.routes.ts(40,35): error TS7030: Not all code paths return a value.
src/admin/routes/db-health.routes.ts(243,43): error TS6133: 'req' is declared but its value is never read.
src/admin/routes/models.routes.ts(4,1): error TS6133: 'fs' is declared but its value is never read.
src/admin/routes/models.routes.ts(5,1): error TS6133: 'path' is declared but its value is never read.
src/admin/routes/models.routes.ts(6,1): error TS6133: 'yaml' is declared but its value is never read.
src/admin/routes/models.routes.ts(29,18): error TS7030: Not all code paths return a value.
src/admin/routes/models.routes.ts(29,25): error TS6133: 'req' is declared but its value is never read.
src/admin/routes/models.routes.ts(109,28): error TS6133: 'req' is declared but its value is never read.
src/admin/routes/models.routes.ts(143,29): error TS6133: 'req' is declared but its value is never read.
src/admin/routes/models.routes.ts(193,23): error TS7030: Not all code paths return a value.
src/admin/routes/schema-debug.routes.ts(22,44): error TS2694: Namespace 'global.Express' has no exported member 'Session'.
src/admin/routes/schema-debug.routes.ts(95,7): error TS2322: Type '{ id: number; user_id: number; created_at: Date; updated_at: Date; title: string | null; status: ConversationStatus; meta_data: JsonValue; settings: JsonValue; ... 19 more ...; agent_pipeline_stages: JsonValue; }[]' is not assignable to type 'ConversationRecord[]'.
  Type '{ id: number; user_id: number; created_at: Date; updated_at: Date; title: string | null; status: ConversationStatus; meta_data: JsonValue; settings: JsonValue; ... 19 more ...; agent_pipeline_stages: JsonValue; }' is not assignable to type 'ConversationRecord'.
    Types of property 'title' are incompatible.
      Type 'string | null' is not assignable to type 'string | undefined'.
        Type 'null' is not assignable to type 'string | undefined'.
src/admin/routes/schema-debug.routes.ts(195,31): error TS6133: 'req' is declared but its value is never read.
src/admin/routes/schema-debug.routes.ts(235,34): error TS6133: 'req' is declared but its value is never read.
src/admin/routes/ui.routes.ts(3,1): error TS6133: 'path' is declared but its value is never read.
src/admin/routes/ui.routes.ts(92,11): error TS6133: 'tableName' is declared but its value is never read.
src/api/agent/routes.ts(12,1): error TS6192: All imports in import declaration are unused.
src/api/agent/routes.ts(160,53): error TS2345: Argument of type 'IncomingMessage & { url?: string | undefined; params?: any; }' is not assignable to parameter of type 'Request<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
  Type 'IncomingMessage & { url?: string | undefined; params?: any; }' is missing the following properties from type 'Request<ParamsDictionary, any, any, ParsedQs, Record<string, any>>': cookies, signedCookies, get, header, and 28 more.
src/api/ai/controllers/ai-agent.controller.ts(4,10): error TS6133: 'Request' is declared but its value is never read.
src/api/ai/controllers/ai-agent.controller.ts(9,26): error TS6133: 'roleMiddleware' is declared but its value is never read.
src/api/blog/controllers/blog.controller.ts(263,20): error TS6133: 'req' is declared but its value is never read.
src/api/blog/dto/blog.dto.ts(7,3): error TS6133: 'IsArray' is declared but its value is never read.
src/api/blog/dto/blog.dto.ts(8,3): error TS6133: 'ValidateNested' is declared but its value is never read.
src/api/blog/dto/blog.dto.ts(10,1): error TS6133: 'Type' is declared but its value is never read.
src/api/payment/controllers/payment.controller.ts(4,10): error TS6133: 'Request' is declared but its value is never read.
src/api/payment/controllers/payment.controller.ts(12,3): error TS6133: 'UpdatePaymentDto' is declared but its value is never read.
src/api/payment/controllers/payment.controller.ts(14,3): error TS6133: 'UpdateRefundDto' is declared but its value is never read.
src/api/payment/controllers/payment.controller.ts(16,3): error TS6133: 'UpdateSubscriptionDto' is declared but its value is never read.
src/api/payment/controllers/payment.controller.ts(18,3): error TS6133: 'CreateSubscriptionPaymentDto' is declared but its value is never read.
src/api/payment/dto/payment.dto.ts(7,3): error TS6133: 'IsBoolean' is declared but its value is never read.
src/api/payment/dto/payment.dto.ts(10,3): error TS6133: 'IsArray' is declared but its value is never read.
src/api/payment/dto/payment.dto.ts(13,3): error TS6133: 'ValidateNested' is declared but its value is never read.
src/api/payment/dto/payment.dto.ts(15,1): error TS6133: 'Type' is declared but its value is never read.
src/api/payment/dto/subscription.dto.ts(5,3): error TS6133: 'IsNumber' is declared but its value is never read.
src/api/payment/dto/subscription.dto.ts(6,3): error TS6133: 'IsEnum' is declared but its value is never read.
src/api/payment/dto/subscription.dto.ts(10,3): error TS6133: 'IsArray' is declared but its value is never read.
src/api/payment/dto/subscription.dto.ts(12,3): error TS6133: 'Min' is declared but its value is never read.
src/api/payment/dto/subscription.dto.ts(13,3): error TS6133: 'ValidateNested' is declared but its value is never read.
src/api/qa/controllers/index.ts(15,3): error TS6133: 'CreateTagDto' is declared but its value is never read.
src/api/qa/controllers/index.ts(16,3): error TS6133: 'UpdateTagDto' is declared but its value is never read.
src/api/qa/controllers/index.ts(320,17): error TS6133: 'req' is declared but its value is never read.
src/api/qa/controllers/index.ts(338,24): error TS6133: 'req' is declared but its value is never read.
src/api/qa/controllers/qa.controller.ts(18,3): error TS6133: 'CreateTagDto' is declared but its value is never read.
src/api/qa/controllers/qa.controller.ts(19,3): error TS6133: 'UpdateTagDto' is declared but its value is never read.
src/api/qa/dto/question.dto.ts(10,3): error TS6133: 'ArrayMinSize' is declared but its value is never read.
src/api/social/controllers/social.controller.ts(4,10): error TS6133: 'Request' is declared but its value is never read.
src/api/social/dto/channel.dto.ts(10,3): error TS6133: 'ArrayMinSize' is declared but its value is never read.
src/api/social/dto/notification.dto.ts(1,10): error TS6133: 'IsString' is declared but its value is never read.
src/api/social/dto/notification.dto.ts(1,20): error TS6133: 'IsNotEmpty' is declared but its value is never read.
src/api/social/dto/social.dto.ts(6,3): error TS6133: 'IsInt' is declared but its value is never read.
src/api/social/dto/social.dto.ts(7,3): error TS6133: 'IsEnum' is declared but its value is never read.
src/api/social/dto/social.dto.ts(8,3): error TS6133: 'IsArray' is declared but its value is never read.
src/api/users/dto/user.dto.ts(6,3): error TS6133: 'IsBoolean' is declared but its value is never read.
src/api/workshop/dto/module-lesson.dto.ts(4,10): error TS6133: 'IsBoolean' is declared but its value is never read.
src/api/workshop/dto/module.dto.ts(2,3): error TS6133: 'IsBoolean' is declared but its value is never read.
src/api/workshop/dto/workshop.dto.ts(14,3): error TS6133: 'ValidateNested' is declared but its value is never read.
src/api/workshop/dto/workshop.dto.ts(19,1): error TS6133: 'Type' is declared but its value is never read.
src/app.ts(42,42): error TS6133: 'res' is declared but its value is never read.
src/app.ts(114,15): error TS6133: 'res' is declared but its value is never read.
src/app.ts(126,21): error TS6133: 'req' is declared but its value is never read.
src/app.ts(130,25): error TS6133: 'req' is declared but its value is never read.
src/app.ts(159,29): error TS6133: 'res' is declared but its value is never read.
src/app.ts(192,15): error TS6133: 'req' is declared but its value is never read.
src/app/index.ts(42,33): error TS6133: 'res' is declared but its value is never read.
src/app/index.ts(53,36): error TS6133: 'req' is declared but its value is never read.
src/app/index.ts(77,24): error TS6133: 'req' is declared but its value is never read.
src/app/index.ts(86,31): error TS6133: 'req' is declared but its value is never read.
src/app/index.ts(86,60): error TS6133: 'next' is declared but its value is never read.
src/common/config.ts(3,1): error TS6133: 'path' is declared but its value is never read.
src/db/client.ts(5,6): error TS6196: 'PrismaEventTypes' is declared but never used.
src/db/repositories/conversation.repository.ts(20,11): error TS6133: 'prismaClient' is declared but its value is never read.
src/db/repositories/conversation.repository.ts(297,24): error TS2347: Untyped function calls may not accept type arguments.
src/db/repositories/project.repository.ts(5,3): error TS6133: 'PrismaClient' is declared but its value is never read.
src/db/repositories/user.repository.ts(5,1): error TS6133: 'PrismaClient' is declared but its value is never read.
src/guards/auth.guard.ts(7,11): error TS6133: 'request' is declared but its value is never read.
src/guards/role.guard.ts(15,11): error TS6133: 'request' is declared but its value is never read.
src/middleware/errorHandler.ts(80,9): error TS6133: '_formattedErrors' is declared but its value is never read.
src/middleware/validation.ts(27,56): error TS2554: Expected 2-3 arguments, but got 4.
src/routes/blog/blog.routes.ts(1,18): error TS6133: 'Request' is declared but its value is never read.
src/routes/blog/blog.routes.ts(1,27): error TS6133: 'Response' is declared but its value is never read.
src/routes/blog/blog.routes.ts(1,37): error TS6133: 'NextFunction' is declared but its value is never read.
src/routes/conversation-tasks/index.ts(1,37): error TS6133: 'NextFunction' is declared but its value is never read.
src/routes/conversation-tasks/index.ts(1,51): error TS6133: 'RequestHandler' is declared but its value is never read.
src/routes/conversation-tasks/index.ts(2,1): error TS6133: 'AuthenticatedRequest' is declared but its value is never read.
src/routes/conversation-tasks/index.ts(6,1): error TS6133: 'ChatResponse' is declared but its value is never read.
src/routes/conversation-tasks/index.ts(79,10): error TS6133: 'processTaskResponse' is declared but its value is never read.
src/routes/conversation-tasks/index.ts(146,3): error TS7030: Not all code paths return a value.
src/routes/conversation-tasks/index.ts(235,3): error TS7030: Not all code paths return a value.
src/routes/conversation-tasks/index.ts(343,3): error TS7030: Not all code paths return a value.
src/routes/conversation-tasks/index.ts(473,3): error TS7030: Not all code paths return a value.
src/routes/conversation-tasks/index.ts(602,49): error TS7030: Not all code paths return a value.
src/routes/conversation-tasks/index.ts(720,45): error TS7030: Not all code paths return a value.
src/routes/conversation/index.ts(10,1): error TS6133: 'ConversationStatus' is declared but its value is never read.
src/routes/conversation/index.ts(47,11): error TS6196: 'MockupRequest' is declared but never used.
src/routes/conversation/index.ts(55,11): error TS6196: 'TestCaseRequest' is declared but never used.
src/routes/conversation/index.ts(61,11): error TS6196: 'SlideRequest' is declared but never used.
src/routes/conversation/index.ts(164,33): error TS7030: Not all code paths return a value.
src/routes/conversation/index.ts(237,33): error TS7030: Not all code paths return a value.
src/routes/conversation/index.ts(294,36): error TS7030: Not all code paths return a value.
src/routes/conversation/index.ts(359,42): error TS7030: Not all code paths return a value.
src/routes/conversation/index.ts(433,19): error TS7030: Not all code paths return a value.
src/routes/conversation/index.ts(524,31): error TS7030: Not all code paths return a value.
src/routes/conversation/index.ts(606,34): error TS7030: Not all code paths return a value.
src/routes/conversation/index.ts(698,29): error TS7030: Not all code paths return a value.
src/routes/conversation/index.ts(778,38): error TS7030: Not all code paths return a value.
src/routes/conversation/index.ts(873,40): error TS7030: Not all code paths return a value.
src/routes/health.routes.ts(34,18): error TS6133: 'req' is declared but its value is never read.
src/routes/memory.routes.ts(1,37): error TS6133: 'NextFunction' is declared but its value is never read.
src/services/activity/activity.service.ts(6,1): error TS6133: 'PrismaClient' is declared but its value is never read.
src/services/activity/activity.service.ts(37,5): error TS6133: 'limit' is declared but its value is never read.
src/services/activity/activity.service.ts(38,5): error TS6133: 'skip' is declared but its value is never read.
src/services/activity/activity.service.ts(40,5): error TS6133: 'channelId' is declared but its value is never read.
src/services/activity/activity.service.ts(42,5): error TS6133: 'sortOrder' is declared but its value is never read.
src/services/agent/agent.service.ts(49,11): error TS6133: 'activeSessions' is declared but its value is never read.
src/services/agent/agent.service.ts(60,44): error TS6138: Property 'pipelineService' is declared but its value is never read.
src/services/agent/agent.service.ts(412,36): error TS2339: Property 'match' does not exist on type 'GenerateTextResponse'.
src/services/agent/agent.service.ts(512,36): error TS2339: Property 'match' does not exist on type 'GenerateTextResponse'.
src/services/ai/index.ts(183,5): error TS6133: 'headers' is declared but its value is never read.
src/services/audio.service.ts(3,1): error TS6133: 'Readable' is declared but its value is never read.
src/services/audio.service.ts(31,11): error TS6133: 'sttDeploymentName' is declared but its value is never read.
src/services/conversation/base-task-strategy.ts(9,37): error TS6133: 'TaskOptions' is declared but its value is never read.
src/services/conversation/base-task-strategy.ts(58,43): error TS6133: 'context' is declared but its value is never read.
src/services/conversation/strategies/chat-task-strategy.ts(61,13): error TS6133: 'startTime' is declared but its value is never read.
src/services/conversation/strategies/code-generation-task-strategy.ts(68,17): error TS6133: 'context' is declared but its value is never read.
src/services/conversation/strategies/code-generation-task-strategy.ts(76,24): error TS6133: 'context' is declared but its value is never read.
src/services/conversation/strategies/code-planning-task-strategy.ts(59,17): error TS6133: 'context' is declared but its value is never read.
src/services/conversation/strategies/code-planning-task-strategy.ts(67,24): error TS6133: 'context' is declared but its value is never read.
src/services/conversation/strategies/slide-generation-task-strategy.ts(70,17): error TS6133: 'context' is declared but its value is never read.
src/services/conversation/strategies/slide-generation-task-strategy.ts(78,24): error TS6133: 'context' is declared but its value is never read.
src/services/conversation/strategies/svg-mockup-task-strategy.ts(78,17): error TS6133: 'context' is declared but its value is never read.
src/services/conversation/strategies/svg-mockup-task-strategy.ts(86,24): error TS6133: 'context' is declared but its value is never read.
src/services/conversation/strategies/test-cases-task-strategy.ts(80,17): error TS6133: 'context' is declared but its value is never read.
src/services/conversation/strategies/test-cases-task-strategy.ts(88,24): error TS6133: 'context' is declared but its value is never read.
src/services/conversation/unified-conversation.service.ts(31,11): error TS6196: 'TokenUsage' is declared but never used.
src/services/file-storage/file-storage.service.ts(9,1): error TS6133: 'Readable' is declared but its value is never read.
src/services/file-storage/file-storage.service.ts(10,1): error TS6133: 'finished' is declared but its value is never read.


src/services/user-onboarding.service.ts(9,32): error TS6133: 'ProjectMotivationType' is declared but its value is never read.
src/services/user-onboarding.service.ts(11,21): error TS6133: 'AIProvider' is declared but its value is never read.
src/services/user-onboarding.service.ts(119,35): error TS2339: Property 'match' does not exist on type 'GenerateTextResponse'.
src/services/user-onboarding.service.ts(125,36): error TS2345: Argument of type 'GenerateTextResponse' is not assignable to parameter of type 'string'.
src/services/user.service.ts(8,22): error TS6133: 'UserUpdate' is declared but its value is never read.
src/utils/serverSetup.example.ts(10,18): error TS6133: 'loggerStream' is declared but its value is never read.
src/utils/serverSetup.example.ts(49,17): error TS6133: 'res' is declared but its value is never read.
src/utils/serverSetup.example.ts(60,23): error TS6133: 'req' is declared but its value is never read.
src/utils/serverSetup.example.ts(71,25): error TS6133: 'req' is declared but its value is never read.
src/utils/serverSetup.example.ts(81,25): error TS6133: 'req' is declared but its value is never read.
src/utils/serverSetup.example.ts(81,30): error TS6133: 'res' is declared but its value is never read.
src/websockets/nova-sonic/nova-sonic.socket.ts(149,52): error TS6133: 'session' is declared but its value is never read.
src/websockets/nova-sonic/nova-sonic.socket.ts(430,15): error TS6133: 'modelType' is declared but its value is never read.
src/websockets/nova-sonic/session-manager.ts(5,1): error TS6133: 'randomUUID' is declared but its value is never read.
src/websockets/nova-sonic/session-manager.ts(6,23): error TS6133: 'SessionStatus' is declared but its value is never read.
src/websockets/nova-sonic/session-manager.ts(16,20): error TS6133: 'SESSION_TIMEOUT_MS' is declared but its value is never read.
src/websockets/nova-sonic/tool-handlers.ts(380,48): error TS6133: 'colorScheme' is declared but its value is never read.
src/websockets/nova-sonic/tool-handlers.ts(380,69): error TS6133: 'size' is declared but its value is never read.
src/websockets/nova-sonic/tool-handlers.ts(400,50): error TS6133: 'colorScheme' is declared but its value is never read.
src/websockets/nova-sonic/tool-handlers.ts(400,71): error TS6133: 'size' is declared but its value is never read.
src/websockets/nova-sonic/tool-handlers.ts(417,51): error TS6133: 'colorScheme' is declared but its value is never read.
src/websockets/nova-sonic/tool-handlers.ts(417,72): error TS6133: 'size' is declared but its value is never read.
src/websockets/nova-sonic/tool-handlers.ts(528,55): error TS6133: 'focusArea' is declared but its value is never read.
src/websockets/nova-sonic/tool-handlers.ts(549,29): error TS6133: 'code' is declared but its value is never read.
src/websockets/nova-sonic/tool-handlers.ts(549,43): error TS6133: 'language' is declared but its value is never read.
tests/audio/mock-audio.service.ts(5,1): error TS6133: 'protos' is declared but its value is never read.
tests/audio/mock-audio.service.ts(6,1): error TS6133: 'Readable' is declared but its value is never read.
tests/audio/mock-audio.service.ts(17,22): error TS6133: 'text' is declared but its value is never read.
tests/audio/mock-audio.service.ts(17,36): error TS6133: 'voice' is declared but its value is never read.
tests/audio/mock-audio.service.ts(28,30): error TS6133: 'audioBytes' is declared but its value is never read.
tests/audio/mock-audio.service.ts(28,50): error TS6133: 'filename' is declared but its value is never read.
tests/audio/mock-audio.service.ts(33,31): error TS6133: 'audioStream' is declared but its value is never read.
tests/audio/mock-audio.service.ts(33,67): error TS6133: 'filename' is declared but its value is never read.
tests/audio/mock-audio.service.ts(39,3): error TS4053: Return type of public method from exported class has or is using name 'VoiceConfigMap' from external module "/Users/<USER>/Code/kapi-fresh/nodejs_backend/src/services/audio.service" but cannot be named.
tests/audio/type-verification.ts(6,7): error TS6133: 'testClient' is declared but its value is never read.
tests/audio/type-verification.ts(7,7): error TS6133: 'testAudioConfig' is declared but its value is never read.
tests/nova-sonic/mock-nova-sonic.ts(6,1): error TS6133: 'Subject' is declared but its value is never read.
tests/nova-sonic/mock-nova-sonic.ts(7,46): error TS6133: 'StreamSession' is declared but its value is never read.
tests/setup.ts(5,13): error TS2540: Cannot assign to 'NODE_ENV' because it is a read-only property.
