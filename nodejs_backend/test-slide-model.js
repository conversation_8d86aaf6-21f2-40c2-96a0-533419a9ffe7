#!/usr/bin/env node

/**
 * Test script to verify slide generation model selection
 */

const { taskStrategyRegistry } = require('./dist/src/services/conversation/task-strategy-registry');

console.log('🔧 Testing slide generation model selection...\n');

try {
  // Get the slide generation strategy
  const strategy = taskStrategyRegistry.getStrategy('slides');
  
  console.log('✅ Strategy found:', strategy.constructor.name);
  console.log('✅ Task type:', strategy.getTaskType());
  console.log('✅ Default model:', strategy.getDefaultModel());
  console.log('✅ Default max tokens:', strategy.getDefaultMaxTokens());
  console.log('✅ Default temperature:', strategy.getDefaultTemperature());
  
  // Test the model detection in AI service
  const aiService = require('./dist/src/services/ai/index').default;
  
  console.log('\n🔧 Testing AI service model detection...');
  
  // Simulate what would happen with claude-3.7-sonnet
  const model = 'claude-3.7-sonnet';
  console.log('✅ Testing model:', model);
  console.log('✅ Contains "claude":', model.includes('claude'));
  console.log('✅ Contains "anthropic":', model.includes('anthropic'));
  
  // Test config lookup
  const config = require('./dist/config/config').config;
  console.log('\n🔧 Testing config model priorities...');
  
  const slidesPriority = config.getModelPriorities('slides');
  if (slidesPriority && slidesPriority.length > 0) {
    console.log('✅ Slides priority list:', slidesPriority);
    console.log('✅ First priority model:', slidesPriority[0]);
  } else {
    console.log('❌ No slides priority found in config');
  }
  
  const codeGenBigPriority = config.getModelPriorities('code_gen_big');
  if (codeGenBigPriority && codeGenBigPriority.length > 0) {
    console.log('✅ code_gen_big priority list:', codeGenBigPriority);
    console.log('✅ First priority model:', codeGenBigPriority[0]);
  } else {
    console.log('❌ No code_gen_big priority found in config');
  }
  
  // Test YAML config loading
  console.log('\n🔧 Testing YAML config structure...');
  console.log('✅ YAML config keys:', Object.keys(config.yamlConfig || {}));
  console.log('✅ Priorities section:', config.yamlConfig?.priorities ? 'exists' : 'missing');
  
  console.log('\n✅ Test completed successfully!');
  
} catch (error) {
  console.error('❌ Test failed:', error.message);
  console.error(error.stack);
}