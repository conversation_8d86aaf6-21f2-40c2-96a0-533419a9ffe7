#!/bin/bash

echo "🛠️  Setting up audit tools for Kapi website..."
echo "=============================================="

# Install Playwright in this directory for screenshots
echo "📦 Installing Playwright..."
npm install playwright

echo "🌐 Installing Playwright browsers..."
npx playwright install chromium

echo "✅ Audit tools setup complete!"
echo ""
echo "📋 Available commands:"
echo "   ./check-links.sh                    - Check for broken links"
echo "   node screenshot-pages.js            - Take full-page screenshots"
echo "   ./site-audit.sh                     - Complete audit (links + screenshots)"
echo ""
echo "🚀 Ready to audit your site at http://localhost:3001"