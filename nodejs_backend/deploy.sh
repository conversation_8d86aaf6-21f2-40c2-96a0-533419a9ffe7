#!/bin/bash

# Deploy script for <PERSON><PERSON> backend and Nova Sonic service
set -e

echo "🚀 Starting deployment..."

# Pull latest code
git pull origin main

# Deploy backend service
echo "📦 Installing backend dependencies..."
cd nodejs_backend
npm ci --production

# Install Next.js dependencies
cd src/next
npm ci --production
cd ../..

# Build the backend
echo "🔨 Building backend application..."
npm run build:all

# Run database migrations
echo "🗄️ Running database migrations..."
npm run migration:run

cd ..

# Deploy Nova Sonic service
echo "📦 Installing Nova Sonic dependencies..."
cd services/nova-sonic-service
npm ci --production

# Build Nova Sonic
echo "🔨 Building Nova Sonic service..."
npm run build

cd ../..

# Create PM2 logs directory
mkdir -p ~/kapi/pm2-logs

# Restart PM2 processes
echo "🔄 Restarting applications..."
pm2 reload nodejs_backend/ecosystem.config.json --env production

# Health checks
echo "🏥 Performing health checks..."
sleep 10

# Check backend health
curl -f http://localhost:3000/health || exit 1
echo "✅ Backend health check passed"

# Check Nova Sonic health
curl -f http://localhost:3005/health || echo "⚠️ Nova Sonic health check failed (service may not have health endpoint)"

echo "✅ Deployment completed successfully!"