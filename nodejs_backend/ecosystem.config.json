{"apps": [{"name": "kapi-backend", "script": "dist/src/server.js", "cwd": "/home/<USER>/kapi/kapi/nodejs_backend", "instances": "max", "exec_mode": "cluster", "env": {"NODE_ENV": "production", "PORT": 3000}, "env_production": {"NODE_ENV": "production", "PORT": 3000}, "log_file": "/home/<USER>/kapi/pm2-logs/kapi-backend.log", "error_file": "/home/<USER>/kapi/pm2-logs/kapi-backend-error.log", "out_file": "/home/<USER>/kapi/pm2-logs/kapi-backend-out.log", "merge_logs": true, "time": true, "max_memory_restart": "1G", "autorestart": true, "watch": false, "ignore_watch": ["node_modules", "logs", "tmp"], "node_args": ["--max-old-space-size=2048"]}, {"name": "nova-sonic-service", "script": "dist/server.js", "cwd": "/home/<USER>/kapi/kapi/services/nova-sonic-service", "instances": 1, "exec_mode": "fork", "env": {"NODE_ENV": "production", "PORT": 3005}, "env_production": {"NODE_ENV": "production", "PORT": 3005}, "log_file": "/home/<USER>/kapi/pm2-logs/nova-sonic.log", "error_file": "/home/<USER>/kapi/pm2-logs/nova-sonic-error.log", "out_file": "/home/<USER>/kapi/pm2-logs/nova-sonic-out.log", "merge_logs": true, "time": true, "max_memory_restart": "1G", "autorestart": true, "watch": false, "ignore_watch": ["node_modules", "logs", "dist"]}]}