const express = require('express');
const { Router } = express;

// Test if path-to-regexp works with various patterns
const testPatterns = [
  '/',
  '/test',
  '/test/:id',
  '/test/:id/edit',
  '/test/:id?',
  '/test/:id(\\d+)',
  '/test/*',
  '/test/**',
  '/test/:',  // This should cause an error
  '/test/:/edit',  // This should cause an error
  '/:',  // This should cause an error
];

console.log('Testing Express 5 route patterns...\n');

testPatterns.forEach((pattern, index) => {
  try {
    const router = Router();
    router.get(pattern, (req, res) => res.send('OK'));
    console.log(`✓ Pattern ${index + 1}: "${pattern}" - OK`);
  } catch (error) {
    console.log(`✗ Pattern ${index + 1}: "${pattern}" - ERROR: ${error.message}`);
  }
});

// Test with app.use
console.log('\nTesting with app.use...\n');

const app = express();
testPatterns.forEach((pattern, index) => {
  try {
    const router = Router();
    app.use(pattern, router);
    console.log(`✓ app.use ${index + 1}: "${pattern}" - OK`);
  } catch (error) {
    console.log(`✗ app.use ${index + 1}: "${pattern}" - ERROR: ${error.message}`);
  }
});
