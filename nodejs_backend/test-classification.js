const { unifiedConversationService } = require('./dist/src/services/conversation/unified-conversation.service');

async function testClassification() {
  console.log('🧪 Testing Intelligent Task Classification...\n');
  
  const testCases = [
    {
      name: 'Slide Generation',
      prompt: 'Create slides for my AI-powered e-commerce platform with payment integration',
      expected: 'slide_generation'
    },
    {
      name: 'Mockup Generation', 
      prompt: 'Design a wireframe for a dashboard with charts and user analytics',
      expected: 'svg_mockup'
    },
    {
      name: 'Code Generation',
      prompt: 'Write a React component for user authentication with form validation',
      expected: 'code_generation'
    }
  ];

  for (const testCase of testCases) {
    try {
      console.log(`🔍 Test: ${testCase.name}`);
      console.log(`📝 Prompt: "${testCase.prompt}"`);
      
      // Access the private method through reflection (for testing)
      const taskType = await unifiedConversationService.classifyTaskType(testCase.prompt);
      
      console.log(`🎯 Result: ${taskType}`);
      console.log(`✅ Expected: ${testCase.expected}`);
      console.log(`${taskType === testCase.expected ? '✅ PASS' : '❌ FAIL'}\n`);
      
    } catch (error) {
      console.log(`❌ ERROR: ${error.message}\n`);
    }
  }
}

testClassification().catch(console.error);