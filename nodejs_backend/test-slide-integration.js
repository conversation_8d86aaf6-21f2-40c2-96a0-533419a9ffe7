#!/usr/bin/env node

/**
 * Integration test to run actual slide generation
 */

const axios = require('axios');

async function testSlideGeneration() {
  console.log('🔧 Testing slide generation integration...\n');
  
  try {
    const BASE_URL = 'http://localhost:3000';
    
    // Test the conversation task slides endpoint
    console.log('✅ Testing /tasks/slides endpoint...');
    
    const response = await axios.post(`${BASE_URL}/tasks/slides`, {
      prompt: "Create a professional banking app slide deck",
      topic: "Modern Banking Application",
      numSlides: 9,
      format: "html",
      conversationId: 1,
      maxTokens: 8192,
      temperature: 0.7
    }, {
      headers: {
        'Content-Type': 'application/json',
        // Add basic auth header for testing - you may need to adjust this
        'Authorization': `Bearer ${process.env.TEST_TOKEN || 'test'}`
      },
      timeout: 60000 // 60 second timeout
    });
    
    console.log('✅ Response status:', response.status);
    console.log('✅ Response data keys:', Object.keys(response.data));
    
    if (response.data.message) {
      console.log('✅ Model used:', response.data.message.model);
      console.log('✅ Content length:', response.data.message.content?.length || 0);
      console.log('✅ Token usage:', {
        prompt: response.data.message.promptTokens,
        completion: response.data.message.completionTokens,
        cost: response.data.message.cost
      });
      
      // Check if it's HTML content
      const content = response.data.message.content || '';
      const isHTML = content.includes('<!DOCTYPE html>') || content.includes('<html');
      console.log('✅ Contains HTML:', isHTML);
      
      if (isHTML) {
        console.log('✅ HTML preview (first 200 chars):', content.substring(0, 200));
        console.log('✅ HTML preview (last 200 chars):', content.substring(content.length - 200));
      }
    }
    
    console.log('\n✅ Integration test completed successfully!');
    
  } catch (error) {
    console.error('❌ Integration test failed:', error.response?.data || error.message);
    
    if (error.response?.status === 401) {
      console.log('💡 Note: Authentication required. This is expected in production.');
    }
  }
}

testSlideGeneration();