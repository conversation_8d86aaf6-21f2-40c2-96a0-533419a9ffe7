#!/bin/bash

# Link Checker Script for Kapi Website
# Usage: ./check-links.sh [URL] [options]

URL=${1:-"http://localhost:3001"}
OPTIONS=${2:-""}

echo "🔍 Checking links on: $URL"
echo "=========================="

# Basic link check
echo "Running basic link check..."
blc $URL --recursive --filter-level 2 --ordered $OPTIONS

echo ""
echo "📊 Summary complete!"
echo "Check output above for any broken links."

# Advanced check excluding external links (faster)
echo ""
echo "🚀 Want to run faster check (excluding external links)?"
echo "Run: blc $URL --recursive --exclude-external --filter-level 2"