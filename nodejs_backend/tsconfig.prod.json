{"extends": "./tsconfig.json", "compilerOptions": {"strict": false, "noImplicitAny": false, "strictNullChecks": false, "strictFunctionTypes": false, "strictBindCallApply": false, "noImplicitThis": false, "noImplicitReturns": false, "noUnusedLocals": false, "noUnusedParameters": false, "skipLibCheck": true, "skipDefaultLibCheck": true}, "include": ["src/app.ts", "src/server.ts", "src/initialize.ts", "src/inversify.config.ts", "src/types.ts", "src/common/**/*", "src/config/**/*", "src/db/client.ts", "src/db/database.ts", "src/db/index.ts", "src/db/prisma.service.ts", "src/db/database.service.ts", "src/db/repositories/base.repository.ts", "src/db/repositories/concrete-base.repository.ts", "src/db/repositories/index.ts", "src/db/repositories/project.repository.ts", "src/db/repositories/user.repository.ts", "src/db/repositories/ai/**/*", "src/db/repositories/gamification/**/*", "src/db/repositories/payment/**/*", "src/db/repositories/qa/**/*", "src/db/repositories/social/**/*", "src/db/repositories/template/**/*", "src/db/repositories/workshop/**/*", "src/middleware/**/*", "src/routes/index.ts", "src/routes/conversation-tasks/**/*", "src/routes/health.routes.ts", "src/routes/memory.routes.ts", "src/routes/auth/**/*", "src/routes/blog/**/*", "src/routes/conversation/**/*", "src/routes/payment.routes.ts", "src/routes/projects/**/*", "src/routes/qa.routes.ts", "src/routes/social.routes.ts", "src/routes/socket-test.routes.ts", "src/routes/template.routes.ts", "src/routes/users/**/*", "src/routes/webhooks/**/*", "src/api/blog/**/*", "src/api/conversations/**/*", "src/api/projects/**/*", "src/api/users/**/*", "src/api/workshop/**/*", "src/api/ai/**/*", "src/api/auth/**/*", "src/api/payment/**/*", "src/api/qa/**/*", "src/api/social/**/*", "src/api/template/**/*", "src/services/base.service.ts", "src/services/clerk.service.ts", "src/services/model-usage.service.ts", "src/services/project.service.ts", "src/services/unified-conversation.service.ts", "src/services/audio.service.ts", "src/services/index.ts", "src/services/conversation/**/*", "src/services/ai/**/*", "src/services/activity/**/*", "src/services/file-storage/**/*", "src/services/memory/**/*", "src/services/payment/**/*", "src/services/qa/**/*", "src/services/search/**/*", "src/services/social/**/*", "src/services/template/**/*", "src/services/workshop/**/*", "src/utils/**/*", "src/websockets/**/*", "src/docs/**/*"]}