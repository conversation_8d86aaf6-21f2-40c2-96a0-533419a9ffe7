import { CodeAnalysisService } from '../../src/services/code-analysis.service';
import { PrismaService } from '../../src/db/prisma.service';
import UnifiedConversationService from '../../src/services/unified-conversation.service';
import ProjectService from '../../src/services/project.service';

// Mock dependencies
jest.mock('../../src/db/prisma.service');
jest.mock('../../src/services/unified-conversation.service');
jest.mock('../../src/services/project.service');
jest.mock('ts-morph');

describe('CodeAnalysisService', () => {
  let codeAnalysisService: CodeAnalysisService;
  let mockPrismaService: jest.Mocked<PrismaService>;
  let mockConversationService: jest.Mocked<UnifiedConversationService>;
  let mockProjectService: jest.Mocked<ProjectService>;

  beforeEach(() => {
    // Reset mocks
    mockPrismaService = new PrismaService() as jest.Mocked<PrismaService>;
    mockConversationService = new UnifiedConversationService() as jest.Mocked<UnifiedConversationService>;
    mockProjectService = new ProjectService() as jest.Mocked<ProjectService>;

    // Create service instance
    codeAnalysisService = new CodeAnalysisService(
      mockPrismaService,
      mockConversationService,
      mockProjectService
    );
  });

  describe('calculateRiskLevel', () => {
    it('should return critical for high complexity', () => {
      // Use reflection to access private method for testing
      const calculateRiskLevel = (codeAnalysisService as any).calculateRiskLevel;
      
      expect(calculateRiskLevel(20, 8, 15)).toBe('critical');
    });

    it('should return high for moderate complexity', () => {
      const calculateRiskLevel = (codeAnalysisService as any).calculateRiskLevel;
      
      expect(calculateRiskLevel(15, 4, 8)).toBe('high');
    });

    it('should return medium for some complexity', () => {
      const calculateRiskLevel = (codeAnalysisService as any).calculateRiskLevel;
      
      expect(calculateRiskLevel(8, 3, 5)).toBe('medium');
    });

    it('should return low for minimal complexity', () => {
      const calculateRiskLevel = (codeAnalysisService as any).calculateRiskLevel;
      
      expect(calculateRiskLevel(2, 1, 3)).toBe('low');
    });
  });

  describe('calculateHealthScores', () => {
    it('should calculate correct scores for empty metrics', () => {
      const calculateHealthScores = (codeAnalysisService as any).calculateHealthScores;
      
      const result = calculateHealthScores([], []);
      
      expect(result.overallScore).toBe(100);
      expect(result.complexityScore).toBe(100);
      expect(result.maintainability).toBe(100);
      expect(result.technicalDebt).toBe(0);
    });

    it('should calculate scores based on metrics and patterns', () => {
      const calculateHealthScores = (codeAnalysisService as any).calculateHealthScores;
      
      const fileMetrics = [
        { complexity: 15, nestingDepth: 5, functionCount: 8 },
        { complexity: 8, nestingDepth: 3, functionCount: 12 }
      ];
      
      const patterns = [
        { severity: 'critical' },
        { severity: 'high' },
        { severity: 'medium' }
      ];
      
      const result = calculateHealthScores(fileMetrics, patterns);
      
      expect(result.overallScore).toBeGreaterThan(0);
      expect(result.overallScore).toBeLessThan(100);
      expect(result.technicalDebt).toBeGreaterThan(0);
    });
  });

  describe('extractRecommendations', () => {
    it('should extract recommendations from AI response', () => {
      const extractRecommendations = (codeAnalysisService as any).extractRecommendations;
      
      const content = `
Analysis complete.

Priority Recommendations:
- Fix the complex function in payment.ts
- Add error handling to async operations
- Reduce nesting depth in validation logic

Additional notes here.
`;
      
      const recommendations = extractRecommendations(content);
      
      expect(recommendations).toHaveLength(3);
      expect(recommendations[0]).toBe('Fix the complex function in payment.ts');
      expect(recommendations[1]).toBe('Add error handling to async operations');
      expect(recommendations[2]).toBe('Reduce nesting depth in validation logic');
    });
  });

  describe('buildAnalysisPrompt', () => {
    it('should build comprehensive analysis prompt', () => {
      const buildAnalysisPrompt = (codeAnalysisService as any).buildAnalysisPrompt;
      
      const mockReport = {
        overallScore: 75,
        metrics: {
          avgComplexity: 12,
          highComplexityFiles: 3,
          missingErrorHandling: 2
        },
        fileCount: 25,
        issues: [
          {
            severity: 'critical',
            description: 'High complexity function',
            filePath: 'payment.ts',
            lineNumber: 45
          },
          {
            severity: 'high',
            description: 'Missing error handling',
            filePath: 'auth.ts'
          }
        ]
      };
      
      const prompt = buildAnalysisPrompt(mockReport);
      
      expect(prompt).toContain('Project Health Score: 75/100');
      expect(prompt).toContain('Critical Issues (1)');
      expect(prompt).toContain('High Priority Issues (1)');
      expect(prompt).toContain('Average function complexity: 12');
      expect(prompt).toContain('payment.ts:45');
      expect(prompt).toContain('auth.ts');
    });
  });
});

describe('CodeAnalysisService Integration', () => {
  // These would be integration tests that test the service with real dependencies
  // For now, they're placeholders to show the structure
  
  it('should integrate with Prisma for data persistence', async () => {
    // Test that the service correctly stores analysis results in the database
    expect(true).toBe(true); // Placeholder
  });

  it('should integrate with ConversationService for AI analysis', async () => {
    // Test that the service correctly calls the conversation service for AI insights
    expect(true).toBe(true); // Placeholder
  });

  it('should handle file system errors gracefully', async () => {
    // Test error handling when project files can't be read
    expect(true).toBe(true); // Placeholder
  });
});