import * as dotenv from 'dotenv';
import { fromIni } from '@aws-sdk/credential-providers';

// Load .env file
dotenv.config();

describe('AWS Credentials', () => {
  describe('Environment Variables', () => {
    it('should load AWS credentials from .env file', () => {
      expect(process.env.AWS_ACCESS_KEY_ID).toBeDefined();
      expect(process.env.AWS_SECRET_ACCESS_KEY).toBeDefined();
      expect(process.env.AWS_REGION).toBeDefined();
      
      expect(process.env.AWS_ACCESS_KEY_ID).toContain('AKIA');
      expect(process.env.AWS_REGION).toBe('us-east-1');
    });

    it('should determine to use environment credentials over profile', () => {
      const useEnvCredentials = process.env.AWS_ACCESS_KEY_ID && process.env.AWS_SECRET_ACCESS_KEY;
      expect(useEnvCredentials).toBeTruthy();
    });
  });

  describe('AWS SDK Credentials Loading', () => {
    it('should successfully load credentials from environment variables', async () => {
      // Test direct environment credential usage (simulating Nova Sonic configuration)
      const envCredentials = {
        accessKeyId: process.env.AWS_ACCESS_KEY_ID!,
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY!,
        sessionToken: process.env.AWS_SESSION_TOKEN,
      };

      expect(envCredentials.accessKeyId).toBeDefined();
      expect(envCredentials.secretAccessKey).toBeDefined();
      expect(envCredentials.accessKeyId).toMatch(/^AKIA[A-Z0-9]+$/);
    });

    it('should fallback to AWS profile when environment variables are not available', async () => {
      // Temporarily remove env vars to test profile fallback
      const originalAccessKey = process.env.AWS_ACCESS_KEY_ID;
      const originalSecretKey = process.env.AWS_SECRET_ACCESS_KEY;
      
      delete process.env.AWS_ACCESS_KEY_ID;
      delete process.env.AWS_SECRET_ACCESS_KEY;

      try {
        const profileName = process.env.AWS_PROFILE || 'bedrock-test';
        const credentials = fromIni({ profile: profileName });
        const creds = await credentials();
        
        expect(creds.accessKeyId).toBeDefined();
        expect(creds.secretAccessKey).toBeDefined();
        expect(creds.accessKeyId).toMatch(/^AKIA[A-Z0-9]+$/);
      } finally {
        // Restore environment variables
        if (originalAccessKey) process.env.AWS_ACCESS_KEY_ID = originalAccessKey;
        if (originalSecretKey) process.env.AWS_SECRET_ACCESS_KEY = originalSecretKey;
      }
    });

    it('should match Nova Sonic client configuration logic', () => {
      // Test the exact logic used in Nova Sonic WebSocket handler
      const useEnvCredentials = process.env.AWS_ACCESS_KEY_ID && process.env.AWS_SECRET_ACCESS_KEY;
      const AWS_PROFILE_NAME = process.env.AWS_PROFILE || 'bedrock-test';

      expect(useEnvCredentials).toBeTruthy();
      
      const clientConfig = {
        region: process.env.AWS_REGION || 'us-east-1',
        credentials: useEnvCredentials
          ? {
              accessKeyId: process.env.AWS_ACCESS_KEY_ID!,
              secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY!,
              sessionToken: process.env.AWS_SESSION_TOKEN,
            }
          : fromIni({ profile: AWS_PROFILE_NAME }),
      };

      expect(clientConfig.region).toBe('us-east-1');
      expect(clientConfig.credentials).toEqual({
        accessKeyId: process.env.AWS_ACCESS_KEY_ID,
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
        sessionToken: process.env.AWS_SESSION_TOKEN,
      });
    });
  });

  describe('Credentials Validation', () => {
    it('should have valid AWS access key format', () => {
      const accessKeyId = process.env.AWS_ACCESS_KEY_ID;
      expect(accessKeyId).toMatch(/^AKIA[A-Z0-9]{16}$/);
    });

    it('should have secret access key', () => {
      const secretAccessKey = process.env.AWS_SECRET_ACCESS_KEY;
      expect(secretAccessKey).toBeDefined();
      expect(secretAccessKey!.length).toBeGreaterThan(20);
    });

    it('should have correct region', () => {
      expect(process.env.AWS_REGION).toBe('us-east-1');
    });
  });
});