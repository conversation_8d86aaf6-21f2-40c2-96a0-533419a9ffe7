// Setup file for Jest tests
import 'reflect-metadata'; // Required for decorators

// Set test environment variables
// NODE_ENV is set by Jest automatically
process.env.LOG_LEVEL = 'error'; // Reduce noise during tests

// Mock console methods to reduce test output noise
global.console = {
  ...console,
  log: jest.fn(),
  debug: jest.fn(),
  info: jest.fn(),
  // Keep error and warn for important messages
  error: console.error,
  warn: console.warn,
};

// Add custom matchers if needed
// expect.extend({
//   // Custom matchers here
// });

// Global test utilities
export const testUtils = {
  // Add reusable test utilities here
  async wait(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  },
};

// Cleanup after all tests
afterAll(async () => {
  // Close database connections, clear caches, etc.
});
