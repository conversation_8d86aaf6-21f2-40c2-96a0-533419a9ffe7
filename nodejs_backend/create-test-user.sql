-- Create a test user for development
INSERT INTO users (
  id, 
  email, 
  clerk_id, 
  role, 
  is_active, 
  created_at, 
  updated_at
) VALUES (
  1,
  '<EMAIL>',
  'test_admin_clerk_id',
  'ADMIN',
  true,
  NOW(),
  NOW()
) ON CONFLICT (id) DO NOTHING;

-- Create a fallback user with ID 2 as well
INSERT INTO users (
  id, 
  email, 
  clerk_id, 
  role, 
  is_active, 
  created_at, 
  updated_at
) VALUES (
  2,
  '<EMAIL>',
  'test_user_clerk_id',
  'DEVELOPER',
  true,
  NOW(),
  NOW()
) ON CONFLICT (id) DO NOTHING;

-- Reset the sequence to start from 3 for new users
SELECT setval('users_id_seq', (SELECT GREATEST(MAX(id), 2) FROM users), true);
