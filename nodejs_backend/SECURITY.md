# Security Guide

## Critical Security Fixes Applied

### ✅ P0 Critical Issues Fixed

1. **Authentication Bypass Removed**
   - Removed automatic admin user creation in development
   - Added explicit environment variable controls
   - Requires `DEVELOPMENT_AUTH_BYPASS=true` to enable any bypass

2. **Hardcoded Secrets Eliminated**
   - Removed `'dev-ide-test-token'` hardcoded token
   - All development bypasses now require environment variables
   - Added explicit warnings for insecure development settings

3. **Session Logging Secured**
   - Removed sensitive session data from logs
   - Only logs session existence, not contents
   - Eliminated cookie logging

4. **WebSocket Authentication Added**
   - WebSocket connections now require JWT authentication
   - Session ownership verification implemented
   - Proper error handling for unauthorized connections

## Environment Variables Security

### Required for Production
```bash
# Database
DATABASE_URL="postgresql://..."

# Clerk Authentication
CLERK_SECRET_KEY="..."
CLERK_FRONTEND_API="..."
CLERK_JWKS_URL="..."

# Sessions
SESSION_SECRET="long-random-secret"

# Application
NODE_ENV="production"
CORS_ORIGIN="https://your-domain.com"
```

### Development Security Controls
```bash
# DANGEROUS - Only for development
DEVELOPMENT_AUTH_BYPASS="false"  # Set to true only if needed
DEV_ADMIN_USER="admin"
DEV_ADMIN_PASS="secure-password"
DEV_IDE_TOKEN="random-secure-token"
ALLOW_UNVERIFIED_JWT="false"    # Set to true only if needed
```

## Security Checklist for Deployment

### Pre-Deployment
- [ ] All environment variables set in production
- [ ] No hardcoded secrets in code
- [ ] `NODE_ENV=production`
- [ ] `DEVELOPMENT_AUTH_BYPASS=false` or unset
- [ ] `ALLOW_UNVERIFIED_JWT=false` or unset
- [ ] Strong session secret configured
- [ ] CORS origins properly configured
- [ ] Rate limiting enabled

### Post-Deployment
- [ ] Test authentication works correctly
- [ ] Verify WebSocket connections require auth
- [ ] Check logs for security warnings
- [ ] Confirm no development bypasses active
- [ ] Test with invalid tokens to ensure rejection

## Security Headers

Ensure these headers are configured:
```
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
Strict-Transport-Security: max-age=31536000; includeSubDomains
```

## Monitoring

Watch for these in logs:
- `SECURITY WARNING` messages
- Failed authentication attempts
- WebSocket connection rejections
- JWT verification failures

## Incident Response

If security issues are detected:
1. Check environment variables are properly set
2. Verify no development bypasses are enabled in production
3. Review authentication logs for patterns
4. Rotate secrets if compromise suspected