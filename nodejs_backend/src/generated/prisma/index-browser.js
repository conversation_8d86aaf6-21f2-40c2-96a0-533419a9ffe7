
/* !!! This is code generated by Prisma. Do not edit directly. !!!
/* eslint-disable */

Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('./runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.8.2
 * Query Engine version: 2060c79ba17c6bb9f5823312b6f6b7f4a845738e
 */
Prisma.prismaVersion = {
  client: "6.8.2",
  engine: "2060c79ba17c6bb9f5823312b6f6b7f4a845738e"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
});

exports.Prisma.Ai_agent_actionsScalarFieldEnum = {
  id: 'id',
  agent_id: 'agent_id',
  action_type: 'action_type',
  input: 'input',
  output: 'output',
  execution_time: 'execution_time',
  status: 'status',
  error_message: 'error_message',
  created_at: 'created_at',
  metadata: 'metadata'
};

exports.Prisma.Ai_agentsScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  provider: 'provider',
  model_name: 'model_name',
  system_prompt: 'system_prompt',
  temperature: 'temperature',
  max_tokens: 'max_tokens',
  capabilities: 'capabilities',
  daily_token_limit: 'daily_token_limit',
  monthly_token_limit: 'monthly_token_limit',
  cost_per_1k_tokens_input: 'cost_per_1k_tokens_input',
  cost_per_1k_tokens_output: 'cost_per_1k_tokens_output',
  user_id: 'user_id',
  project_id: 'project_id',
  is_active: 'is_active',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.Ai_prompt_templatesScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  system_prompt: 'system_prompt',
  template: 'template',
  parameters: 'parameters',
  user_id: 'user_id',
  is_public: 'is_public',
  version: 'version',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.Answer_votesScalarFieldEnum = {
  id: 'id',
  answer_id: 'answer_id',
  user_id: 'user_id',
  vote_value: 'vote_value',
  created_at: 'created_at'
};

exports.Prisma.AnswersScalarFieldEnum = {
  id: 'id',
  content: 'content',
  code_snippet: 'code_snippet',
  code_language: 'code_language',
  timestamp: 'timestamp',
  updated_at: 'updated_at',
  is_accepted: 'is_accepted',
  question_id: 'question_id',
  author_id: 'author_id'
};

exports.Prisma.Blog_post_tagsScalarFieldEnum = {
  blog_post_id: 'blog_post_id',
  tag_id: 'tag_id'
};

exports.Prisma.Blog_postsScalarFieldEnum = {
  id: 'id',
  title: 'title',
  content: 'content',
  slug: 'slug',
  author_id: 'author_id',
  published: 'published',
  published_at: 'published_at',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.BookmarksScalarFieldEnum = {
  id: 'id',
  user_id: 'user_id',
  entity_type: 'entity_type',
  entity_id: 'entity_id',
  created_at: 'created_at'
};

exports.Prisma.Channel_membersScalarFieldEnum = {
  channel_id: 'channel_id',
  user_id: 'user_id',
  joined_at: 'joined_at',
  role: 'role',
  last_read_at: 'last_read_at'
};

exports.Prisma.ChannelsScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  is_private: 'is_private',
  created_at: 'created_at',
  updated_at: 'updated_at',
  creator_id: 'creator_id',
  project_id: 'project_id'
};

exports.Prisma.Chat_contextsScalarFieldEnum = {
  id: 'id',
  conversation_id: 'conversation_id',
  context_snapshot: 'context_snapshot',
  context_selection_rationale: 'context_selection_rationale',
  token_usage: 'token_usage',
  created_at: 'created_at'
};

exports.Prisma.Collection_templatesScalarFieldEnum = {
  collection_id: 'collection_id',
  template_id: 'template_id'
};

exports.Prisma.Context_registryScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  contextType: 'contextType',
  is_enabled: 'is_enabled',
  priority: 'priority',
  max_tokens: 'max_tokens',
  default_format: 'default_format',
  created_at: 'created_at',
  updated_at: 'updated_at',
  metadata: 'metadata'
};

exports.Prisma.Context_versionsScalarFieldEnum = {
  id: 'id',
  context_id: 'context_id',
  version: 'version',
  content: 'content',
  format: 'format',
  token_count: 'token_count',
  changed_by: 'changed_by',
  change_reason: 'change_reason',
  created_at: 'created_at'
};

exports.Prisma.ConversationsScalarFieldEnum = {
  id: 'id',
  user_id: 'user_id',
  title: 'title',
  status: 'status',
  created_at: 'created_at',
  updated_at: 'updated_at',
  meta_data: 'meta_data',
  settings: 'settings',
  project_id: 'project_id',
  key_objective: 'key_objective',
  category: 'category',
  user_rating: 'user_rating',
  user_feedback: 'user_feedback',
  is_pinned: 'is_pinned',
  agent_status: 'agent_status',
  agent_progress: 'agent_progress',
  agent_iteration_count: 'agent_iteration_count',
  agent_allowed_actions: 'agent_allowed_actions',
  agent_commands_executed: 'agent_commands_executed',
  agent_completion_summary: 'agent_completion_summary',
  last_activity_type: 'last_activity_type',
  parent_id: 'parent_id',
  agent_id: 'agent_id',
  file_paths: 'file_paths',
  total_tokens: 'total_tokens',
  total_cost: 'total_cost',
  current_stage: 'current_stage',
  agent_pipeline_stages: 'agent_pipeline_stages'
};

exports.Prisma.Elo_historyScalarFieldEnum = {
  id: 'id',
  user_id: 'user_id',
  previous_rating: 'previous_rating',
  new_rating: 'new_rating',
  change: 'change',
  reason: 'reason',
  programming_session_id: 'programming_session_id',
  opponent_id: 'opponent_id',
  timestamp: 'timestamp'
};

exports.Prisma.File_summariesScalarFieldEnum = {
  id: 'id',
  project_id: 'project_id',
  file_path: 'file_path',
  summary: 'summary',
  token_count: 'token_count',
  last_updated: 'last_updated',
  updated_at: 'updated_at',
  git_hash: 'git_hash',
  is_stale: 'is_stale'
};

exports.Prisma.Karma_transactionsScalarFieldEnum = {
  id: 'id',
  user_id: 'user_id',
  amount: 'amount',
  reason: 'reason',
  related_entity_type: 'related_entity_type',
  related_entity_id: 'related_entity_id',
  created_at: 'created_at'
};

exports.Prisma.Lesson_completionsScalarFieldEnum = {
  id: 'id',
  completed_at: 'completed_at',
  score: 'score',
  feedback: 'feedback',
  user_id: 'user_id',
  lesson_id: 'lesson_id'
};

exports.Prisma.LessonsScalarFieldEnum = {
  id: 'id',
  title: 'title',
  description: 'description',
  content: 'content',
  lesson_type: 'lesson_type',
  duration_minutes: 'duration_minutes',
  order_index: 'order_index',
  module_id: 'module_id',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.Memory_contextsScalarFieldEnum = {
  id: 'id',
  project_id: 'project_id',
  slide_deck_id: 'slide_deck_id',
  contextType: 'contextType',
  name: 'name',
  description: 'description',
  token_count: 'token_count',
  last_updated: 'last_updated',
  last_synced_at: 'last_synced_at',
  version: 'version',
  created_at: 'created_at',
  updated_at: 'updated_at',
  is_active: 'is_active',
  metadata: 'metadata'
};

exports.Prisma.Memory_slidesScalarFieldEnum = {
  id: 'id',
  slide_deck_id: 'slide_deck_id',
  title: 'title',
  content: 'content',
  order: 'order',
  format: 'format',
  token_count: 'token_count',
  last_updated: 'last_updated',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.Message_reactionsScalarFieldEnum = {
  id: 'id',
  message_id: 'message_id',
  user_id: 'user_id',
  reaction_type: 'reaction_type',
  created_at: 'created_at'
};

exports.Prisma.MessagesScalarFieldEnum = {
  id: 'id',
  conversation_id: 'conversation_id',
  role: 'role',
  content: 'content',
  model: 'model',
  prompt_tokens: 'prompt_tokens',
  completion_tokens: 'completion_tokens',
  cost: 'cost',
  duration_ms: 'duration_ms',
  user_feedback: 'user_feedback',
  project_id: 'project_id',
  created_at: 'created_at',
  meta_data: 'meta_data',
  code_language: 'code_language',
  code_snippet: 'code_snippet',
  file_path: 'file_path',
  line_start: 'line_start',
  line_end: 'line_end'
};

exports.Prisma.Model_usageScalarFieldEnum = {
  id: 'id',
  user_id: 'user_id',
  model_name: 'model_name',
  provider: 'provider',
  prompt_tokens: 'prompt_tokens',
  completion_tokens: 'completion_tokens',
  total_tokens: 'total_tokens',
  estimated_cost: 'estimated_cost',
  response_time: 'response_time',
  taskType: 'taskType',
  success: 'success',
  error_message: 'error_message',
  timestamp: 'timestamp',
  metadata: 'metadata'
};

exports.Prisma.Module_summariesScalarFieldEnum = {
  id: 'id',
  project_id: 'project_id',
  module_path: 'module_path',
  summary: 'summary',
  token_count: 'token_count',
  last_updated: 'last_updated',
  updated_at: 'updated_at',
  is_stale: 'is_stale'
};

exports.Prisma.ModulesScalarFieldEnum = {
  id: 'id',
  title: 'title',
  description: 'description',
  order_index: 'order_index',
  workshop_id: 'workshop_id',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.NotificationsScalarFieldEnum = {
  id: 'id',
  user_id: 'user_id',
  type: 'type',
  title: 'title',
  message: 'message',
  is_read: 'is_read',
  link: 'link',
  related_entity_type: 'related_entity_type',
  related_entity_id: 'related_entity_id',
  created_at: 'created_at',
  expires_at: 'expires_at'
};

exports.Prisma.Payment_refundsScalarFieldEnum = {
  id: 'id',
  payment_id: 'payment_id',
  amount: 'amount',
  reason: 'reason',
  status: 'status',
  provider_refund_id: 'provider_refund_id',
  refunded_by_user_id: 'refunded_by_user_id',
  created_at: 'created_at',
  updated_at: 'updated_at',
  completed_at: 'completed_at'
};

exports.Prisma.PaymentsScalarFieldEnum = {
  id: 'id',
  user_id: 'user_id',
  amount: 'amount',
  currency: 'currency',
  payment_method: 'payment_method',
  payment_provider: 'payment_provider',
  provider_payment_id: 'provider_payment_id',
  status: 'status',
  description: 'description',
  metadata: 'metadata',
  created_at: 'created_at',
  updated_at: 'updated_at',
  completed_at: 'completed_at'
};

exports.Prisma.Programming_sessionsScalarFieldEnum = {
  id: 'id',
  user_id: 'user_id',
  project_id: 'project_id',
  start_time: 'start_time',
  end_time: 'end_time',
  duration: 'duration',
  lines_added: 'lines_added',
  lines_deleted: 'lines_deleted',
  files_modified: 'files_modified',
  ai_interactions: 'ai_interactions',
  tokens_consumed: 'tokens_consumed',
  summary: 'summary',
  code_quality_delta: 'code_quality_delta'
};

exports.Prisma.Project_code_qualityScalarFieldEnum = {
  id: 'id',
  project_id: 'project_id',
  code_coverage: 'code_coverage',
  lint_score: 'lint_score',
  avg_function_length: 'avg_function_length',
  code_duplication: 'code_duplication',
  tech_debt_score: 'tech_debt_score',
  last_analysis: 'last_analysis'
};

exports.Prisma.Project_dependenciesScalarFieldEnum = {
  id: 'id',
  project_id: 'project_id',
  name: 'name',
  version: 'version',
  type: 'type',
  security_issues: 'security_issues',
  license: 'license'
};

exports.Prisma.Project_documentationScalarFieldEnum = {
  id: 'id',
  project_id: 'project_id',
  type: 'type',
  title: 'title',
  content: 'content',
  format: 'format',
  last_updated: 'last_updated'
};

exports.Prisma.Project_git_reposScalarFieldEnum = {
  id: 'id',
  project_id: 'project_id',
  url: 'url',
  branch: 'branch',
  access_token: 'access_token'
};

exports.Prisma.Project_objectivesScalarFieldEnum = {
  id: 'id',
  project_id: 'project_id',
  title: 'title',
  description: 'description',
  priority: 'priority',
  status: 'status'
};

exports.Prisma.Project_quality_metricsScalarFieldEnum = {
  id: 'id',
  project_id: 'project_id',
  name: 'name',
  value: 'value',
  target: 'target'
};

exports.Prisma.Project_slidesScalarFieldEnum = {
  id: 'id',
  project_id: 'project_id',
  title: 'title',
  content: 'content',
  order: 'order'
};

exports.Prisma.Project_tech_stacksScalarFieldEnum = {
  id: 'id',
  project_id: 'project_id',
  category: 'category',
  technology: 'technology',
  version: 'version'
};

exports.Prisma.Project_templatesScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  structure: 'structure',
  default_branch: 'default_branch',
  creator_id: 'creator_id',
  is_public: 'is_public',
  usage_count: 'usage_count',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.Project_testsScalarFieldEnum = {
  id: 'id',
  project_id: 'project_id',
  name: 'name',
  type: 'type',
  status: 'status',
  code: 'code'
};

exports.Prisma.Project_visualizationsScalarFieldEnum = {
  id: 'id',
  project_id: 'project_id',
  type: 'type',
  title: 'title',
  content: 'content',
  format: 'format'
};

exports.Prisma.ProjectsScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  user_id: 'user_id',
  repository_url: 'repository_url',
  main_branch: 'main_branch',
  local_path: 'local_path',
  language: 'language',
  framework: 'framework',
  tech_stack: 'tech_stack',
  project_type: 'project_type',
  project_motivation: 'project_motivation',
  template_id: 'template_id',
  domain: 'domain',
  target_audience: 'target_audience',
  constraints: 'constraints',
  stage: 'stage',
  is_public: 'is_public',
  is_active: 'is_active',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.Question_tagsScalarFieldEnum = {
  question_id: 'question_id',
  tag_id: 'tag_id'
};

exports.Prisma.Question_votesScalarFieldEnum = {
  id: 'id',
  question_id: 'question_id',
  user_id: 'user_id',
  vote_value: 'vote_value',
  created_at: 'created_at'
};

exports.Prisma.QuestionsScalarFieldEnum = {
  id: 'id',
  title: 'title',
  content: 'content',
  code_snippet: 'code_snippet',
  code_language: 'code_language',
  timestamp: 'timestamp',
  updated_at: 'updated_at',
  is_resolved: 'is_resolved',
  accepted_answer_id: 'accepted_answer_id',
  view_count: 'view_count',
  project_id: 'project_id',
  context_type: 'context_type',
  context_id: 'context_id',
  author_id: 'author_id'
};

exports.Prisma.ResourcesScalarFieldEnum = {
  id: 'id',
  title: 'title',
  description: 'description',
  type: 'type',
  url: 'url',
  content: 'content',
  is_required: 'is_required',
  workshop_id: 'workshop_id',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.Slide_decksScalarFieldEnum = {
  id: 'id',
  project_id: 'project_id',
  title: 'title',
  description: 'description',
  type: 'type',
  token_count: 'token_count',
  last_updated: 'last_updated',
  created_at: 'created_at',
  updated_at: 'updated_at',
  is_active: 'is_active',
  metadata: 'metadata'
};

exports.Prisma.Social_messagesScalarFieldEnum = {
  id: 'id',
  channel_id: 'channel_id',
  user_id: 'user_id',
  content: 'content',
  is_edited: 'is_edited',
  is_pinned: 'is_pinned',
  parent_id: 'parent_id',
  created_at: 'created_at',
  updated_at: 'updated_at',
  metadata: 'metadata'
};

exports.Prisma.Subscription_eventsScalarFieldEnum = {
  id: 'id',
  subscription_id: 'subscription_id',
  event_type: 'event_type',
  description: 'description',
  metadata: 'metadata',
  created_at: 'created_at'
};

exports.Prisma.Subscription_paymentsScalarFieldEnum = {
  id: 'id',
  subscription_id: 'subscription_id',
  payment_id: 'payment_id',
  billing_period_start: 'billing_period_start',
  billing_period_end: 'billing_period_end',
  created_at: 'created_at'
};

exports.Prisma.SubscriptionsScalarFieldEnum = {
  id: 'id',
  user_id: 'user_id',
  plan_name: 'plan_name',
  plan_id: 'plan_id',
  status: 'status',
  current_period_start: 'current_period_start',
  current_period_end: 'current_period_end',
  cancel_at_period_end: 'cancel_at_period_end',
  provider: 'provider',
  provider_subscription_id: 'provider_subscription_id',
  metadata: 'metadata',
  created_at: 'created_at',
  updated_at: 'updated_at',
  canceled_at: 'canceled_at',
  ended_at: 'ended_at'
};

exports.Prisma.TagsScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  category: 'category'
};

exports.Prisma.Task_commentsScalarFieldEnum = {
  id: 'id',
  task_id: 'task_id',
  user_id: 'user_id',
  content: 'content',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.Task_dependenciesScalarFieldEnum = {
  id: 'id',
  task_id: 'task_id',
  depends_on_task_id: 'depends_on_task_id',
  dependency_type: 'dependency_type',
  created_at: 'created_at'
};

exports.Prisma.TasksScalarFieldEnum = {
  id: 'id',
  project_id: 'project_id',
  title: 'title',
  description: 'description',
  status: 'status',
  priority: 'priority',
  due_date: 'due_date',
  assigned_to_user_id: 'assigned_to_user_id',
  created_at: 'created_at',
  updated_at: 'updated_at',
  completed_at: 'completed_at',
  tags: 'tags'
};

exports.Prisma.Template_collectionsScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  is_public: 'is_public',
  creator_id: 'creator_id',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.Template_filesScalarFieldEnum = {
  id: 'id',
  template_id: 'template_id',
  path: 'path',
  content: 'content',
  is_executable: 'is_executable',
  is_binary: 'is_binary',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.Template_tagsScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.Template_to_tagsScalarFieldEnum = {
  template_id: 'template_id',
  tag_id: 'tag_id'
};

exports.Prisma.Template_usagesScalarFieldEnum = {
  id: 'id',
  template_id: 'template_id',
  user_id: 'user_id',
  project_id: 'project_id',
  variable_values: 'variable_values',
  used_at: 'used_at'
};

exports.Prisma.Template_variablesScalarFieldEnum = {
  id: 'id',
  template_id: 'template_id',
  name: 'name',
  default_value: 'default_value',
  description: 'description',
  required: 'required',
  variable_type: 'variable_type',
  options: 'options',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.TemplatesScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  category: 'category',
  version: 'version',
  is_public: 'is_public',
  creator_id: 'creator_id',
  thumbnail_url: 'thumbnail_url',
  metadata: 'metadata',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.User_context_preferencesScalarFieldEnum = {
  id: 'id',
  user_id: 'user_id',
  contextType: 'contextType',
  is_pinned: 'is_pinned',
  is_disabled: 'is_disabled',
  priority: 'priority',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.User_onboarding_statesScalarFieldEnum = {
  id: 'id',
  user_id: 'user_id',
  step_key: 'step_key',
  title: 'title',
  completion_status: 'completion_status',
  start_date: 'start_date',
  completion_date: 'completion_date',
  attempts: 'attempts',
  last_activity: 'last_activity',
  project_motivation: 'project_motivation',
  code_type: 'code_type',
  project_goals: 'project_goals',
  team_size: 'team_size',
  ai_preferences: 'ai_preferences',
  feature_adaptations: 'feature_adaptations',
  completed_with_partner_id: 'completed_with_partner_id',
  achievement_unlocked: 'achievement_unlocked',
  feedback_rating: 'feedback_rating'
};

exports.Prisma.User_sessionsScalarFieldEnum = {
  id: 'id',
  user_id: 'user_id',
  session_token: 'session_token',
  ip_address: 'ip_address',
  user_agent: 'user_agent',
  device_info: 'device_info',
  last_activity: 'last_activity',
  expires_at: 'expires_at',
  is_active: 'is_active',
  created_at: 'created_at'
};

exports.Prisma.User_storiesScalarFieldEnum = {
  id: 'id',
  project_id: 'project_id',
  title: 'title',
  description: 'description',
  acceptance_criteria: 'acceptance_criteria',
  status: 'status',
  priority: 'priority',
  story_points: 'story_points',
  assigned_to_user_id: 'assigned_to_user_id',
  created_by_user_id: 'created_by_user_id',
  created_at: 'created_at',
  updated_at: 'updated_at',
  completed_at: 'completed_at'
};

exports.Prisma.UsersScalarFieldEnum = {
  id: 'id',
  email: 'email',
  username: 'username',
  first_name: 'first_name',
  last_name: 'last_name',
  clerk_id: 'clerk_id',
  role: 'role',
  is_active: 'is_active',
  bio: 'bio',
  profile_image_url: 'profile_image_url',
  email_verified: 'email_verified',
  email_verification_token: 'email_verification_token',
  email_verification_sent_at: 'email_verification_sent_at',
  elo_rating: 'elo_rating',
  karma: 'karma',
  skills_graph: 'skills_graph',
  total_challenges_completed: 'total_challenges_completed',
  streak_count: 'streak_count',
  last_active_date: 'last_active_date',
  referred_by: 'referred_by',
  failed_login_attempts: 'failed_login_attempts',
  account_locked_until: 'account_locked_until',
  last_password_change: 'last_password_change',
  daily_llm_token_quota: 'daily_llm_token_quota',
  daily_llm_token_usage: 'daily_llm_token_usage',
  quota_reset_date: 'quota_reset_date',
  created_at: 'created_at',
  updated_at: 'updated_at',
  last_login: 'last_login',
  preferred_ide: 'preferred_ide',
  learning_style: 'learning_style',
  developer_strengths: 'developer_strengths',
  preferred_ai_models: 'preferred_ai_models',
  additional_info: 'additional_info',
  onboarding_completed: 'onboarding_completed',
  onboarding_completed_at: 'onboarding_completed_at'
};

exports.Prisma.Workshop_materialsScalarFieldEnum = {
  id: 'id',
  workshop_id: 'workshop_id',
  title: 'title',
  description: 'description',
  material_type: 'material_type',
  file_path: 'file_path',
  external_url: 'external_url',
  order_index: 'order_index',
  is_required: 'is_required',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.Workshop_participantsScalarFieldEnum = {
  workshop_id: 'workshop_id',
  user_id: 'user_id',
  registration_date: 'registration_date',
  status: 'status',
  payment_status: 'payment_status',
  payment_id: 'payment_id',
  completion_percentage: 'completion_percentage',
  feedback_submitted: 'feedback_submitted',
  notes: 'notes',
  last_updated: 'last_updated'
};

exports.Prisma.WorkshopsScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  workshop_type: 'workshop_type',
  start_date: 'start_date',
  end_date: 'end_date',
  syllabus_link: 'syllabus_link',
  geography: 'geography',
  payment_link: 'payment_link',
  cost: 'cost',
  max_participants: 'max_participants',
  is_active: 'is_active',
  enrollment_deadline: 'enrollment_deadline',
  prerequisites: 'prerequisites',
  instructor_ids: 'instructor_ids',
  timezone: 'timezone',
  schedule: 'schedule',
  is_online: 'is_online',
  venue_details: 'venue_details',
  featured: 'featured',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.WorkshopScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  startDate: 'startDate',
  endDate: 'endDate',
  maxParticipants: 'maxParticipants',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ModuleScalarFieldEnum = {
  id: 'id',
  workshopId: 'workshopId',
  title: 'title',
  description: 'description',
  orderIndex: 'orderIndex',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.LessonScalarFieldEnum = {
  id: 'id',
  moduleId: 'moduleId',
  title: 'title',
  content: 'content',
  type: 'type',
  orderIndex: 'orderIndex',
  codeSnippet: 'codeSnippet',
  codeLanguage: 'codeLanguage',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.WorkshopParticipantScalarFieldEnum = {
  workshopId: 'workshopId',
  userId: 'userId',
  status: 'status',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ModuleProgressScalarFieldEnum = {
  workshopId: 'workshopId',
  userId: 'userId',
  moduleId: 'moduleId',
  completionPercentage: 'completionPercentage',
  notes: 'notes',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.LessonCompletionScalarFieldEnum = {
  id: 'id',
  lessonId: 'lessonId',
  userId: 'userId',
  score: 'score',
  feedback: 'feedback',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.UserScalarFieldEnum = {
  id: 'id',
  email: 'email',
  name: 'name',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.NullableJsonNullValueInput = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull
};

exports.Prisma.JsonNullValueInput = {
  JsonNull: Prisma.JsonNull
};

exports.Prisma.QueryMode = {
  default: 'default',
  insensitive: 'insensitive'
};

exports.Prisma.JsonNullValueFilter = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull,
  AnyNull: Prisma.AnyNull
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};
exports.ContextType = exports.$Enums.ContextType = {
  USER: 'USER',
  BUSINESS: 'BUSINESS',
  TECHNICAL: 'TECHNICAL',
  CODE: 'CODE',
  TASK: 'TASK'
};

exports.ContextFormat = exports.$Enums.ContextFormat = {
  MARKDOWN: 'MARKDOWN',
  YAML: 'YAML',
  JSON: 'JSON',
  MERMAID: 'MERMAID'
};

exports.ConversationStatus = exports.$Enums.ConversationStatus = {
  active: 'active',
  archived: 'archived'
};

exports.ConversationCategory = exports.$Enums.ConversationCategory = {
  chat: 'chat',
  agent: 'agent',
  code_assist: 'code_assist',
  debugging: 'debugging',
  planning: 'planning',
  learning: 'learning',
  other: 'other'
};

exports.AgentStatus = exports.$Enums.AgentStatus = {
  initializing: 'initializing',
  running: 'running',
  paused: 'paused',
  completed: 'completed',
  failed: 'failed',
  waiting: 'waiting',
  cancelled: 'cancelled'
};

exports.LastActivityType = exports.$Enums.LastActivityType = {
  thinking: 'thinking',
  command: 'command',
  file_edit: 'file_edit',
  complete: 'complete',
  error: 'error',
  user_input: 'user_input'
};

exports.LessonType = exports.$Enums.LessonType = {
  LECTURE: 'LECTURE',
  INTERACTIVE: 'INTERACTIVE',
  LAB: 'LAB',
  QUIZ: 'QUIZ',
  PROJECT: 'PROJECT',
  DISCUSSION: 'DISCUSSION'
};

exports.Provider = exports.$Enums.Provider = {
  bedrock: 'bedrock',
  azure: 'azure',
  google: 'google',
  other: 'other'
};

exports.TaskType = exports.$Enums.TaskType = {
  chat: 'chat',
  code_review: 'code_review',
  code_gen_big: 'code_gen_big',
  code_gen_agentic: 'code_gen_agentic',
  svg_mockup: 'svg_mockup',
  slides: 'slides',
  test_cases: 'test_cases',
  general: 'general'
};

exports.ProjectType = exports.$Enums.ProjectType = {
  WEB_APPLICATION: 'WEB_APPLICATION',
  MOBILE_APPLICATION: 'MOBILE_APPLICATION',
  LIBRARY: 'LIBRARY',
  CLI_TOOL: 'CLI_TOOL',
  API: 'API',
  OTHER: 'OTHER'
};

exports.ProjectMotivationType = exports.$Enums.ProjectMotivationType = {
  learner: 'learner',
  contributor: 'contributor',
  builder: 'builder'
};

exports.SlideDeckType = exports.$Enums.SlideDeckType = {
  BUSINESS: 'BUSINESS',
  TECHNICAL: 'TECHNICAL',
  USER: 'USER',
  CODE: 'CODE',
  TASK: 'TASK'
};

exports.CodeType = exports.$Enums.CodeType = {
  fresh: 'fresh',
  legacy: 'legacy',
  throwaway: 'throwaway'
};

exports.UserRole = exports.$Enums.UserRole = {
  FREE: 'FREE',
  DEVELOPER: 'DEVELOPER',
  ADMIN: 'ADMIN'
};

exports.ParticipationStatus = exports.$Enums.ParticipationStatus = {
  REGISTERED: 'REGISTERED',
  PAID: 'PAID',
  CONFIRMED: 'CONFIRMED',
  ATTENDED: 'ATTENDED',
  COMPLETED: 'COMPLETED',
  CANCELLED: 'CANCELLED'
};

exports.WorkshopType = exports.$Enums.WorkshopType = {
  ESSENTIALS: 'ESSENTIALS',
  PRACTITIONER: 'PRACTITIONER',
  VIBECODERS: 'VIBECODERS',
  AGENTIC: 'AGENTIC'
};

exports.Prisma.ModelName = {
  ai_agent_actions: 'ai_agent_actions',
  ai_agents: 'ai_agents',
  ai_prompt_templates: 'ai_prompt_templates',
  answer_votes: 'answer_votes',
  answers: 'answers',
  blog_post_tags: 'blog_post_tags',
  blog_posts: 'blog_posts',
  bookmarks: 'bookmarks',
  channel_members: 'channel_members',
  channels: 'channels',
  chat_contexts: 'chat_contexts',
  collection_templates: 'collection_templates',
  context_registry: 'context_registry',
  context_versions: 'context_versions',
  conversations: 'conversations',
  elo_history: 'elo_history',
  file_summaries: 'file_summaries',
  karma_transactions: 'karma_transactions',
  lesson_completions: 'lesson_completions',
  lessons: 'lessons',
  memory_contexts: 'memory_contexts',
  memory_slides: 'memory_slides',
  message_reactions: 'message_reactions',
  messages: 'messages',
  model_usage: 'model_usage',
  module_summaries: 'module_summaries',
  modules: 'modules',
  notifications: 'notifications',
  payment_refunds: 'payment_refunds',
  payments: 'payments',
  programming_sessions: 'programming_sessions',
  project_code_quality: 'project_code_quality',
  project_dependencies: 'project_dependencies',
  project_documentation: 'project_documentation',
  project_git_repos: 'project_git_repos',
  project_objectives: 'project_objectives',
  project_quality_metrics: 'project_quality_metrics',
  project_slides: 'project_slides',
  project_tech_stacks: 'project_tech_stacks',
  project_templates: 'project_templates',
  project_tests: 'project_tests',
  project_visualizations: 'project_visualizations',
  projects: 'projects',
  question_tags: 'question_tags',
  question_votes: 'question_votes',
  questions: 'questions',
  resources: 'resources',
  slide_decks: 'slide_decks',
  social_messages: 'social_messages',
  subscription_events: 'subscription_events',
  subscription_payments: 'subscription_payments',
  subscriptions: 'subscriptions',
  tags: 'tags',
  task_comments: 'task_comments',
  task_dependencies: 'task_dependencies',
  tasks: 'tasks',
  template_collections: 'template_collections',
  template_files: 'template_files',
  template_tags: 'template_tags',
  template_to_tags: 'template_to_tags',
  template_usages: 'template_usages',
  template_variables: 'template_variables',
  templates: 'templates',
  user_context_preferences: 'user_context_preferences',
  user_onboarding_states: 'user_onboarding_states',
  user_sessions: 'user_sessions',
  user_stories: 'user_stories',
  users: 'users',
  workshop_materials: 'workshop_materials',
  workshop_participants: 'workshop_participants',
  workshops: 'workshops',
  Workshop: 'Workshop',
  Module: 'Module',
  Lesson: 'Lesson',
  WorkshopParticipant: 'WorkshopParticipant',
  ModuleProgress: 'ModuleProgress',
  LessonCompletion: 'LessonCompletion',
  User: 'User'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }

        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
