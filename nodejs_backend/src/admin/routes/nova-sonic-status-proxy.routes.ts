import { Router, Request, Response } from 'express';
import { isAuthenticated } from '../middleware/auth.middleware';
import axios from 'axios';

const router = Router();

// Get Nova Sonic service URL from environment or default
const NOVA_SONIC_SERVICE_URL = process.env.NOVA_SONIC_SERVICE_URL || 'http://localhost:3005';

/**
 * GET /api/nova-sonic/sessions
 * Get all active Nova Sonic sessions from the service
 */
router.get('/sessions', isAuthenticated, async (_req: Request, res: Response) => {
  try {
    const response = await axios.get(`${NOVA_SONIC_SERVICE_URL}/sessions`);
    return res.json(response.data);
  } catch (error) {
    console.error('Error getting Nova Sonic sessions:', error);
    
    if (axios.isAxiosError(error) && error.code === 'ECONNREFUSED') {
      return res.status(503).json({
        error: 'Nova Sonic service not available',
        message: 'The Nova Sonic service is not currently running',
        serviceUrl: NOVA_SONIC_SERVICE_URL
      });
    }
    
    return res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to retrieve session information'
    });
  }
});

/**
 * GET /api/nova-sonic/health
 * Check Nova Sonic service health
 */
router.get('/health', isAuthenticated, async (_req: Request, res: Response) => {
  try {
    const response = await axios.get(`${NOVA_SONIC_SERVICE_URL}/health`);
    
    // Get proxy status from the app if available
    const app = _req.app as any;
    const proxyStatus = app.novaSonicProxy ? await app.novaSonicProxy.getStatus() : null;
    
    const healthStatus = {
      service: 'Nova Sonic',
      status: 'healthy',
      serviceHealth: response.data,
      proxyStatus,
      timestamp: new Date().toISOString()
    };
    
    return res.json(healthStatus);
  } catch (error) {
    console.error('Error checking Nova Sonic health:', error);
    
    const isServiceDown = axios.isAxiosError(error) && error.code === 'ECONNREFUSED';
    
    return res.status(isServiceDown ? 503 : 500).json({
      service: 'Nova Sonic',
      status: isServiceDown ? 'unavailable' : 'error',
      error: isServiceDown ? 'Service not reachable' : 'Health check failed',
      serviceUrl: NOVA_SONIC_SERVICE_URL,
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * GET /api/nova-sonic/proxy/status
 * Get proxy-specific status information
 */
router.get('/proxy/status', isAuthenticated, async (req: Request, res: Response) => {
  try {
    const app = req.app as any;
    
    if (!app.novaSonicProxy) {
      return res.status(503).json({
        error: 'Nova Sonic proxy not initialized',
        message: 'The proxy to Nova Sonic service is not running'
      });
    }
    
    const proxyStatus = await app.novaSonicProxy.getStatus();
    return res.json(proxyStatus);
  } catch (error) {
    console.error('Error getting proxy status:', error);
    return res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to retrieve proxy status'
    });
  }
});

/**
 * GET /api/nova-sonic/sessions/:sessionId
 * Get detailed information about a specific session (proxy through to service)
 */
router.get('/sessions/:sessionId', isAuthenticated, async (req: Request, res: Response) => {
  try {
    const { sessionId } = req.params;
    
    // First check the service sessions endpoint
    const response = await axios.get(`${NOVA_SONIC_SERVICE_URL}/sessions`);
    const sessions = response.data.sessions || [];
    
    const session = sessions.find((s: any) => s.sessionId === sessionId);
    
    if (!session) {
      return res.status(404).json({
        error: 'Session not found',
        message: `Session ${sessionId} does not exist or has been cleaned up`
      });
    }
    
    return res.json({
      ...session,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error getting Nova Sonic session details:', error);
    
    if (axios.isAxiosError(error) && error.code === 'ECONNREFUSED') {
      return res.status(503).json({
        error: 'Nova Sonic service not available',
        message: 'The Nova Sonic service is not currently running'
      });
    }
    
    return res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to retrieve session details'
    });
  }
});

/**
 * GET /api/nova-sonic/metrics
 * Get Nova Sonic usage metrics from the service
 */
router.get('/metrics', isAuthenticated, async (_req: Request, res: Response) => {
  try {
    // Get sessions from the service
    const response = await axios.get(`${NOVA_SONIC_SERVICE_URL}/sessions`);
    const { count, sessions } = response.data;
    
    const now = Date.now();
    
    // Calculate metrics based on session data
    const sessionMetrics = sessions.map((session: any) => {
      const lastActivityAgo = session.lastActivity > 0 ? now - session.lastActivity : null;
      return {
        ...session,
        lastActivityAgo
      };
    });
    
    const metrics = {
      totalActiveSessions: count,
      sessionAgeDistribution: {
        under1Min: sessionMetrics.filter((s: any) => s.lastActivityAgo && s.lastActivityAgo < 60000).length,
        under5Min: sessionMetrics.filter((s: any) => s.lastActivityAgo && s.lastActivityAgo < 300000).length,
        under15Min: sessionMetrics.filter((s: any) => s.lastActivityAgo && s.lastActivityAgo < 900000).length,
        over15Min: sessionMetrics.filter((s: any) => s.lastActivityAgo && s.lastActivityAgo >= 900000).length
      },
      serviceUrl: NOVA_SONIC_SERVICE_URL,
      timestamp: new Date().toISOString()
    };
    
    return res.json(metrics);
  } catch (error) {
    console.error('Error getting Nova Sonic metrics:', error);
    
    if (axios.isAxiosError(error) && error.code === 'ECONNREFUSED') {
      return res.status(503).json({
        error: 'Nova Sonic service not available',
        message: 'The Nova Sonic service is not currently running'
      });
    }
    
    return res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to retrieve metrics'
    });
  }
});

/**
 * GET /api/nova-sonic/proxy/sessions
 * Get detailed session tracking information from proxy
 */
router.get('/proxy/sessions', isAuthenticated, async (req: Request, res: Response) => {
  try {
    const app = req.app as any;
    
    if (!app.novaSonicProxy) {
      return res.status(503).json({
        error: 'Nova Sonic proxy not initialized',
        message: 'The proxy to Nova Sonic service is not running'
      });
    }
    
    const proxyStatus = await app.novaSonicProxy.getStatus();
    
    // Format sessions for display
    const sessionTable = proxyStatus.sessions.map((session: any) => ({
      socketId: session.socketId,
      initialized: session.initialized ? 'Yes' : 'No',
      promptStarted: session.promptStarted ? 'Yes' : 'No',
      initCount: session.initializationCount,
      promptCount: session.promptStartCount,
      duration: `${Math.floor(session.duration / 1000)}s`,
      lastActivity: new Date(session.lastActivity).toLocaleTimeString(),
      warnings: [
        session.initializationCount > 1 ? `${session.initializationCount} init attempts` : null,
        session.promptStartCount > 1 ? `${session.promptStartCount} prompt attempts` : null
      ].filter(Boolean).join(', ') || 'None'
    }));
    
    return res.json({
      totalSessions: sessionTable.length,
      sessions: sessionTable,
      summary: {
        duplicateInits: proxyStatus.sessions.filter((s: any) => s.initializationCount > 1).length,
        duplicatePrompts: proxyStatus.sessions.filter((s: any) => s.promptStartCount > 1).length
      },
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error getting proxy sessions:', error);
    return res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to retrieve proxy session tracking'
    });
  }
});

// Note: Force close functionality should be implemented in the Nova Sonic service
// and then proxied through here if needed

export default router;

// No longer need the setNovaSonicClient function since we're using HTTP proxy
