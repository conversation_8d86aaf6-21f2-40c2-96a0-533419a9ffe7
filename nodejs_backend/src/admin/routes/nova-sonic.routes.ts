import { Router, Request, Response } from 'express';
import { logger } from '../../common/logger';

const router: Router = Router();

/**
 * GET /admin/nova-sonic
 * Nova Sonic base route - shows available endpoints
 */
router.get('/', (req: Request, res: Response) => {
  res.json({
    message: 'Nova Sonic Admin API',
    endpoints: [
      { path: '/api/admin/nova-sonic/status', method: 'GET', description: 'Check Nova Sonic WebSocket status' },
      { path: '/api/admin/nova-sonic/debug', method: 'GET', description: 'Socket.IO debug information' },
      { path: '/admin/nova-sonic/test', method: 'GET', description: 'Nova Sonic test UI' },
      { path: '/admin/nova-sonic/connection-test', method: 'GET', description: 'Connection test UI' }
    ]
  });
});

/**
 * GET /admin/nova-sonic/status
 * Check Nova Sonic WebSocket status
 */
router.get('/status', (req: Request, res: Response) => {
  try {
    const io = (req.app as any).io;
    const novaSonicHandler = (req.app as any).novaSonicHandler;
    const novaSonicNamespace = io?.of('/ws/nova-sonic');

    // Get all namespaces
    const allNamespaces = io ? Array.from(io._nsps.keys()) : [];

    const status = {
      socketIOVersion: 'socket.io installed',
      nodeVersion: process.version,
      novaSonicNamespace: {
        exists: !!novaSonicNamespace,
        connected: novaSonicNamespace ? novaSonicNamespace.sockets.size : 0,
        path: '/ws/nova-sonic',
        name: novaSonicNamespace?.name,
        adapter: novaSonicNamespace?.adapter?.constructor?.name
      },
      handler: {
        exists: !!novaSonicHandler,
        hasBedrockClient: !!novaSonicHandler?.getBedrockClient,
        activeSessions: novaSonicHandler?.getActiveWebSocketSessions ? novaSonicHandler.getActiveWebSocketSessions().size : 0
      },
      allNamespaces,
      endpoints: {
        websocket: `ws://localhost:${process.env.PORT || 3000}/ws/nova-sonic`,
        test: `http://localhost:${process.env.PORT || 3000}/admin/nova-sonic/test`
      },
      corsOrigin: io?._opts?.cors?.origin,
      timestamp: new Date().toISOString()
    };

    res.json(status);
  } catch (error) {
    logger.error('Error checking Nova Sonic status:', error);
    res.status(500).json({
      error: 'Failed to check status',
      message: error instanceof Error ? error.message : String(error)
    });
  }
});

/**
 * GET /admin/nova-sonic/debug
 * Debug endpoint to check socket.io configuration
 */
router.get('/debug', (req: Request, res: Response) => {
  try {
    const io = (req.app as any).io;
    
    if (!io) {
      return res.status(503).json({ error: 'Socket.IO not initialized' });
    }

    const debug = {
      engine: {
        clientsCount: io.engine?.clientsCount || 0,
        cors: io.engine?.opts?.cors || null,
        transports: io.engine?.opts?.transports || [],
        path: io.engine?.opts?.path || '/socket.io/'
      },
      namespaces: {}
    };

    // List all namespaces and their details
    if (io._nsps && typeof io._nsps.forEach === 'function') {
      io._nsps.forEach((namespace: any, name: string) => {
        debug.namespaces[name] = {
          name: namespace.name,
          socketsCount: namespace.sockets?.size || 0,
          rooms: namespace.rooms ? Array.from(namespace.rooms.keys()) : [],
          middleware: namespace._fns?.length || 0
        };
      });
    } else {
      // Try alternative method to get namespaces
      const novaSonicNs = io.of('/ws/nova-sonic');
      if (novaSonicNs) {
        debug.namespaces['/ws/nova-sonic'] = {
          name: novaSonicNs.name,
          socketsCount: novaSonicNs.sockets?.size || 0,
          rooms: [],
          middleware: 0
        };
      }
    }

    res.json(debug);
  } catch (error) {
    logger.error('Error in debug endpoint:', error);
    res.status(500).json({
      error: 'Failed to get debug info',
      message: error instanceof Error ? error.message : String(error)
    });
  }
});

// Note: The /admin/nova-sonic/test and /admin/nova-sonic/connection-test routes are now handled by ui.routes.ts
// This avoids duplication and uses the template system properly

export default router;