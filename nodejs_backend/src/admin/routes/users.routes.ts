import { Router, Request, Response, RequestHandler } from 'express';
import { logger } from '../../common/logger';
import { prisma } from '../../db/client';
import { clerkClient } from '@clerk/clerk-sdk-node';
import { UserRole } from '@prisma/client';

const router = Router();

/**
 * @swagger
 * /admin/api/users:
 *   get:
 *     summary: Get all users
 *     tags: [Admin]
 *     responses:
 *       200:
 *         description: List of users
 *       500:
 *         description: Server error
 */
router.get('/', (async (req: Request, res: Response) => {
  try {
    // We'll always return JSON for this endpoint as it's an API route
    // UI rendering is handled by ui.routes.ts

    // Get users from database
    const users = await prisma.users.findMany({
      orderBy: {
        id: 'asc',
      },
    });

    // Return the users as JSON
    res.json({
      status: 'success',
      data: users,
    });
  } catch (error) {
    logger.error('Error getting users:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to get users',
      details: error instanceof Error ? error.message : 'Unknown error',
    });
  }
}) as RequestHandler);

/**
 * @swagger
 * /admin/api/users/list:
 *   get:
 *     summary: Get users for admin UI
 *     tags: [Admin]
 *     responses:
 *       200:
 *         description: HTML page with user list
 *       500:
 *         description: Server error
 */
router.get('/list', (async (req: Request, res: Response) => {
  try {
    // Get query parameters for sorting and filtering
    const sortBy = req.query.sort_by as string || 'id';
    const sortOrder = req.query.sort_order as string || 'asc';
    const search = req.query.search as string;
    
    // Build where clause for search
    let where = {};
    if (search) {
      where = {
        OR: [
          { email: { contains: search, mode: 'insensitive' } },
          { first_name: { contains: search, mode: 'insensitive' } },
          { last_name: { contains: search, mode: 'insensitive' } },
          { username: { contains: search, mode: 'insensitive' } },
        ]
      };
    }
    
    // Build orderBy clause
    let orderBy = {};
    if (sortBy) {
      orderBy = {
        [sortBy]: sortOrder.toLowerCase() === 'desc' ? 'desc' : 'asc',
      };
    }
    
    // Get users with filtering and sorting
    const users = await prisma.users.findMany({
      where,
      orderBy,
    });
    
    // Render the users list template
    return res.render('admin/users/list.html', {
      title: 'User Management',
      users,
      admin_user: (req.session as any)?.admin_user || null,
      sort_by: sortBy,
      sort_order: sortOrder,
      search,
      request: {
        url: {
          path: req.path,
        },
      },
    });
  } catch (error) {
    logger.error('Error getting user list:', error);
    res.status(500).render('admin/error.html', {
      title: 'Error',
      error: 'Error getting user list',
      admin_user: (req.session as any)?.admin_user || null,
      request: {
        url: {
          path: req.path,
        },
      },
    });
  }
}) as RequestHandler);

/**
 * @swagger
 * /admin/api/users/{id}:
 *   get:
 *     summary: Get user details
 *     tags: [Admin]
 *     parameters:
 *       - name: id
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: User details page
 *       404:
 *         description: User not found
 *       500:
 *         description: Server error
 */
router.get('/:id', (async (req: Request, res: Response) => {
  try {
    const userId = parseInt(req.params.id);
    
    // Get user from database
    const user = await prisma.users.findUnique({
      where: {
        id: userId,
      },
      include: {
        projects: {
          take: 5,
          orderBy: {
            created_at: 'desc',
          },
        },
        conversations: {
          take: 5,
          orderBy: {
            created_at: 'desc',
          },
        },
      },
    });
    
    if (!user) {
      return res.status(404).render('admin/error.html', {
        title: 'Error',
        error: 'User not found',
        admin_user: (req.session as any)?.admin_user || null,
        request: {
          url: {
            path: req.path,
          },
        },
      });
    }
    
    // Render the user details template
    return res.render('admin/users/details.html', {
      title: `User: ${user.email}`,
      user,
      admin_user: (req.session as any)?.admin_user || null,
      request: {
        url: {
          path: req.path,
        },
      },
    });
  } catch (error) {
    logger.error(`Error getting user details for ${req.params.id}:`, error);
    res.status(500).render('admin/error.html', {
      title: 'Error',
      error: `Error getting user details for ${req.params.id}`,
      admin_user: (req.session as any)?.admin_user || null,
      request: {
        url: {
          path: req.path,
        },
      },
    });
  }
}) as RequestHandler);

/**
 * @swagger
 * /admin/api/users/create:
 *   get:
 *     summary: Show user creation form
 *     tags: [Admin]
 *     responses:
 *       200:
 *         description: User creation form
 *       500:
 *         description: Server error
 */
router.get('/create', async (req: Request, res: Response) => {
  res.render('admin/users/create.html', {
    title: 'Create User',
    formData: {},
    error: null,
    admin_user: (req.session as any)?.admin_user || null,
    request: { url: { path: req.path } },
  });
});

/**
 * @swagger
 * /admin/api/users/create:
 *   post:
 *     summary: Create a new user
 *     tags: [Admin]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *               first_name:
 *                 type: string
 *               last_name:
 *                 type: string
 *               username:
 *                 type: string
 *               role:
 *                 type: string
 *                 enum: [FREE, DEVELOPER, ADMIN]
 *               is_active:
 *                 type: boolean
 *               bio:
 *                 type: string
 *               send_welcome_email:
 *                 type: boolean
 *     responses:
 *       302:
 *         description: Redirect to user details page
 *       400:
 *         description: Bad request
 *       500:
 *         description: Server error
 */
router.post('/create', async (req: Request, res: Response) => {
  try {
    const { 
      email, 
      first_name, 
      last_name, 
      username, 
      role = 'FREE', 
      is_active = 'true', 
      bio,
      send_welcome_email = 'true' 
    } = req.body;
    
    // Validate required fields
    if (!email) {
      return res.status(400).render('admin/users/create.html', {
        title: 'Create User',
        error: 'Email is required',
        admin_user: (req.session as any)?.admin_user || null,
        request: {
          url: {
            path: req.path,
          },
        },
        formData: req.body,
      });
    }
    
    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return res.status(400).render('admin/users/create.html', {
        title: 'Create User',
        error: 'Invalid email format',
        admin_user: (req.session as any)?.admin_user || null,
        request: {
          url: {
            path: req.path,
          },
        },
        formData: req.body,
      });
    }
    
    // Check if the user already exists in the database
    const existingUser = await prisma.users.findUnique({
      where: {
        email,
      },
    });
    
    if (existingUser) {
      return res.status(400).render('admin/users/create.html', {
        title: 'Create User',
        error: 'A user with this email already exists',
        admin_user: (req.session as any)?.admin_user || null,
        request: {
          url: {
            path: req.path,
          },
        },
        formData: req.body,
      });
    }
    
    // Check if username is provided and if it's unique
    if (username) {
      const existingUsername = await prisma.users.findUnique({
        where: {
          username,
        },
      });
      
      if (existingUsername) {
        return res.status(400).render('admin/users/create.html', {
          title: 'Create User',
          error: 'This username is already taken',
          admin_user: (req.session as any)?.admin_user || null,
          request: {
            url: {
              path: req.path,
            },
          },
          formData: req.body,
        });
      }
    }
    
    // Convert values
    const isActive = is_active === 'true';
    const sendWelcomeEmail = send_welcome_email === 'true';
    
    // Convert role string to enum
    let roleEnum: UserRole = UserRole.FREE;
    if (role === 'DEVELOPER') {
      roleEnum = UserRole.DEVELOPER;
    } else if (role === 'ADMIN') {
      roleEnum = UserRole.ADMIN;
    }
    
    try {
      // Create user in Clerk with or without welcome email
      const clerkUser = await clerkClient.users.createUser({
        emailAddress: [email],
        firstName: first_name || undefined,
        lastName: last_name || undefined,
        username: username || undefined,
        skipPasswordChecks: true,
        skipPasswordRequirement: true,
        publicMetadata: {
          role: role,
          isActive: isActive,
        },
      });
      
      logger.info(`Created Clerk user: ${clerkUser.id}`);
      
      // If send welcome email is true, send verification email
      if (sendWelcomeEmail) {
        try {
          await clerkClient.emailAddresses.createEmailAddress({
            userId: clerkUser.id,
            emailAddress: email,
            verified: false, // Force verification flow
          });
          
          logger.info(`Sent verification email to ${email}`);
        } catch (emailError) {
          logger.warn(`Failed to trigger verification email: ${emailError instanceof Error ? emailError.message : 'Unknown error'}`);
          // Continue with user creation even if email sending fails
        }
      }
      
      // Create user in database
      const newUser = await prisma.users.create({
        data: {
          email,
          first_name: first_name || null,
          last_name: last_name || null,
          username: username || null,
          clerk_id: clerkUser.id,
          role: roleEnum,
          is_active: isActive,
          bio: bio || null,
        },
      });
      
      logger.info(`Successfully created user in database with ID: ${newUser.id}`);
      
      // Redirect to user details page
      return res.redirect(`/admin/api/users/${newUser.id}`);
    } catch (clerkError) {
      logger.error('Error creating user in Clerk:', clerkError);
      return res.status(500).render('admin/users/create.html', {
        title: 'Create User',
        error: `Error creating user in Clerk: ${clerkError instanceof Error ? clerkError.message : 'Unknown error'}`,
        admin_user: (req.session as any)?.admin_user || null,
        request: {
          url: {
            path: req.path,
          },
        },
        formData: req.body,
      });
    }
  } catch (error) {
    logger.error('Error creating user:', error);
    res.status(500).render('admin/users/create.html', {
      title: 'Create User',
      error: `Error creating user: ${error instanceof Error ? error.message : 'Unknown error'}`,
      admin_user: (req.session as any)?.admin_user || null,
      request: {
        url: {
          path: req.path,
        },
      },
      formData: req.body,
    });
  }
});

/**
 * @swagger
 * /admin/api/users/{id}/edit:
 *   get:
 *     summary: Show user edit form
 *     tags: [Admin]
 *     parameters:
 *       - name: id
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: User edit form
 *       404:
 *         description: User not found
 *       500:
 *         description: Server error
 */
router.get('/:id/edit', (async (req: Request, res: Response) => {
  try {
    const userId = parseInt(req.params.id);
    
    // Get user from database
    const user = await prisma.users.findUnique({
      where: {
        id: userId,
      },
    });
    
    if (!user) {
      return res.status(404).render('admin/error.html', {
        title: 'Error',
        error: 'User not found',
        admin_user: (req.session as any)?.admin_user || null,
        request: {
          url: {
            path: req.path,
          },
        },
      });
    }
    
    // Render the user edit form
    return res.render('admin/users/edit.html', {
      title: `Edit User: ${user.email}`,
      user,
      admin_user: (req.session as any)?.admin_user || null,
      request: {
        url: {
          path: req.path,
        },
      },
    });
  } catch (error) {
    logger.error(`Error rendering edit form for user ${req.params.id}:`, error);
    res.status(500).render('admin/error.html', {
      title: 'Error',
      error: `Error rendering edit form for user ${req.params.id}`,
      admin_user: (req.session as any)?.admin_user || null,
      request: {
        url: {
          path: req.path,
        },
      },
    });
  }
}) as RequestHandler);

/**
 * @swagger
 * /admin/api/users/{id}/edit:
 *   post:
 *     summary: Update a user
 *     tags: [Admin]
 *     parameters:
 *       - name: id
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *               first_name:
 *                 type: string
 *               last_name:
 *                 type: string
 *               username:
 *                 type: string
 *               role:
 *                 type: string
 *                 enum: [FREE, DEVELOPER, ADMIN]
 *               is_active:
 *                 type: boolean
 *               bio:
 *                 type: string
 *               profile_image_url:
 *                 type: string
 *     responses:
 *       302:
 *         description: Redirect to user details page
 *       404:
 *         description: User not found
 *       500:
 *         description: Server error
 */
router.post('/:id/edit', (async (req: Request, res: Response) => {
  try {
    const userId = parseInt(req.params.id);
    
    // Extract form data
    const { 
      email, 
      first_name, 
      last_name, 
      username, 
      role = 'FREE', 
      is_active = 'true', 
      bio,
      profile_image_url
    } = req.body;
    
    // Get user from database
    const user = await prisma.users.findUnique({
      where: {
        id: userId,
      },
    });
    
    if (!user) {
      return res.status(404).render('admin/error.html', {
        title: 'Error',
        error: 'User not found',
        admin_user: (req.session as any)?.admin_user || null,
        request: {
          url: {
            path: req.path,
          },
        },
      });
    }
    
    // Convert is_active string to boolean
    const isActive = is_active === 'true';
    
    // Convert role string to enum
    let roleEnum: UserRole = UserRole.FREE;
    if (role === 'DEVELOPER') {
      roleEnum = UserRole.DEVELOPER;
    } else if (role === 'ADMIN') {
      roleEnum = UserRole.ADMIN;
    }
    
    // Update user in Clerk
    await clerkClient.users.updateUser(user.clerk_id, {
      firstName: first_name || undefined,
      lastName: last_name || undefined,
      username: username || undefined,
    });
    
    // Update user in database
    await prisma.users.update({
      where: {
        id: userId,
      },
      data: {
        email,
        first_name: first_name || null,
        last_name: last_name || null,
        username: username || null,
        role: roleEnum,
        is_active: isActive,
        bio: bio || null,
        profile_image_url: profile_image_url || null,
      },
    });
    
    // Redirect to user details page
    return res.redirect(`/admin/api/users/${userId}`);
  } catch (error) {
    logger.error(`Error updating user ${req.params.id}:`, error);
    res.status(500).render('admin/error.html', {
      title: 'Error',
      error: `Error updating user ${req.params.id}: ${error instanceof Error ? error.message : 'Unknown error'}`,
      admin_user: (req.session as any)?.admin_user || null,
      request: {
        url: {
          path: req.path,
        },
      },
    });
  }
}) as RequestHandler);

/**
 * @swagger
 * /admin/api/users/{id}/delete:
 *   get:
 *     summary: Show user deletion confirmation page
 *     tags: [Admin]
 *     parameters:
 *       - name: id
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: User deletion confirmation page
 *       404:
 *         description: User not found
 *       500:
 *         description: Server error
 */
router.get('/:id/delete', (async (req: Request, res: Response) => {
  try {
    const userId = parseInt(req.params.id);
    
    // Get user from database
    const user = await prisma.users.findUnique({
      where: {
        id: userId,
      },
    });
    
    if (!user) {
      return res.status(404).render('admin/error.html', {
        title: 'Error',
        error: 'User not found',
        admin_user: (req.session as any)?.admin_user || null,
        request: {
          url: {
            path: req.path,
          },
        },
      });
    }
    
    // Don't allow deleting the admin user who is logged in
    if (user.id === (req.session as any)?.admin_user?.id) {
      return res.render('admin/users/delete.html', {
        title: `Delete User: ${user.email}`,
        user,
        admin_user: (req.session as any)?.admin_user || null,
        error: 'You cannot delete your own account',
        request: {
          url: {
            path: req.path,
          },
        },
      });
    }
    
    // Render the user deletion confirmation page
    return res.render('admin/users/delete.html', {
      title: `Delete User: ${user.email}`,
      user,
      admin_user: (req.session as any)?.admin_user || null,
      request: {
        url: {
          path: req.path,
        },
      },
    });
  } catch (error) {
    logger.error(`Error rendering delete confirmation for user ${req.params.id}:`, error);
    res.status(500).render('admin/error.html', {
      title: 'Error',
      error: `Error rendering delete confirmation for user ${req.params.id}`,
      admin_user: (req.session as any)?.admin_user || null,
      request: {
        url: {
          path: req.path,
        },
      },
    });
  }
}) as RequestHandler);

/**
 * @swagger
 * /admin/api/users/{id}/delete:
 *   post:
 *     summary: Delete a user
 *     tags: [Admin]
 *     parameters:
 *       - name: id
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       302:
 *         description: Redirect to user list
 *       404:
 *         description: User not found
 *       500:
 *         description: Server error
 */
router.post('/:id/delete', (async (req: Request, res: Response) => {
  try {
    const userId = parseInt(req.params.id);
    
    // Get user from database
    const user = await prisma.users.findUnique({
      where: {
        id: userId,
      },
    });
    
    if (!user) {
      return res.status(404).render('admin/error.html', {
        title: 'Error',
        error: 'User not found',
        admin_user: (req.session as any)?.admin_user || null,
        request: {
          url: {
            path: req.path,
          },
        },
      });
    }
    
    // Don't allow deleting the admin user who is logged in
    if (user.id === (req.session as any)?.admin_user?.id) {
      return res.render('admin/users/delete.html', {
        title: `Delete User: ${user.email}`,
        user,
        admin_user: (req.session as any)?.admin_user || null,
        error: 'You cannot delete your own account',
        request: {
          url: {
            path: req.path,
          },
        },
      });
    }
    
    // Delete user from Clerk
    await clerkClient.users.deleteUser(user.clerk_id);
    
    // Delete user from database
    await prisma.users.delete({
      where: {
        id: userId,
      },
    });
    
    // Redirect to user list
    return res.redirect('/admin/api/users/list');
  } catch (error) {
    logger.error(`Error deleting user ${req.params.id}:`, error);
    res.status(500).render('admin/error.html', {
      title: 'Error',
      error: `Error deleting user ${req.params.id}: ${error instanceof Error ? error.message : 'Unknown error'}`,
      admin_user: (req.session as any)?.admin_user || null,
      request: {
        url: {
          path: req.path,
        },
      },
    });
  }
}) as RequestHandler);

export default router;
