import { Router, Request, Response, RequestHandler } from 'express';
import { logger } from '../../common/logger';
import { conversationService } from '../../services';
import { prisma } from '../../db/client';

const router = Router();

/**
 * Debug route to check conversations and schema compatibility
 */
router.get('/schema-debug', async (req: Request, res: Response) => {
  try {
    logger.info('Schema debug route called');
    logger.info(`Session data: ${JSON.stringify(req.session || {})}`);
    const cookiesStr = JSON.stringify(req.cookies || {});
    logger.info(`Cookies: ${cookiesStr.substring(0, 50)}...`);

    // Log request headers
    logger.info(`Request headers: ${JSON.stringify(req.headers)}`);

    // Get admin user ID from session
    interface AdminSession extends Express.Session {
      admin_user?: { id: number };
    }
    const adminUserId = (req.session as AdminSession)?.admin_user?.id;
    logger.info(`Admin user ID: ${adminUserId}, type: ${typeof adminUserId}`);

    // Check database structure
    let dbSchema: Record<string, any> = {};
    try {
      // Check required tables
      interface TableInfo {
        table_name: string;
      }
      const tables = await prisma.$queryRaw<TableInfo[]>`
        SELECT table_name
        FROM information_schema.tables
        WHERE table_schema = 'public';
      `;
      dbSchema['tables'] = tables;

      // Check for enums
      interface EnumInfo {
        enum_name: string;
        enum_value: string;
      }
      const enums = await prisma.$queryRaw<EnumInfo[]>`
        SELECT t.typname as enum_name, e.enumlabel as enum_value
        FROM pg_type t
        JOIN pg_enum e ON t.oid = e.enumtypid
        JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace
        WHERE n.nspname = 'public';
      `;
      dbSchema['enums'] = enums;

      // Check specific tables
      const requiredTables = ['conversations', 'messages', 'users'];
      for (const table of requiredTables) {
        try {
          interface ColumnInfo {
            column_name: string;
            data_type: string;
            is_nullable: string;
          }
          const columns = await prisma.$queryRaw<ColumnInfo[]>`
            SELECT column_name, data_type, is_nullable
            FROM information_schema.columns
            WHERE table_schema = 'public'
            AND table_name = ${table};
          `;
          dbSchema[`${table}_columns`] = columns;
        } catch (err) {
          logger.error(`Error fetching columns for table ${table}:`, err);
        }
      }

      logger.info(`Database schema: ${JSON.stringify(dbSchema)}`);
    } catch (schemaError) {
      logger.error('Error checking database schema:', schemaError);
      dbSchema['error'] = String(schemaError);
    }

    // Get all conversations for debugging
    logger.info('Attempting to get all conversations...');
    interface ConversationRecord {
      id: number;
      user_id: number;
      title?: string;
      status: string;
      created_at: Date | string;
      updated_at: Date | string;
    }
    let allConversations: ConversationRecord[] = [];
    try {
      allConversations = await conversationService.getAllConversations();
      logger.info(`Found ${allConversations.length} total conversations in the database`);
    } catch (convError) {
      logger.error('Error getting all conversations:', convError);
      return res.status(500).json({
        status: 'error',
        message: 'Failed to get conversations',
        details: convError instanceof Error ? convError.message : 'Unknown error',
      });
    }

    // Get just the first conversation (if any) to check its structure
    let sampleConversation = null;
    if (allConversations.length > 0) {
      sampleConversation = allConversations[0];
      logger.info(`Sample conversation: ${JSON.stringify(sampleConversation)}`);
    }

    // Check Date objects and convert them to strings to avoid JSON serialization issues
    const sanitizedConversations = allConversations.map((conv) => {
      try {
        return {
          id: conv.id,
          userId: conv.user_id,
          title: conv.title || `Conversation ${conv.id}`,
          status: conv.status,
          createdAt:
            conv.created_at instanceof Date
              ? conv.created_at.toISOString()
              : String(conv.created_at),
          updatedAt:
            conv.updated_at instanceof Date
              ? conv.updated_at.toISOString()
              : String(conv.updated_at),
        };
      } catch (convError) {
        logger.error(`Error sanitizing conversation ${conv.id}:`, convError);
        return {
          id: conv.id || 'unknown',
          error: 'Failed to sanitize',
        };
      }
    });

    // Check session data
    const sessionData = {
      hasSession: !!req.session,
      adminUserId: adminUserId,
      adminUserIdType: typeof adminUserId,
      sessionId: req.sessionID,
      cookies: req.cookies,
    };

    // Return all debug info
    return res.json({
      status: 'success',
      message: 'Schema debug info',
      sessionData,
      dbSchema,
      conversationCount: allConversations.length,
      sampleRawConversation: sampleConversation,
      sampleSanitizedConversation: sanitizedConversations[0] || null,
      environment: process.env.NODE_ENV || 'unknown',
    });
  } catch (error) {
    logger.error('Error in schema debug route:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    const errorStack = error instanceof Error ? error.stack : '';

    return res.status(500).json({
      status: 'error',
      message: 'Error in schema debug route',
      details: errorMessage,
      stack: errorStack,
    });
  }
});

/**
 * Simple health check route to test API connectivity
 */
router.get('/health', (_req: Request, res: Response) => {
  try {
    logger.info('Debug health check called');

    // Return a simple response
    return res.json({
      status: 'success',
      message: 'Debug health check successful',
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.error('Error in debug health check:', error);
    return res.status(500).json({
      status: 'error',
      message: 'Error in debug health check',
    });
  }
});

router.get('/schema', (async (req: Request, res: Response) => {
  try {
    // Get database schema information
    interface TableInfo {
      table_name: string;
    }
    const tables = await prisma.$queryRaw<TableInfo[]>`
      SELECT table_name
      FROM information_schema.tables
      WHERE table_schema = 'public';
    `;

    interface EnumInfo {
      enum_name: string;
      enum_value: string;
    }
    const enums = await prisma.$queryRaw<EnumInfo[]>`
      SELECT t.typname as enum_name, e.enumlabel as enum_value
      FROM pg_type t
      JOIN pg_enum e ON t.oid = e.enumtypid
      JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace
      WHERE n.nspname = 'public';
    `;

    const schema = { tables, enums };

    res.json({
      status: 'success',
      data: schema,
    });
  } catch (error) {
    logger.error('Error getting schema:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to get schema',
      details: error instanceof Error ? error.message : 'Unknown error',
    });
  }
}) as RequestHandler);

router.get('/db-tables', (async (req: Request, res: Response) => {
  try {
    // Get database tables with column information
    interface TableWithColumns {
      table_name: string;
      column_count: number;
    }
    const tables = await prisma.$queryRaw<TableWithColumns[]>`
      SELECT 
        table_name,
        (SELECT COUNT(*) FROM information_schema.columns WHERE table_name = t.table_name) AS column_count
      FROM information_schema.tables t
      WHERE table_schema = 'public'
      ORDER BY table_name;
    `;

    res.json({
      status: 'success',
      data: tables,
    });
  } catch (error) {
    logger.error('Error getting tables:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to get tables',
      details: error instanceof Error ? error.message : 'Unknown error',
    });
  }
}) as RequestHandler);

export default router;
