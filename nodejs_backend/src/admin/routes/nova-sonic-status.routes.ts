import { Router, Request, Response } from 'express';
import { isAuthenticated } from '../middleware/auth.middleware';

const router = Router();

// Get Nova Sonic client instance (will be injected by server)
let novaSonicClient: any = null;

export function setNovaSonicClient(client: any) {
  novaSonicClient = client;
  console.log('Nova Sonic client set for status monitoring');
}

/**
 * GET /api/nova-sonic/sessions
 * Get all active Nova Sonic sessions
 */
router.get('/sessions', isAuthenticated, async (_req: Request, res: Response) => {
  try {
    if (!novaSonicClient) {
      return res.status(503).json({
        error: 'Nova Sonic client not available',
        message: 'The Nova Sonic service is not currently running'
      });
    }

    const activeSessions = novaSonicClient.getActiveSessions();
    const sessionDetails = activeSessions.map((sessionId: string) => ({
      sessionId,
      isActive: novaSonicClient.isSessionActive(sessionId),
      lastActivity: novaSonicClient.getLastActivityTime(sessionId),
      lastActivityAgo: Date.now() - novaSonicClient.getLastActivityTime(sessionId),
      cleanupInProgress: novaSonicClient.isCleanupInProgress(sessionId)
    }));

    return res.json({
      totalSessions: activeSessions.length,
      sessions: sessionDetails,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error getting Nova Sonic sessions:', error);
    return res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to retrieve session information'
    });
  }
});

/**
 * GET /api/nova-sonic/sessions/:sessionId
 * Get detailed information about a specific session
 */
router.get('/sessions/:sessionId', isAuthenticated, async (req: Request, res: Response) => {
  try {
    const { sessionId } = req.params;

    if (!novaSonicClient) {
      return res.status(503).json({
        error: 'Nova Sonic client not available',
        message: 'The Nova Sonic service is not currently running'
      });
    }

    const isActive = novaSonicClient.isSessionActive(sessionId);
    const lastActivity = novaSonicClient.getLastActivityTime(sessionId);
    const cleanupInProgress = novaSonicClient.isCleanupInProgress(sessionId);

    if (!isActive && lastActivity === 0) {
      return res.status(404).json({
        error: 'Session not found',
        message: `Session ${sessionId} does not exist or has been cleaned up`
      });
    }

    const sessionInfo = {
      sessionId,
      isActive,
      lastActivity,
      lastActivityAgo: lastActivity > 0 ? Date.now() - lastActivity : null,
      cleanupInProgress,
      status: isActive ? 'active' : 'inactive',
      timestamp: new Date().toISOString()
    };

    return res.json(sessionInfo);
  } catch (error) {
    console.error('Error getting Nova Sonic session details:', error);
    return res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to retrieve session details'
    });
  }
});

/**
 * POST /api/nova-sonic/sessions/:sessionId/close
 * Force close a specific session
 */
router.post('/sessions/:sessionId/close', isAuthenticated, async (req: Request, res: Response) => {
  try {
    const { sessionId } = req.params;

    if (!novaSonicClient) {
      return res.status(503).json({
        error: 'Nova Sonic client not available',
        message: 'The Nova Sonic service is not currently running'
      });
    }

    const isActive = novaSonicClient.isSessionActive(sessionId);
    
    if (!isActive) {
      return res.status(404).json({
        error: 'Session not found or already inactive',
        message: `Session ${sessionId} is not active`
      });
    }

    // Force close the session
    novaSonicClient.forceCloseSession(sessionId);

    return res.json({
      message: `Session ${sessionId} has been force closed`,
      sessionId,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error force closing Nova Sonic session:', error);
    return res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to close session'
    });
  }
});

/**
 * GET /api/nova-sonic/health
 * Check Nova Sonic service health
 */
router.get('/health', isAuthenticated, async (_req: Request, res: Response) => {
  try {
    const isAvailable = !!novaSonicClient;
    const activeSessions = isAvailable ? novaSonicClient.getActiveSessions() : [];
    
    const healthStatus = {
      service: 'Nova Sonic',
      status: isAvailable ? 'healthy' : 'unavailable',
      activeSessionCount: activeSessions.length,
      timestamp: new Date().toISOString(),
      details: {
        clientAvailable: isAvailable,
        hasActiveSessions: activeSessions.length > 0
      }
    };

    const statusCode = isAvailable ? 200 : 503;
    return res.status(statusCode).json(healthStatus);
  } catch (error) {
    console.error('Error checking Nova Sonic health:', error);
    return res.status(500).json({
      service: 'Nova Sonic',
      status: 'error',
      error: 'Health check failed',
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * GET /api/nova-sonic/metrics
 * Get Nova Sonic usage metrics
 */
router.get('/metrics', isAuthenticated, async (_req: Request, res: Response) => {
  try {
    if (!novaSonicClient) {
      return res.status(503).json({
        error: 'Nova Sonic client not available',
        message: 'The Nova Sonic service is not currently running'
      });
    }

    const activeSessions = novaSonicClient.getActiveSessions();
    const now = Date.now();
    
    const sessionMetrics = activeSessions.map((sessionId: string) => {
      const lastActivity = novaSonicClient.getLastActivityTime(sessionId);
      return {
        sessionId,
        isActive: novaSonicClient.isSessionActive(sessionId),
        lastActivityAgo: lastActivity > 0 ? now - lastActivity : null,
        cleanupInProgress: novaSonicClient.isCleanupInProgress(sessionId)
      };
    });

    const metrics = {
      totalActiveSessions: activeSessions.length,
      sessionsActive: sessionMetrics.filter(s => s.isActive).length,
      sessionsInCleanup: sessionMetrics.filter(s => s.cleanupInProgress).length,
      sessionAgeDistribution: {
        under1Min: sessionMetrics.filter(s => s.lastActivityAgo && s.lastActivityAgo < 60000).length,
        under5Min: sessionMetrics.filter(s => s.lastActivityAgo && s.lastActivityAgo < 300000).length,
        under15Min: sessionMetrics.filter(s => s.lastActivityAgo && s.lastActivityAgo < 900000).length,
        over15Min: sessionMetrics.filter(s => s.lastActivityAgo && s.lastActivityAgo >= 900000).length
      },
      timestamp: new Date().toISOString()
    };

    return res.json(metrics);
  } catch (error) {
    console.error('Error getting Nova Sonic metrics:', error);
    return res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to retrieve metrics'
    });
  }
});

export default router;