/**
 * Central export file for all middleware
 */

// Error handling
export {
  errorH<PERSON><PERSON>,
  notFoundHandler,
  handleUnhandledRejections,
  handleUncaughtExceptions,
  asyncRoute,
} from './errorHandler';

// Validation
export {
  handleValidationErrors,
  validate,
  body,
  param,
  query,
  header,
  cookie,
  check,
  commonValidations,
  isValidObjectId,
  isValidUUID,
} from './validation';

// Rate limiting
export {
  createRateLimiter,
  apiRateLimiter,
  strictRateLimiter,
  authRateLimiter,
  uploadRateLimiter,
} from './rateLimiter';
