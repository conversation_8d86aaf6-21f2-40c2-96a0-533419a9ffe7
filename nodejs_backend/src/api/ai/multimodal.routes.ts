/**
 * AI Multimodal API Routes
 * Provides image analysis and mockup processing endpoints
 */

import { Router } from 'express';
import { body, validationResult } from 'express-validator';
import azureService from '../../services/ai/azure.service';
import { logger } from '../../common/logger';
import { unifiedAuthMiddleware } from '../../middleware/unified-auth';

const router = Router();

/**
 * @swagger
 * /api/ai/mockups/process:
 *   post:
 *     summary: Process and enhance UI/UX mockups
 *     description: Analyze mockup images and provide improvement recommendations with updated designs
 *     tags: [AI, Multimodal, Mockups]
 *     security:
 *       - ApiKeyAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - images
 *               - instructions
 *             properties:
 *               images:
 *                 type: array
 *                 items:
 *                   type: object
 *                   required:
 *                     - url
 *                   properties:
 *                     url:
 *                       type: string
 *                       format: uri
 *                       description: URL of the mockup image
 *                     description:
 *                       type: string
 *                       description: Optional description of the mockup
 *                 description: Array of mockup images to process
 *               instructions:
 *                 type: string
 *                 description: Specific instructions for improving the mockups
 *               modelType:
 *                 type: string
 *                 enum: [gpt-4.1, gpt-4.1-mini, o3, o4-mini]
 *                 default: gpt-4.1
 *                 description: AI model to use for analysis
 *               maxTokens:
 *                 type: number
 *                 default: 8000
 *                 minimum: 100
 *                 maximum: 16000
 *                 description: Maximum tokens for response
 *               temperature:
 *                 type: number
 *                 default: 0.7
 *                 minimum: 0
 *                 maximum: 2
 *                 description: Model creativity level
 *               outputFormat:
 *                 type: string
 *                 enum: [description, code, both]
 *                 default: both
 *                 description: Format of the output
 *               designStyle:
 *                 type: string
 *                 default: modern
 *                 description: Design style preference
 *               targetPlatform:
 *                 type: string
 *                 enum: [web, mobile, desktop]
 *                 default: web
 *                 description: Target platform for the design
 *             example:
 *               images:
 *                 - url: "https://example.com/mockup1.png"
 *                   description: "Login page mockup"
 *                 - url: "https://example.com/mockup2.png"
 *                   description: "Dashboard mockup"
 *               instructions: "Make the design more modern and improve accessibility"
 *               modelType: "gpt-4.1"
 *               outputFormat: "both"
 *               designStyle: "modern"
 *               targetPlatform: "web"
 *     responses:
 *       200:
 *         description: Mockups processed successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 analysis:
 *                   type: string
 *                   description: Detailed analysis of the mockups
 *                 recommendations:
 *                   type: array
 *                   items:
 *                     type: string
 *                   description: Specific improvement recommendations
 *                 updatedMockup:
 *                   type: object
 *                   properties:
 *                     description:
 *                       type: string
 *                       description: Description of the improved mockup
 *                     components:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           type:
 *                             type: string
 *                           properties:
 *                             type: object
 *                           position:
 *                             type: object
 *                             properties:
 *                               x:
 *                                 type: number
 *                               y:
 *                                 type: number
 *                               width:
 *                                 type: number
 *                               height:
 *                                 type: number
 *                     codeSnippet:
 *                       type: string
 *                       description: Implementation code (when requested)
 *                 usage:
 *                   type: object
 *                   properties:
 *                     promptTokens:
 *                       type: number
 *                     completionTokens:
 *                       type: number
 *                     totalTokens:
 *                       type: number
 *                     cost:
 *                       type: number
 *                     durationMs:
 *                       type: number
 *                 model:
 *                   type: string
 *       400:
 *         description: Bad request - validation errors
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.post(
  '/mockups/process',
  unifiedAuthMiddleware,
  [
    body('images')
      .isArray({ min: 1 })
      .withMessage('images must be a non-empty array'),
    body('images.*.url')
      .isURL()
      .withMessage('each image must have a valid URL'),
    body('images.*.description')
      .optional()
      .isString()
      .withMessage('image description must be a string'),
    body('instructions')
      .isString()
      .notEmpty()
      .withMessage('instructions is required and must be a non-empty string'),
    body('modelType')
      .optional()
      .isIn(['gpt-4.1', 'gpt-4.1-mini', 'o3', 'o4-mini'])
      .withMessage('modelType must be one of: gpt-4.1, gpt-4.1-mini, o3, o4-mini'),
    body('maxTokens')
      .optional()
      .isInt({ min: 100, max: 16000 })
      .withMessage('maxTokens must be between 100 and 16000'),
    body('temperature')
      .optional()
      .isFloat({ min: 0, max: 2 })
      .withMessage('temperature must be between 0 and 2'),
    body('outputFormat')
      .optional()
      .isIn(['description', 'code', 'both'])
      .withMessage('outputFormat must be one of: description, code, both'),
    body('designStyle')
      .optional()
      .isString()
      .withMessage('designStyle must be a string'),
    body('targetPlatform')
      .optional()
      .isIn(['web', 'mobile', 'desktop'])
      .withMessage('targetPlatform must be one of: web, mobile, desktop'),
  ],
  async (req, res) => {
    try {
      // Check validation results
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const {
        images,
        instructions,
        modelType,
        maxTokens,
        temperature,
        outputFormat,
        designStyle,
        targetPlatform
      } = req.body;

      logger.info(`[Mockup Processing API] Processing ${images.length} mockup image(s) with model ${modelType || 'gpt-4.1'}`);

      // Process mockups using Azure service
      const result = await azureService.processMockupImages(images, instructions, {
        modelType,
        maxTokens,
        temperature,
        outputFormat,
        designStyle,
        targetPlatform
      });

      logger.info(
        `[Mockup Processing API] Successfully processed mockups. ` +
        `Recommendations: ${result.recommendations.length}, ` +
        `Cost: $${result.usage.cost.toFixed(6)}, Duration: ${result.usage.durationMs}ms`
      );

      res.json({
        success: true,
        analysis: result.analysis,
        recommendations: result.recommendations,
        updatedMockup: result.updatedMockup,
        usage: result.usage,
        model: result.model
      });
    } catch (error) {
      logger.error('[Mockup Processing API] Error processing mockups:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to process mockups',
        details: error instanceof Error ? error.message : String(error)
      });
    }
  }
);

/**
 * @swagger
 * /api/ai/image/analyze:
 *   post:
 *     summary: Analyze single image with custom prompt
 *     description: Analyze an image using AI with a custom prompt for general purpose analysis
 *     tags: [AI, Multimodal, Image Analysis]
 *     security:
 *       - ApiKeyAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - imageUrl
 *               - prompt
 *             properties:
 *               imageUrl:
 *                 type: string
 *                 format: uri
 *                 description: URL of the image to analyze
 *               prompt:
 *                 type: string
 *                 description: Analysis prompt for the image
 *               modelType:
 *                 type: string
 *                 enum: [gpt-4.1, gpt-4.1-mini, o3, o4-mini]
 *                 default: gpt-4.1
 *                 description: AI model to use for analysis
 *               maxTokens:
 *                 type: number
 *                 default: 4000
 *                 minimum: 100
 *                 maximum: 16000
 *                 description: Maximum tokens for response
 *               temperature:
 *                 type: number
 *                 default: 0.7
 *                 minimum: 0
 *                 maximum: 2
 *                 description: Model creativity level
 *               systemPrompt:
 *                 type: string
 *                 description: Custom system prompt
 *               detail:
 *                 type: string
 *                 enum: [low, high, auto]
 *                 default: high
 *                 description: Image analysis detail level
 *             example:
 *               imageUrl: "https://example.com/image.png"
 *               prompt: "Describe this image in detail"
 *               modelType: "gpt-4.1"
 *               detail: "high"
 *     responses:
 *       200:
 *         description: Image analyzed successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 content:
 *                   type: string
 *                   description: Analysis result
 *                 usage:
 *                   type: object
 *                   properties:
 *                     promptTokens:
 *                       type: number
 *                     completionTokens:
 *                       type: number
 *                     totalTokens:
 *                       type: number
 *                     cost:
 *                       type: number
 *                     durationMs:
 *                       type: number
 *                 model:
 *                   type: string
 *       400:
 *         description: Bad request - validation errors
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.post(
  '/image/analyze',
  unifiedAuthMiddleware,
  [
    body('imageUrl')
      .isURL()
      .withMessage('imageUrl must be a valid URL'),
    body('prompt')
      .isString()
      .notEmpty()
      .withMessage('prompt is required and must be a non-empty string'),
    body('modelType')
      .optional()
      .isIn(['gpt-4.1', 'gpt-4.1-mini', 'o3', 'o4-mini'])
      .withMessage('modelType must be one of: gpt-4.1, gpt-4.1-mini, o3, o4-mini'),
    body('maxTokens')
      .optional()
      .isInt({ min: 100, max: 16000 })
      .withMessage('maxTokens must be between 100 and 16000'),
    body('temperature')
      .optional()
      .isFloat({ min: 0, max: 2 })
      .withMessage('temperature must be between 0 and 2'),
    body('systemPrompt')
      .optional()
      .isString()
      .withMessage('systemPrompt must be a string'),
    body('detail')
      .optional()
      .isIn(['low', 'high', 'auto'])
      .withMessage('detail must be one of: low, high, auto'),
  ],
  async (req, res) => {
    try {
      // Check validation results
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const {
        imageUrl,
        prompt,
        modelType,
        maxTokens,
        temperature,
        systemPrompt,
        detail
      } = req.body;

      logger.info(`[Image Analysis API] Analyzing image with model ${modelType || 'gpt-4.1'}`);

      // Analyze image using Azure service
      const result = await azureService.analyzeImage(imageUrl, prompt, {
        modelType,
        maxTokens,
        temperature,
        systemPrompt,
        detail
      });

      logger.info(
        `[Image Analysis API] Successfully analyzed image. ` +
        `Cost: $${result.usage.cost.toFixed(6)}, Duration: ${result.usage.durationMs}ms`
      );

      res.json({
        success: true,
        content: result.content,
        usage: result.usage,
        model: result.model
      });
    } catch (error) {
      logger.error('[Image Analysis API] Error analyzing image:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to analyze image',
        details: error instanceof Error ? error.message : String(error)
      });
    }
  }
);

/**
 * @swagger
 * /api/ai/multimodal/health:
 *   get:
 *     summary: Health check for multimodal AI service
 *     description: Check if the multimodal AI service is available and working
 *     tags: [AI, Multimodal]
 *     responses:
 *       200:
 *         description: Service is healthy
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Multimodal AI service is healthy"
 *                 capabilities:
 *                   type: array
 *                   items:
 *                     type: string
 *                   example: ["image_analysis", "mockup_processing", "multimodal_chat"]
 *                 models:
 *                   type: array
 *                   items:
 *                     type: string
 *                   example: ["gpt-4.1", "gpt-4.1-mini", "o3", "o4-mini"]
 *       500:
 *         description: Service is unavailable
 */
router.get('/multimodal/health', async (req, res) => {
  try {
    // Test basic text generation to verify the service is working
    const testResult = await azureService.invokeModel('Test prompt', {
      modelType: 'gpt-4.1-mini',
      maxTokens: 10,
      temperature: 0.1,
    });

    res.json({
      success: true,
      message: 'Multimodal AI service is healthy',
      capabilities: [
        'image_analysis',
        'mockup_processing',
        'multimodal_chat',
        'text_generation',
        'embeddings'
      ],
      models: ['gpt-4.1', 'gpt-4.1-mini', 'o3', 'o4-mini'],
      testTokens: testResult.usage.totalTokens,
      testCost: testResult.usage.cost
    });
  } catch (error) {
    logger.error('[Multimodal AI API] Health check failed:', error);
    res.status(500).json({
      success: false,
      error: 'Multimodal AI service is unavailable',
      details: error instanceof Error ? error.message : String(error)
    });
  }
});

export default router;