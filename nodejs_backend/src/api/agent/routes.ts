/**
 * Agent Routes
 *
 * This file defines the routes for agent operations.
 */

import express from 'express';
import { container } from '../../inversify.config';
import { TYPES } from '../../types';
import { AgentController } from './controllers/agent.controller';
import { validateRequest } from '../../middleware/validate-request';
import { StartAgentSessionDto, CreateCustomPipelineDto } from './dto';
import { z } from 'zod';

// Zod schema for validation
const StartAgentSessionSchema = z.object({
  body: z.object({
    userId: z.number(),
    taskDescription: z.string(),
    projectId: z.number().optional(),
  }),
});
import { authenticateJwt } from '../../middleware/auth';
import WebSocket from 'ws';

// Get controller from container
const agentController = container.get<AgentController>(TYPES.AgentController);

// Create router
const router = express.Router();

/**
 * @swagger
 * /api/agent/session:
 *   post:
 *     summary: Start a new agent session
 *     tags: [Agent]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/StartAgentSessionDto'
 *     responses:
 *       200:
 *         description: Agent session started successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/AgentSessionResponseDto'
 *       400:
 *         description: Invalid request
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.post('/session', authenticateJwt, validateRequest(StartAgentSessionSchema), (req, res) =>
  agentController.startAgentSession(req, res),
);

/**
 * @swagger
 * /api/agent/session/{sessionId}:
 *   get:
 *     summary: Get agent session status
 *     tags: [Agent]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: sessionId
 *         required: true
 *         schema:
 *           type: string
 *         description: Agent session ID
 *     responses:
 *       200:
 *         description: Agent session status
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/AgentSessionResponseDto'
 *       400:
 *         description: Invalid request
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Session not found
 *       500:
 *         description: Internal server error
 */
router.get('/session/:sessionId', authenticateJwt, (req, res) =>
  agentController.getAgentSessionStatus(req, res),
);

/**
 * @swagger
 * /api/agent/session/{sessionId}/messages:
 *   get:
 *     summary: Get agent session messages
 *     tags: [Agent]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: sessionId
 *         required: true
 *         schema:
 *           type: string
 *         description: Agent session ID
 *     responses:
 *       200:
 *         description: Agent session messages
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/AgentMessageResponseDto'
 *       400:
 *         description: Invalid request
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Session not found
 *       500:
 *         description: Internal server error
 */
router.get('/session/:sessionId/messages', authenticateJwt, (req, res) =>
  agentController.getAgentSessionMessages(req, res),
);

/**
 * WebSocket route for real-time updates
 *
 * This route is handled separately in the WebSocket server setup.
 *
 * @example
 * const ws = new WebSocket('ws://localhost:3000/api/agent/session/{sessionId}/ws');
 * ws.onmessage = (event) => {
 *   const message = JSON.parse(event.data);
 *   console.log(message);
 * };
 */
import { IncomingMessage } from 'http';

export function setupAgentWebSocket(wss: WebSocket.Server): void {
  wss.on('connection', (ws: WebSocket, req: IncomingMessage & { url?: string; params?: any }) => {
    // Extract sessionId from URL
    const match = req.url?.match(/\/api\/agent\/session\/([^\/]+)\/ws/);
    if (match) {
      const sessionId = match[1];
      if (!req.params) req.params = {};
      req.params.sessionId = sessionId;

      // Handle WebSocket connection
      agentController.handleWebSocketConnection(ws, req);
    } else {
      // Invalid URL
      ws.send(
        JSON.stringify({
          type: 'error',
          message: 'Invalid WebSocket URL',
        }),
      );
      ws.close();
    }
  });
}

export default router;
