/**
 * Agent Controller
 *
 * This controller handles API endpoints for agent operations.
 */

import { Request, Response } from 'express';
import { IncomingMessage } from 'http';
import { AgentService } from '../../../services/agent/agent.service';
import { PipelineService } from '../../../services/agent/pipeline.service';
import { logger } from '../../../common/logger';
import { injectable, inject } from 'inversify';
import { TYPES } from '../../../types';
import { WebSocket } from 'ws';
import { verifyClerkToken } from '../../../middleware/clerk.middleware';
import { AuthenticatedRequest } from '../../../common/types/authenticated-request';

@injectable()
export class AgentController {
  constructor(
    @inject(TYPES.AgentService) private agentService: AgentService,
    @inject(TYPES.PipelineService) private pipelineService: PipelineService,
  ) {}

  /**
   * Start a new agent session
   */
  async startAgentSession(req: Request, res: Response): Promise<void> {
    try {
      const { userId, taskDescription, projectId } = req.body;

      if (!userId || !taskDescription) {
        res.status(400).json({
          success: false,
          error: 'Missing required parameters: userId, taskDescription',
        });
        return;
      }

      const session = await this.agentService.startAgentSession(
        parseInt(userId, 10),
        taskDescription,
        projectId ? parseInt(projectId, 10) : undefined,
      );

      res.status(200).json({
        success: true,
        data: session,
      });
    } catch (error) {
      logger.error('Error starting agent session:', error);

      res.status(500).json({
        success: false,
        error: error instanceof Error ? error.message : 'Internal server error',
      });
    }
  }

  /**
   * Get agent session status
   */
  async getAgentSessionStatus(req: Request, res: Response): Promise<void> {
    try {
      const { sessionId } = req.params;

      if (!sessionId) {
        res.status(400).json({
          success: false,
          error: 'Missing required parameter: sessionId',
        });
        return;
      }

      const session = await this.getSessionWithDetails(parseInt(sessionId, 10));

      if (!session) {
        res.status(404).json({
          success: false,
          error: `Session ${sessionId} not found`,
        });
        return;
      }

      res.status(200).json({
        success: true,
        data: session,
      });
    } catch (error) {
      logger.error(`Error getting agent session status:`, error);

      res.status(500).json({
        success: false,
        error: error instanceof Error ? error.message : 'Internal server error',
      });
    }
  }

  /**
   * Get agent session messages
   */
  async getAgentSessionMessages(req: Request, res: Response): Promise<void> {
    try {
      const { sessionId } = req.params;

      if (!sessionId) {
        res.status(400).json({
          success: false,
          error: 'Missing required parameter: sessionId',
        });
        return;
      }

      const messages = await this.getSessionMessages(parseInt(sessionId, 10));

      res.status(200).json({
        success: true,
        data: messages,
      });
    } catch (error) {
      logger.error(`Error getting agent session messages:`, error);

      res.status(500).json({
        success: false,
        error: error instanceof Error ? error.message : 'Internal server error',
      });
    }
  }

  /**
   * Handle WebSocket connection for real-time updates
   */
  async handleWebSocketConnection(ws: WebSocket, req: IncomingMessage & { params?: any }): Promise<void> {
    try {
      const sessionId = req.params?.sessionId;
      const authHeader = req.headers?.authorization;

      if (!sessionId) {
        ws.send(
          JSON.stringify({
            type: 'error',
            message: 'Missing sessionId parameter',
          }),
        );
        ws.close();
        return;
      }

      // Authenticate WebSocket connection
      let userId: number | null = null;
      
      if (authHeader && authHeader.startsWith('Bearer ')) {
        const token = authHeader.split(' ')[1];
        
        // Development bypass for IDE testing
        if (process.env.NODE_ENV === 'development' && process.env.DEV_IDE_TOKEN && token === process.env.DEV_IDE_TOKEN) {
          userId = parseInt(process.env.DEV_USER_ID || '1', 10);
        } else {
          // Verify JWT token
          const payload = await verifyClerkToken(token);
          if (payload?.sub) {
            userId = parseInt(payload.sub, 10);
          }
        }
      }

      if (!userId) {
        ws.send(
          JSON.stringify({
            type: 'error',
            message: 'Authentication required for WebSocket connection',
          }),
        );
        ws.close();
        return;
      }

      // Verify user has access to this session
      const session = await this.prisma.conversations.findFirst({
        where: {
          id: parseInt(sessionId, 10),
          user_id: userId,
        },
      });

      if (!session) {
        ws.send(
          JSON.stringify({
            type: 'error',
            message: 'Session not found or access denied',
          }),
        );
        ws.close();
        return;
      }

      // Register WebSocket connection
      this.agentService.registerWebSocket(sessionId, ws);

      // Send initial connection message
      ws.send(
        JSON.stringify({
          type: 'connected',
          sessionId,
          userId,
          timestamp: new Date().toISOString(),
        }),
      );

      logger.info(`WebSocket authenticated for user ${userId}, session ${sessionId}`);

      // Handle WebSocket close
      ws.on('close', () => {
        logger.info(`WebSocket connection closed for session ${sessionId}`);
      });

      // Handle WebSocket errors
      ws.on('error', (error) => {
        logger.error(`WebSocket error for session ${sessionId}:`, error);
      });
    } catch (error) {
      logger.error('Error handling WebSocket connection:', error);

      ws.send(
        JSON.stringify({
          type: 'error',
          message: error instanceof Error ? error.message : 'Internal server error',
        }),
      );

      ws.close();
    }
  }

  /**
   * Get session with details
   */
  private async getSessionWithDetails(sessionId: number): Promise<any> {
    const session = await this.prisma.conversations.findUnique({
      where: { id: sessionId },
    });

    if (!session) {
      return null;
    }

    // Get current pipeline stage
    const currentStage = await this.pipelineService.getCurrentStage(sessionId);

    // Get pipeline stages
    const pipelineStages = await this.pipelineService.getPipelineStages(sessionId);

    return {
      ...session,
      currentStage,
      pipelineStages,
    };
  }

  /**
   * Get session messages
   */
  private async getSessionMessages(sessionId: number): Promise<any[]> {
    const messages = await this.prisma.messages.findMany({
      where: { conversation_id: sessionId },
      orderBy: { created_at: 'asc' },
    });

    return messages.map((message: any) => ({
      id: message.id,
      role: message.role,
      content: message.content,
      timestamp: message.created_at,
      type:
        typeof message.meta_data === 'object' &&
        message.meta_data !== null &&
        'type' in message.meta_data
          ? String(message.meta_data.type)
          : 'message',
      stage:
        typeof message.meta_data === 'object' &&
        message.meta_data !== null &&
        'stage' in message.meta_data
          ? String(message.meta_data.stage)
          : 'evidence',
    }));
  }

  /**
   * Get Prisma service
   */
  private get prisma() {
    return this.agentService['prisma'];
  }
}
