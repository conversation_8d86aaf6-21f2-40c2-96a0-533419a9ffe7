import { inject, injectable } from 'inversify';
import { TYPES } from '../types';
import { PrismaClient } from '@prisma/client';
import { logger } from '../common/logger';
import azureService from './ai/azure.service';
import { BadRequestError, NotFoundError } from '../common/errors/http.error';

interface DocumentEmbedding {
  id: string;
  projectId: string;
  filePath: string;
  documentType: 'auto-doc' | 'interview' | 'readme' | 'comment';
  content: string;
  embedding: number[];
  metadata: Record<string, any>;
  commitHash?: string;
  createdAt: Date;
  updatedAt: Date;
}

interface SearchResult {
  document: DocumentEmbedding;
  similarity: number;
  relevantSection?: string;
}

@injectable()
export class VectorService {
  constructor(
    @inject(TYPES.PrismaService) private prisma: PrismaClient,
  ) {}

  /**
   * Store document embedding in the database
   */
  async storeDocumentEmbedding(doc: {
    projectId: string;
    filePath: string;
    content: string;
    type: 'auto-doc' | 'interview' | 'readme' | 'comment';
    commit?: string;
    embedding: number[];
    metadata: Record<string, any>;
  }): Promise<DocumentEmbedding> {
    try {
      logger.info(`Storing document embedding for ${doc.filePath}`);

      // Store in database (Note: You'll need to add a table for this in your Prisma schema)
      // For now, this is a placeholder that shows the intended structure
      const stored = {
        id: `emb_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        projectId: doc.projectId,
        filePath: doc.filePath,
        documentType: doc.type,
        content: doc.content,
        embedding: doc.embedding,
        metadata: doc.metadata,
        commitHash: doc.commit,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      // TODO: Implement actual database storage
      // const result = await this.prisma.documentEmbedding.create({
      //   data: stored
      // });

      logger.info(`Successfully stored embedding for ${doc.filePath}`);
      return stored as DocumentEmbedding;
    } catch (error) {
      logger.error('Error storing document embedding:', error);
      throw new BadRequestError('Failed to store document embedding');
    }
  }

  /**
   * Search for similar documents using vector similarity
   */
  async searchSimilar(
    query: string,
    projectId: string,
    options: {
      limit?: number;
      threshold?: number;
      documentTypes?: ('auto-doc' | 'interview' | 'readme' | 'comment')[];
      filePattern?: string;
    } = {}
  ): Promise<SearchResult[]> {
    try {
      const { limit = 10, threshold = 0.7, documentTypes, filePattern } = options;

      logger.info(`Searching for documents similar to: "${query.substring(0, 100)}..."`);

      // Generate embedding for the query
      const queryEmbeddingResult = await azureService.generateEmbedding(query);
      const queryEmbedding = queryEmbeddingResult.embedding;

      // TODO: Implement actual vector similarity search using pgvector
      // For now, this is a placeholder showing the intended functionality
      
      // Example SQL for pgvector (once implemented):
      // SELECT *, 1 - (embedding <=> $1::vector) as similarity
      // FROM document_embeddings
      // WHERE project_id = $2
      //   AND ($3::text[] IS NULL OR document_type = ANY($3))
      //   AND ($4::text IS NULL OR file_path LIKE $4)
      //   AND 1 - (embedding <=> $1::vector) > $5
      // ORDER BY embedding <=> $1::vector
      // LIMIT $6

      // Placeholder results
      const results: SearchResult[] = [];

      logger.info(`Found ${results.length} similar documents`);
      
      return results;
    } catch (error) {
      logger.error('Error searching similar documents:', error);
      throw new BadRequestError('Failed to search similar documents');
    }
  }

  /**
   * Calculate cosine similarity between two embeddings
   */
  private calculateCosineSimilarity(embedding1: number[], embedding2: number[]): number {
    if (embedding1.length !== embedding2.length) {
      throw new Error('Embeddings must have the same dimension');
    }

    let dotProduct = 0;
    let magnitude1 = 0;
    let magnitude2 = 0;

    for (let i = 0; i < embedding1.length; i++) {
      dotProduct += embedding1[i] * embedding2[i];
      magnitude1 += embedding1[i] * embedding1[i];
      magnitude2 += embedding2[i] * embedding2[i];
    }

    magnitude1 = Math.sqrt(magnitude1);
    magnitude2 = Math.sqrt(magnitude2);

    if (magnitude1 === 0 || magnitude2 === 0) {
      return 0;
    }

    return dotProduct / (magnitude1 * magnitude2);
  }

  /**
   * Update embeddings for changed documents
   */
  async updateDocumentEmbeddings(
    projectId: string,
    documents: Array<{
      filePath: string;
      content: string;
      type: 'auto-doc' | 'interview' | 'readme' | 'comment';
      metadata?: Record<string, any>;
    }>
  ): Promise<void> {
    try {
      logger.info(`Updating embeddings for ${documents.length} documents in project ${projectId}`);

      // Generate embeddings in batches to optimize API usage
      const batchSize = 20;
      for (let i = 0; i < documents.length; i += batchSize) {
        const batch = documents.slice(i, i + batchSize);
        const texts = batch.map(doc => doc.content);

        // Generate embeddings for the batch
        const embeddingsResult = await azureService.generateEmbeddings(texts);

        // Store each embedding
        for (let j = 0; j < batch.length; j++) {
          await this.storeDocumentEmbedding({
            projectId,
            filePath: batch[j].filePath,
            content: batch[j].content,
            type: batch[j].type,
            embedding: embeddingsResult.embeddings[j],
            metadata: batch[j].metadata || {},
          });
        }

        logger.info(`Processed batch ${Math.floor(i / batchSize) + 1} of ${Math.ceil(documents.length / batchSize)}`);
      }

      logger.info('Successfully updated all document embeddings');
    } catch (error) {
      logger.error('Error updating document embeddings:', error);
      throw new BadRequestError('Failed to update document embeddings');
    }
  }

  /**
   * Delete embeddings for removed documents
   */
  async deleteDocumentEmbeddings(projectId: string, filePaths: string[]): Promise<void> {
    try {
      logger.info(`Deleting embeddings for ${filePaths.length} documents`);

      // TODO: Implement actual deletion from database
      // await this.prisma.documentEmbedding.deleteMany({
      //   where: {
      //     projectId,
      //     filePath: { in: filePaths }
      //   }
      // });

      logger.info('Successfully deleted document embeddings');
    } catch (error) {
      logger.error('Error deleting document embeddings:', error);
      throw new BadRequestError('Failed to delete document embeddings');
    }
  }

  /**
   * Get embedding statistics for a project
   */
  async getEmbeddingStats(projectId: string): Promise<{
    totalDocuments: number;
    documentTypes: Record<string, number>;
    lastUpdated: Date | null;
    totalTokensUsed: number;
    estimatedCost: number;
  }> {
    try {
      // TODO: Implement actual statistics query
      // const stats = await this.prisma.documentEmbedding.groupBy({
      //   by: ['documentType'],
      //   where: { projectId },
      //   _count: true
      // });

      // Placeholder statistics
      return {
        totalDocuments: 0,
        documentTypes: {
          'auto-doc': 0,
          'interview': 0,
          'readme': 0,
          'comment': 0,
        },
        lastUpdated: null,
        totalTokensUsed: 0,
        estimatedCost: 0,
      };
    } catch (error) {
      logger.error('Error getting embedding statistics:', error);
      throw new BadRequestError('Failed to get embedding statistics');
    }
  }
}
