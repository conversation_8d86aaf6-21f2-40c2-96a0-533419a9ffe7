import { createClient, RedisClientType } from 'redis';
import { config } from '../../config/config';

export class RedisService {
  private client: RedisClientType;
  private isConnected: boolean = false;

  constructor() {
    // Initialize Redis client with configuration
    this.client = createClient({
      url: process.env.REDIS_URL || 'redis://localhost:6379',
      socket: {
        connectTimeout: 5000,
        reconnectStrategy: (retries) => {
          if (retries > 10) {
            console.error('Redis: Max reconnection attempts reached');
            return new Error('Max reconnection attempts reached');
          }
          // Exponential backoff: 100ms, 200ms, 400ms, etc.
          return Math.min(retries * 100, 3000);
        }
      }
    });

    // Set up event handlers
    this.client.on('error', (err) => {
      console.error('Redis Client Error:', err);
      this.isConnected = false;
    });

    this.client.on('connect', () => {
      console.log('Redis Client Connected');
      this.isConnected = true;
    });

    this.client.on('ready', () => {
      console.log('Redis Client Ready');
      this.isConnected = true;
    });

    this.client.on('end', () => {
      console.log('Redis Client Disconnected');
      this.isConnected = false;
    });
  }

  /**
   * Connect to Redis
   */
  async connect(): Promise<void> {
    if (!this.isConnected) {
      try {
        await this.client.connect();
      } catch (error) {
        console.error('Failed to connect to Redis:', error);
        // Don't throw - allow app to run without Redis
      }
    }
  }

  /**
   * Disconnect from Redis
   */
  async disconnect(): Promise<void> {
    if (this.isConnected) {
      await this.client.quit();
    }
  }

  /**
   * Check if Redis is available
   */
  isAvailable(): boolean {
    return this.isConnected;
  }

  /**
   * Get a value from cache
   */
  async get<T>(key: string): Promise<T | null> {
    if (!this.isAvailable()) return null;
    
    try {
      const value = await this.client.get(key);
      return value && typeof value === 'string' ? JSON.parse(value) : null;
    } catch (error) {
      console.error(`Redis GET error for key ${key}:`, error);
      return null;
    }
  }

  /**
   * Set a value in cache with optional TTL
   */
  async set<T>(key: string, value: T, ttlSeconds?: number): Promise<boolean> {
    if (!this.isAvailable()) return false;
    
    try {
      const serialized = JSON.stringify(value);
      if (ttlSeconds) {
        await this.client.setEx(key, ttlSeconds, serialized);
      } else {
        await this.client.set(key, serialized);
      }
      return true;
    } catch (error) {
      console.error(`Redis SET error for key ${key}:`, error);
      return false;
    }
  }

  /**
   * Delete a key from cache
   */
  async del(key: string): Promise<boolean> {
    if (!this.isAvailable()) return false;
    
    try {
      await this.client.del(key);
      return true;
    } catch (error) {
      console.error(`Redis DEL error for key ${key}:`, error);
      return false;
    }
  }

  /**
   * Delete keys matching a pattern
   */
  async delPattern(pattern: string): Promise<number> {
    if (!this.isAvailable()) return 0;
    
    try {
      const keys = await this.client.keys(pattern);
      if (keys.length > 0) {
        return await this.client.del(keys);
      }
      return 0;
    } catch (error) {
      console.error(`Redis DEL pattern error for ${pattern}:`, error);
      return 0;
    }
  }

  /**
   * Cache wrapper function
   * Tries to get from cache first, otherwise executes function and caches result
   */
  async cached<T>(
    key: string,
    fn: () => Promise<T>,
    ttlSeconds: number = 300 // Default 5 minutes
  ): Promise<T> {
    // Try to get from cache first
    const cached = await this.get<T>(key);
    if (cached !== null) {
      return cached;
    }

    // Execute function
    const result = await fn();

    // Cache the result
    await this.set(key, result, ttlSeconds);

    return result;
  }

  /**
   * Invalidate cache for a specific pattern
   * Useful for invalidating all cache entries for a user or project
   */
  async invalidateCache(pattern: string): Promise<void> {
    if (!this.isAvailable()) return;
    
    try {
      await this.delPattern(pattern);
    } catch (error) {
      console.error(`Cache invalidation error for pattern ${pattern}:`, error);
    }
  }
}

// Singleton instance
let redisInstance: RedisService | null = null;

export function getRedisService(): RedisService {
  if (!redisInstance) {
    redisInstance = new RedisService();
  }
  return redisInstance;
}

// Cache key generators for consistency
export const CacheKeys = {
  // User-related keys
  user: (userId: number) => `user:${userId}`,
  userSessions: (userId: number) => `user:${userId}:sessions`,
  userConversations: (userId: number) => `user:${userId}:conversations`,
  
  // Conversation-related keys
  conversation: (convId: number) => `conversation:${convId}`,
  conversationMessages: (convId: number) => `conversation:${convId}:messages`,
  
  // AI Model responses
  aiResponse: (hash: string) => `ai:response:${hash}`,
  
  // Model usage stats
  modelUsage: (userId: number, date: string) => `usage:${userId}:${date}`,
  
  // Project-related keys
  project: (projectId: number) => `project:${projectId}`,
  projectFiles: (projectId: number) => `project:${projectId}:files`,
  
  // Pattern for invalidation
  userPattern: (userId: number) => `user:${userId}:*`,
  conversationPattern: (convId: number) => `conversation:${convId}:*`,
  projectPattern: (projectId: number) => `project:${projectId}:*`,
};

// TTL constants (in seconds)
export const CacheTTL = {
  SHORT: 60,        // 1 minute
  MEDIUM: 300,      // 5 minutes  
  LONG: 3600,       // 1 hour
  DAY: 86400,       // 24 hours
  WEEK: 604800,     // 7 days
};
