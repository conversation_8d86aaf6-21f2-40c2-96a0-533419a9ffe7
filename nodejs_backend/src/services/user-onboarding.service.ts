/**
 * User Onboarding Service
 *
 * This service handles conversation-based user onboarding processes, including
 * starting onboarding conversations and extracting user preferences from the
 * conversation's content.
 */
import { logger } from '../common/logger';
import { ConversationCategory, ProjectMotivationType } from '../generated/prisma';

import aiService, { AIProvider } from './ai/index';
import conversationService from './unified-conversation.service';
import { userService } from './user.service';

interface OnboardingProfile {
  motivation: string;
  preferred_ide: string | null;
  learning_style: string | null;
  developer_strengths: string[];
  preferred_ai_models: string[];
}

class UserOnboardingService {
  /**
   * Start a new onboarding conversation
   *
   * @param userId - The ID of the user being onboarded
   * @returns Object containing conversation ID and first question
   */
  async startOnboarding(userId: number): Promise<{
    conversation_id: number;
    message: string;
    first_question: string;
  } | null> {
    try {
      logger.info(`Starting onboarding for user ${userId}`);

      // Create a new conversation for onboarding
      const conversation = await conversationService.createConversation(userId, {
        title: 'KAPI Onboarding',
        category: ConversationCategory.chat,
      });

      if (!conversation) {
        logger.error(`Failed to create onboarding conversation for user ${userId}`);
        return null;
      }

      // Add system message with onboarding instructions
      await conversationService.addMessage(
        conversation.id,
        'system',
        this.getOnboardingSystemPrompt(),
      );

      // Add the initial assistant message to start the conversation
      const initialAssistantMessage = this.getInitialAssistantMessage();

      await conversationService.addMessage(conversation.id, 'assistant', initialAssistantMessage);

      return {
        conversation_id: conversation.id,
        message: 'Onboarding conversation started',
        first_question: initialAssistantMessage,
      };
    } catch (error) {
      logger.error(`Error starting onboarding for user ${userId}:`, error);
      return null;
    }
  }

  /**
   * Process an onboarding conversation and update user profile
   *
   * @param userId - The user's ID
   * @param conversationId - The onboarding conversation ID
   * @returns The updated user or null if unsuccessful
   */
  async completeOnboarding(userId: number, conversationId: number): Promise<boolean> {
    try {
      logger.info(`Completing onboarding for user ${userId} with conversation ${conversationId}`);

      // Get all messages from the onboarding conversation
      const messages = await conversationService.getMessages(conversationId);

      // Prepare message context for information extraction
      const messagesText = messages.map((m) => `${m.role}: ${m.content}`).join('\n');

      // Create a specialized prompt for extracting structured information
      const extractionPrompt = `
        Based on the following conversation, extract these user preferences:
        1. motivation: Must be one of "learner", "contributor", or "builder"
        2. preferred_ide: The name of their preferred IDE or development environment (null if unclear)
        3. learning_style: Their stated learning style (null if unclear)
        4. developer_strengths: Array of their top strengths (empty array if unclear)
        5. preferred_ai_models: Array of their preferred AI models (empty array if unclear)

        Return ONLY valid JSON with these exact keys. No explanation or additional text.

        Conversation:
        ${messagesText}
      `;

      // Get structured information via a model completion
      const result = await aiService.generateText({
        prompt: extractionPrompt,
        provider: 'claude', // Using Claude for extraction accuracy
        model: 'claude-3-5-sonnet-20240307',
        maxTokens: 1000,
        temperature: 0.1, // Low temperature for more deterministic results
      });

      // Parse the JSON response
      let profileData: OnboardingProfile;
      try {
        // Extract just the JSON part (handling possible text before/after)
        const content = (typeof result === 'string' ? result : (result?.content || '{}'));
        // Find JSON content (between curly braces or the whole string if it's valid JSON)
        const jsonMatch = content.match(/\{.*\}/s);

        if (jsonMatch) {
          profileData = JSON.parse(jsonMatch[0]);
        } else {
          // Try parsing the whole string
          profileData = JSON.parse(content);
        }
      } catch (error) {
        logger.error(`Error parsing JSON from AI response during onboarding:`, error);
        // Fallback values if JSON parsing fails
        profileData = {
          motivation: 'learner', // Default
          preferred_ide: null,
          learning_style: null,
          developer_strengths: [],
          preferred_ai_models: [],
        };
      }

      // Update user profile with collected information
      const additionalInfo = JSON.stringify({
        motivation: profileData.motivation || 'learner',
      });

      const updated = await userService.updateProfile(userId, {
        preferredIde: profileData.preferred_ide || undefined,
        learningStyle: profileData.learning_style || undefined,
        developerStrengths: profileData.developer_strengths || [],
        preferredAiModels: profileData.preferred_ai_models || [],
        additionalInfo, // Store motivation in additionalInfo as JSON
      });

      if (!updated) {
        logger.error(`Failed to update profile for user ${userId} during onboarding`);
        return false;
      }

      // Mark onboarding as completed
      return await userService.completeOnboarding(userId);
    } catch (error) {
      logger.error(`Error completing onboarding for user ${userId}:`, error);
      return false;
    }
  }

  /**
   * Get system prompt for onboarding conversation
   */
  private getOnboardingSystemPrompt(): string {
    return `
    You are KAPI's onboarding assistant. Your task is to collect the following user information
    through a conversational flow, asking ONE question at a time:

    1. User's motivation (learner, contributor, or builder):
       - Learner: Focuses on learning and skill development
       - Contributor: Participates in open source or community projects
       - Builder: Creates products or applications

    2. Their preferred IDE or development environment

    3. Their learning style (visual, hands-on, theoretical, etc.)

    4. Their developer strengths (pick top 3):
       - Coding
       - Architecture/System Design
       - Testing/QA
       - Documentation
       - UI/UX
       - DevOps

    5. Their preferred AI models to work with (e.g., Claude, GPT-4, Gemini, etc.)

    Important guidelines:
    - Ask only ONE question at a time
    - Keep responses brief and conversational
    - Track what information you've already collected
    - Adapt questions based on previous answers
    - When all information is collected, provide a summary
    - After the summary, explicitly say "ONBOARDING_COMPLETE" to signal completion
    `;
  }

  /**
   * Get initial assistant message to start onboarding
   */
  private getInitialAssistantMessage(): string {
    return `
    Welcome to KAPI! 👋 I'll help personalize your experience by asking a few brief questions.

    First, which best describes your primary goal with KAPI?
    - Learner: Focusing on learning GenAI concepts and skills
    - Contributor: Participating in open source or community projects
    - Builder: Creating products or applications
    `;
  }
}

// Export a singleton instance
export const userOnboardingService = new UserOnboardingService();
export default userOnboardingService;
