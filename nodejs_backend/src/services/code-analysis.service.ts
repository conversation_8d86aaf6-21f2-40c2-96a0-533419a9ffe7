import { injectable, inject } from 'inversify';
import { Project, SourceFile, Node, SyntaxKind } from 'ts-morph';
import * as ts from 'typescript';
import { PrismaClient } from '@prisma/client';
import UnifiedConversationService from './unified-conversation.service';
import ProjectService from './project.service';
import { BaseService } from './base.service';
import { logger } from '../common/logger';
import { TYPES } from '../types';
import path from 'path';
import fs from 'fs';

export interface CodeHealthReport {
  id: number;
  projectId: number;
  overallScore: number;
  complexityScore: number;
  maintainability: number;
  technicalDebt: number;
  fileCount: number;
  analysisTimeMs: number;
  createdAt: Date;
  aiAnalysisModel?: string;
  metrics: {
    avgComplexity: number;
    highComplexityFiles: number;
    missingErrorHandling: number;
    totalFunctions: number;
    totalLines: number;
  };
  issues: CodeIssue[];
  aiInsights?: AIAnalysisResult;
}

export interface CodeIssue {
  id: number;
  patternType: string;
  severity: 'info' | 'warning' | 'error' | 'critical';
  filePath: string;
  lineNumber?: number;
  description: string;
  suggestion?: string;
}

export interface AIAnalysisResult {
  explanation: string;
  recommendations: string[];
  riskAssessment: string;
  refactoringSuggestions: string[];
  modelUsed: string;
  conversationId?: number;
  fallbackApplied?: boolean;
}

export interface FileMetrics {
  filePath: string;
  complexity: number;
  nestingDepth: number;
  functionCount: number;
  lineCount: number;
  hasErrorHandling: boolean;
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  issues: any[];
  functions: FunctionMetrics[];
}

export interface FunctionMetrics {
  functionName: string;
  startLine: number;
  endLine: number;
  complexity: number;
  parameterCount: number;
  returnCount: number;
  isAsync: boolean;
  hasErrorHandling: boolean;
}

export interface DetectedPattern {
  patternType: string;
  severity: 'info' | 'warning' | 'error' | 'critical';
  filePath: string;
  lineNumber?: number;
  description: string;
  suggestion?: string;
}

@injectable()
export class CodeAnalysisService extends BaseService {
  private tsProject: Project;

  constructor(
    @inject(TYPES.PrismaService) protected prisma: PrismaClient,
    @inject(TYPES.UnifiedConversationService) private conversationService: typeof UnifiedConversationService,
    @inject(TYPES.ProjectService) private projectService: typeof ProjectService
  ) {
    super(prisma);
    
    // Initialize TypeScript project for AST parsing
    this.tsProject = new Project({
      compilerOptions: {
        target: ts.ScriptTarget.Latest,
        allowJs: true,
        declaration: false,
      },
    });
  }

  async analyzeProject(
    projectId: number, 
    userId: number,
    options: {
      includeAI?: boolean;
      analysisDepth?: 'quick' | 'detailed';
    } = {}
  ): Promise<CodeHealthReport> {
    const { includeAI = true, analysisDepth = 'detailed' } = options;
    const startTime = Date.now();
    
    try {
      logger.info(`Starting code analysis for project ${projectId} with depth: ${analysisDepth}`);
      
      // 1. Validate project access
      const project = await this.validateProjectAccess(projectId, userId);
      
      // 2. Get project path
      if (!project.local_path) {
        throw new Error('Project local_path not found');
      }
      
      // 3. Parse TypeScript files using ts-morph
      const astAnalysis = await this.performASTAnalysis(project.local_path);
      
      // 4. Calculate complexity metrics
      const metrics = await this.calculateComplexityMetrics(astAnalysis);
      
      // 5. Detect patterns and anti-patterns
      const patterns = await this.detectCodePatterns(metrics);
      
      // 6. Calculate overall scores
      const scores = this.calculateHealthScores(metrics, patterns);
      
      // 7. Store base analysis results
      const report = await this.storeAnalysisResults(
        projectId, 
        metrics, 
        patterns, 
        scores,
        Date.now() - startTime
      );
      
      // 8. Add AI analysis if requested
      if (includeAI) {
        const taskType = analysisDepth === 'quick' ? 'chat' : 'code_review';
        const aiAnalysis = await this.generateAIAnalysis(report, taskType, userId);
        report.aiInsights = aiAnalysis;
      }
      
      logger.info(`Code analysis completed for project ${projectId} in ${Date.now() - startTime}ms`);
      return report;
      
    } catch (error) {
      logger.error(`Error analyzing project ${projectId}:`, error);
      throw error;
    }
  }

  private async validateProjectAccess(projectId: number, userId: number) {
    const project = await this.prisma.projects.findFirst({
      where: {
        id: projectId,
        user_id: userId
      }
    });
    
    if (!project) {
      throw new Error('Project not found or access denied');
    }
    
    return project;
  }

  private async performASTAnalysis(projectPath: string): Promise<SourceFile[]> {
    try {
      // Clear previous files
      this.tsProject.getSourceFiles().forEach(file => file.forget());
      
      // Add TypeScript/JavaScript files from project
      const pattern = path.join(projectPath, '**/*.{ts,tsx,js,jsx}');
      const sourceFiles = this.tsProject.addSourceFilesAtPaths(pattern);
      
      // Filter out node_modules and other irrelevant files
      const filteredFiles = sourceFiles.filter(file => {
        const filePath = file.getFilePath();
        return !filePath.includes('node_modules') && 
               !filePath.includes('.git') &&
               !filePath.includes('dist') &&
               !filePath.includes('build');
      });
      
      logger.info(`Loaded ${filteredFiles.length} source files for AST analysis`);
      return filteredFiles;
      
    } catch (error) {
      logger.error('Error performing AST analysis:', error);
      throw new Error(`Failed to analyze project files: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  private async calculateComplexityMetrics(sourceFiles: SourceFile[]): Promise<FileMetrics[]> {
    const fileMetrics: FileMetrics[] = [];
    
    for (const sourceFile of sourceFiles) {
      try {
        const metrics = this.analyzeFile(sourceFile);
        fileMetrics.push(metrics);
      } catch (error) {
        logger.warn(`Error analyzing file ${sourceFile.getFilePath()}:`, error);
      }
    }
    
    return fileMetrics;
  }

  private analyzeFile(sourceFile: SourceFile): FileMetrics {
    const filePath = sourceFile.getFilePath();
    const functions = this.analyzeFunctions(sourceFile);
    
    return {
      filePath: filePath,
      complexity: this.calculateCyclomaticComplexity(sourceFile),
      nestingDepth: this.calculateNestingDepth(sourceFile),
      functionCount: functions.length,
      lineCount: sourceFile.getFullText().split('\n').length,
      hasErrorHandling: this.checkErrorHandling(sourceFile),
      riskLevel: this.calculateRiskLevel(
        this.calculateCyclomaticComplexity(sourceFile),
        this.calculateNestingDepth(sourceFile),
        functions.length
      ),
      issues: [],
      functions: functions
    };
  }

  private analyzeFunctions(sourceFile: SourceFile): FunctionMetrics[] {
    const functions: FunctionMetrics[] = [];
    
    sourceFile.forEachDescendant(node => {
      if (Node.isFunctionDeclaration(node) || 
          Node.isMethodDeclaration(node) || 
          Node.isArrowFunction(node) ||
          Node.isFunctionExpression(node)) {
        
        try {
          const startLine = node.getStartLineNumber();
          const endLine = node.getEndLineNumber();
          const name = this.getFunctionName(node);
          
          functions.push({
            functionName: name,
            startLine: startLine,
            endLine: endLine,
            complexity: this.calculateFunctionComplexity(node),
            parameterCount: this.getParameterCount(node),
            returnCount: this.getReturnStatementCount(node),
            isAsync: this.isAsyncFunction(node),
            hasErrorHandling: this.hasFunctionErrorHandling(node)
          });
        } catch (error) {
          logger.warn(`Error analyzing function in ${sourceFile.getFilePath()}:`, error);
        }
      }
    });
    
    return functions;
  }

  private calculateCyclomaticComplexity(sourceFile: SourceFile): number {
    let complexity = 1; // Base complexity
    
    sourceFile.forEachDescendant(node => {
      // Count decision points: if, while, for, switch, &&, ||, ?, catch
      if (
        Node.isIfStatement(node) ||
        Node.isWhileStatement(node) ||
        Node.isForStatement(node) ||
        Node.isForInStatement(node) ||
        Node.isForOfStatement(node) ||
        Node.isSwitchStatement(node) ||
        Node.isCatchClause(node) ||
        Node.isConditionalExpression(node) ||
        Node.isDoStatement(node)
      ) {
        complexity++;
      }
      
      // Count logical operators
      if (Node.isBinaryExpression(node)) {
        const operator = node.getOperatorToken().getKind();
        if (operator === SyntaxKind.AmpersandAmpersandToken || 
            operator === SyntaxKind.BarBarToken) {
          complexity++;
        }
      }
    });
    
    return complexity;
  }

  private calculateFunctionComplexity(node: Node): number {
    let complexity = 1;
    
    node.forEachDescendant(child => {
      if (
        Node.isIfStatement(child) ||
        Node.isWhileStatement(child) ||
        Node.isForStatement(child) ||
        Node.isForInStatement(child) ||
        Node.isForOfStatement(child) ||
        Node.isSwitchStatement(child) ||
        Node.isCatchClause(child) ||
        Node.isConditionalExpression(child) ||
        Node.isDoStatement(child)
      ) {
        complexity++;
      }
      
      if (Node.isBinaryExpression(child)) {
        const operator = child.getOperatorToken().getKind();
        if (operator === SyntaxKind.AmpersandAmpersandToken || 
            operator === SyntaxKind.BarBarToken) {
          complexity++;
        }
      }
    });
    
    return complexity;
  }

  private calculateNestingDepth(sourceFile: SourceFile): number {
    let maxDepth = 0;
    
    const calculateDepth = (node: Node, currentDepth: number): void => {
      if (this.isBlockStatement(node)) {
        currentDepth++;
        maxDepth = Math.max(maxDepth, currentDepth);
      }
      
      node.forEachChild(child => {
        calculateDepth(child, currentDepth);
      });
    };
    
    calculateDepth(sourceFile, 0);
    return maxDepth;
  }

  private isBlockStatement(node: Node): boolean {
    return Node.isBlock(node) || 
           Node.isIfStatement(node) || 
           Node.isWhileStatement(node) ||
           Node.isForStatement(node) ||
           Node.isTryStatement(node);
  }

  private checkErrorHandling(sourceFile: SourceFile): boolean {
    let hasTryCatch = false;
    let hasErrorChecking = false;
    
    sourceFile.forEachDescendant(node => {
      if (Node.isTryStatement(node) || Node.isCatchClause(node)) {
        hasTryCatch = true;
      }
      
      // Look for error checking patterns
      if (Node.isIdentifier(node) && 
          (node.getText().toLowerCase().includes('error') ||
           node.getText().toLowerCase().includes('err'))) {
        hasErrorChecking = true;
      }
    });
    
    return hasTryCatch || hasErrorChecking;
  }

  private getFunctionName(node: Node): string {
    if (Node.isFunctionDeclaration(node) || Node.isMethodDeclaration(node)) {
      return node.getName() || '<anonymous>';
    }
    
    if (Node.isArrowFunction(node) || Node.isFunctionExpression(node)) {
      const parent = node.getParent();
      if (Node.isVariableDeclaration(parent)) {
        return parent.getName();
      }
      if (Node.isPropertyAssignment(parent)) {
        return parent.getName();
      }
      return '<anonymous>';
    }
    
    return '<unknown>';
  }

  private getParameterCount(node: Node): number {
    if (Node.isFunctionLikeDeclaration(node)) {
      return node.getParameters().length;
    }
    return 0;
  }

  private getReturnStatementCount(node: Node): number {
    let count = 0;
    node.forEachDescendant(child => {
      if (Node.isReturnStatement(child)) {
        count++;
      }
    });
    return count;
  }

  private isAsyncFunction(node: Node): boolean {
    if (Node.isFunctionDeclaration(node) || 
        Node.isMethodDeclaration(node) ||
        Node.isArrowFunction(node) ||
        Node.isFunctionExpression(node)) {
      return node.isAsync?.() || false;
    }
    return false;
  }

  private hasFunctionErrorHandling(node: Node): boolean {
    let hasErrorHandling = false;
    
    node.forEachDescendant(child => {
      if (Node.isTryStatement(child) || 
          Node.isCatchClause(child) ||
          (Node.isIdentifier(child) && child.getText().toLowerCase().includes('error'))) {
        hasErrorHandling = true;
      }
    });
    
    return hasErrorHandling;
  }

  private calculateRiskLevel(
    complexity: number, 
    nestingDepth: number, 
    functionCount: number
  ): 'low' | 'medium' | 'high' | 'critical' {
    const score = complexity + (nestingDepth * 2) + (functionCount > 10 ? 5 : 0);
    
    if (score > 30) return 'critical';
    if (score > 20) return 'high';
    if (score > 10) return 'medium';
    return 'low';
  }

  private async detectCodePatterns(fileMetrics: FileMetrics[]): Promise<DetectedPattern[]> {
    const patterns: DetectedPattern[] = [];
    
    for (const fileMetric of fileMetrics) {
      // Pattern 1: Missing error handling
      if (!fileMetric.hasErrorHandling && fileMetric.functions.some(f => f.isAsync)) {
        patterns.push({
          patternType: 'missing_error_handling',
          severity: 'error',
          filePath: fileMetric.filePath,
          description: 'Async functions without error handling detected',
          suggestion: 'Add try-catch blocks or .catch() handlers for async operations'
        });
      }
      
      // Pattern 2: High complexity functions
      for (const func of fileMetric.functions) {
        if (func.complexity > 20) {
          patterns.push({
            patternType: 'high_complexity_function',
            severity: func.complexity > 30 ? 'critical' : 'error',
            filePath: fileMetric.filePath,
            lineNumber: func.startLine,
            description: `Function '${func.functionName}' has high complexity (${func.complexity})`,
            suggestion: 'Break down the function into smaller, more focused functions'
          });
        }
      }
      
      // Pattern 3: Deep nesting
      if (fileMetric.nestingDepth > 6) {
        patterns.push({
          patternType: 'deep_nesting',
          severity: fileMetric.nestingDepth > 8 ? 'critical' : 'error',
          filePath: fileMetric.filePath,
          description: `Deep nesting detected (depth: ${fileMetric.nestingDepth})`,
          suggestion: 'Consider early returns or extracting nested logic into separate functions'
        });
      }
      
      // Pattern 4: Too many functions in a file
      if (fileMetric.functionCount > 20) {
        patterns.push({
          patternType: 'large_file',
          severity: 'warning',
          filePath: fileMetric.filePath,
          description: `File contains many functions (${fileMetric.functionCount})`,
          suggestion: 'Consider splitting the file into smaller modules'
        });
      }
    }
    
    return patterns;
  }

  private calculateHealthScores(
    fileMetrics: FileMetrics[], 
    patterns: DetectedPattern[]
  ): {
    overallScore: number;
    complexityScore: number;
    maintainability: number;
    technicalDebt: number;
  } {
    if (fileMetrics.length === 0) {
      return {
        overallScore: 100,
        complexityScore: 100,
        maintainability: 100,
        technicalDebt: 0
      };
    }
    
    // Calculate average complexity
    const avgComplexity = fileMetrics.reduce((sum, f) => sum + f.complexity, 0) / fileMetrics.length;
    const complexityScore = Math.max(0, 100 - (avgComplexity - 5) * 5);
    
    // Calculate maintainability based on patterns
    const criticalIssues = patterns.filter(p => p.severity === 'critical').length;
    const highIssues = patterns.filter(p => p.severity === 'error').length;
    const maintainability = Math.max(0, 100 - (criticalIssues * 20) - (highIssues * 10));
    
    // Calculate technical debt
    const technicalDebt = Math.min(100, (criticalIssues * 15) + (highIssues * 10) + (patterns.length * 2));
    
    // Overall score
    const overallScore = Math.round((complexityScore + maintainability + (100 - technicalDebt)) / 3);
    
    return {
      overallScore,
      complexityScore: Math.round(complexityScore),
      maintainability: Math.round(maintainability),
      technicalDebt: Math.round(technicalDebt)
    };
  }

  private async storeAnalysisResults(
    projectId: number,
    fileMetrics: FileMetrics[],
    patterns: DetectedPattern[],
    scores: any,
    analysisDuration: number
  ): Promise<CodeHealthReport> {
    const result = await this.prisma.code_health_reports.create({
      data: {
        project_id: projectId,
        overall_score: scores.overallScore,
        complexity_score: scores.complexityScore,
        maintainability: scores.maintainability,
        technical_debt: scores.technicalDebt,
        analysis_duration: analysisDuration,
        file_count: fileMetrics.length,
        file_metrics: {
          create: fileMetrics.map(fm => ({
            file_path: fm.filePath,
            complexity: fm.complexity,
            nesting_depth: fm.nestingDepth,
            function_count: fm.functionCount,
            line_count: fm.lineCount,
            has_error_handling: fm.hasErrorHandling,
            risk_level: fm.riskLevel,
            issues: fm.issues,
            function_metrics: {
              create: fm.functions.map(func => ({
                function_name: func.functionName,
                start_line: func.startLine,
                end_line: func.endLine,
                complexity: func.complexity,
                parameter_count: func.parameterCount,
                return_count: func.returnCount,
                is_async: func.isAsync,
                has_error_handling: func.hasErrorHandling
              }))
            }
          }))
        },
        code_patterns: {
          create: patterns.map(pattern => ({
            pattern_type: pattern.patternType,
            severity: pattern.severity,
            file_path: pattern.filePath,
            line_number: pattern.lineNumber,
            description: pattern.description,
            suggestion: pattern.suggestion
          }))
        }
      },
      include: {
        file_metrics: {
          include: {
            function_metrics: true
          }
        },
        code_patterns: true
      }
    });

    // Transform to our interface
    const totalFunctions = fileMetrics.reduce((sum, f) => sum + f.functionCount, 0);
    const totalLines = fileMetrics.reduce((sum, f) => sum + f.lineCount, 0);
    const avgComplexity = fileMetrics.reduce((sum, f) => sum + f.complexity, 0) / fileMetrics.length;
    const highComplexityFiles = fileMetrics.filter(f => f.complexity > 15).length;
    const missingErrorHandling = fileMetrics.filter(f => !f.hasErrorHandling).length;

    return {
      id: result.id,
      projectId: result.project_id,
      overallScore: result.overall_score,
      complexityScore: result.complexity_score,
      maintainability: result.maintainability,
      technicalDebt: result.technical_debt,
      fileCount: result.file_count,
      analysisTimeMs: result.analysis_duration,
      createdAt: result.created_at,
      metrics: {
        avgComplexity: Math.round(avgComplexity),
        highComplexityFiles,
        missingErrorHandling,
        totalFunctions,
        totalLines
      },
      issues: result.code_patterns.map(p => ({
        id: p.id,
        patternType: p.pattern_type,
        severity: p.severity as any,
        filePath: p.file_path,
        lineNumber: p.line_number,
        description: p.description,
        suggestion: p.suggestion
      }))
    };
  }

  private async generateAIAnalysis(
    report: CodeHealthReport, 
    taskType: string,
    userId: number
  ): Promise<AIAnalysisResult> {
    try {
      logger.info(`Generating AI analysis with task type: ${taskType}`);
      
      // Create conversation for code analysis
      const conversation = await this.conversationService.createConversation(userId, {
        title: `Code Analysis - Project ${report.projectId}`
      });

      // Build analysis prompt from metrics
      const prompt = this.buildAnalysisPrompt(report);

      // Send message - model selection is handled automatically by middleware
      const response = await this.conversationService.addUserMessageAndGetResponse(
        conversation.id,
        prompt,
        {
          maxTokens: taskType === 'chat' ? 1000 : 2000,
          temperature: 0.3,
          // No need to specify modelId - dynamic selection handles this
        }
      );

      const messageContent = response.message?.content || 'Analysis completed';
      
      return {
        explanation: messageContent,
        recommendations: this.extractRecommendations(messageContent),
        riskAssessment: this.extractRiskAssessment(messageContent),
        refactoringSuggestions: this.extractRefactoringSuggestions(messageContent),
        modelUsed: response.model || 'unknown',
        conversationId: conversation.id,
        fallbackApplied: false
      };
    } catch (error) {
      logger.error('Error generating AI analysis:', error);
      // Graceful degradation - return analysis without AI insights
      return {
        explanation: 'AI analysis temporarily unavailable',
        recommendations: [],
        riskAssessment: 'Unable to assess',
        refactoringSuggestions: [],
        modelUsed: 'none',
        fallbackApplied: true
      };
    }
  }

  private buildAnalysisPrompt(report: CodeHealthReport): string {
    const criticalIssues = report.issues.filter(i => i.severity === 'critical');
    const highIssues = report.issues.filter(i => i.severity === 'error');
    
    return `
Analyze this code quality report and provide insights:

**Project Health Score**: ${report.overallScore}/100

**Critical Issues (${criticalIssues.length})**:
${criticalIssues.map(issue => `- ${issue.description} in ${issue.filePath}${issue.lineNumber ? ':' + issue.lineNumber : ''}`).join('\n')}

**High Priority Issues (${highIssues.length})**:
${highIssues.map(issue => `- ${issue.description} in ${issue.filePath}${issue.lineNumber ? ':' + issue.lineNumber : ''}`).join('\n')}

**Complexity Metrics**:
- Average function complexity: ${report.metrics.avgComplexity}
- Files with high complexity: ${report.metrics.highComplexityFiles}
- Functions missing error handling: ${report.metrics.missingErrorHandling}
- Total files analyzed: ${report.fileCount}

Please provide:
1. **Risk Assessment**: What are the main risks these issues pose?
2. **Priority Recommendations**: Which issues should be fixed first and why?
3. **Refactoring Strategy**: High-level approach to improve code health
4. **Specific Examples**: Show how to fix 1-2 critical issues with code examples

Focus on actionable insights that help developers improve code quality.
`;
  }

  private extractRecommendations(content: string): string[] {
    const recommendations: string[] = [];
    const lines = content.split('\n');
    let inRecommendations = false;
    
    for (const line of lines) {
      if (line.toLowerCase().includes('recommendation') || 
          line.toLowerCase().includes('priority')) {
        inRecommendations = true;
        continue;
      }
      
      if (inRecommendations && line.trim().startsWith('-')) {
        recommendations.push(line.trim().substring(1).trim());
      }
      
      if (inRecommendations && line.trim() === '') {
        inRecommendations = false;
      }
    }
    
    return recommendations;
  }

  private extractRiskAssessment(content: string): string {
    const lines = content.split('\n');
    let inRiskSection = false;
    let riskAssessment = '';
    
    for (const line of lines) {
      if (line.toLowerCase().includes('risk assessment')) {
        inRiskSection = true;
        continue;
      }
      
      if (inRiskSection) {
        if (line.toLowerCase().includes('recommendation') || 
            line.toLowerCase().includes('refactoring')) {
          break;
        }
        riskAssessment += line + '\n';
      }
    }
    
    return riskAssessment.trim() || 'No specific risk assessment provided';
  }

  private extractRefactoringSuggestions(content: string): string[] {
    const suggestions: string[] = [];
    const lines = content.split('\n');
    let inRefactoring = false;
    
    for (const line of lines) {
      if (line.toLowerCase().includes('refactoring') || 
          line.toLowerCase().includes('specific examples')) {
        inRefactoring = true;
        continue;
      }
      
      if (inRefactoring && line.trim().startsWith('-')) {
        suggestions.push(line.trim().substring(1).trim());
      }
    }
    
    return suggestions;
  }

  async getLatestHealthScore(projectId: number, userId: number): Promise<any> {
    await this.validateProjectAccess(projectId, userId);
    
    const latestReport = await this.prisma.code_health_reports.findFirst({
      where: { project_id: projectId },
      orderBy: { created_at: 'desc' },
      include: {
        code_patterns: true
      }
    });
    
    if (!latestReport) {
      return null;
    }
    
    return {
      overallScore: latestReport.overall_score,
      complexityScore: latestReport.complexity_score,
      maintainability: latestReport.maintainability,
      technicalDebt: latestReport.technical_debt,
      lastAnalysis: latestReport.created_at,
      topIssues: latestReport.code_patterns.map(p => ({
        description: p.description,
        severity: p.severity,
        filePath: p.file_path
      }))
    };
  }
}