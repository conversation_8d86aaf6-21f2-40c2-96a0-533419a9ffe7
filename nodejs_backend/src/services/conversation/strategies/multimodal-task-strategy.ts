/**
 * Multimodal Task Strategy
 *
 * Strategy for analyzing mockup images and providing design recommendations using multimodal AI.
 */

import { BaseTaskStrategy } from '../base-task-strategy';
import { TaskContext } from '../task-strategy.interface';
import { ChatResponse, MessageResponse } from '../../../common/types/conversation.types';
import azureService from '../../../services/ai/azure.service';
import { logger } from '../../../common/logger';
import { config } from '../../../config/config';

export class MultimodalTaskStrategy extends BaseTaskStrategy {
  /**
   * Get the task type identifier
   */
  getTaskType(): string {
    return 'multimodal';
  }

  /**
   * Get the default model for multimodal tasks
   */
  getDefaultModel(): string {
    return 'gpt-4.1'; // Use GPT-4.1 for high-quality multimodal analysis
  }

  /**
   * Get the default max tokens for multimodal tasks
   */
  getDefaultMaxTokens(): number {
    return 8000; // Higher token limit for detailed analysis
  }

  /**
   * Get the default temperature for multimodal tasks
   */
  getDefaultTemperature(): number {
    return 0.7; // Balanced creativity for design recommendations
  }

  /**
   * Format the user message for multimodal tasks
   */
  formatUserMessage(context: TaskContext): string {
    const { userMessage } = context;
    const multimodalOptions = context.options.multimodalOptions || {};
    const { instructions, outputFormat, designStyle, targetPlatform } = multimodalOptions;

    let formattedMessage = userMessage || '';

    // Add context from previous conversation if available
    if (context.messages && context.messages.length > 0) {
      formattedMessage += '\n\nPrevious conversation context:\n';
      
      // Include the last few messages for context (limit to avoid token overflow)
      const recentMessages = context.messages.slice(-3);
      recentMessages.forEach((message) => {
        const role = message.role === 'user' ? 'User' : 'Assistant';
        formattedMessage += `${role}: ${message.content.substring(0, 200)}...\n`;
      });
    }

    // Add specific multimodal instructions
    formattedMessage += `\n\nMultimodal Analysis Request:
- Instructions: ${instructions}
- Output Format: ${outputFormat}
- Design Style: ${designStyle}
- Target Platform: ${targetPlatform}

Please analyze the provided mockup images and provide detailed recommendations that build upon our conversation history.`;

    return formattedMessage;
  }

  /**
   * Get the system prompt for multimodal tasks
   */
  getSystemPrompt(context: TaskContext): string | null {
    const multimodalOptions = context.options.multimodalOptions || {};
    
    // Use the SVG-specific prompt from prompts.yaml for SVG generation
    const svgSystemPrompt = config.prompts?.multimodal?.svg_mockup?.system;
    
    if (svgSystemPrompt) {
      logger.info('[MultimodalTaskStrategy] Using SVG-specific system prompt from prompts.yaml');
      return svgSystemPrompt;
    }

    // Fallback to the original prompt if yaml prompt is not available
    const { outputFormat, designStyle, targetPlatform } = multimodalOptions;
    
    logger.warn('[MultimodalTaskStrategy] SVG prompt not found in prompts.yaml, using fallback');
    
    return (
      multimodalOptions.systemPrompt ||
      `You are an expert UI/UX designer and developer specializing in mockup analysis and enhancement within conversational contexts.

Your task is to analyze provided mockup images and provide:
1. Detailed analysis of the current design
2. Specific improvement recommendations that build on our conversation
3. Updated mockup description with precise component specifications
4. ${outputFormat === 'code' || outputFormat === 'both' ? 'Implementation code when requested' : 'Design guidance'}

Design Context:
- Style: ${designStyle}
- Platform: ${targetPlatform}
- Output Format: ${outputFormat}

Important: Consider the conversation history when making recommendations. Your suggestions should be contextual and build upon any previous design decisions or requirements discussed.

Provide structured, actionable recommendations in clear, professional language.`
    );
  }

  /**
   * Execute the task strategy and get a response
   */
  async execute(context: TaskContext): Promise<ChatResponse> {
    try {
      const startTime = Date.now();
      const modelId = context.options.modelId || this.getDefaultModel();
      const maxTokens = context.options.maxTokens || this.getDefaultMaxTokens();
      const temperature = context.options.temperature || this.getDefaultTemperature();
      const systemPrompt = this.getSystemPrompt(context) || undefined;
      const formattedMessage = this.formatUserMessage(context);
      
      const multimodalOptions = context.options.multimodalOptions || {};
      const { images, instructions, outputFormat, designStyle, targetPlatform } = multimodalOptions;

      if (!images || images.length === 0) {
        throw new Error('No images provided for multimodal analysis');
      }

      logger.info(`[Multimodal Strategy] Processing ${images.length} image(s) with conversation context`);

      // Use Azure service's processMockupImages method which includes conversation context
      const response = await azureService.processMockupImages(
        images,
        instructions,
        {
          modelType: modelId,
          maxTokens,
          temperature,
          outputFormat,
          designStyle,
          targetPlatform,
        }
      );

      // Create a comprehensive response that includes conversation context
      let contextualContent = `## Mockup Analysis & Recommendations\n\n`;
      contextualContent += `**Design Analysis:**\n${response.analysis}\n\n`;
      
      if (response.recommendations.length > 0) {
        contextualContent += `**Recommendations:**\n`;
        response.recommendations.forEach((rec, index) => {
          contextualContent += `${index + 1}. ${rec}\n`;
        });
        contextualContent += '\n';
      }

      if (response.updatedMockup) {
        contextualContent += `**Updated Design Specification:**\n${response.updatedMockup.description}\n\n`;
        
        if (response.updatedMockup.components && response.updatedMockup.components.length > 0) {
          contextualContent += `**Component Structure:**\n`;
          response.updatedMockup.components.forEach((comp, index) => {
            contextualContent += `${index + 1}. ${comp.type}: ${JSON.stringify(comp.properties, null, 2)}\n`;
          });
          contextualContent += '\n';
        }

        if (response.updatedMockup.codeSnippet && (outputFormat === 'code' || outputFormat === 'both')) {
          contextualContent += `**Implementation Code:**\n\`\`\`\n${response.updatedMockup.codeSnippet}\n\`\`\`\n\n`;
        }
      }

      // Add conversation context note
      if (context.messages && context.messages.length > 0) {
        contextualContent += `*Note: These recommendations consider our previous conversation and build upon earlier design decisions.*`;
      }

      const message: MessageResponse = {
        role: 'assistant',
        content: contextualContent,
        conversationId: context.conversationId,
        createdAt: new Date(),
      };

      logger.info(
        `[Multimodal Strategy] Analysis complete. Cost: $${response.usage.cost.toFixed(6)}, Duration: ${response.usage.durationMs}ms`
      );

      return {
        status: 'success',
        message,
        model: response.model,
        processingTime: response.usage.durationMs,
        promptTokens: response.usage.promptTokens,
        completionTokens: response.usage.completionTokens,
        cost: response.usage.cost,
        durationMs: response.usage.durationMs,
        // Include structured data for potential frontend use (as extended property)
        ...(response as any).structuredData && { structuredData: (response as any).structuredData },
      };
    } catch (error) {
      logger.error('Error in multimodal task strategy execute:', error);
      
      // Return error response in the expected format
      const message: MessageResponse = {
        role: 'assistant',
        content: `I apologize, but I encountered an error while analyzing the mockup images: ${error instanceof Error ? error.message : 'Unknown error'}. Please try again or check that the image URLs are accessible.`,
        conversationId: context.conversationId,
        createdAt: new Date(),
      };

      return {
        status: 'error',
        message,
        model: this.getDefaultModel(),
        processingTime: 0,
        promptTokens: 0,
        completionTokens: 0,
        cost: 0,
        durationMs: 0,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Stream the response from the task strategy
   * Note: Multimodal analysis doesn't support streaming yet, so we'll simulate it
   */
  async *streamExecute(context: TaskContext): AsyncGenerator<any, void, unknown> {
    try {
      logger.info('[Multimodal Strategy] Starting streaming execution (simulated)');
      
      // Execute the full task
      const result = await this.execute(context);
      
      if (result.status === 'error') {
        yield {
          content: result.message?.content || 'Error occurred',
          done: true,
          error: result.error,
        };
        return;
      }

      // Simulate streaming by chunking the response
      const content = result.message?.content || '';
      const chunkSize = 50; // Characters per chunk
      
      for (let i = 0; i < content.length; i += chunkSize) {
        const chunk = content.substring(i, i + chunkSize);
        yield {
          content: chunk,
          done: false,
        };
        
        // Add small delay to simulate streaming
        await new Promise(resolve => setTimeout(resolve, 50));
      }

      // Send final chunk with usage info
      yield {
        content: '',
        done: true,
        usage: {
          prompt_tokens: result.promptTokens || 0,
          completion_tokens: result.completionTokens || 0,
          total_tokens: (result.promptTokens || 0) + (result.completionTokens || 0),
          cost: result.cost || 0,
          duration_ms: result.durationMs || 0,
        },
        model: result.model,
        structuredData: (result as any).structuredData,
      };
    } catch (error) {
      logger.error('Error in multimodal task strategy streamExecute:', error);
      yield {
        content: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        done: true,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }
}