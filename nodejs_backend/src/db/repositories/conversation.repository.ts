/**
 * Conversation repository for database operations related to conversations
 */
import {
  conversations as Conversation,
  ConversationStatus,
  ConversationCategory,
  AgentStatus,
  LastActivityType,
  messages as Message,
} from '../../generated/prisma/index';
import { BaseRepository } from './base.repository';
import { injectable, inject } from 'inversify';
import { TYPES } from '../../types';
import { DatabaseService } from '../database.service';
import { PrismaClient } from '../../generated/prisma/index';

@injectable()
export class ConversationRepository extends BaseRepository<Conversation> {
  private prismaClient: PrismaClient;

  constructor(@inject(TYPES.DatabaseService) databaseService: DatabaseService) {
    super('conversations'); // Note: Changed from 'conversation' to 'conversations' to match the actual table name
    this.prismaClient = databaseService.getClient();
  }

  // Override the modelClient getter to ensure it uses the correct model
  protected get modelClient(): any {
    // Use the parent's prisma instance which is already initialized
    return this.prisma.conversations;
  }

  /**
   * Find conversations by user ID
   */
  async findByUserId(
    userId: number,
    options: { skip?: number; take?: number; status?: ConversationStatus } = {},
  ): Promise<Conversation[]> {
    return this.prisma.conversations.findMany({
      where: {
        userId,
        ...(options.status ? { status: options.status } : {}),
      },
      orderBy: { updatedAt: 'desc' },
      skip: options.skip,
      take: options.take,
    });
  }

  /**
   * Find conversations by status
   */
  async findByStatus(status: ConversationStatus): Promise<Conversation[]> {
    return this.prisma.conversations.findMany({
      where: { status },
    });
  }

  /**
   * Find conversations by category
   */
  async findByCategory(category: ConversationCategory): Promise<Conversation[]> {
    return this.prisma.conversations.findMany({
      where: { category },
    });
  }

  /**
   * Find pinned conversations for a user
   */
  async findPinnedByUserId(userId: number): Promise<Conversation[]> {
    return this.prisma.conversations.findMany({
      where: {
        userId,
        isPinned: true,
      },
      orderBy: { updatedAt: 'desc' },
    });
  }

  /**
   * Find conversations by project ID
   */
  async findByProjectId(projectId: number): Promise<Conversation[]> {
    return this.prisma.conversations.findMany({
      where: { projectId },
      orderBy: { updatedAt: 'desc' },
    });
  }

  /**
   * Count conversations by user ID
   */
  async countByUserId(userId: number, status?: ConversationStatus): Promise<number> {
    return this.prisma.conversations.count({
      where: {
        userId,
        ...(status ? { status } : {}),
      },
    });
  }

  /**
   * Get full conversation with messages
   */
  async getWithMessages(id: number): Promise<Conversation & { messages: Message[] }> {
    return this.prisma.conversations.findUnique({
      where: { id },
      include: {
        messages: {
          orderBy: { createdAt: 'asc' },
        },
      },
    }) as Promise<Conversation & { messages: Message[] }>;
  }

  /**
   * Add a message to a conversation
   */
  async addMessage(
    conversationId: number,
    data: {
      role: string;
      content: string;
      model?: string;
      prompt_tokens?: number;
      completion_tokens?: number;
      cost?: number;
      duration_ms?: number;
      user_feedback?: string;
      project_id?: number;
      meta_data?: any;
    },
  ): Promise<Message> {
    try {
      console.log(`Repository: Adding ${data.role} message to conversation ${conversationId}`);

      // First, check if the conversation exists
      const conversation = await this.prisma.conversations.findUnique({
        where: { id: conversationId },
      });

      if (!conversation) {
        throw new Error(`Conversation with ID ${conversationId} not found`);
      }

      // Update the conversation's updated_at timestamp
      await this.prisma.conversations.update({
        where: { id: conversationId },
        data: { updated_at: new Date() },
      });

      // Then create the message
      const message = await this.prisma.messages.create({
        data: {
          ...data,
          conversation_id: conversationId,
          created_at: new Date(),
        },
      });

      console.log(
        `Repository: Successfully created message ID ${message.id} for conversation ${conversationId}`,
      );
      return message;
    } catch (error) {
      console.error(`Repository: Error adding message to conversation ${conversationId}:`, error);
      throw error;
    }
  }

  /**
   * Get conversation messages formatted for LLM
   */
  async getMessagesForLlm(
    conversationId: number,
    limit?: number,
  ): Promise<Array<{ role: string; content: string }>> {
    const messages = await this.prisma.messages.findMany({
      where: { conversation_id: conversationId },
      orderBy: { created_at: 'asc' },
      select: {
        role: true,
        content: true,
      },
      ...(limit ? { take: limit } : {}),
    });

    return messages;
  }

  /**
   * Get all messages for a conversation
   */
  async getMessages(conversationId: number, skip?: number, limit?: number): Promise<Message[]> {
    return this.prisma.messages.findMany({
      where: { conversation_id: conversationId },
      orderBy: { created_at: 'asc' },
      skip,
      ...(limit ? { take: limit } : {}),
    });
  }

  /**
   * Update agent status
   */
  async updateAgentStatus(id: number, status: AgentStatus): Promise<Conversation> {
    return this.prisma.conversations.update({
      where: { id },
      data: { agent_status: status },
    });
  }

  /**
   * Update agent progress
   */
  async updateAgentProgress(id: number, progress: any): Promise<Conversation> {
    return this.prisma.conversations.update({
      where: { id },
      data: {
        agent_progress: progress,
        agent_iteration_count: { increment: 1 },
      },
    });
  }

  /**
   * Record agent command execution
   */
  async recordAgentCommand(id: number, command: any): Promise<Conversation> {
    const conversation = await this.prisma.conversations.findUnique({
      where: { id },
      select: { agent_commands_executed: true },
    });

    const commands = (conversation?.agent_commands_executed as any[]) || [];
    commands.push({
      ...command,
      timestamp: new Date().toISOString(),
    });

    return this.prisma.conversations.update({
      where: { id },
      data: { agent_commands_executed: commands },
    });
  }

  /**
   * Update last activity type
   */
  async updateLastActivityType(id: number, activityType: LastActivityType): Promise<Conversation> {
    return this.prisma.conversations.update({
      where: { id },
      data: {
        last_activity_type: activityType,
        updated_at: new Date(),
      },
    });
  }

  /**
   * Delete all messages in a conversation
   */
  async deleteAllMessages(conversationId: number): Promise<number> {
    const result = await this.prisma.messages.deleteMany({
      where: { conversation_id: conversationId },
    });
    return result.count;
  }

  /**
   * Update user feedback for a conversation
   */
  async updateUserFeedback(id: number, rating: number, feedback?: string): Promise<Conversation> {
    return this.prisma.conversations.update({
      where: { id },
      data: {
        user_rating: rating,
        user_feedback: feedback,
      },
    });
  }
}

// Export a function to get the repository from the container
// This ensures the container is initialized before accessing it
export const getConversationRepository = (): ConversationRepository => {
  try {
    // Lazy load the container to avoid circular dependencies
    const { container } = require('../../inversify.config');
    
    if (!container) {
      throw new Error('Inversify container is not initialized');
    }
    
    const repository = container.get(TYPES.ConversationRepository) as ConversationRepository;
    
    if (!repository) {
      throw new Error('ConversationRepository not found in container');
    }
    
    return repository;
  } catch (error) {
    console.error('Error getting ConversationRepository:', error);
    throw new Error(`Failed to get ConversationRepository: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};
