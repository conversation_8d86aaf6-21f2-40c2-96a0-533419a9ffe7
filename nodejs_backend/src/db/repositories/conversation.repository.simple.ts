/**
 * Simplified conversation repository factory that doesn't rely on Inversify
 */
import { ConversationRepository } from './conversation.repository';
import { prisma } from '../client';
import { logger } from '../../common/logger';

// Create a simple database service that just returns the prisma client
const simpleDatabaseService = {
  getClient: () => prisma
};

// Create a singleton instance
let repositoryInstance: ConversationRepository | null = null;

/**
 * Get or create a conversation repository instance
 * This provides a fallback that doesn't rely on the Inversify container
 */
export function getSimpleConversationRepository(): ConversationRepository {
  if (!repositoryInstance) {
    logger.info('Creating simple conversation repository instance');
    repositoryInstance = new ConversationRepository(simpleDatabaseService as any);
  }
  return repositoryInstance;
}
