import { Request, Response, NextFunction } from 'express';

// Extend Express Request and Response types to fix TypeScript errors
declare global {
  namespace Express {
    interface Request {}
    interface Response {}
  }
}

// Define a custom middleware type that works with async functions
export type AsyncRequestHandler = (
  req: Request,
  res: Response,
  next: NextFunction,
) => Promise<void> | void;

// Export the module
export {};
