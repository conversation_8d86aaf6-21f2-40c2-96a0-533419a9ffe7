import { Router, Request, Response } from 'express';
import { clerkAuthMiddleware } from '../middleware/clerk.middleware';
import { unifiedConversationService } from '../services/conversation/unified-conversation.service';
import geminiService from '../services/ai/gemini.service';
import { logger } from '../common/logger';

const router = Router();

/**
 * Task routing using Gemini function calling
 * 
 * This endpoint uses Gemini 2.0 Flash to analyze user requests and automatically
 * route them to the appropriate task handler (slides, mockup, tests, code)
 */

// Define the available task functions for Gemini
const taskFunctions = [
  {
    name: "generate_slides",
    description: "Generate a professional slide presentation using reveal.js. Use for requests about creating slides, presentations, pitch decks, or slide shows.",
    parameters: {
      type: "object",
      properties: {
        topic: {
          type: "string",
          description: "The main topic or subject for the presentation"
        },
        style: {
          type: "string", 
          description: "Visual style preferences (e.g., 'dark theme', 'professional', 'modern')",
          default: "professional"
        },
        audience: {
          type: "string",
          description: "Target audience for the presentation",
          default: "business"
        }
      },
      required: ["topic"]
    }
  },
  {
    name: "generate_mockup",
    description: "Generate UI/UX mockups or wireframes in SVG format. Use for requests about creating mockups, wireframes, UI designs, or visual prototypes.",
    parameters: {
      type: "object",
      properties: {
        description: {
          type: "string",
          description: "Description of the UI/interface to create"
        },
        type: {
          type: "string",
          description: "Type of interface (web, mobile, desktop)",
          default: "web"
        },
        style: {
          type: "string",
          description: "Design style preferences",
          default: "modern"
        }
      },
      required: ["description"]
    }
  },
  {
    name: "generate_tests",
    description: "Generate test cases, unit tests, or testing code. Use for requests about creating tests, test suites, or testing documentation.",
    parameters: {
      type: "object",
      properties: {
        codeToTest: {
          type: "string",
          description: "The code that needs testing"
        },
        testFramework: {
          type: "string", 
          description: "Testing framework to use (jest, mocha, pytest, etc.)",
          default: "jest"
        },
        testType: {
          type: "string",
          description: "Type of tests (unit, integration, e2e)",
          default: "unit"
        }
      },
      required: ["codeToTest"]
    }
  },
  {
    name: "generate_code",
    description: "Generate code, functions, classes, or complete applications. Use for requests about writing code, implementing features, or building applications.",
    parameters: {
      type: "object",
      properties: {
        requirements: {
          type: "string",
          description: "Detailed requirements for the code to generate"
        },
        language: {
          type: "string",
          description: "Programming language",
          default: "javascript"
        },
        framework: {
          type: "string",
          description: "Framework or library to use",
          default: "none"
        }
      },
      required: ["requirements"]
    }
  }
];

/**
 * POST /task-router
 * 
 * Intelligent task routing using Gemini function calling
 */
router.post('/', clerkAuthMiddleware, async (req: Request, res: Response) => {
  try {
    const { prompt, conversationId } = req.body;
    const userId = (req as any).userId;

    if (!prompt) {
      return res.status(400).json({
        status: 'error',
        message: 'prompt is required'
      });
    }

    if (!conversationId) {
      return res.status(400).json({
        status: 'error', 
        message: 'conversationId is required'
      });
    }

    logger.info(`🔧 [TASK-ROUTER] Routing task for user ${userId}: "${prompt.substring(0, 100)}..."`);

    // Use Gemini function calling to determine the task type
    const routingResponse = await geminiService.chatWithFunctionCalling({
      messages: [
        {
          role: 'system',
          content: 'You are a task router. Analyze the user\'s request and call the appropriate function to handle their task. Choose the function that best matches their request type.'
        },
        {
          role: 'user', 
          content: prompt
        }
      ],
      model: 'gemini-2.0-flash',
      tools: taskFunctions,
      maxTokens: 1000,
      temperature: 0.3
    });

    logger.info('🔧 [TASK-ROUTER] Gemini routing response:', JSON.stringify(routingResponse, null, 2));

    // Check if Gemini called a function
    if (routingResponse.tool_calls && routingResponse.tool_calls.length > 0) {
      const toolCall = routingResponse.tool_calls[0];
      const functionName = toolCall.function.name;
      const functionArgs = JSON.parse(toolCall.function.arguments);

      logger.info(`🔧 [TASK-ROUTER] Routed to function: ${functionName}`);
      logger.info(`🔧 [TASK-ROUTER] Function arguments:`, functionArgs);

      // Map function names to task types
      const taskTypeMap: Record<string, string> = {
        'generate_slides': 'slide_generation',
        'generate_mockup': 'svg_mockup', 
        'generate_tests': 'test_cases',
        'generate_code': 'code_generation'
      };

      const taskType = taskTypeMap[functionName];
      if (!taskType) {
        throw new Error(`Unknown function: ${functionName}`);
      }

      // Route to the appropriate strategy
      logger.info(`🔧 [TASK-ROUTER] Executing task type: ${taskType}`);
      
      const result = await unifiedConversationService.streamResponse(
        conversationId,
        prompt,
        {
          taskType,
          maxTokens: 8192,
          temperature: 0.7,
          modelId: 'claude-3.7-sonnet' // Force Claude for better results
        }
      );

      // Stream the response back
      res.setHeader('Content-Type', 'text/event-stream');
      res.setHeader('Cache-Control', 'no-cache');
      res.setHeader('Connection', 'keep-alive');
      res.setHeader('Access-Control-Allow-Origin', '*');

      for await (const chunk of result) {
        if (chunk.content) {
          res.write(`data: ${JSON.stringify({ type: 'content', content: chunk.content })}\n\n`);
        }
        
        if (chunk.done) {
          res.write(`data: ${JSON.stringify({ 
            type: 'done', 
            usage: chunk.usage,
            model: chunk.model,
            taskType: taskType,
            routedBy: 'gemini-function-calling'
          })}\n\n`);
          break;
        }
      }

      res.end();

    } else {
      // No function called - default to chat
      logger.info('🔧 [TASK-ROUTER] No function called, defaulting to chat');
      
      const result = await unifiedConversationService.streamResponse(
        conversationId,
        prompt,
        {
          taskType: 'chat',
          maxTokens: 2000,
          temperature: 0.7
        }
      );

      // Stream the response back
      res.setHeader('Content-Type', 'text/event-stream');
      res.setHeader('Cache-Control', 'no-cache');
      res.setHeader('Connection', 'keep-alive');
      res.setHeader('Access-Control-Allow-Origin', '*');

      for await (const chunk of result) {
        if (chunk.content) {
          res.write(`data: ${JSON.stringify({ type: 'content', content: chunk.content })}\n\n`);
        }
        
        if (chunk.done) {
          res.write(`data: ${JSON.stringify({ 
            type: 'done', 
            usage: chunk.usage,
            model: chunk.model,
            taskType: 'chat',
            routedBy: 'gemini-function-calling'
          })}\n\n`);
          break;
        }
      }

      res.end();
    }

  } catch (error: any) {
    logger.error('🔧 [TASK-ROUTER] Error in task routing:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to route task',
      details: error?.message || 'Unknown error'
    });
  }
});

export default router;