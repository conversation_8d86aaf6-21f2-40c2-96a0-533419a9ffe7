/**
 * Tools Routes - API endpoints for Nova Sonic tool integration
 * 
 * These routes are called by Nova Sonic tools to leverage the unified conversation service
 * and existing task strategies for Demo 1 workflow implementation.
 */

import { Router } from 'express';
import { Request, Response } from 'express';
import unifiedConversationService from '../services/unified-conversation.service';
import { logger } from '../common/logger';
import path from 'path';
import { promises as fs } from 'fs';
import axios from 'axios';
import { 
  getOrCreateProjectStructure, 
  saveToProjectFolder, 
  generateTimestampedFilename,
  ProjectStructure 
} from '../utils/project-folders';

const router = Router();

// Temporary user ID for demo purposes - in production this would come from auth
const DEMO_USER_ID = 1;

/**
 * Generate Product Idea Document
 * POST /api/tools/generate-product-idea
 */
router.post('/generate-product-idea', async (req: Request, res: Response) => {
  try {
    const { productName, description, targetAudience, keyFeatures, useCase } = req.body;

    console.log('🔧 [BACKEND] Product idea generation tool called');
    console.log('🔧 [BACKEND] Request body:', JSON.stringify(req.body, null, 2));
    logger.info('Product idea generation requested:', { productName, description });

    // Create a conversation for product planning
    const conversation = await unifiedConversationService.createConversation(DEMO_USER_ID, {
      title: `Product Idea: ${productName}`,
      category: 'planning',
      keyObjective: 'Generate comprehensive product documentation'
    });

    // Build enhanced prompt for product idea generation
    const prompt = `Generate a comprehensive product idea document for "${productName}".

Product Details:
- Description: ${description}
- Target Audience: ${targetAudience || 'General users'}
- Key Features: ${keyFeatures?.join(', ') || 'To be determined'}
- Primary Use Case: ${useCase || 'To be determined'}

Please create a detailed product_idea.md document that includes:
1. Executive Summary
2. Problem Statement
3. Target Audience Analysis
4. Key Features & Functionality
5. User Personas
6. Key User Flows
7. Success Metrics
8. Next Steps

Format the response as a markdown document ready to save as product_idea.md.`;

    // Use the conversation service with chat strategy
    const response = await unifiedConversationService.addUserMessageAndGetResponse(
      conversation.id,
      prompt,
      {
        taskType: 'chat',
        maxTokens: 3000,
        temperature: 0.7
      }
    );

    // Get or create project structure
    console.log('🔧 [BACKEND] Creating project structure for:', productName);
    const projectStructure = await getOrCreateProjectStructure(productName);
    console.log('🔧 [BACKEND] Project structure created:', projectStructure.projectPath);
    
    // Save the generated document to docs folder
    const fileName = generateTimestampedFilename('product_idea', 'md');
    console.log('🔧 [BACKEND] Saving document as:', fileName);
    const relativePath = await saveToProjectFolder(
      projectStructure,
      'docs',
      fileName,
      response.message?.content || ''
    );
    console.log('🔧 [BACKEND] Document saved to:', relativePath);

    logger.info('Product idea document generated:', { relativePath, conversationId: conversation.id });

    const responseData = {
      success: true,
      document: response.message?.content,
      filePath: relativePath,
      projectPath: projectStructure.projectPath,
      conversationId: conversation.id,
      message: 'Product idea document generated successfully'
    };
    
    console.log('🔧 [BACKEND] Sending response:', JSON.stringify(responseData, null, 2));
    res.json(responseData);

  } catch (error) {
    logger.error('Error generating product idea:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to generate product idea document',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Generate Slide Deck
 * POST /api/tools/generate-slide-deck
 */
router.post('/generate-slide-deck', async (req: Request, res: Response) => {
  try {
    const { productName, slides, theme = 'modern' } = req.body;

    console.log('🔧 [BACKEND] Slide deck generation tool called');
    console.log('🔧 [BACKEND] Request body:', JSON.stringify(req.body, null, 2));
    logger.info('Slide deck generation requested:', { productName, slideCount: slides?.length });

    // Create a conversation for slide generation
    const conversation = await unifiedConversationService.createConversation(DEMO_USER_ID, {
      title: `Slide Deck: ${productName}`,
      category: 'planning',
      keyObjective: 'Generate pitch presentation'
    });

    // Build prompt for slide deck generation
    const slideContent = slides?.map((slide: any, index: number) => 
      `Slide ${index + 1}: ${slide.title}\n${slide.content}`
    ).join('\n\n') || '';

    const prompt = `Generate an HTML slide deck presentation for "${productName}" with a ${theme} theme.

${slideContent ? `Slide Content:\n${slideContent}` : 'Create a professional product pitch presentation with appropriate slides.'}

Please create a complete HTML file with:
1. Modern responsive design
2. Professional styling with ${theme} theme
3. Navigation between slides
4. Clean typography
5. Proper slide transitions
6. Mobile-friendly layout

Return the complete HTML code ready to save as index.html.`;

    // Use the slides task strategy
    const response = await unifiedConversationService.addUserMessageAndGetResponse(
      conversation.id,
      prompt,
      {
        taskType: 'slides',
        maxTokens: 4000,
        temperature: 0.7,
        theme,
        productName
      }
    );

    // Get or create project structure
    console.log('🔧 [BACKEND] Creating project structure for slides:', productName);
    const projectStructure = await getOrCreateProjectStructure(productName);
    console.log('🔧 [BACKEND] Project structure created for slides:', projectStructure.projectPath);
    
    // Save the generated HTML to slides folder
    const fileName = generateTimestampedFilename('slide_deck', 'html');
    console.log('🔧 [BACKEND] Saving slides as:', fileName);
    const relativePath = await saveToProjectFolder(
      projectStructure,
      'slides',
      fileName,
      response.message?.content || ''
    );
    console.log('🔧 [BACKEND] Slides saved to:', relativePath);

    logger.info('Slide deck generated:', { relativePath, conversationId: conversation.id });

    res.json({
      success: true,
      html: response.message?.content,
      filePath: relativePath,
      projectPath: projectStructure.projectPath,
      conversationId: conversation.id,
      message: 'Slide deck generated successfully'
    });

  } catch (error) {
    logger.error('Error generating slide deck:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to generate slide deck',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Generate Lo-Fi Mockup
 * POST /api/tools/generate-lofi-mockup
 */
router.post('/generate-lofi-mockup', async (req: Request, res: Response) => {
  try {
    const { productType, keyFeatures, layoutStyle = 'simple', targetPlatform = 'web' } = req.body;

    logger.info('Lo-fi mockup generation requested:', { productType, targetPlatform });

    // Create a conversation for mockup generation
    const conversation = await unifiedConversationService.createConversation(DEMO_USER_ID, {
      title: `Mockup: ${productType}`,
      category: 'other',
      keyObjective: 'Generate UI mockup'
    });

    // Build prompt for lo-fi mockup generation
    const prompt = `Generate a lo-fi SVG mockup for a ${productType} application.

Requirements:
- Product Type: ${productType}
- Key Features: ${keyFeatures?.join(', ') || 'Basic functionality'}
- Layout Style: ${layoutStyle}
- Target Platform: ${targetPlatform}

Create a clean, simple SVG mockup that shows:
1. Main layout structure
2. Key feature areas
3. Navigation elements
4. Content sections
5. Call-to-action buttons

Use a wireframe style with gray boxes, simple typography, and clear hierarchy.
Respond with valid SVG code only, optimized for ${targetPlatform} display.`;

    // Use the SVG mockup task strategy
    const response = await unifiedConversationService.addUserMessageAndGetResponse(
      conversation.id,
      prompt,
      {
        taskType: 'svg_mockup',
        maxTokens: 3000,
        temperature: 0.6,
        style: layoutStyle,
        components: keyFeatures,
        width: targetPlatform === 'mobile' ? 375 : 800,
        height: targetPlatform === 'mobile' ? 667 : 600
      }
    );

    // Get or create project structure
    const projectStructure = await getOrCreateProjectStructure(productType);
    
    // Save the generated SVG to mockups folder
    const fileName = generateTimestampedFilename('mockup', 'svg');
    const relativePath = await saveToProjectFolder(
      projectStructure,
      'mockups',
      fileName,
      response.message?.content || ''
    );

    logger.info('Lo-fi mockup generated:', { relativePath, conversationId: conversation.id });

    res.json({
      success: true,
      svg: response.message?.content,
      filePath: relativePath,
      projectPath: projectStructure.projectPath,
      conversationId: conversation.id,
      message: 'Lo-fi mockup generated successfully'
    });

  } catch (error) {
    logger.error('Error generating lo-fi mockup:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to generate lo-fi mockup',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Create Project Scaffold
 * POST /api/tools/create-project-scaffold
 */
router.post('/create-project-scaffold', async (req: Request, res: Response) => {
  try {
    const { projectName, tech, features, projectType } = req.body;

    logger.info('Project scaffold generation requested:', { projectName, projectType });

    // Create a conversation for code generation
    const conversation = await unifiedConversationService.createConversation(DEMO_USER_ID, {
      title: `Scaffold: ${projectName}`,
      category: 'other',
      keyObjective: 'Generate project structure'
    });

    // Build prompt for project scaffolding
    const techStack = tech || { frontend: 'React', backend: 'Node.js', database: 'PostgreSQL' };
    const featureList = features?.join(', ') || 'Basic CRUD operations';

    const prompt = `Generate a complete project scaffold for "${projectName}", a ${projectType} application.

Technology Stack:
- Frontend: ${techStack.frontend}
- Backend: ${techStack.backend}
- Database: ${techStack.database}

Features to include:
${featureList}

Please generate:
1. Project structure with folders and files
2. Package.json files with dependencies
3. Basic configuration files
4. Sample component files (Home.jsx, App.jsx, etc.)
5. API route files (api.js, routes/, etc.)
6. Database schema files (schema.sql, migrations/)
7. README.md with setup instructions

Provide the complete file structure and key file contents ready for a working application.`;

    // Use the code generation task strategy
    const response = await unifiedConversationService.addUserMessageAndGetResponse(
      conversation.id,
      prompt,
      {
        taskType: 'code_generation',
        maxTokens: 4000,
        temperature: 0.5,
        projectType,
        techStack,
        features
      }
    );

    // Get or create project structure
    const projectStructure = await getOrCreateProjectStructure(projectName);
    
    // Save the generated scaffold content to code folder
    const fileName = generateTimestampedFilename('project_scaffold', 'md');
    const relativePath = await saveToProjectFolder(
      projectStructure,
      'code',
      fileName,
      response.message?.content || ''
    );
    
    logger.info('Project scaffold generated:', { projectPath: projectStructure.projectPath, conversationId: conversation.id });

    res.json({
      success: true,
      files: response.message?.content,
      filePath: relativePath,
      projectPath: projectStructure.projectPath,
      conversationId: conversation.id,
      message: 'Project scaffold created successfully'
    });

  } catch (error) {
    logger.error('Error creating project scaffold:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create project scaffold',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Demo 1 Complete Workflow Orchestrator
 * POST /api/tools/demo1-workflow
 */
router.post('/demo1-workflow', async (req: Request, res: Response) => {
  try {
    const { productIdea, details, workflowStep = 'start' } = req.body;

    console.log('🔧 [BACKEND] Demo 1 workflow orchestration tool called');
    console.log('🔧 [BACKEND] Request body:', JSON.stringify(req.body, null, 2));
    logger.info('Demo 1 workflow orchestration requested:', { productIdea, workflowStep });

    // Extract product details
    const productName = extractProductName(productIdea);
    const targetAudience = details?.targetAudience || 'General users';
    const keyFeatures = details?.keyFeatures || [];
    const projectType = details?.projectType || 'web application';
    
    console.log('🔧 [BACKEND] Extracted product details:', {
      productName,
      targetAudience,
      keyFeatures,
      projectType,
      workflowStep
    });

    const assets: any = {};
    let nextStep = workflowStep;

    // Step 1: Generate Product Idea Document
    if (workflowStep === 'start') {
      try {
        const productIdeaResponse = await axios.post(`${req.protocol}://${req.get('host')}/api/tools/generate-product-idea`, {
          productName,
          description: productIdea,
          targetAudience,
          keyFeatures,
          useCase: `${projectType} for ${targetAudience}`
        });
        
        assets.productDocument = {
          content: productIdeaResponse.data.document,
          filePath: productIdeaResponse.data.filePath
        };
        nextStep = 'idea_ready';
        
        logger.info('Demo 1: Product idea document generated');
      } catch (error) {
        logger.error('Demo 1: Product idea generation failed:', error);
      }
    }

    // Step 2: Generate Slide Deck
    if (workflowStep === 'idea_ready' || (workflowStep === 'start' && assets.productDocument)) {
      try {
        const slides = [
          { title: `${productName}`, content: `${productIdea}` },
          { title: 'Problem Statement', content: `Addressing the needs of ${targetAudience}` },
          { title: 'Key Features', content: keyFeatures.join(', ') || 'Core functionality to be defined' },
          { title: 'Target Market', content: targetAudience },
          { title: 'Next Steps', content: 'Development roadmap and implementation plan' }
        ];

        const slideDeckResponse = await axios.post(`${req.protocol}://${req.get('host')}/api/tools/generate-slide-deck`, {
          productName,
          slides,
          theme: 'modern'
        });
        
        assets.slideDecK = {
          html: slideDeckResponse.data.html,
          filePath: slideDeckResponse.data.filePath
        };
        nextStep = 'slides_ready';
        
        logger.info('Demo 1: Slide deck generated');
      } catch (error) {
        logger.error('Demo 1: Slide deck generation failed:', error);
      }
    }

    // Step 3: Generate Lo-Fi Mockup
    if (workflowStep === 'slides_ready' || (nextStep === 'slides_ready' && assets.slideDecK)) {
      try {
        const mockupResponse = await axios.post(`${req.protocol}://${req.get('host')}/api/tools/generate-lofi-mockup`, {
          productType: projectType,
          keyFeatures,
          layoutStyle: 'simple',
          targetPlatform: 'web'
        });
        
        assets.mockup = {
          svg: mockupResponse.data.svg,
          filePath: mockupResponse.data.filePath
        };
        nextStep = 'mockup_ready';
        
        logger.info('Demo 1: Lo-fi mockup generated');
      } catch (error) {
        logger.error('Demo 1: Mockup generation failed:', error);
      }
    }

    // Step 4: Generate Project Scaffold (Optional for Demo 1)
    if (workflowStep === 'mockup_ready' || (nextStep === 'mockup_ready' && assets.mockup)) {
      try {
        const scaffoldResponse = await axios.post(`${req.protocol}://${req.get('host')}/api/tools/create-project-scaffold`, {
          projectName: productName.replace(/\s+/g, '-').toLowerCase(),
          tech: {
            frontend: 'React',
            backend: 'Node.js',
            database: 'PostgreSQL'
          },
          features: keyFeatures,
          projectType
        });
        
        assets.projectScaffold = {
          files: scaffoldResponse.data.files,
          projectPath: scaffoldResponse.data.projectPath
        };
        nextStep = 'complete';
        
        logger.info('Demo 1: Project scaffold generated');
      } catch (error) {
        logger.error('Demo 1: Project scaffold generation failed:', error);
      }
    }

    // Calculate completion status
    const totalSteps = 4;
    const completedSteps = Object.keys(assets).length;
    const progress = (completedSteps / totalSteps) * 100;

    console.log('🔧 [BACKEND] Demo 1 workflow completed:', {
      completedSteps,
      totalSteps,
      progress: Math.round(progress),
      assetKeys: Object.keys(assets),
      nextStep
    });

    const responseData = {
      success: true,
      workflowStep: nextStep,
      progress: Math.round(progress),
      assets,
      summary: {
        productName,
        targetAudience,
        keyFeatures,
        projectType,
        completedSteps,
        totalSteps
      },
      nextStep: nextStep === 'complete' ? null : getNextStepDescription(nextStep),
      message: `Demo 1 workflow progress: ${Math.round(progress)}% complete`
    };
    
    console.log('🔧 [BACKEND] Sending Demo 1 workflow response:', JSON.stringify(responseData, null, 2));
    res.json(responseData);

  } catch (error) {
    logger.error('Error in Demo 1 workflow orchestration:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to orchestrate Demo 1 workflow',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Health check for tools API
 */
router.get('/health', (req: Request, res: Response) => {
  res.json({
    status: 'healthy',
    service: 'tools-api',
    timestamp: new Date().toISOString(),
    availableTools: [
      'generate-product-idea',
      'generate-slide-deck',
      'generate-lofi-mockup',
      'create-project-scaffold',
      'demo1-workflow'
    ]
  });
});

/**
 * Helper Functions
 */

function extractProductName(productIdea: string): string {
  // Simple extraction - in production this could be more sophisticated
  const words = productIdea.split(' ');
  const nameKeywords = ['tool', 'app', 'platform', 'system', 'service', 'solution'];
  
  for (const keyword of nameKeywords) {
    const index = words.findIndex(word => word.toLowerCase().includes(keyword));
    if (index > 0) {
      return words.slice(Math.max(0, index - 2), index + 1).join(' ');
    }
  }
  
  // Fallback: use first 3 words or "Product"
  return words.slice(0, 3).join(' ') || 'Product';
}

function getNextStepDescription(step: string): string {
  const descriptions = {
    'idea_ready': 'Generate presentation slides',
    'slides_ready': 'Create lo-fi mockup',
    'mockup_ready': 'Generate project scaffold',
    'complete': 'Workflow complete'
  };
  
  return descriptions[step] || 'Continue workflow';
}

export default router;