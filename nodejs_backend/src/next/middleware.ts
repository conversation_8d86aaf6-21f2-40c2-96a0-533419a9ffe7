import { clerkMiddleware, createRouteMatcher } from "@clerk/nextjs/server";

// Define public routes that don't require authentication
const isPublicRoute = createRouteMatcher([
  "/",
  "/blog(.*)",
  "/benefits",
  "/features",
  "/modernai",
  "/api(.*)",
  "/about",
  "/terms",
  "/cookies",
  "/privacy"
]);

// Define routes that should be ignored by Clerk
const isIgnoredRoute = createRouteMatcher([
  "/api/webhook(.*)"
]);

export default clerkMiddleware(async (auth, req) => {
  //console.log("NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY:", process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY);
  // If the route is not public and not ignored, protect it
  if (!isPublicRoute(req) && !isIgnoredRoute(req)) {
    await auth.protect();
  }
});

export const config = {
  matcher: ["/((?!.*\\..*|_next).*)", "/(api|trpc)(.*)"],
};
