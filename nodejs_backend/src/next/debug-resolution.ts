// Create a simple debug test to see what's available
import { readFileSync, existsSync } from 'fs';
import { join } from 'path';

console.log('=== Path Resolution Debug ===');
console.log('Current working directory:', process.cwd());
console.log('__dirname:', __dirname);

const libPath = join(process.cwd(), 'lib');
const blogPath = join(libPath, 'blog.ts');
const markdownPath = join(libPath, 'markdownToHtml.ts');

console.log('lib directory exists:', existsSync(libPath));
console.log('blog.ts exists:', existsSync(blogPath));
console.log('markdownToHtml.ts exists:', existsSync(markdownPath));

if (existsSync(libPath)) {
  const fs = require('fs');
  console.log('Files in lib:', fs.readdirSync(libPath));
}

export {};