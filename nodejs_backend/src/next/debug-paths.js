const fs = require('fs');
const path = require('path');

console.log('=== Debug Path Resolution ===');
console.log('Current working directory:', process.cwd());

// Check if lib directory exists
const libPath = path.join(process.cwd(), 'lib');
console.log('Lib directory exists:', fs.existsSync(libPath));

if (fs.existsSync(libPath)) {
  console.log('Files in lib directory:');
  fs.readdirSync(libPath).forEach(file => {
    console.log(`  - ${file}`);
  });
}

// Check specific files
const blogPath = path.join(process.cwd(), 'lib', 'blog.ts');
const markdownPath = path.join(process.cwd(), 'lib', 'markdownToHtml.ts');

console.log('blog.ts exists:', fs.existsSync(blogPath));
console.log('markdownToHtml.ts exists:', fs.existsSync(markdownPath));

// Check if any case variations exist
const variations = [
  'lib/Blog.ts',
  'lib/BLOG.ts',
  'lib/blog.js',
  'Lib/blog.ts',
  'LIB/blog.ts'
];

variations.forEach(variation => {
  const fullPath = path.join(process.cwd(), variation);
  if (fs.existsSync(fullPath)) {
    console.log(`Found case variation: ${variation}`);
  }
});
