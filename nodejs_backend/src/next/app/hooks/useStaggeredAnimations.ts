'use client';

import { useState, useEffect, useCallback } from 'react';

interface UseStaggeredAnimationsProps {
  totalAnimations: number;
  staggerDelay?: number; // Delay between animations in ms
  isMobile?: boolean;
  enabled?: boolean;
}

interface AnimationState {
  isVisible: boolean;
  canLoad: boolean;
  isLoaded: boolean;
}

export function useStaggeredAnimations({
  totalAnimations,
  staggerDelay = 300,
  isMobile = false,
  enabled = true
}: UseStaggeredAnimationsProps) {
  const [animations, setAnimations] = useState<AnimationState[]>(() =>
    Array(totalAnimations).fill(null).map(() => ({
      isVisible: false,
      canLoad: false,
      isLoaded: false
    }))
  );

  const [currentIndex, setCurrentIndex] = useState(0);

  // Mark animation as visible (triggered by intersection observer)
  const setAnimationVisible = useCallback((index: number) => {
    setAnimations(prev => {
      const newState = [...prev];
      newState[index] = { ...newState[index], isVisible: true };
      return newState;
    });
  }, []);

  // Mark animation as loaded
  const setAnimationLoaded = useCallback((index: number) => {
    setAnimations(prev => {
      const newState = [...prev];
      newState[index] = { ...newState[index], isLoaded: true };
      return newState;
    });
  }, []);

  // Staggered loading logic
  useEffect(() => {
    if (!enabled) return;

    const interval = setInterval(() => {
      setAnimations(prev => {
        const newState = [...prev];
        
        // Find next visible animation that can load
        for (let i = currentIndex; i < totalAnimations; i++) {
          if (newState[i].isVisible && !newState[i].canLoad) {
            newState[i] = { ...newState[i], canLoad: true };
            setCurrentIndex(i + 1);
            break;
          }
        }
        
        return newState;
      });
    }, isMobile ? staggerDelay * 2 : staggerDelay); // Slower on mobile

    return () => clearInterval(interval);
  }, [enabled, currentIndex, totalAnimations, staggerDelay, isMobile]);

  // Immediate load for priority animations
  const setPriorityAnimation = useCallback((index: number) => {
    setAnimations(prev => {
      const newState = [...prev];
      newState[index] = { 
        ...newState[index], 
        isVisible: true, 
        canLoad: true 
      };
      return newState;
    });
  }, []);

  return {
    animations,
    setAnimationVisible,
    setAnimationLoaded,
    setPriorityAnimation,
    // Utility methods
    canLoad: (index: number) => animations[index]?.canLoad || false,
    isVisible: (index: number) => animations[index]?.isVisible || false,
    isLoaded: (index: number) => animations[index]?.isLoaded || false,
  };
}