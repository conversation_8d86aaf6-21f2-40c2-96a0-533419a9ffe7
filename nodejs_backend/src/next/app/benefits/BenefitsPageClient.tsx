'use client';

import React from "react";
import Link from "next/link";
import styles from '../styles/benefits.module.css';
import whiteStyles from '../styles/white-section.module.css';

export default function BenefitsPageClient() {
  const openWaitlistModal = () => {
    window.dispatchEvent(new Event('openWaitlistModal'));
  };

  return (
    <main className="flex flex-col min-h-screen">
      {/* The Struggle - Dark Section */}
      <section className={styles.darkSection}>
        <div className={styles.container}>
          <div className={styles.content}>
            <div className={styles.textContent}>
              <div className={styles.heroBadge}>The Struggle</div>
              <h2 className={styles.sectionTitle}>Remember This Feeling?</h2>
              <p className={styles.sectionDescription}>
                It&apos;s 3 AM. You&apos;re debugging code you wrote last month. Cursor broke what little was working. The documentation is outdated, the tests are missing, and you&apos;re copy-pasting from ChatGPT hoping something works.
              </p>
              <div className={styles.painPoints}>
                <div className={styles.painItem}>
                  <div className={styles.painIcon}>😔</div>
                  <p>&quot;I spend more time on boilerplate than solving problems&quot;</p>
                </div>
                <div className={styles.painItem}>
                  <div className={styles.painIcon}>🤯</div>
                  <p>&quot;My AI costs are exploding with every API call&quot;</p>
                </div>
                <div className={styles.painItem}>
                  <div className={styles.painIcon}>😫</div>
                  <p>&quot;I can&apos;t keep docs, tests, and code in sync&quot;</p>
                </div>
                <div className={styles.painItem}>
                  <div className={styles.painIcon}>😩</div>
                  <p>&quot;I&apos;m stuck on problems others have already solved&quot;</p>
                </div>
              </div>
            </div>
            <div className={styles.animationContainer}>
              <iframe 
                src="https://lottie.host/embed/806df942-4377-4d06-912e-bb0d003a171f/6uxFxZQPRI.lottie"
                width="600"
                height="600"
                style={{ display: 'block', margin: '0 auto', border: 'none', background: 'transparent' }}
                title="Developer Struggle Animation"
                allowFullScreen
              />
            </div>
          </div>
        </div>
      </section>

      {/* The Discovery - White Section */}
      <section className={whiteStyles.section}>
        <div className={whiteStyles.container}>
          <div className={styles.content}>
            <div className={styles.animationContainer}>
              <iframe 
                src="https://lottie.host/embed/9b03ac4a-9cb7-433a-95ad-b134337b96c4/YCOTGjCZEX.lottie"
                width="600"
                height="600"
                style={{ display: 'block', margin: '0 auto', border: 'none', background: 'transparent' }}
                title="Discovery Animation"
                allowFullScreen
              />
            </div>
            <div className={styles.textContent}>
              <div className={styles.heroBadgeWhite}>The Discovery</div>
              <h2 className={whiteStyles.title}>What If Development Felt Natural?</h2>
              <p className={whiteStyles.description}>
                Imagine describing your idea out loud and watching it transform into working code. Picture your rough sketches becoming polished interfaces. Envision documentation that writes itself and stays perfectly synchronized.
              </p>
              <div className={styles.discoveryGrid}>
                <div className={styles.discoveryItem}>
                  <h3 className={styles.discoveryTitle}>Your Voice Becomes Code</h3>
                  <p className={styles.discoveryDesc}>Simply say &quot;Build me a user dashboard with real-time notifications&quot; and watch it happen</p>
                </div>
                <div className={styles.discoveryItem}>
                  <h3 className={styles.discoveryTitle}>Your Sketches Come Alive</h3>
                  <p className={styles.discoveryDesc}>Draw on paper, snap a photo, and get a fully functional component</p>
                </div>
                <div className={styles.discoveryItem}>
                  <h3 className={styles.discoveryTitle}>Your Ideas Stay Organized</h3>
                  <p className={styles.discoveryDesc}>Everything from vision to deployment stays perfectly aligned</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* The Transformation - Dark Section */}
      <section className={styles.darkSection}>
        <div className={styles.container}>
          <div className={styles.content}>
            <div className={styles.textContent}>
              <div className={styles.heroBadge}>The Transformation</div>
              <h2 className={styles.sectionTitle}>Your New Development Reality</h2>
              <p className={styles.sectionDescription}>
                With KAPI, you&apos;re not just coding faster—you&apos;re engineering smarter. Every feature is designed to amplify your expertise, not replace it.
              </p>
              <div className={styles.transformationGrid}>
                <div className={styles.transformCard}>
                  <div className={styles.transformBefore}>
                    <h4>Before KAPI</h4>
                    <p>6 hours debugging a payment integration</p>
                  </div>
                  <div className={styles.transformArrow}>→</div>
                  <div className={styles.transformAfter}>
                    <h4>With KAPI</h4>
                    <p>30 minutes with pre-built, tested templates</p>
                  </div>
                </div>
                <div className={styles.transformCard}>
                  <div className={styles.transformBefore}>
                    <h4>Before KAPI</h4>
                    <p>$200/month in AI API costs</p>
                  </div>
                  <div className={styles.transformArrow}>→</div>
                  <div className={styles.transformAfter}>
                    <h4>With KAPI</h4>
                    <p>$20/month with 90% cost reduction</p>
                  </div>
                </div>
                <div className={styles.transformCard}>
                  <div className={styles.transformBefore}>
                    <h4>Before KAPI</h4>
                    <p>Documentation always out of date</p>
                  </div>
                  <div className={styles.transformArrow}>→</div>
                  <div className={styles.transformAfter}>
                    <h4>With KAPI</h4>
                    <p>Auto-synced docs with every code change</p>
                  </div>
                </div>
              </div>
            </div>
            <div className={styles.animationContainer}>
              <iframe 
                src="https://lottie.host/embed/0515d42b-b195-4104-aebf-5079b36c8f31/XyKfD5gX8a.lottie"
                width="600"
                height="600"
                style={{ display: 'block', margin: '0 auto', border: 'none', background: 'transparent' }}
                title="Transformation Animation"
                allowFullScreen
              />
            </div>
          </div>
        </div>
      </section>

      {/* The Community - White Section */}
      <section className={whiteStyles.section}>
        <div className={whiteStyles.container}>
          <div className={styles.content}>
            <div className={styles.textContent}>
              <div className={styles.heroBadgeWhite}>The Community</div>
              <h2 className={whiteStyles.title}>You&apos;re Never Stuck Again</h2>
              <p className={whiteStyles.description}>
                Join a community where every question gets answered, every challenge becomes a learning opportunity, and every success is celebrated together.
              </p>
              <div className={styles.communityStats}>
                <div className={styles.statCard}>
                  <h3 className={styles.statNumber}>15 min</h3>
                  <p className={styles.statLabel}>Average response time</p>
                </div>
                <div className={styles.statCard}>
                  <h3 className={styles.statNumber}>10,000+</h3>
                  <p className={styles.statLabel}>Active developers</p>
                </div>
                <div className={styles.statCard}>
                  <h3 className={styles.statNumber}>24/7</h3>
                  <p className={styles.statLabel}>Global collaboration</p>
                </div>
                <div className={styles.statCard}>
                  <h3 className={styles.statNumber}>95%</h3>
                  <p className={styles.statLabel}>Problem resolution rate</p>
                </div>
              </div>
              <div className={styles.testimonial}>
                <p className={styles.testimonialQuote}>
                  &quot;I asked about implementing WebSockets at 2 PM. By 2:15, I had three developers helping me, and by 3 PM, it was working in production.&quot;
                </p>
                <p className={styles.testimonialAuthor}>— Sarah Chen, Full-Stack Developer</p>
              </div>
            </div>
            <div className={styles.animationContainer}>
              <iframe 
                src="https://lottie.host/embed/e4431338-015c-4d4f-bfeb-28529ad4aba8/J2IO07R18o.lottie"
                width="600"
                height="600"
                style={{ display: 'block', margin: '0 auto', border: 'none', background: 'transparent' }}
                title="Community Animation"
                allowFullScreen
              />
            </div>
          </div>
        </div>
      </section>

      {/* The Results - Dark Section */}
      <section className={styles.darkSection}>
        <div className={styles.container}>
          <div className={styles.content}>
            <div className={styles.textContent}>
              <div className={styles.heroBadge}>The Results</div>
              <h2 className={styles.sectionTitle}>Real Developers, Real Impact</h2>
              <p className={styles.sectionDescription}>
                These aren&apos;t just numbers—they&apos;re stories of developers who transformed their careers and their code.
              </p>
              <div className={styles.resultsGrid}>
                <div className={styles.resultStory}>
                  <div className={styles.resultIcon}>🚀</div>
                  <h3>Startup Founder</h3>
                  <p>&quot;Built my MVP in 2 weeks instead of 2 months. Now have 50K users.&quot;</p>
                  <span className={styles.resultMetric}>10x faster launch</span>
                </div>
                <div className={styles.resultStory}>
                  <div className={styles.resultIcon}>💼</div>
                  <h3>Enterprise Developer</h3>
                  <p>&quot;Reduced our AI costs by 75% while improving code quality.&quot;</p>
                  <span className={styles.resultMetric}>$30K annual savings</span>
                </div>
                <div className={styles.resultStory}>
                  <div className={styles.resultIcon}>🎓</div>
                  <h3>Career Switcher</h3>
                  <p>&quot;From bootcamp to senior developer in 18 months with KAPI.&quot;</p>
                  <span className={styles.resultMetric}>3x salary increase</span>
                </div>
                <div className={styles.resultStory}>
                  <div className={styles.resultIcon}>🏆</div>
                  <h3>Open Source Maintainer</h3>
                  <p>&quot;My project went from 10 to 1000 contributors. KAPI made it accessible.&quot;</p>
                  <span className={styles.resultMetric}>100x growth</span>
                </div>
              </div>
            </div>
            <div className={styles.animationContainer}>
              <iframe 
                src="https://lottie.host/embed/54d45745-4002-4e96-b59a-0f81bd1e7a4b/he0iFDZbNQ.lottie"
                width="600"
                height="600"
                style={{ display: 'block', margin: '0 auto', border: 'none', background: 'transparent' }}
                title="Results Animation"
                allowFullScreen
              />
            </div>
          </div>
        </div>
      </section>

      {/* The Choice - White Section */}
      <section className={whiteStyles.section}>
        <div className={whiteStyles.container}>
          <div className={styles.choiceContent}>
            <div className={styles.heroBadgeWhite}>The Choice</div>
            <h2 className={whiteStyles.title}>Two Paths Forward</h2>
            <div className={styles.pathComparison}>
              <div className={styles.pathCard}>
                <h3 className={styles.pathTitle}>Continue the Old Way</h3>
                <ul className={styles.pathList}>
                  <li>Keep wrestling with boilerplate</li>
                  <li>Watch AI costs eat your budget</li>
                  <li>Struggle to maintain documentation</li>
                  <li>Debug alone at 3 AM</li>
                  <li>Hope for the best in production</li>
                </ul>
              </div>
              <div className={styles.pathDivider}>
                <span>OR</span>
              </div>
              <div className={styles.pathCard}>
                <h3 className={styles.pathTitle}>Start Your KAPI Journey</h3>
                <ul className={styles.pathList}>
                  <li>Focus on solving real problems</li>
                  <li>Cut development costs by 80%</li>
                  <li>Ship with confidence every time</li>
                  <li>Collaborate with brilliant minds</li>
                  <li>Build the future of software</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className={styles.ctaSection}>
        <div className={styles.ctaContent}>
          <h2 className={styles.ctaTitle}>Your Story Starts Today</h2>
          <p className={styles.ctaSubtitle}>
            Join thousands of developers who chose to stop struggling and start building
          </p>
          <div className={styles.ctaButtons}>
            <button onClick={openWaitlistModal} className={`${styles.ctaButton} ${styles.ctaPrimary}`}>
              Join Waitlist
              <svg className={styles.ctaIcon} width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M5 12H19M19 12L12 5M19 12L12 19" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </button>
            <Link href="/features" className={`${styles.ctaButton} ${styles.ctaSecondary}`}>
              See How It Works
            </Link>
          </div>
          <p className={styles.ctaNote}>
            Free to start • No credit card required • 5-minute setup
          </p>
        </div>
      </section>
    </main>
  );
}