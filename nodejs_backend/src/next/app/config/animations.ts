// Animation performance configuration
export const ANIMATION_CONFIG = {
  // Global settings
  MOBILE_BREAKPOINT: 768,
  STAGGER_DELAY: 300, // ms between animation loads
  MOBILE_STAGGER_DELAY: 600, // Slower on mobile
  INTERSECTION_THRESHOLD: 0.1,

  // Performance thresholds
  MAX_CONCURRENT_ANIMATIONS: 2, // Limit concurrent animations on mobile
  PRIORITY_ANIMATIONS: ['hero', 'main-cta'], // Load immediately
  
  // Mobile optimizations
  MOBILE_OPTIMIZATIONS: {
    SHOW_STATIC_IMAGES: true,
    REDUCE_ANIMATION_QUALITY: true,
    DISABLE_SCROLL_ANIMATIONS: true,
    LIMIT_FRAME_RATE: true
  },

  // Fallback settings
  FALLBACKS: {
    ENABLE_STATIC_FALLBACKS: true,
    SHOW_LOADING_PLACEHOLDERS: true,
    GRACEFUL_DEGRADATION: true
  }
};

// Animation definitions for different pages
export const PAGE_ANIMATIONS = {
  HOME: {
    hero: {
      src: 'https://lottie.host/embed/4711d258-b3c9-4795-ad87-7c1f4a709a97/VK2hu4bRQw.lottie',
      mobileStatic: '/animations/hero-static.png',
      fallback: '/voice-icon.svg',
      priority: true,
      width: 600,
      height: 600
    },
    features: [
      {
        src: 'https://lottie.host/embed/another-animation/feature1.lottie',
        mobileStatic: '/animations/feature1-static.png',
        fallback: '/feature1-icon.svg',
        width: 400,
        height: 400
      },
      // Add more feature animations
    ]
  },
  
  BENEFITS: {
    // Define animations for benefits page
  },
  
  FEATURES: {
    // Define animations for features page
  }
};

// Utility function to check if device should use optimizations
export function shouldOptimizeForPerformance(): boolean {
  if (typeof window === 'undefined') return true; // SSR safe default
  
  const isMobile = window.innerWidth < ANIMATION_CONFIG.MOBILE_BREAKPOINT;
  const isSlowConnection = 'connection' in navigator && 
    (navigator as any).connection?.effectiveType === '2g';
  const hasLimitedMemory = 'deviceMemory' in navigator && 
    (navigator as any).deviceMemory < 4;
  
  return isMobile || isSlowConnection || hasLimitedMemory;
}

// Check if user prefers reduced motion
export function prefersReducedMotion(): boolean {
  if (typeof window === 'undefined') return false;
  
  return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
}