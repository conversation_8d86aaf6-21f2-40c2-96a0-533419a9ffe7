/**
 * Authentication utilities for the frontend
 */

/**
 * Get the <PERSON> authentication token
 * @returns Promise<string | null> - A promise that resolves to the token or null if not authenticated
 */
export async function getAuthToken(): Promise<string | null> {
  try {
    const response = await fetch('/api/auth/token');

    if (!response.ok) {
      // Don't log errors for 401 Unauthorized as that's expected when not signed in
      if (response.status !== 401) {
        console.error('Failed to get auth token:', response.status, response.statusText);
      }
      return null;
    }

    const data = await response.json();
    return data.token || null;
  } catch (error) {
    // Only log errors in development to avoid console spam in production
    if (process.env.NODE_ENV === 'development') {
      console.error('Error getting auth token:', error);
    }
    return null;
  }
}

/**
 * Add authentication headers to a fetch request
 * @param headers - The headers object to add authentication to
 * @returns Promise<Headers> - A promise that resolves to the headers with authentication added
 */
export async function addAuthHeaders(headers: Headers = new Headers()): Promise<Headers> {
  const token = await getAuthToken();

  if (token) {
    headers.set('Authorization', `Bear<PERSON> ${token}`);
  }

  return headers;
}

/**
 * Create authenticated fetch options
 * @param options - The fetch options to add authentication to
 * @returns Promise<RequestInit> - A promise that resolves to the options with authentication added
 */
export async function createAuthFetchOptions(options: RequestInit = {}): Promise<RequestInit> {
  const headers = options.headers instanceof Headers
    ? options.headers
    : new Headers(options.headers as Record<string, string>);

  const authHeaders = await addAuthHeaders(headers);

  return {
    ...options,
    headers: authHeaders,
  };
}

/**
 * Authenticated fetch function
 * @param url - The URL to fetch
 * @param options - The fetch options
 * @returns Promise<Response> - A promise that resolves to the fetch response
 */
export async function authFetch(url: string, options: RequestInit = {}): Promise<Response> {
  const authOptions = await createAuthFetchOptions(options);
  return fetch(url, authOptions);
}