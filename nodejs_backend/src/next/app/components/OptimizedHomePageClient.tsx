'use client';

import { useState, useEffect } from 'react';
import FAQ from './landing/FAQ';
import CTA from './landing/CTA';
import TheNeed from './landing/TheNeed';

// Optimized components
import OptimizedAnimatedHero from './landing/OptimizedAnimatedHero';
import StaggeredAnimationSection from './StaggeredAnimationSection';
import { shouldOptimizeForPerformance, ANIMATION_CONFIG } from '../config/animations';

// Regular components (will be wrapped in optimization logic)
import RobotPairProgramming from './landing/RobotPairProgramming';
import FromThoughtToCode from './landing/FromThoughtToCode';
import DrawItWatchItCode from './landing/DrawItWatchItCode';
import SayItCodeIt from './landing/SayItCodeIt';
import FocusOnEngineering from './landing/FocusOnEngineering';
import DebugTheVibes from './landing/DebugTheVibes';
import SocialAI from './landing/SocialAI';
import FunFirePower from './landing/FunFirePower';
import EverythingInSync from './landing/EverythingInSync';

export default function OptimizedHomePageClient() {
  const [shouldOptimize, setShouldOptimize] = useState(true);
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    setShouldOptimize(shouldOptimizeForPerformance());
    setIsMobile(window.innerWidth < ANIMATION_CONFIG.MOBILE_BREAKPOINT);

    const handleResize = () => {
      setIsMobile(window.innerWidth < ANIMATION_CONFIG.MOBILE_BREAKPOINT);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const openWaitlistModal = () => {
    window.dispatchEvent(new Event('openWaitlistModal'));
  };

  // Animation sections configuration
  const animationSections = [
    {
      component: <RobotPairProgramming />,
      animation: {
        src: 'https://lottie.host/embed/robot-pair-programming-animation.lottie',
        mobileStatic: '/animations/robot-static.png',
        fallbackSrc: '/robot-icon.svg',
        width: 400,
        height: 400
      }
    },
    {
      component: <FromThoughtToCode />,
      animation: {
        src: 'https://lottie.host/embed/thought-to-code-animation.lottie',
        mobileStatic: '/animations/thought-static.png',
        fallbackSrc: '/thought-icon.svg',
        width: 400,
        height: 400
      }
    },
    {
      component: <DrawItWatchItCode />,
      animation: {
        src: 'https://lottie.host/embed/draw-watch-code-animation.lottie',
        mobileStatic: '/animations/draw-static.png',
        fallbackSrc: '/draw-icon.svg',
        width: 400,
        height: 400
      }
    },
    {
      component: <SayItCodeIt />,
      animation: {
        src: 'https://lottie.host/embed/say-code-animation.lottie',
        mobileStatic: '/animations/voice-static.png',
        fallbackSrc: '/voice-icon.svg',
        width: 400,
        height: 400
      }
    }
  ];

  return (
    <>
      <main className="flex flex-col min-h-screen">
        {/* Optimized Hero Section */}
        <OptimizedAnimatedHero onJoinWaitlist={openWaitlistModal} />

        {/* Non-animated section loads normally */}
        <TheNeed />

        {/* Performance-optimized animation sections */}
        {shouldOptimize && isMobile ? (
          // Mobile: Load sections individually with staggered animations
          <>
            {animationSections.map((section, index) => (
              <StaggeredAnimationSection
                key={index}
                animations={[section.animation]}
                staggerDelay={ANIMATION_CONFIG.MOBILE_STAGGER_DELAY}
                className="animation-section"
              >
                {section.component}
              </StaggeredAnimationSection>
            ))}
          </>
        ) : shouldOptimize ? (
          // Desktop optimization: Group animations with staggered loading
          <StaggeredAnimationSection
            animations={animationSections.map(s => s.animation)}
            staggerDelay={ANIMATION_CONFIG.STAGGER_DELAY}
            className="desktop-animations"
          >
            <RobotPairProgramming />
            <FromThoughtToCode />
            <DrawItWatchItCode />
            <SayItCodeIt />
          </StaggeredAnimationSection>
        ) : (
          // Fallback: Original components for optimal connections
          <>
            <RobotPairProgramming />
            <FromThoughtToCode />
            <DrawItWatchItCode />
            <SayItCodeIt />
          </>
        )}

        {/* Remaining sections with lighter animations */}
        <FocusOnEngineering />
        <DebugTheVibes />
        <SocialAI />
        <FunFirePower />
        <EverythingInSync />

        {/* FAQ Section */}
        <FAQ />

        {/* CTA Section */}
        <CTA onJoinWaitlist={openWaitlistModal} />
      </main>
    </>
  );
}