'use client';

import { useEffect, useRef, useState, useCallback } from 'react';
import Image from 'next/image';

interface LazyLottieProps {
  src: string;
  fallbackSrc?: string;
  mobileStatic?: string; // Static image for mobile
  width?: number;
  height?: number;
  className?: string;
  priority?: boolean; // Load immediately (for above-fold content)
  reducedMotion?: boolean; // Respect user's motion preferences
  threshold?: number; // Intersection observer threshold
}

export default function LazyLottie({
  src,
  fallbackSrc,
  mobileStatic,
  width = 400,
  height = 400,
  className = '',
  priority = false,
  reducedMotion = false,
  threshold = 0.1
}: LazyLottieProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const [isVisible, setIsVisible] = useState(priority);
  const [isLoaded, setIsLoaded] = useState(false);
  const [hasError, setHasError] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [prefersReducedMotion, setPrefersReducedMotion] = useState(false);

  // Detect mobile and motion preferences
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    const checkMotionPreference = () => {
      const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
      setPrefersReducedMotion(mediaQuery.matches);
    };

    checkMobile();
    checkMotionPreference();

    window.addEventListener('resize', checkMobile);
    
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    mediaQuery.addEventListener('change', checkMotionPreference);

    return () => {
      window.removeEventListener('resize', checkMobile);
      mediaQuery.removeEventListener('change', checkMotionPreference);
    };
  }, []);

  // Intersection Observer for lazy loading
  useEffect(() => {
    if (priority || isVisible) return;

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setIsVisible(true);
            observer.disconnect();
          }
        });
      },
      { threshold }
    );

    if (containerRef.current) {
      observer.observe(containerRef.current);
    }

    return () => observer.disconnect();
  }, [priority, isVisible, threshold]);

  // Load animation when visible
  const loadAnimation = useCallback(async () => {
    if (!isVisible || isLoaded || hasError) return;

    try {
      // For mobile or reduced motion, show static image if available
      if ((isMobile || prefersReducedMotion || reducedMotion) && mobileStatic) {
        setIsLoaded(true);
        return;
      }

      // Load Lottie player dynamically
      await import('@lottiefiles/lottie-player');
      
      if (containerRef.current) {
        const player = document.createElement('lottie-player');
        player.setAttribute('src', src);
        player.setAttribute('background', 'transparent');
        player.setAttribute('speed', '1');
        player.setAttribute('loop', 'true');
        player.setAttribute('autoplay', 'true');
        player.style.width = `${width}px`;
        player.style.height = `${height}px`;
        player.style.maxWidth = '100%';
        player.style.height = 'auto';

        // Clear container and add player
        containerRef.current.innerHTML = '';
        containerRef.current.appendChild(player);
        
        player.addEventListener('ready', () => setIsLoaded(true));
        player.addEventListener('error', () => setHasError(true));
      }
    } catch (error) {
      console.warn('Failed to load Lottie animation:', error);
      setHasError(true);
    }
  }, [isVisible, isLoaded, hasError, isMobile, prefersReducedMotion, reducedMotion, mobileStatic, src, width, height]);

  useEffect(() => {
    loadAnimation();
  }, [loadAnimation]);

  // Render static image for mobile or reduced motion
  if ((isMobile || prefersReducedMotion || reducedMotion) && mobileStatic) {
    return (
      <div ref={containerRef} className={className}>
        <Image
          src={mobileStatic}
          alt="Animation preview"
          width={width}
          height={height}
          className="w-full h-auto"
          priority={priority}
        />
      </div>
    );
  }

  // Render placeholder while loading
  if (!isVisible || (!isLoaded && !hasError)) {
    return (
      <div 
        ref={containerRef}
        className={`${className} bg-gray-100 rounded-lg flex items-center justify-center`}
        style={{ width, height: 'auto', aspectRatio: `${width}/${height}` }}
      >
        {!isVisible && (
          <div className="text-gray-400 text-sm">Loading animation...</div>
        )}
      </div>
    );
  }

  // Render fallback on error
  if (hasError && fallbackSrc) {
    return (
      <div ref={containerRef} className={className}>
        <Image
          src={fallbackSrc}
          alt="Animation fallback"
          width={width}
          height={height}
          className="w-full h-auto"
        />
      </div>
    );
  }

  // Animation container (will be populated by loadAnimation)
  return (
    <div 
      ref={containerRef}
      className={className}
      style={{ width, height: 'auto', aspectRatio: `${width}/${height}` }}
    />
  );
}