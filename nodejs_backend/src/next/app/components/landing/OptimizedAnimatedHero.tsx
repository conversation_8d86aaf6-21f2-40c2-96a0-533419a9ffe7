'use client';

import { useEffect, useRef, useState } from 'react';
import Link from 'next/link';
import Lazy<PERSON>ottie from '../LazyLottie';
import styles from '../../styles/landing.module.css';

interface OptimizedAnimatedHeroProps {
  onJoinWaitlist?: () => void;
}

export default function OptimizedAnimatedHero({ onJoinWaitlist }: OptimizedAnimatedHeroProps) {
  const animationRef = useRef<HTMLDivElement>(null);
  const [isMobile, setIsMobile] = useState(false);
  const [prefersReducedMotion, setPrefersReducedMotion] = useState(false);

  // Detect mobile and motion preferences
  useEffect(() => {
    const checkMobile = () => setIsMobile(window.innerWidth < 768);
    const checkMotionPreference = () => {
      const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
      setPrefersReducedMotion(mediaQuery.matches);
    };

    checkMobile();
    checkMotionPreference();

    window.addEventListener('resize', checkMobile);
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    mediaQuery.addEventListener('change', checkMotionPreference);

    return () => {
      window.removeEventListener('resize', checkMobile);
      mediaQuery.removeEventListener('change', checkMotionPreference);
    };
  }, []);

  useEffect(() => {
    // Skip scroll animation on mobile or if user prefers reduced motion
    if (isMobile || prefersReducedMotion) return;

    const handleScroll = () => {
      if (!animationRef.current) return;

      const scrollY = window.scrollY;
      const translateY = Math.max(0, 150 - scrollY * 0.8);
      const scale = Math.min(1.1, 1 + Math.max(0, (150 - scrollY)) * 0.0003);
      const opacity = Math.min(1, 0.4 + scrollY * 0.005);

      animationRef.current.style.transform = `translateY(${translateY}px) scale(${scale})`;
      animationRef.current.style.opacity = opacity.toString();
    };

    // Initial position
    handleScroll();

    // Use passive listener for better performance
    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, [isMobile, prefersReducedMotion]);

  return (
    <section className={styles.hero}>
      <div className={styles.heroContainer}>
        <div className={styles.heroContent}>
          <div className={styles.heroText}>
            <div className={styles.heroTop}>
              <span className={styles.heroTagline}>
                ✨ Build serious games, mobile apps, web, enterprise software, and more
              </span>
            </div>
            <h1 className={styles.heroTitle}>
              <span className={styles.heroTitleGradient}>Vibe coding</span> that{' '}
              <span className={styles.heroTitleSecondary}>just works!</span>
            </h1>
            <p className={styles.heroSubtitle}>
              Build real software using natural input — voice, sketches, or text. AI handles the rest.
            </p>
            <div className={styles.heroCta}>
              <button 
                onClick={onJoinWaitlist}
                className={`${styles.ctaButton} ${styles.ctaPrimary}`}
              >
                Join Waitlist
                <svg className={styles.ctaIcon} width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M5 12H19M19 12L12 5M19 12L12 19" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </button>
              <Link href="/benefits" className={`${styles.ctaButton} ${styles.ctaSecondary}`}>
                Learn More
              </Link>
            </div>
          </div>

          <div className={styles.heroAnimation} ref={animationRef}>
            <div className={styles.animationContainer}>
              <div className={styles.animationGlow}></div>
              
              {/* Mobile-first approach with lazy loading */}
              {isMobile ? (
                // Show static image on mobile for instant loading
                <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-purple-900/20 to-indigo-900/20 rounded-2xl">
                  <div className="text-white/60 text-center">
                    <div className="text-4xl mb-2">🎤</div>
                    <div className="text-sm">Voice Coding</div>
                  </div>
                </div>
              ) : (
                // Full animation for desktop with lazy loading
                <LazyLottie
                  src="https://lottie.host/embed/4711d258-b3c9-4795-ad87-7c1f4a709a97/VK2hu4bRQw.lottie"
                  fallbackSrc="/voice-icon.svg"
                  mobileStatic="/voice-icon.svg"
                  width={600}
                  height={600}
                  className="w-full h-full"
                  priority={true} // Load immediately since it's above the fold
                  reducedMotion={prefersReducedMotion}
                />
              )}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}