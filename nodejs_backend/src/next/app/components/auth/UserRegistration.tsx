'use client';

import { useEffect, useState } from 'react';
import { useAuth } from '@clerk/nextjs';

/**
 * Component that registers the user with the backend when they sign in with <PERSON>
 * This component should be included in the layout or on pages that require authentication
 */
export default function UserRegistration() {
  // Use client-side only rendering to avoid SSR issues with <PERSON>
  const [isMounted, setIsMounted] = useState(false);
  const [registered, setRegistered] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Set isMounted to true on client-side
  useEffect(() => {
    setIsMounted(true);
  }, []);

  const auth = useAuth();
  const { userId, isLoaded } = auth;

  useEffect(() => {
    // Only register the user if they are signed in and not already registered
    if (isMounted && isLoaded && userId && !registered) {
      registerUser();
    }
  }, [isMounted, isLoaded, userId, registered]);

  const registerUser = async () => {
    try {
      // Call the register API endpoint
      const response = await fetch('/api/auth/register', {
        method: 'POST',
      });

      if (!response.ok) {
        let errorMessage = 'Failed to register user';
        try {
          const errorData = await response.json();
          errorMessage = errorData.error || errorMessage;
        } catch (e) {
          // If the response is not JSON, use the status text
          errorMessage = `${errorMessage}: ${response.status} ${response.statusText}`;
        }
        throw new Error(errorMessage);
      }

      // User registered successfully
      setRegistered(true);
      setError(null);
      console.log('User registered with backend');
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Unknown error');
      console.error('Error registering user:', error);

      // Even if registration fails, we'll consider it "done" to prevent infinite retries
      // This is important for development when the backend might not be fully set up
      setRegistered(true);
    }
  };

  // This component doesn't render anything visible
  return null;
}
