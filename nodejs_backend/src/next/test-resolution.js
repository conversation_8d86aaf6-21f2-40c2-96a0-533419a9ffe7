// Simple test to verify module resolution
console.log('Testing module resolution...');

try {
  // Test direct relative import
  const blog = require('./lib/blog');
  console.log('✅ Direct relative import works');
} catch (e) {
  console.log('❌ Direct relative import failed:', e.message);
}

try {
  // Test if files exist
  const fs = require('fs');
  const path = require('path');
  
  const blogPath = path.join(__dirname, 'lib', 'blog.ts');
  const markdownPath = path.join(__dirname, 'lib', 'markdownToHtml.ts');
  
  console.log('blog.ts exists:', fs.existsSync(blogPath));
  console.log('markdownToHtml.ts exists:', fs.existsSync(markdownPath));
  
  if (fs.existsSync('lib')) {
    console.log('lib directory contents:', fs.readdirSync('lib'));
  }
} catch (e) {
  console.log('❌ File system check failed:', e.message);
}
