/**
 * Nova Sonic WebSocket handler
 * Simplified version based on the working test implementation
 */

import { Server, Socket } from 'socket.io';
import { fromIni } from '@aws-sdk/credential-providers';
import { Buffer } from 'node:buffer';
import { NovaSonicBidirectionalStreamClient, StreamSession } from './client';
import { DefaultSystemPrompt } from './constants';
import { logger } from '../../common/logger';
import axios from 'axios';
import { validateAuth, extractSocketAuthContext } from '../../middleware/unified-auth';

// Import config with proper path
import { config } from '../../common/config';

/**
 * Handler for Nova Sonic bidirectional audio streaming WebSocket connections
 */
export class NovaSonicSocketHandler {
  private bedrockClient: NovaSonicBidirectionalStreamClient;
  private activeSessions: Map<string, StreamSession> = new Map();
  private namespace: any;

  constructor(private io: Server) {
    logger.info('Initializing Nova Sonic WebSocket handler...');
    
    // Create the AWS Bedrock client
    // Use environment variables directly if available, otherwise fall back to profile
    const useEnvCredentials = process.env.AWS_ACCESS_KEY_ID && process.env.AWS_SECRET_ACCESS_KEY;
    const AWS_PROFILE_NAME = process.env.AWS_PROFILE || config.aws.profile || 'bedrock-test';

    try {
      logger.info('Initializing Nova Sonic Bedrock client...');
      logger.info(`Using ${useEnvCredentials ? 'environment' : 'profile'} credentials`);
      if (!useEnvCredentials) {
        logger.info(`AWS Profile: ${AWS_PROFILE_NAME}`);
      }
      logger.info(`AWS Region: ${process.env.AWS_REGION || config.aws.region || 'us-east-1'}`);
      
      this.bedrockClient = new NovaSonicBidirectionalStreamClient({
        requestHandlerConfig: {
          maxConcurrentStreams: 10,
        },
        clientConfig: {
          region: process.env.AWS_REGION || config.aws.region || 'us-east-1',
          credentials: useEnvCredentials
            ? {
                accessKeyId: process.env.AWS_ACCESS_KEY_ID!,
                secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY!,
                sessionToken: process.env.AWS_SESSION_TOKEN,
              }
            : fromIni({ profile: AWS_PROFILE_NAME }),
        },
      });
      
      logger.info('Nova Sonic Bedrock client initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize Bedrock client:', error);
      logger.error('Error details:', {
        message: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined,
        name: error instanceof Error ? error.name : 'Unknown'
      });
      // Don't throw - let the handler continue without AWS support for testing
      logger.warn('Continuing without AWS Bedrock support for testing WebSocket connectivity');
      this.bedrockClient = null as any; // Allow null for testing
    }

    // Set up namespace and connection handler
    this.setupNamespace();

    // Periodically check for and close inactive sessions (every minute)
    setInterval(() => this.cleanupInactiveSessions(), 60000);

    logger.info('Nova Sonic WebSocket handler initialized');
  }

  private setupNamespace(): void {
    try {
      // Set up WebSocket namespace
      this.namespace = this.io.of('/ws/nova-sonic');
      
      // Add unified authentication middleware
      this.namespace.use(async (socket: Socket, next: any) => {
        logger.info(`🔌 Nova Sonic namespace middleware - socket ${socket.id} attempting to connect`);
        
        const authContext = extractSocketAuthContext(socket);
        
        logger.info(`🔌 Socket authentication data:`, {
          address: socket.handshake.address,
          hasAuth: !!authContext.token,
          token: authContext.token ? `${authContext.token.substring(0, 20)}...` : undefined,
          sessionAuth: authContext.session_auth,
          userId: authContext.user_id,
          role: authContext.role
        });
        
        const authResult = await validateAuth(authContext);
        
        if (authResult.success) {
          logger.info(`🔌 ✅ Authentication successful for socket ${socket.id}, userId: ${authResult.userId}, role: ${authResult.role}`);
          // Store auth info on socket for later use
          (socket as any).authInfo = {
            userId: authResult.userId,
            role: authResult.role
          };
          next();
        } else {
          logger.error(`🔌 ❌ Authentication failed for socket ${socket.id}: ${authResult.error}`);
          next(new Error(authResult.error || 'Authentication failed'));
        }
      });
      
      this.namespace.on('connection', (socket: Socket) => {
        logger.info(`🔌✅ Nova Sonic namespace connection event - socket ${socket.id}`);
        logger.info(`🔌✅ Socket connected from: ${socket.handshake.address}`);
        logger.info(`🔌✅ Total clients in namespace: ${this.namespace.sockets.size}`);
        this.handleConnection(socket);
      });
      
      this.namespace.on('connect_error', (error: any) => {
        logger.error('Nova Sonic namespace connection error:', error);
      });
      
      // Log all namespace events for debugging
      this.namespace.on('error', (error: any) => {
        logger.error('Nova Sonic namespace error:', error);
      });
      
      // Check if namespace was created successfully
      logger.info(`Nova Sonic namespace /ws/nova-sonic set up successfully`);
      logger.info(`Namespace name: ${this.namespace.name}`);
      logger.info(`Namespace adapter: ${this.namespace.adapter.constructor.name}`);
    } catch (error) {
      logger.error('Error setting up Nova Sonic namespace:', error);
      throw error;
    }
  }

  private async handleConnection(socket: Socket): Promise<void> {
    logger.info(`🔌✅ New Nova Sonic client connected: ${socket.id}`);
    logger.info(`🔌✅ Setting up socket event handlers for ${socket.id}`);

    // Create a unique session ID for this client
    const sessionId = socket.id;

    try {
      // Check if this socket already has an active session and clean it up
      const existingSession = this.activeSessions.get(sessionId);
      if (existingSession) {
        logger.info(`🔌🧹 Cleaning up existing session for socket: ${sessionId}`);
        try {
          await existingSession.close();
          this.activeSessions.delete(sessionId);
        } catch (cleanupError) {
          logger.warn(`Error cleaning up existing session ${sessionId}:`, cleanupError);
        }
      }

      // Also check if the Bedrock client has any lingering sessions for this ID
      if (this.bedrockClient && this.bedrockClient.isSessionActive(sessionId)) {
        logger.info(`🔌🧹 Force closing lingering Bedrock session for: ${sessionId}`);
        try {
          this.bedrockClient.forceCloseSession(sessionId);
        } catch (forceCloseError) {
          logger.warn(`Error force closing Bedrock session ${sessionId}:`, forceCloseError);
        }
      }

      // Set up socket handlers for initialization
      this.setupInitializationHandlers(socket);

      // Send connected confirmation
      socket.emit('connected', { 
        sessionId,
        status: 'awaiting_initialization',
        message: 'Connected to Nova Sonic WebSocket. Send "initializeSession" to start voice streaming.'
      });

    } catch (error) {
      logger.error('Error creating session:', error);
      socket.emit('error', {
        message: 'Failed to initialize session',
        details: error instanceof Error ? error.message : String(error)
      });
      socket.disconnect();
    }
  }

  private setupInitializationHandlers(socket: Socket): void {
    // Handler for explicit session initialization
    socket.on('initializeSession', async (data?: { systemPrompt?: string }) => {
      const sessionId = socket.id;
      logger.info(`🎯 Received initializeSession request for ${sessionId}`);
      
      try {
        // Check if session already exists
        if (this.activeSessions.has(sessionId)) {
          logger.warn(`Session ${sessionId} already initialized`);
          socket.emit('error', {
            message: 'Session already initialized',
            type: 'session_already_exists'
          });
          return;
        }

        // Create session
        const session = this.bedrockClient.createStreamSession(sessionId);
        this.activeSessions.set(sessionId, session);
        logger.info(`Created stream session for ${sessionId}`);

        // Set up event handlers
        this.setupEventHandlers(session, socket);
        
        // Set up socket handlers
        this.setupSocketHandlers(socket, session);

        // Initiate the AWS Bedrock session
        this.bedrockClient.initiateSession(sessionId).catch(error => {
          logger.error(`Failed to initiate session ${sessionId}:`, error);
          logger.error(`Error stack:`, error.stack);
          
          // Enhanced error handling
          if (axios.isAxiosError(error) && error.code === 'ECONNABORTED') {
            logger.warn(`Timeout occurred in session ${sessionId}: `, error.message);
            socket.emit('timeout', {
              source: 'bidirectionalStream',
              message: 'Session timed out, please try again.'
            });
          } else {
            socket.emit('error', {
              message: 'Failed to initiate AWS Bedrock session',
              details: error instanceof Error ? error.message : String(error),
              type: 'bedrock_session_error'
            });
          }
          
          // Clean up on failure
          this.activeSessions.delete(sessionId);
        });

        // Send success confirmation
        socket.emit('sessionInitialized', {
          sessionId,
          status: 'ready',
          message: 'Session initialized successfully. You can now start streaming.'
        });

      } catch (error) {
        logger.error(`Error initializing session for ${sessionId}:`, error);
        socket.emit('error', {
          message: 'Failed to initialize session',
          details: error instanceof Error ? error.message : String(error),
          type: 'initialization_error'
        });
      }
    });

    // Handler for checking session status
    socket.on('getSessionStatus', () => {
      const sessionId = socket.id;
      const hasSession = this.activeSessions.has(sessionId);
      const isActive = this.bedrockClient.isSessionActive(sessionId);
      
      socket.emit('sessionStatus', {
        sessionId,
        initialized: hasSession,
        active: isActive,
        timestamp: new Date().toISOString()
      });
    });

    // Add disconnect handler here for non-initialized sockets
    socket.on('disconnect', (reason) => {
      logger.info(`[${socket.id}] Client disconnected before initialization: ${reason}`);
    });
  }

  private setupEventHandlers(session: StreamSession, socket: Socket): void {
    session.onEvent('contentStart', (data) => {
      logger.debug(`[${socket.id}] contentStart:`, data);
      socket.emit('contentStart', data);
    });

    session.onEvent('textOutput', (data) => {
      logger.debug(`[${socket.id}] textOutput:`, data);
      socket.emit('textOutput', data);
    });

    session.onEvent('audioOutput', (data) => {
      logger.debug(`[${socket.id}] audioOutput received, size: ${data?.content?.length || 0}`);
      socket.emit('audioOutput', data);
    });

    session.onEvent('error', (data) => {
      logger.error(`[${socket.id}] Error in session:`, data);
      socket.emit('error', data);
    });

    session.onEvent('sessionTimeout', (data) => {
      logger.info(`[${socket.id}] Session timeout:`, data);
      socket.emit('sessionTimeout', data);
    });

    session.onEvent('toolUse', (data) => {
      logger.debug(`[${socket.id}] Tool use detected:`, data.toolName);
      socket.emit('toolUse', data);
    });

    session.onEvent('toolResult', (data) => {
      logger.debug(`[${socket.id}] Tool result received`);
      socket.emit('toolResult', data);
    });

    session.onEvent('contentEnd', (data) => {
      logger.debug(`[${socket.id}] Content end:`, data);
      socket.emit('contentEnd', data);
    });

    session.onEvent('streamComplete', () => {
      logger.debug(`[${socket.id}] Stream completed`);
      socket.emit('streamComplete');
    });
  }

  private setupSocketHandlers(socket: Socket, session: StreamSession): void {
    let sessionInitialized = false;

    // Audio input handler
    socket.on('audioInput', async (audioData) => {
      try {
        // Guard against audioInput before session is initialized
        if (!this.bedrockClient.isSessionActive(session.getSessionId())) {
          logger.warn(`Session ${session.getSessionId()} is not active, ignoring audio input`);
          return;
        }

        // Convert base64 string to Buffer
        const audioBuffer = typeof audioData === 'string'
          ? Buffer.from(audioData, 'base64')
          : Buffer.from(audioData);

        // Stream the audio
        await session.streamAudio(audioBuffer);

      } catch (error) {
        logger.error('Error processing audio:', error);
        socket.emit('error', {
          message: 'Error processing audio',
          details: error instanceof Error ? error.message : String(error)
        });
      }
    });

    // Prompt start handler
    socket.on('promptStart', async () => {
      try {
        logger.info(`[${socket.id}] Prompt start received`);
        await session.setupPromptStart();
        socket.emit('promptStarted');
      } catch (error) {
        logger.error('Error processing prompt start:', error);
        socket.emit('error', {
          message: 'Error processing prompt start',
          details: error instanceof Error ? error.message : String(error)
        });
      }
    });

    // System prompt handler
    socket.on('systemPrompt', async (data) => {
      try {
        logger.info(`[${socket.id}] System prompt received:`, data);
        await session.setupSystemPrompt(undefined, data || DefaultSystemPrompt);
        socket.emit('systemPromptSet');
      } catch (error) {
        logger.error('Error processing system prompt:', error);
        socket.emit('error', {
          message: 'Error processing system prompt',
          details: error instanceof Error ? error.message : String(error)
        });
      }
    });

    // Audio start handler
    socket.on('audioStart', async () => {
      try {
        logger.info(`[${socket.id}] Audio start received`);
        
        // Initialize session if not already done
        if (!sessionInitialized) {
          logger.info(`[${socket.id}] Initializing session before audio start`);
          await session.setupPromptStart();
          await new Promise(resolve => setTimeout(resolve, 100));
          
          await session.setupSystemPrompt(undefined, DefaultSystemPrompt);
          await new Promise(resolve => setTimeout(resolve, 100));
          
          sessionInitialized = true;
          logger.info(`[${socket.id}] Session initialization complete`);
        }

        await session.setupStartAudio();
        logger.info(`[${socket.id}] Audio start setup complete`);
        socket.emit('audioStarted');
      } catch (error) {
        logger.error(`[${socket.id}] Error processing audio start:`, error);
        socket.emit('error', {
          message: 'Error processing audio start',
          details: error instanceof Error ? error.message : String(error)
        });
      }
    });

    // Stop audio handler
    socket.on('stopAudio', async () => {
      try {
        logger.info(`[${socket.id}] Stop audio requested, beginning proper shutdown sequence`);

        // Temporarily disable the session to stop audio processing
        const sessionDisabler = session.temporarilyDisable();

        try {
          // Only end the current audio content and prompt, but don't close the session
          await session.endAudioContent();
          await session.endPrompt();

          // Reset the session state to prepare for a new streaming attempt
          session.resetState();
          
          if (this.bedrockClient.resetSessionState) {
            this.bedrockClient.resetSessionState(session.getSessionId());
          }

          logger.info(`[${socket.id}] Audio content and prompt ended, session reset and remains active`);
        } finally {
          // Restore session state after reset
          sessionDisabler.restore();
        }

        // Reset the initialization flag for next interaction
        sessionInitialized = false;

        // Emit an event to notify the client that the session is ready for a new streaming attempt
        socket.emit('sessionReadyForNewStream', {
          sessionId: session.getSessionId(),
          timestamp: new Date().toISOString()
        });
      } catch (error) {
        logger.error(`[${socket.id}] Error processing streaming end events:`, error);
        socket.emit('error', {
          message: 'Error processing streaming end events',
          details: error instanceof Error ? error.message : String(error)
        });
      }
    });

    // Text input handler (for non-voice messages)
    socket.on('textInput', async (text) => {
      try {
        logger.info(`[${socket.id}] Text input received:`, text);
        // For now, just echo back that we received it
        socket.emit('textOutput', {
          role: 'SYSTEM',
          content: `Text input received: "${text}". Voice streaming is required for Nova Sonic responses.`
        });
      } catch (error) {
        logger.error(`[${socket.id}] Error processing text input:`, error);
        socket.emit('error', {
          message: 'Error processing text input',
          details: error instanceof Error ? error.message : String(error)
        });
      }
    });

    // Enhanced disconnect handling with better timeout and error recovery
    socket.on('disconnect', async (reason) => {
      logger.info(`[${socket.id}] Client disconnected: ${reason}`);
      const sessionId = session.getSessionId();

      if (this.bedrockClient.isSessionActive(sessionId)) {
        try {
          logger.info(`[${socket.id}] Beginning enhanced cleanup for disconnected session`);

          // Enhanced cleanup with better state management
          const cleanupPromise = Promise.race([
            (async () => {
              // First, temporarily disable the session to prevent new audio from being processed
              const sessionDisabler = session.temporarilyDisable();

              try {
                // Clear any pending audio data
                session.clearAudioQueue();
                
                // Reset session state to ensure clean state before cleanup
                if (this.bedrockClient.isSessionActive(sessionId)) {
                  this.bedrockClient.resetSessionState(sessionId);
                }

                // Wait a bit to ensure state reset is processed
                await new Promise(resolve => setTimeout(resolve, 200));

                // End any active audio content
                await session.endAudioContent();

                // Wait to ensure content end is processed
                await new Promise(resolve => setTimeout(resolve, 500));

                // Then end the prompt
                await session.endPrompt();

                // Wait to ensure prompt end is processed
                await new Promise(resolve => setTimeout(resolve, 500));

                // Finally close the session
                await session.close();
              } finally {
                // Restore session state (though it will be closed anyway)
                sessionDisabler.restore();
              }
            })(),
            new Promise((_, reject) =>
              setTimeout(() => reject(new Error('Enhanced session cleanup timeout')), 8000) // Increased timeout
            )
          ]);

          await cleanupPromise;
          logger.info(`[${socket.id}] Successfully completed enhanced cleanup after disconnect`);
        } catch (error) {
          logger.error(`[${socket.id}] Error during enhanced cleanup after disconnect:`, error);
          try {
            this.bedrockClient.forceCloseSession(sessionId);
            logger.info(`[${socket.id}] Force closed session after enhanced cleanup failure`);
          } catch (e) {
            logger.error(`[${socket.id}] Failed even force close after enhanced cleanup:`, e);
          }
        } finally {
          // Make sure session is removed from tracking
          this.activeSessions.delete(sessionId);
          
          // Ensure socket is fully disconnected
          if (socket.connected) {
            socket.disconnect(true);
          }
        }
      }
    });

    // Ping handler for keep-alive
    socket.on('ping', () => {
      logger.info(`[${socket.id}] Ping received, sending pong`);
      socket.emit('pong');
    });
    
    // Health check handler
    socket.on('health', () => {
      logger.info(`[${socket.id}] Health check received`);
      socket.emit('health-response', {
        status: 'ok',
        timestamp: new Date().toISOString(),
        sessionId: session.getSessionId()
      });
    });
  }

  private cleanupInactiveSessions(): void {
    const now = Date.now();

    // Log active sessions
    const activeSessions = this.bedrockClient.getActiveSessions();
    if (activeSessions.length > 0) {
      logger.debug(`Active Bedrock sessions: ${activeSessions.length}`);
    }

    // Check all active sessions
    activeSessions.forEach(sessionId => {
      const lastActivity = this.bedrockClient.getLastActivityTime(sessionId);

      // If no activity for 5 minutes, force close
      if (now - lastActivity > 5 * 60 * 1000) {
        logger.info(`Closing inactive session ${sessionId} after 5 minutes of inactivity`);
        try {
          this.bedrockClient.forceCloseSession(sessionId);
          this.activeSessions.delete(sessionId);
        } catch (error) {
          logger.error(`Error force closing inactive session ${sessionId}:`, error);
        }
      }
    });
  }

  /**
   * Get the Nova Sonic Bedrock client for external access
   * Used by admin API endpoints for session monitoring
   */
  public getBedrockClient(): NovaSonicBidirectionalStreamClient {
    return this.bedrockClient;
  }

  /**
   * Get active WebSocket sessions
   */
  public getActiveWebSocketSessions(): Map<string, StreamSession> {
    return this.activeSessions;
  }
}
