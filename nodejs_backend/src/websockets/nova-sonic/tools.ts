/**
 * Tool handlers for Nova Sonic function calling
 */

import axios from 'axios';
import * as https from 'https';

// Interface for tool handlers
export interface ToolHandler {
  name: string;
  description: string;
  schema: string;
  execute: (content: any) => Promise<any>;
}

// Interface for tool registry
export interface ToolRegistry {
  registerTool: (tool: ToolHandler) => void;
  getTool: (name: string) => ToolHandler | undefined;
  getAllTools: () => ToolHandler[];
  processToolUse: (toolName: string, toolUseContent: any) => Promise<any>;
}

// Implementation of the tool registry
export class DefaultToolRegistry implements ToolRegistry {
  private tools: Map<string, ToolHandler> = new Map();

  constructor() {
    // Register default tools
    this.registerTool(getDateAndTimeTool);
    this.registerTool(getWeatherTool);
  }

  registerTool(tool: ToolHandler): void {
    this.tools.set(tool.name.toLowerCase(), tool);
  }

  getTool(name: string): Tool<PERSON>and<PERSON> | undefined {
    return this.tools.get(name.toLowerCase());
  }

  getAllTools(): ToolHandler[] {
    return Array.from(this.tools.values());
  }

  async processToolUse(toolName: string, toolUseContent: any): Promise<any> {
    const tool = this.getTool(toolName.toLowerCase());
    
    if (!tool) {
      throw new Error(`Tool ${toolName} not supported`);
    }
    
    try {
      return await tool.execute(toolUseContent);
    } catch (error) {
      console.error(`Error executing tool ${toolName}:`, error);
      throw error;
    }
  }
}

// Date and Time Tool
export const getDateAndTimeTool: ToolHandler = {
  name: "getDateAndTimeTool",
  description: "Get information about the current date and time.",
  schema: JSON.stringify({
    "type": "object",
    "properties": {},
    "required": []
  }),
  execute: async () => {
    const date = new Date().toLocaleString("en-US", { timeZone: "America/Los_Angeles" });
    const pstDate = new Date(date);
    return {
      date: pstDate.toISOString().split('T')[0],
      year: pstDate.getFullYear(),
      month: pstDate.getMonth() + 1,
      day: pstDate.getDate(),
      dayOfWeek: pstDate.toLocaleString('en-US', { weekday: 'long' }).toUpperCase(),
      timezone: "PST",
      formattedTime: pstDate.toLocaleTimeString('en-US', {
        hour12: true,
        hour: '2-digit',
        minute: '2-digit'
      })
    };
  }
};

// Weather Tool
export const getWeatherTool: ToolHandler = {
  name: "getWeatherTool",
  description: "Get the current weather for a given location, based on its WGS84 coordinates.",
  schema: JSON.stringify({
    "type": "object",
    "properties": {
      "latitude": {
        "type": "string",
        "description": "Geographical WGS84 latitude of the location."
      },
      "longitude": {
        "type": "string",
        "description": "Geographical WGS84 longitude of the location."
      }
    },
    "required": ["latitude", "longitude"]
  }),
  execute: async (toolUseContent: any) => {
    const parsedContent = await parseToolUseContentForWeather(toolUseContent);
    if (!parsedContent) {
      throw new Error('Failed to parse weather tool content');
    }
    return fetchWeatherData(parsedContent.latitude, parsedContent.longitude);
  }
};

// Helper function to parse weather tool content
async function parseToolUseContentForWeather(toolUseContent: any): Promise<{ latitude: number; longitude: number; } | null> {
  try {
    // Check if the content field exists and is a string
    if (toolUseContent && typeof toolUseContent.content === 'string') {
      // Parse the JSON string into an object
      const parsedContent = JSON.parse(toolUseContent.content);
      console.log(`parsedContent ${parsedContent}`);
      // Return the parsed content
      return {
        latitude: parsedContent.latitude,
        longitude: parsedContent.longitude
      };
    }
    return null;
  } catch (error) {
    console.error("Failed to parse tool use content:", error);
    return null;
  }
}

// Helper function to fetch weather data
async function fetchWeatherData(
  latitude: number,
  longitude: number
): Promise<Record<string, any>> {
  const ipv4Agent = new https.Agent({ family: 4 });
  const url = `https://api.open-meteo.com/v1/forecast?latitude=${latitude}&longitude=${longitude}&current_weather=true`;

  try {
    const response = await axios.get(url, {
      httpsAgent: ipv4Agent,
      timeout: 5000,
      headers: {
        'User-Agent': 'MyApp/1.0',
        'Accept': 'application/json'
      }
    });
    const weatherData = response.data;
    console.log("weatherData:", weatherData);

    return {
      weather_data: weatherData
    };
  } catch (error) {
    if (axios.isAxiosError(error)) {
      console.error(`Error fetching weather data: ${error.message}`, error);
    } else {
      console.error(`Unexpected error: ${error instanceof Error ? error.message : String(error)} `, error);
    }
    throw error;
  }
}
