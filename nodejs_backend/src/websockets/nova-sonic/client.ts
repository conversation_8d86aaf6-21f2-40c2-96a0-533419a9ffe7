/**
 * Nova Sonic bidirectional streaming client - Simplified version
 */

import {
  BedrockRuntimeClient,
  BedrockRuntimeClientConfig,
  InvokeModelWithBidirectionalStreamCommand,
  InvokeModelWithBidirectionalStreamInput,
} from "@aws-sdk/client-bedrock-runtime";
import {
  NodeHttp2Handler,
  NodeHttp2HandlerOptions,
} from "@smithy/node-http-handler";
import { Provider } from "@smithy/types";
import { Buffer } from "node:buffer";
import { randomUUID } from "node:crypto";
import { InferenceConfig } from "./types";
import { Subject } from 'rxjs';
import { take } from 'rxjs/operators';
import { firstValueFrom } from 'rxjs';
import {
  DefaultAudioInputConfiguration,
  DefaultAudioOutputConfiguration,
  DefaultSystemPrompt,
  DefaultTextConfiguration
} from "./constants";
import { DefaultToolRegistry, ToolRegistry } from "./tools";

export interface NovaSonicBidirectionalStreamClientConfig {
  requestHandlerConfig?: NodeHttp2HandlerOptions | Provider<NodeHttp2HandlerOptions | void>;
  clientConfig: Partial<BedrockRuntimeClientConfig>;
  inferenceConfig?: InferenceConfig;
  toolRegistry?: ToolRegistry;
}

// Session data type
interface SessionData {
  queue: Array<any>;
  queueSignal: Subject<void>;
  closeSignal: Subject<void>;
  responseSubject: Subject<any>;
  toolUseContent: any;
  toolUseId: string;
  toolName: string;
  responseHandlers: Map<string, (data: any) => void>;
  promptName: string;
  inferenceConfig: InferenceConfig;
  isActive: boolean;
  isPromptStartSent: boolean;
  isAudioContentStartSent: boolean;
  currentAudioContentId: string | null;
}

export class StreamSession {
  private audioBufferQueue: Buffer[] = [];
  private maxQueueSize = 200;
  private isProcessingAudio = false;
  private isActive = true;
  private lastActivityTimestamp = Date.now();
  private inactivityTimer: NodeJS.Timeout | null = null;
  private readonly INACTIVITY_TIMEOUT_MS = 30000; // 30 seconds

  constructor(
    private sessionId: string,
    private client: NovaSonicBidirectionalStreamClient
  ) {
    // Start the inactivity timer when session is created
    this.startInactivityTimer();
  }

  // Register event handlers for this specific session
  public onEvent(eventType: string, handler: (data: any) => void): StreamSession {
    this.client.registerEventHandler(this.sessionId, eventType, handler);
    return this;
  }

  private startInactivityTimer(): void {
    // Clear any existing timer
    if (this.inactivityTimer) {
      clearTimeout(this.inactivityTimer);
    }

    // Start a new timer
    this.inactivityTimer = setTimeout(() => {
      this.handleInactivityTimeout();
    }, this.INACTIVITY_TIMEOUT_MS);

    // Update the timestamp
    this.lastActivityTimestamp = Date.now();
  }

  private async handleInactivityTimeout(): Promise<void> {
    if (!this.isActive) return;

    const timeSinceLastActivity = Date.now() - this.lastActivityTimestamp;

    if (timeSinceLastActivity >= this.INACTIVITY_TIMEOUT_MS) {
      console.log(`Session ${this.sessionId} has been inactive for ${timeSinceLastActivity}ms, allowing natural timeout`);
      // Don't restart the timer - we want the session to time out
      this.inactivityTimer = null;
    }
  }

  private updateActivity(): void {
    this.lastActivityTimestamp = Date.now();
    this.startInactivityTimer();
  }

  public async setupPromptStart(): Promise<void> {
    await this.client.setupPromptStartEvent(this.sessionId);
  }

  public async setupSystemPrompt(
    textConfig: typeof DefaultTextConfiguration = DefaultTextConfiguration,
    systemPromptContent: string = DefaultSystemPrompt
  ): Promise<void> {
    await this.client.setupSystemPromptEvent(this.sessionId, textConfig, systemPromptContent);
  }

  public async setupStartAudio(
    audioConfig: typeof DefaultAudioInputConfiguration = DefaultAudioInputConfiguration
  ): Promise<void> {
    await this.client.setupStartAudioEvent(this.sessionId, audioConfig);
  }

  // Stream audio for this session
  public async streamAudio(audioData: Buffer): Promise<void> {
    if (!this.isActive) {
      console.log(`[${this.sessionId}] Ignoring audio data for inactive session`);
      return;
    }

    // Update activity timestamp since we received audio data
    this.updateActivity();

    // Check queue size to avoid memory issues
    if (this.audioBufferQueue.length >= this.maxQueueSize) {
      // Queue is full, drop oldest chunk
      this.audioBufferQueue.shift();
      console.log("Audio queue full, dropping oldest chunk");
    }

    // Queue the audio chunk for streaming
    this.audioBufferQueue.push(audioData);
    this.processAudioQueue();
  }

  // Process audio queue for continuous streaming
  private async processAudioQueue() {
    if (this.isProcessingAudio || this.audioBufferQueue.length === 0 || !this.isActive) return;

    this.isProcessingAudio = true;
    try {
      const maxChunksPerBatch = 5;
      let processedChunks = 0;

      while (this.audioBufferQueue.length > 0 && processedChunks < maxChunksPerBatch && this.isActive) {
        const audioChunk = this.audioBufferQueue.shift();
        if (audioChunk) {
          await this.client.streamAudioChunk(this.sessionId, audioChunk);
          processedChunks++;
        }
      }
    } finally {
      this.isProcessingAudio = false;

      if (this.audioBufferQueue.length > 0 && this.isActive) {
        setTimeout(() => this.processAudioQueue(), 0);
      }
    }
  }

  public clearAudioQueue(): void {
    console.log(`[${this.sessionId}] Clearing audio buffer queue (${this.audioBufferQueue.length} items)`);
    this.audioBufferQueue = [];
  }

  public async endAudioContent(): Promise<void> {
    if (!this.isActive) return;
    
    this.audioBufferQueue = [];
    this.isProcessingAudio = false;
    
    await this.client.sendContentEnd(this.sessionId);
  }

  public async endPrompt(): Promise<void> {
    if (!this.isActive) return;
    await this.client.sendPromptEnd(this.sessionId);
  }

  public async close(): Promise<void> {
    if (!this.isActive) return;

    this.isActive = false;
    this.audioBufferQueue = [];

    // Clean up timers on close
    if (this.inactivityTimer) {
      clearTimeout(this.inactivityTimer);
      this.inactivityTimer = null;
    }

    await this.client.sendSessionEnd(this.sessionId);
    console.log(`Session ${this.sessionId} close completed`);
  }

  public resetState(): void {
    // Clear any pending audio data
    this.clearAudioQueue();

    // Reset internal state flags but keep the session active
    this.isProcessingAudio = false;

    // Reset the inactivity timer
    this.startInactivityTimer();

    // Force a pause in audio processing
    setTimeout(() => {
      if (this.audioBufferQueue.length > 0) {
        console.log(`[${this.sessionId}] Found ${this.audioBufferQueue.length} items still in queue after reset, clearing again`);
        this.audioBufferQueue = [];
      }
    }, 100);

    console.log(`Session ${this.sessionId} state reset`);
  }

  public temporarilyDisable(): { restore: () => void } {
    const originalActiveState = this.isActive;

    // Pause the inactivity timer
    if (this.inactivityTimer) {
      clearTimeout(this.inactivityTimer);
      this.inactivityTimer = null;
    }

    // Set to inactive
    this.isActive = false;

    // Clear the queue immediately
    this.clearAudioQueue();

    // Return a function to restore the original state
    return {
      restore: () => {
        this.isActive = originalActiveState;

        // If we're restoring to active state, restart the inactivity timer
        if (originalActiveState) {
          this.startInactivityTimer();
        }

        console.log(`[${this.sessionId}] Session active state restored to ${originalActiveState}`);
      }
    };
  }

  public getSessionId(): string {
    return this.sessionId;
  }
}

export class NovaSonicBidirectionalStreamClient {
  private bedrockRuntimeClient: BedrockRuntimeClient;
  private inferenceConfig: InferenceConfig;
  private activeSessions: Map<string, SessionData> = new Map();
  private sessionLastActivity: Map<string, number> = new Map();
  private sessionCleanupInProgress = new Set<string>();
  private toolRegistry: ToolRegistry;

  constructor(config: NovaSonicBidirectionalStreamClientConfig) {
    const nodeHttp2Handler = new NodeHttp2Handler({
      requestTimeout: 300000,
      sessionTimeout: 300000,
      disableConcurrentStreams: false,
      maxConcurrentStreams: 20,
      ...config.requestHandlerConfig,
    });

    if (!config.clientConfig.credentials) {
      throw new Error("No credentials provided");
    }

    this.bedrockRuntimeClient = new BedrockRuntimeClient({
      ...config.clientConfig,
      credentials: config.clientConfig.credentials,
      region: config.clientConfig.region || "us-east-1",
      requestHandler: nodeHttp2Handler
    });

    this.inferenceConfig = config.inferenceConfig ?? {
      maxTokens: 1024,
      topP: 0.9,
      temperature: 0.7,
    };

    // Initialize the tool registry
    this.toolRegistry = config.toolRegistry ?? new DefaultToolRegistry();
  }

  public isSessionActive(sessionId: string): boolean {
    const session = this.activeSessions.get(sessionId);
    return !!session && session.isActive;
  }

  public getActiveSessions(): string[] {
    return Array.from(this.activeSessions.keys());
  }

  public getLastActivityTime(sessionId: string): number {
    return this.sessionLastActivity.get(sessionId) || 0;
  }

  private updateSessionActivity(sessionId: string): void {
    this.sessionLastActivity.set(sessionId, Date.now());
  }

  public isCleanupInProgress(sessionId: string): boolean {
    return this.sessionCleanupInProgress.has(sessionId);
  }

  // Create a new streaming session (like the working version)
  public createStreamSession(sessionId: string = randomUUID()): StreamSession {
    // Force cleanup of any existing session with the same ID
    if (this.activeSessions.has(sessionId)) {
      console.log(`Session ${sessionId} already exists, forcing cleanup before creating new one`);
      this.forceCloseSession(sessionId);
    }

    // Generate a unique prompt name using UUID with timestamp for extra uniqueness
    const uniquePromptName = `${randomUUID()}-${Date.now()}`;
    console.log(`Generated unique prompt name: ${uniquePromptName} for session: ${sessionId}`);

    const session: SessionData = {
      queue: [],
      queueSignal: new Subject<void>(),
      closeSignal: new Subject<void>(),
      responseSubject: new Subject<any>(),
      toolUseContent: null,
      toolUseId: "",
      toolName: "",
      responseHandlers: new Map(),
      promptName: uniquePromptName,
      inferenceConfig: this.inferenceConfig,
      isActive: true,
      isPromptStartSent: false,
      isAudioContentStartSent: false,
      currentAudioContentId: null
    };

    this.activeSessions.set(sessionId, session);
    this.updateSessionActivity(sessionId);

    return new StreamSession(sessionId, this);
  }

  // Stream audio for a specific session
  public async initiateSession(sessionId: string): Promise<void> {
    const session = this.activeSessions.get(sessionId);
    if (!session) {
      throw new Error(`Stream session ${sessionId} not found`);
    }

    try {
      // Set up initial events for this session
      this.setupSessionStartEvent(sessionId);

      // Create the bidirectional stream with session-specific async iterator
      const asyncIterable = this.createSessionAsyncIterable(sessionId);

      console.log(`Starting bidirectional stream for session ${sessionId}...`);

      const response = await this.bedrockRuntimeClient.send(
        new InvokeModelWithBidirectionalStreamCommand({
          modelId: "amazon.nova-sonic-v1:0",
          body: asyncIterable,
        })
      );

      console.log(`Stream established for session ${sessionId}, processing responses...`);

      // Process responses for this session
      await this.processResponseStream(sessionId, response);

    } catch (error) {
      console.error(`Error in session ${sessionId}: `, error);
      
      // Enhanced error handling with timeout detection
      this.handleSessionError(sessionId, error, 'bidirectionalStream');

      // Make sure to clean up if there's an error
      if (session.isActive) {
        this.closeSession(sessionId);
      }
    }
  }

  // Create async iterable for streaming
  private createSessionAsyncIterable(sessionId: string): AsyncIterable<InvokeModelWithBidirectionalStreamInput> {
    if (!this.isSessionActive(sessionId)) {
      console.log(`Cannot create async iterable: Session ${sessionId} not active`);
      return {
        [Symbol.asyncIterator]: () => ({
          next: async () => ({ value: undefined, done: true })
        })
      };
    }

    const session = this.activeSessions.get(sessionId);
    if (!session) {
      throw new Error(`Cannot create async iterable: Session ${sessionId} not found`);
    }

    let eventCount = 0;

    return {
      [Symbol.asyncIterator]: () => {
        console.log(`AsyncIterable iterator requested for session ${sessionId}`);

        return {
          next: async (): Promise<IteratorResult<InvokeModelWithBidirectionalStreamInput>> => {
            try {
              // Check if session is still active
              if (!session.isActive || !this.activeSessions.has(sessionId)) {
                console.log(`Iterator closing for session ${sessionId}, done = true`);
                return { value: undefined, done: true };
              }
              
              // Wait for items in the queue or close signal
              if (session.queue.length === 0) {
                try {
                  await Promise.race([
                    firstValueFrom(session.queueSignal.pipe(take(1))),
                    firstValueFrom(session.closeSignal.pipe(take(1))).then(() => {
                      throw new Error("Stream closed signal received");
                    })
                  ]);
                } catch (error) {
                  if (error instanceof Error) {
                    if (error.message === "Stream closed" || !session.isActive) {
                      // This is an expected condition when closing the session
                      if (this.activeSessions.has(sessionId)) {
                        console.log(`Session ${sessionId} closed during wait`);
                      }
                      return { value: undefined, done: true };
                    }
                  } else {
                    console.error(`Error on event close`, error);
                  }
                }
              }

              // If queue is still empty or session is inactive, we're done
              if (session.queue.length === 0 || !session.isActive) {
                console.log(`Queue empty or session inactive: ${sessionId}`);
                return { value: undefined, done: true };
              }

              // Get next item from the session's queue
              const nextEvent = session.queue.shift();
              eventCount++;

              return {
                value: {
                  chunk: {
                    bytes: new TextEncoder().encode(JSON.stringify(nextEvent))
                  }
                },
                done: false
              };
            } catch (error) {
              console.error(`Error in session ${sessionId} iterator: `, error);
              session.isActive = false;
              return { value: undefined, done: true };
            }
          },

          return: async (): Promise<IteratorResult<InvokeModelWithBidirectionalStreamInput>> => {
            console.log(`Iterator return () called for session ${sessionId}`);
            session.isActive = false;
            return { value: undefined, done: true };
          },

          throw: async (error: any): Promise<IteratorResult<InvokeModelWithBidirectionalStreamInput>> => {
            console.log(`Iterator throw () called for session ${sessionId} with error: `, error);
            session.isActive = false;
            throw error;
          }
        };
      }
    };
  }

  // Process the response stream from AWS Bedrock
  private async processResponseStream(sessionId: string, response: any): Promise<void> {
    const session = this.activeSessions.get(sessionId);
    if (!session) return;

    try {
      for await (const event of response.body) {
        if (!session.isActive) {
          console.log(`Session ${sessionId} is no longer active, stopping response processing`);
          break;
        }

        if (event.chunk?.bytes) {
          try {
            const textResponse = new TextDecoder().decode(event.chunk.bytes);
            const jsonResponse = JSON.parse(textResponse);

            if (jsonResponse.event?.contentStart) {
              this.dispatchEvent(sessionId, 'contentStart', jsonResponse.event.contentStart);
            } else if (jsonResponse.event?.textOutput) {
              this.dispatchEvent(sessionId, 'textOutput', jsonResponse.event.textOutput);
            } else if (jsonResponse.event?.audioOutput) {
              this.dispatchEvent(sessionId, 'audioOutput', jsonResponse.event.audioOutput);
            } else if (jsonResponse.event?.toolUse) {
              this.dispatchEvent(sessionId, 'toolUse', jsonResponse.event.toolUse);
              
              // Store tool use information
              session.toolUseContent = jsonResponse.event.toolUse;
              session.toolUseId = jsonResponse.event.toolUse.toolUseId;
              session.toolName = jsonResponse.event.toolUse.toolName;
            } else if (jsonResponse.event?.contentEnd && jsonResponse.event?.contentEnd?.type === 'TOOL') {
              // Process tool use
              const toolResult = await this.processToolUse(session.toolName, session.toolUseContent);
              
              // Send tool result
              this.sendToolResult(sessionId, session.toolUseId, toolResult);
              
              // Dispatch event about tool result
              this.dispatchEvent(sessionId, 'toolResult', {
                toolUseId: session.toolUseId,
                result: toolResult
              });
            } else if (jsonResponse.event?.contentEnd) {
              this.dispatchEvent(sessionId, 'contentEnd', jsonResponse.event.contentEnd);
            }
          } catch (e) {
            console.error(`[${sessionId}] Error parsing response:`, e);
          }
        }
      }

      console.log(`Response stream processing complete for session ${sessionId}`);
      this.dispatchEvent(sessionId, 'streamComplete', {
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      console.error(`Error processing response stream for session ${sessionId}: `, error);
      
      // Enhanced error handling with timeout detection
      this.handleSessionError(sessionId, error, 'responseStream');
    }
  }

  private async processToolUse(toolName: string, toolUseContent: object): Promise<Object> {
    try {
      console.log(`Processing tool: ${toolName}`);
      return await this.toolRegistry.processToolUse(toolName, toolUseContent);
    } catch (error) {
      console.error(`Error processing tool ${toolName}:`, error);
      throw error;
    }
  }

  // Add an event to a session's queue
  private addEventToSessionQueue(sessionId: string, event: any): void {
    const session = this.activeSessions.get(sessionId);
    if (!session || !session.isActive) return;

    this.updateSessionActivity(sessionId);
    session.queue.push(event);
    session.queueSignal.next();
  }

  // Set up initial events for a session
  private setupSessionStartEvent(sessionId: string): void {
    const session = this.activeSessions.get(sessionId);
    if (!session) return;

    this.addEventToSessionQueue(sessionId, {
      event: {
        sessionStart: {
          inferenceConfiguration: session.inferenceConfig
        }
      }
    });
  }

  public async setupPromptStartEvent(sessionId: string): Promise<void> {
    const session = this.activeSessions.get(sessionId);
    if (!session) return;

    // Get all available tools from the registry
    const tools = this.toolRegistry.getAllTools();

    this.addEventToSessionQueue(sessionId, {
      event: {
        promptStart: {
          promptName: session.promptName,
          textOutputConfiguration: {
            mediaType: "text/plain",
          },
          audioOutputConfiguration: DefaultAudioOutputConfiguration,
          toolUseOutputConfiguration: {
            mediaType: "application/json",
          },
          toolConfiguration: {
            tools: tools.map(tool => ({
              toolSpec: {
                name: tool.name,
                description: tool.description,
                inputSchema: {
                  json: tool.schema
                }
              }
            }))
          },
        },
      }
    });
    session.isPromptStartSent = true;
  }

  public async setupSystemPromptEvent(sessionId: string,
    textConfig: typeof DefaultTextConfiguration = DefaultTextConfiguration,
    systemPromptContent: string = DefaultSystemPrompt
  ): Promise<void> {
    const session = this.activeSessions.get(sessionId);
    if (!session) return;

    // Generate a unique content name for this text content
    const textContentId = `text-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    
    // Text content start
    this.addEventToSessionQueue(sessionId, {
      event: {
        contentStart: {
          promptName: session.promptName,
          contentName: textContentId,
          type: "TEXT",
          interactive: true,
          role: "SYSTEM",
          textInputConfiguration: textConfig,
        },
      }
    });

    // Text input content
    this.addEventToSessionQueue(sessionId, {
      event: {
        textInput: {
          promptName: session.promptName,
          contentName: textContentId,
          content: systemPromptContent,
        },
      }
    });

    // Text content end
    this.addEventToSessionQueue(sessionId, {
      event: {
        contentEnd: {
          promptName: session.promptName,
          contentName: textContentId,
        },
      }
    });
  }

  public async setupStartAudioEvent(
    sessionId: string,
    audioConfig: typeof DefaultAudioInputConfiguration = DefaultAudioInputConfiguration
  ): Promise<void> {
    const session = this.activeSessions.get(sessionId);
    if (!session) return;

    // Generate a unique content name for this audio content
    session.currentAudioContentId = `audio-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    
    this.addEventToSessionQueue(sessionId, {
      event: {
        contentStart: {
          promptName: session.promptName,
          contentName: session.currentAudioContentId,
          type: "AUDIO",
          interactive: true,
          role: "USER",
          audioInputConfiguration: audioConfig,
        },
      }
    });
    session.isAudioContentStartSent = true;
  }

  // Stream an audio chunk for a session
  public async streamAudioChunk(sessionId: string, audioData: Buffer): Promise<void> {
    const session = this.activeSessions.get(sessionId);

    if (!session || !session.isActive || !session.currentAudioContentId) {
      return;
    }

    // Convert audio to base64
    const base64Data = audioData.toString('base64');

    this.addEventToSessionQueue(sessionId, {
      event: {
        audioInput: {
          promptName: session.promptName,
          contentName: session.currentAudioContentId,
          content: base64Data,
        },
      }
    });
  }

  // Send tool result back to the model
  private async sendToolResult(sessionId: string, toolUseId: string, result: any): Promise<void> {
    const session = this.activeSessions.get(sessionId);
    if (!session || !session.isActive) return;

    // Generate a unique content name for this tool result
    const contentId = `tool-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

    // Tool content start
    this.addEventToSessionQueue(sessionId, {
      event: {
        contentStart: {
          promptName: session.promptName,
          contentName: contentId,
          interactive: false,
          type: "TOOL",
          role: "TOOL",
          toolResultInputConfiguration: {
            toolUseId: toolUseId,
            type: "TEXT",
            textInputConfiguration: {
              mediaType: "text/plain"
            }
          }
        }
      }
    });

    // Tool content input
    const resultContent = typeof result === 'string' ? result : JSON.stringify(result);
    this.addEventToSessionQueue(sessionId, {
      event: {
        toolResult: {
          promptName: session.promptName,
          contentName: contentId,
          content: resultContent
        }
      }
    });

    // Tool content end
    this.addEventToSessionQueue(sessionId, {
      event: {
        contentEnd: {
          promptName: session.promptName,
          contentName: contentId
        }
      }
    });
  }

  public async sendContentEnd(sessionId: string): Promise<void> {
    const session = this.activeSessions.get(sessionId);
    if (!session) {
      return;
    }

    // Enhanced cleanup with dummy content handling
    if (this.sessionCleanupInProgress.has(sessionId) &&
        (!session.isAudioContentStartSent || !session.currentAudioContentId)) {
      console.log(`sendContentEnd: Creating dummy content end for cleanup of session ${sessionId}`);

      // Generate a unique dummy content ID
      const dummyContentId = `dummy-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

      // Send a dummy content start event first
      this.addEventToSessionQueue(sessionId, {
        event: {
          contentStart: {
            promptName: session.promptName,
            contentName: dummyContentId,
            type: "TEXT",
            interactive: false,
            role: "SYSTEM",
            textInputConfiguration: {
              mediaType: "text/plain"
            }
          }
        }
      });

      // Wait a bit to ensure content start is processed
      await new Promise(resolve => setTimeout(resolve, 300));

      // Then send the content end event
      this.addEventToSessionQueue(sessionId, {
        event: {
          contentEnd: {
            promptName: session.promptName,
            contentName: dummyContentId,
          }
        }
      });

      return;
    }

    // Normal content end for active audio content
    if (!session.isAudioContentStartSent || !session.currentAudioContentId) {
      return;
    }

    this.addEventToSessionQueue(sessionId, {
      event: {
        contentEnd: {
          promptName: session.promptName,
          contentName: session.currentAudioContentId,
        }
      }
    });

    session.isAudioContentStartSent = false;
    session.currentAudioContentId = null;
  }

  public async sendPromptEnd(sessionId: string): Promise<void> {
    const session = this.activeSessions.get(sessionId);
    if (!session || !session.isPromptStartSent) return;

    this.addEventToSessionQueue(sessionId, {
      event: {
        promptEnd: {
          promptName: session.promptName
        }
      }
    });
  }

  public resetSessionState(sessionId: string): void {
    const session = this.activeSessions.get(sessionId);
    if (!session) {
      console.log(`Cannot reset session ${sessionId}: not found`);
      return;
    }

    console.log(`Resetting session state for ${sessionId}`);

    // Enhanced state reset with better state management
    const wasActive = session.isActive;
    
    // Reset session state flags
    session.isAudioContentStartSent = false;
    session.currentAudioContentId = null;
    session.isPromptStartSent = false;
    
    // Clear any pending tool use data
    session.toolUseContent = null;
    session.toolUseId = "";
    session.toolName = "";
    
    // Clear the event queue to prevent stale events
    session.queue = [];

    // Generate a new unique prompt name for the reset session
    const newPromptName = `${randomUUID()}-${Date.now()}`;
    console.log(`Generated new prompt name for reset session ${sessionId}: ${newPromptName} (was: ${session.promptName})`);
    session.promptName = newPromptName;

    // If session was active, keep it active and update activity
    if (wasActive) {
      session.isActive = true;
      this.updateSessionActivity(sessionId);
    }

    console.log(`Session ${sessionId} enhanced state reset complete, active: ${session.isActive}`);
  }

  public async sendSessionEnd(sessionId: string): Promise<void> {
    const session = this.activeSessions.get(sessionId);
    if (!session) return;

    this.addEventToSessionQueue(sessionId, {
      event: {
        sessionEnd: {}
      }
    });

    // Wait to ensure it's processed
    await new Promise(resolve => setTimeout(resolve, 300));

    // Clean up
    session.isActive = false;
    session.closeSignal.next();
    session.closeSignal.complete();
    this.activeSessions.delete(sessionId);
    this.sessionLastActivity.delete(sessionId);
    console.log(`Session ${sessionId} closed and removed from active sessions`);
  }

  // Register an event handler for a session
  public registerEventHandler(sessionId: string, eventType: string, handler: (data: any) => void): void {
    const session = this.activeSessions.get(sessionId);
    if (!session) {
      throw new Error(`Session ${sessionId} not found`);
    }
    session.responseHandlers.set(eventType, handler);
  }

  // Dispatch an event to registered handlers
  private dispatchEvent(sessionId: string, eventType: string, data: any): void {
    const session = this.activeSessions.get(sessionId);
    if (!session) return;

    const handler = session.responseHandlers.get(eventType);
    if (handler) {
      try {
        handler(data);
      } catch (e) {
        console.error(`Error in ${eventType} handler for session ${sessionId}:`, e);
      }
    }

    // Also dispatch to "any" handlers
    const anyHandler = session.responseHandlers.get('any');
    if (anyHandler) {
      try {
        anyHandler({ type: eventType, data });
      } catch (e) {
        console.error(`Error in 'any' handler for session ${sessionId}:`, e);
      }
    }
  }

  // Enhanced error handling with timeout detection for billing optimization
  private handleSessionError(sessionId: string, error: any, source: string): void {
    const session = this.activeSessions.get(sessionId);
    if (!session) return;

    const errorMessage = error?.message || error?.toString() || 'Unknown error';
    
    // Check for specific timeout patterns that indicate natural session timeout
    if (this.isNaturalTimeout(errorMessage)) {
      console.log(`Session ${sessionId} encountered natural timeout - this is expected behavior for billing optimization`);
      
      this.dispatchEvent(sessionId, 'sessionTimeout', {
        type: 'inactivityTimeout',
        message: 'Connection timed out due to inactivity',
        details: 'The session was idle for too long and has timed out naturally. This is expected behavior to optimize AWS billing costs.',
        timestamp: new Date().toISOString(),
        sessionId: sessionId,
        source: source
      });
    } else if (this.isConnectionError(errorMessage)) {
      console.log(`Session ${sessionId} encountered connection error`);
      
      this.dispatchEvent(sessionId, 'connectionError', {
        type: 'connectionError',
        message: 'Connection to AWS Bedrock was interrupted',
        details: errorMessage,
        timestamp: new Date().toISOString(),
        sessionId: sessionId,
        source: source,
        retryable: true
      });
    } else if (this.isAuthenticationError(errorMessage)) {
      console.log(`Session ${sessionId} encountered authentication error`);
      
      this.dispatchEvent(sessionId, 'authenticationError', {
        type: 'authenticationError',
        message: 'Authentication failed with AWS Bedrock',
        details: errorMessage,
        timestamp: new Date().toISOString(),
        sessionId: sessionId,
        source: source,
        retryable: false
      });
    } else {
      // Generic error handling for other types
      this.dispatchEvent(sessionId, 'error', {
        type: 'genericError',
        message: errorMessage,
        details: error,
        timestamp: new Date().toISOString(),
        sessionId: sessionId,
        source: source,
        error
      });
    }
  }

  // Check if error indicates natural timeout (billing optimization)
  private isNaturalTimeout(errorMessage: string): boolean {
    const timeoutPatterns = [
      'Timed out waiting for input events',
      'Session timed out',
      'Request timeout',
      'Connection timeout',
      'Stream timeout',
      'Inactive session timeout'
    ];
    
    return timeoutPatterns.some(pattern => 
      errorMessage.toLowerCase().includes(pattern.toLowerCase())
    );
  }

  // Check if error is connection related
  private isConnectionError(errorMessage: string): boolean {
    const connectionPatterns = [
      'ECONNRESET',
      'ECONNREFUSED', 
      'ENOTFOUND',
      'ETIMEDOUT',
      'Connection reset',
      'Network error',
      'Socket error'
    ];
    
    return connectionPatterns.some(pattern => 
      errorMessage.toLowerCase().includes(pattern.toLowerCase())
    );
  }

  // Check if error is authentication related
  private isAuthenticationError(errorMessage: string): boolean {
    const authPatterns = [
      'UnauthorizedOperation',
      'InvalidUserID.NotFound',
      'AuthFailure',
      'InvalidAccessKeyId',
      'SignatureDoesNotMatch',
      'TokenRefreshRequired',
      'ExpiredToken',
      'Access denied',
      'Forbidden'
    ];
    
    return authPatterns.some(pattern => 
      errorMessage.toLowerCase().includes(pattern.toLowerCase())
    );
  }


  public async closeSession(sessionId: string): Promise<void> {
    // Enhanced cleanup with tracking to prevent concurrent cleanup attempts
    if (this.sessionCleanupInProgress.has(sessionId)) {
      console.log(`Cleanup already in progress for session ${sessionId}, skipping`);
      return;
    }

    this.sessionCleanupInProgress.add(sessionId);

    try {
      console.log(`Starting enhanced close process for session ${sessionId}`);

      const session = this.activeSessions.get(sessionId);
      if (!session) {
        console.log(`Session ${sessionId} not found, skipping cleanup`);
        return;
      }

      console.log(`Closing session ${sessionId} with promptName: ${session.promptName}`);

      // Mark session as inactive
      session.isActive = false;

      // Enhanced cleanup sequence with timeouts
      try {
        // Use Promise.race to add timeouts to each step
        const cleanupPromise = Promise.race([
          (async () => {
            // First, reset session state to ensure clean state
            this.resetSessionState(sessionId);
            
            // Wait a bit to ensure state reset is processed
            await new Promise(resolve => setTimeout(resolve, 200));

            // End any active content
            await this.sendContentEnd(sessionId);
            
            // Wait to ensure content end is processed
            await new Promise(resolve => setTimeout(resolve, 300));

            // End the prompt
            await this.sendPromptEnd(sessionId);
            
            // Wait to ensure prompt end is processed
            await new Promise(resolve => setTimeout(resolve, 300));

            // Finally end the session
            await this.sendSessionEnd(sessionId);
          })(),
          new Promise((_, reject) =>
            setTimeout(() => reject(new Error('Enhanced cleanup timeout')), 5000)
          )
        ]);

        await cleanupPromise;
        console.log(`Enhanced cleanup sequence completed for session ${sessionId}`);
      } catch (error) {
        console.error(`Error during enhanced closing sequence for session ${sessionId}:`, error);
        // Force cleanup if enhanced sequence fails
        this.forceCloseSession(sessionId);
      }

      // Ensure cleanup happens
      this.activeSessions.delete(sessionId);
      console.log(`Session ${sessionId} enhanced cleanup complete`);
    } catch (error) {
      console.error(`Error during enhanced session close for ${sessionId}:`, error);
      this.activeSessions.delete(sessionId);
    } finally {
      // Always clean up the tracking set
      this.sessionCleanupInProgress.delete(sessionId);
    }
  }

  public forceCloseSession(sessionId: string): void {
    const session = this.activeSessions.get(sessionId);
    if (!session) return;

    console.log(`Force closing session ${sessionId} with promptName: ${session.promptName}`);

    // Immediately mark as inactive and clean up resources
    session.isActive = false;
    session.closeSignal.next();
    session.closeSignal.complete();
    this.activeSessions.delete(sessionId);
    this.sessionLastActivity.delete(sessionId);

    console.log(`Session ${sessionId} force closed`);
  }

  // Add method to clear all sessions (useful for debugging)
  public clearAllSessions(): void {
    console.log(`Clearing all ${this.activeSessions.size} active sessions`);
    
    const sessionIds = Array.from(this.activeSessions.keys());
    for (const sessionId of sessionIds) {
      this.forceCloseSession(sessionId);
    }
    
    // Also clear cleanup tracking
    this.sessionCleanupInProgress.clear();
    this.sessionLastActivity.clear();
    
    console.log('All sessions cleared');
  }

}
