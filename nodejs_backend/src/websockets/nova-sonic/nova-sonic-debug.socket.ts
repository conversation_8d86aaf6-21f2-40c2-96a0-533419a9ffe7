/**
 * Nova Sonic WebSocket handler - Debug version
 */

import { Server, Socket } from 'socket.io';
import { logger } from '../../common/logger';

export class NovaSonicSocketHandler {
  private namespace: any;

  constructor(private io: Server) {
    logger.info('Initializing Nova Sonic WebSocket handler (DEBUG MODE - NO BEDROCK)...');

    try {
      // Set up namespace
      this.setupNamespace();
      logger.info('Nova Sonic WebSocket handler initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize Nova Sonic WebSocket handler:', error);
      throw error;
    }
  }

  private setupNamespace(): void {
    try {
      // Create namespace with explicit configuration
      this.namespace = this.io.of('/ws/nova-sonic');

      // Log namespace creation
      logger.info(`Nova Sonic namespace created: /ws/nova-sonic`);
      logger.info(`Namespace adapter: ${this.namespace.adapter.constructor.name}`);

      // Add connection handler
      this.namespace.on('connection', (socket: Socket) => {
        this.handleConnection(socket);
      });

      // Add error handler for namespace
      this.namespace.on('error', (error: Error) => {
        logger.error('Nova Sonic namespace error:', error);
      });

    } catch (error) {
      logger.error('Error setting up Nova Sonic namespace:', error);
      throw error;
    }
  }

  private handleConnection(socket: Socket): void {
    try {
      logger.info(`Nova Sonic client connected: ${socket.id}`);
      logger.info(`Socket handshake:`, {
        address: socket.handshake.address,
        headers: socket.handshake.headers,
        query: socket.handshake.query,
        transport: (socket.conn as any).transport.name
      });

      // Send connection acknowledgment
      socket.emit('connected', {
        sessionId: socket.id,
        timestamp: new Date().toISOString(),
        message: 'Connected to Nova Sonic WebSocket'
      });

      // Set up basic event handlers for debugging
      socket.on('ping', () => {
        logger.debug(`[${socket.id}] Received ping`);
        socket.emit('pong');
      });

      // Health check handler - required by admin test page
      socket.on('health', () => {
        logger.info(`[${socket.id}] Health check received`);
        socket.emit('health-response', {
          status: 'healthy',
          timestamp: new Date().toISOString(),
          sessionId: socket.id
        });
      });

      socket.on('promptStart', () => {
        logger.info(`[${socket.id}] Prompt start received`);
        socket.emit('promptStarted');
      });

      socket.on('systemPrompt', (data) => {
        logger.info(`[${socket.id}] System prompt received:`, data);
        socket.emit('systemPromptSet');
      });

      socket.on('audioStart', () => {
        logger.info(`[${socket.id}] Audio start received`);
        socket.emit('audioStarted');
      });

      socket.on('audioInput', (data) => {
        try {
          logger.info(`[${socket.id}] Audio input received, type: ${typeof data}, length: ${data ? data.length : 0}`);

          // Validate that data is a string (base64)
          if (typeof data !== 'string') {
            throw new Error(`Invalid audio data type: ${typeof data}, expected string`);
          }

          // Try to decode base64 to ensure it's valid
          const buffer = Buffer.from(data, 'base64');
          logger.info(`[${socket.id}] Decoded audio buffer size: ${buffer.length} bytes`);

          // For now, just acknowledge receipt
          socket.emit('audioReceived', { bytes: buffer.length });
        } catch (error) {
          logger.error(`[${socket.id}] Error processing audio input:`, error);
          socket.emit('error', {
            message: 'Error processing audio input',
            details: error instanceof Error ? error.message : String(error)
          });
        }
      });

      socket.on('stopAudio', () => {
        logger.info(`[${socket.id}] Stop audio received`);
        socket.emit('sessionReadyForNewStream', {
          sessionId: socket.id,
          timestamp: new Date().toISOString()
        });
      });

      socket.on('disconnect', (reason) => {
        logger.info(`[${socket.id}] Client disconnected: ${reason}`);
      });

      socket.on('error', (error) => {
        logger.error(`[${socket.id}] Socket error:`, error);
      });

      // Log all events for debugging
      socket.onAny((eventName, ...args) => {
        logger.debug(`[${socket.id}] Event received: ${eventName}`, args);
      });

    } catch (error) {
      logger.error(`Error handling connection for ${socket.id}:`, error);
      socket.emit('error', {
        message: 'Failed to establish connection',
        details: error instanceof Error ? error.message : String(error)
      });
      socket.disconnect();
    }
  }
}
