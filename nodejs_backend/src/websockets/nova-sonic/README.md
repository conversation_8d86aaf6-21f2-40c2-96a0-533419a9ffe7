# Nova Sonic WebSocket Module

This module provides real-time voice streaming capabilities using Amazon Nova Sonic's bidirectional streaming API.

## Overview

Nova Sonic is a unified speech-to-speech foundation model that enables natural, human-like conversational AI experiences with low latency. This implementation provides a WebSocket interface for real-time voice interactions.

## Architecture

```
Client (React/Web) <--WebSocket--> Nova Sonic Handler <--AWS SDK--> Amazon Bedrock
```

## WebSocket Events

### Client → Server Events

- `audioInput` - Stream audio data (base64 encoded PCM)
- `promptStart` - Initialize a new prompt
- `systemPrompt` - Set the system prompt
- `audioStart` - Start audio streaming
- `stopAudio` - Stop audio streaming and finalize
- `textInput` - Send text message (non-voice)

### Server → Client Events

- `contentStart` - Content generation started
- `textOutput` - Text response from AI
- `audioOutput` - Audio response (base64 encoded)
- `toolUse` - Function call initiated
- `toolResult` - Function call result
- `contentEnd` - Content generation ended
- `streamComplete` - Stream fully processed
- `sessionReadyForNewStream` - Ready for new interaction
- `error` - Error occurred
- `sessionTimeout` - Session timed out

## Usage

### 1. Connect to WebSocket

```javascript
const socket = io('/ws/nova-sonic', {
  transports: ['websocket']
});
```

### 2. Initialize Session

```javascript
// Start a new prompt
socket.emit('promptStart');

// Set system prompt
socket.emit('systemPrompt', 'You are a helpful assistant.');

// Start audio streaming
socket.emit('audioStart');
```

### 3. Stream Audio

```javascript
// Send audio chunks as base64
socket.emit('audioInput', base64AudioData);
```

### 4. Handle Responses

```javascript
socket.on('textOutput', (data) => {
  console.log(`${data.role}: ${data.content}`);
});

socket.on('audioOutput', (data) => {
  // Play audio from data.content (base64)
});
```

## Audio Format

- **Input**: 16kHz, 16-bit PCM, mono, base64 encoded
- **Output**: 24kHz, 16-bit PCM, mono, base64 encoded

## Testing

Access the test interface at: `http://localhost:3000/admin/nova-sonic/test`

## Configuration

The module uses AWS credentials configured via:
- Environment variables: `AWS_ACCESS_KEY_ID`, `AWS_SECRET_ACCESS_KEY`
- AWS profile: Set via `AWS_PROFILE` or in config

## Features

- ✅ Real-time bidirectional audio streaming
- ✅ Session management with cleanup
- ✅ Tool/function calling support
- ✅ Automatic reconnection handling
- ✅ Audio queue management
- ✅ Barge-in support
- ✅ Text fallback for non-voice input

## Tools Available

Default tools included:
- `getDateAndTimeTool` - Get current date and time
- `getWeatherTool` - Get weather for coordinates

## Error Handling

The module includes comprehensive error handling:
- Session timeout management
- Network error recovery
- Audio processing errors
- AWS API errors

## Performance

- Low latency streaming (~1 second response time)
- Efficient audio compression
- Automatic session cleanup
- Connection pooling for multiple sessions
