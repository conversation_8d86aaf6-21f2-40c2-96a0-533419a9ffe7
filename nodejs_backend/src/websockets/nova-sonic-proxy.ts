/**
 * Nova Sonic Proxy Handler
 * Proxies WebSocket connections from main app to Nova Sonic microservice
 */

import { Server, Socket } from 'socket.io';
import { io as ioClient, Socket as ClientSocket } from 'socket.io-client';
import { logger } from '../common/logger';
import { validateAuth, extractSocketAuthContext } from '../middleware/unified-auth';

export interface NovaSonicProxyConfig {
  serviceUrl?: string;
  reconnectionAttempts?: number;
  reconnectionDelay?: number;
}

export class NovaSonicProxy {
  private connections: Map<string, ClientSocket> = new Map();
  private namespace: any;
  private serviceUrl: string;
  private config: NovaSonicProxyConfig;
  private sessionTracker: Map<string, {
    socketId: string;
    initialized: boolean;
    promptStarted: boolean;
    promptId?: string;
    initializationCount: number;
    promptStartCount: number;
    createdAt: Date;
    lastActivity: Date;
  }> = new Map();

  constructor(private io: Server, config?: NovaSonicProxyConfig) {
    this.config = {
      serviceUrl: process.env.NOVA_SONIC_SERVICE_URL || 'http://localhost:3005',
      reconnectionAttempts: 5,
      reconnectionDelay: 1000,
      ...config
    };
    
    this.serviceUrl = this.config.serviceUrl!;
    logger.info(`Nova Sonic Proxy initialized with service URL: ${this.serviceUrl}`);
    
    this.setupNamespace();
    this.checkServiceHealth();
  }

  private async checkServiceHealth() {
    try {
      const response = await fetch(`${this.serviceUrl}/health`);
      if (response.ok) {
        const health = await response.json();
        logger.info('Nova Sonic service health check passed:', health);
      } else {
        logger.error('Nova Sonic service health check failed:', response.status);
      }
    } catch (error) {
      logger.error('Nova Sonic service is not accessible:', error);
      logger.warn('Continuing without Nova Sonic service - connections will fail');
    }
  }

  private setupNamespace() {
    this.namespace = this.io.of('/ws/nova-sonic');
    
    // Add unified authentication middleware
    this.namespace.use(async (socket: Socket, next: any) => {
      const authContext = extractSocketAuthContext(socket);
      
      logger.info(`[PROXY] Authentication check for socket ${socket.id}:`, {
        hasAuth: !!authContext.token,
        token: authContext.token ? `${authContext.token.substring(0, 20)}...` : undefined,
        sessionAuth: authContext.session_auth,
        userId: authContext.user_id,
        role: authContext.role
      });
      
      const authResult = await validateAuth(authContext);
      
      if (authResult.success) {
        logger.info(`[PROXY] ✅ Authentication successful for socket ${socket.id}, userId: ${authResult.userId}, role: ${authResult.role}`);
        // Store auth info on socket for later use
        (socket as any).authInfo = {
          userId: authResult.userId,
          role: authResult.role
        };
        next();
      } else {
        logger.error(`[PROXY] ❌ Authentication failed for socket ${socket.id}: ${authResult.error}`);
        next(new Error(authResult.error || 'Authentication failed'));
      }
    });
    
    this.namespace.on('connection', (clientSocket: Socket) => {
      logger.info(`[PROXY] New client connection: ${clientSocket.id}`);
      
      // Create corresponding connection to Nova service
      const serviceSocket = ioClient(this.serviceUrl, {
        transports: ['websocket', 'polling'],
        reconnection: true,
        reconnectionAttempts: this.config.reconnectionAttempts,
        reconnectionDelay: this.config.reconnectionDelay,
      });
      
      this.connections.set(clientSocket.id, serviceSocket);
      
      // Initialize session tracking
      this.sessionTracker.set(clientSocket.id, {
        socketId: clientSocket.id,
        initialized: false,
        promptStarted: false,
        initializationCount: 0,
        promptStartCount: 0,
        createdAt: new Date(),
        lastActivity: new Date()
      });
      
      // Handle service connection
      serviceSocket.on('connect', () => {
        logger.info(`[PROXY] Connected to Nova service for client ${clientSocket.id}`);
        
        // Forward the connected event with modified message
        clientSocket.emit('connected', {
          sessionId: clientSocket.id,
          status: 'awaiting_initialization',
          message: 'Connected to Nova Sonic WebSocket via proxy. Send "initializeSession" to start voice streaming.',
          proxy: true
        });
      });
      
      // Handle service connection error
      serviceSocket.on('connect_error', (error) => {
        logger.error(`[PROXY] Service connection error for ${clientSocket.id}:`, error);
        clientSocket.emit('error', {
          message: 'Failed to connect to Nova Sonic service',
          details: error.message
        });
      });
      
      // Proxy all events from client to service
      clientSocket.onAny((event, ...args) => {
        if (event === 'disconnect') return; // Handle disconnect separately
        
        const session = this.sessionTracker.get(clientSocket.id);
        if (session) {
          session.lastActivity = new Date();
        }
        
        // Add detailed logging for debugging
        // logger.info(`[PROXY] Client->Service: ${event}`); // Commented out to reduce audioInput spam
        
        // Log specific events that might cause issues
        if (event === 'initializeSession') {
          if (session) {
            session.initializationCount++;
            if (session.initialized) {
              logger.warn(`[PROXY] ⚠️ DUPLICATE initializeSession - session already initialized! (count: ${session.initializationCount})`);
              // Don't forward duplicate initialization
              clientSocket.emit('error', {
                message: 'Session already initialized',
                type: 'duplicate_initialization'
              });
              return;
            }
            logger.info(`[PROXY] 🚀 initializeSession event received from client ${clientSocket.id} (count: ${session.initializationCount})`);
          }
        } else if (event === 'promptStart') {
          if (session) {
            session.promptStartCount++;
            if (session.promptStarted) {
              logger.error(`[PROXY] ❌ DUPLICATE promptStart - prompt already started! (count: ${session.promptStartCount})`);
              // Don't forward duplicate prompt start
              clientSocket.emit('error', {
                message: 'Prompt already started for this session',
                type: 'duplicate_prompt_start'
              });
              return;
            }
            logger.warn(`[PROXY] ⚠️ promptStart event received from client ${clientSocket.id} (count: ${session.promptStartCount})`);
          }
        } else if (event === 'systemPrompt') {
          logger.info(`[PROXY] systemPrompt event received from client ${clientSocket.id}`);
        } else if (event === 'audioStart') {
          logger.info(`[PROXY] 🎤 audioStart event received from client ${clientSocket.id}`);
        }
        
        serviceSocket.emit(event, ...args);
      });
      
      // Proxy all events from service to client
      serviceSocket.onAny((event, ...args) => {
        if (event === 'connect' || event === 'disconnect' || event === 'connect_error') return;
        
        const session = this.sessionTracker.get(clientSocket.id);
        
        // Add detailed logging for debugging
        if (event === 'sessionInitialized') {
          if (session) {
            session.initialized = true;
            session.promptId = args[0]?.sessionId || args[0]?.promptId;
          }
          logger.info(`[PROXY] ✅ sessionInitialized event from service to client ${clientSocket.id}`);
        } else if (event === 'promptStarted') {
          if (session) {
            session.promptStarted = true;
          }
          logger.info(`[PROXY] Service->Client: ${event}`);
        } else if (event === 'error') {
          logger.error(`[PROXY] ❌ error event from service to client ${clientSocket.id}:`, args[0]);
        } else if (event === 'systemPromptSet' || event === 'audioStarted') {
          logger.info(`[PROXY] Service->Client: ${event}`);
        } else {
          logger.debug(`[PROXY] Service->Client: ${event}`);
        }
        
        clientSocket.emit(event, ...args);
      });
      
      // Handle client disconnection
      clientSocket.on('disconnect', (reason) => {
        logger.info(`[PROXY] Client ${clientSocket.id} disconnected: ${reason}`);
        
        // Log session state before cleanup
        const session = this.sessionTracker.get(clientSocket.id);
        if (session) {
          logger.info(`[PROXY] Session cleanup for ${clientSocket.id}:`, {
            initialized: session.initialized,
            promptStarted: session.promptStarted,
            initializationCount: session.initializationCount,
            promptStartCount: session.promptStartCount,
            duration: Date.now() - session.createdAt.getTime()
          });
        }
        
        // Disconnect from service
        serviceSocket.disconnect();
        this.connections.delete(clientSocket.id);
        this.sessionTracker.delete(clientSocket.id);
      });
      
      // Handle service disconnection
      serviceSocket.on('disconnect', (reason) => {
        logger.info(`[PROXY] Service disconnected for client ${clientSocket.id}: ${reason}`);
        
        if (reason === 'io server disconnect') {
          // Server disconnected the socket, don't reconnect
          clientSocket.emit('error', {
            message: 'Nova Sonic service disconnected',
            details: 'The service terminated the connection'
          });
        }
        
        // Clean up if client is still connected
        if (clientSocket.connected) {
          clientSocket.disconnect();
        }
        this.connections.delete(clientSocket.id);
      });
    });
    
    logger.info('Nova Sonic proxy namespace configured');
  }

  /**
   * Get active proxy connections count
   */
  public getActiveConnections(): number {
    return this.connections.size;
  }

  /**
   * Get proxy status
   */
  public async getStatus() {
    try {
      const response = await fetch(`${this.serviceUrl}/health`);
      const serviceHealth = response.ok ? await response.json() : null;
      
      // Get session details
      const sessions = Array.from(this.sessionTracker.entries()).map(([id, session]) => ({
        socketId: id,
        initialized: session.initialized,
        promptStarted: session.promptStarted,
        initializationCount: session.initializationCount,
        promptStartCount: session.promptStartCount,
        createdAt: session.createdAt,
        lastActivity: session.lastActivity,
        duration: Date.now() - session.createdAt.getTime()
      }));
      
      return {
        proxy: 'active',
        activeConnections: this.connections.size,
        serviceUrl: this.serviceUrl,
        serviceHealth,
        sessions
      };
    } catch (error) {
      return {
        proxy: 'active',
        activeConnections: this.connections.size,
        serviceUrl: this.serviceUrl,
        serviceHealth: null,
        serviceError: error instanceof Error ? error.message : 'Unknown error',
        sessions: []
      };
    }
  }

  /**
   * Cleanup all connections
   */
  public cleanup() {
    logger.info('Cleaning up Nova Sonic proxy connections');
    
    for (const [clientId, serviceSocket] of this.connections) {
      serviceSocket.disconnect();
      this.connections.delete(clientId);
    }
  }
}
