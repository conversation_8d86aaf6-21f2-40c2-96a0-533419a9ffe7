import {
  BedrockRuntimeClient,
  BedrockRuntimeClientConfig,
  InvokeModelWithBidirectionalStreamCommand,
  InvokeModelWithBidirectionalStreamInput,
} from '@aws-sdk/client-bedrock-runtime';

import { NodeHttp2Handler, NodeHttp2HandlerOptions } from '@smithy/node-http-handler';
import { Provider } from '@smithy/types';
import { Buffer } from 'node:buffer';
import { randomUUID } from 'node:crypto';
import { firstValueFrom } from 'rxjs';
import { take } from 'rxjs/operators';

// Import modules from our refactored structure
import { SessionManager } from './session-manager';
import { EnhancedUuidGenerator } from './uuid-generator';
import { SessionErrorHandler } from './error-handler';
import { ToolHandler } from './tool-handlers';
import { InferenceConfig } from './types';
import {
  DefaultAudioInputConfiguration,
  DefaultAudioOutputConfiguration,
  DefaultSystemPrompt,
  DefaultTextConfiguration,
  DefaultToolSchema,
  WeatherToolSchema,
  UIMockupToolSchema,
  CodeReviewToolSchema,
} from './constants';

export interface NovaSonicBidirectionalStreamClientConfig {
  requestHandlerConfig?: NodeHttp2HandlerOptions | Provider<NodeHttp2HandlerOptions | void>;
  clientConfig: Partial<BedrockRuntimeClientConfig>;
  inferenceConfig?: InferenceConfig;
  sessionManager?: SessionManager;
  errorHandler?: SessionErrorHandler;
  toolHandler?: ToolHandler;
}

export class StreamSession {
  private audioBufferQueue: Buffer[] = [];
  private maxQueueSize = 200; // Maximum number of audio chunks to queue
  private isProcessingAudio = false;
  private isActive = true;
  private lastActivityTimestamp = Date.now();
  private inactivityTimer: NodeJS.Timeout | null = null;
  private readonly INACTIVITY_TIMEOUT_MS = 30000; // 30 seconds inactivity timeout

  constructor(
    private sessionId: string,
    private client: NovaSonicBidirectionalStreamClient,
  ) {
    // Start the inactivity timer when session is created
    this.startInactivityTimer();
  }

  // Register event handlers for this specific session
  public onEvent(eventType: string, handler: (data: any) => void): StreamSession {
    this.client.registerEventHandler(this.sessionId, eventType, handler);
    return this; // For chaining
  }

  public async setupPromptStart(): Promise<void> {
    this.client.setupPromptStartEvent(this.sessionId);
  }

  public async setupSystemPrompt(
    textConfig: typeof DefaultTextConfiguration = DefaultTextConfiguration,
    systemPromptContent: string = DefaultSystemPrompt,
  ): Promise<void> {
    this.client.setupSystemPromptEvent(this.sessionId, textConfig, systemPromptContent);
  }

  public async setupStartAudio(
    audioConfig: typeof DefaultAudioInputConfiguration = DefaultAudioInputConfiguration,
  ): Promise<void> {
    this.client.setupStartAudioEvent(this.sessionId, audioConfig);
  }

  // Starts or resets the inactivity timer
  private startInactivityTimer(): void {
    // Clear any existing timer
    if (this.inactivityTimer) {
      clearTimeout(this.inactivityTimer);
    }

    // Start a new timer
    this.inactivityTimer = setTimeout(() => {
      this.handleInactivityTimeout();
    }, this.INACTIVITY_TIMEOUT_MS);

    // Update the timestamp
    this.lastActivityTimestamp = Date.now();
  }

  // Handles what happens when inactivity timeout is reached
  private async handleInactivityTimeout(): Promise<void> {
    if (!this.isActive) return;

    const timeSinceLastActivity = Date.now() - this.lastActivityTimestamp;

    if (timeSinceLastActivity >= this.INACTIVITY_TIMEOUT_MS) {
      console.log(
        `Session ${this.sessionId} has been inactive for ${timeSinceLastActivity}ms, auto-closing audio content`,
      );

      try {
        // Automatically end audio content due to inactivity
        await this.endAudioContent();

        // Notify through event system that we had an inactivity timeout
        this.client.dispatchEvent(this.sessionId, 'inactivityTimeout', {
          lastActivityTime: this.lastActivityTimestamp,
          timeoutAfter: this.INACTIVITY_TIMEOUT_MS,
          message: 'Audio streaming automatically stopped due to inactivity',
        });
      } catch (error) {
        console.error(`Error handling inactivity timeout for session ${this.sessionId}:`, error);
      }
    }
  }

  // Update activity timestamp and reset timer
  private updateActivity(): void {
    this.lastActivityTimestamp = Date.now();
    this.startInactivityTimer();
  }

  // Stream audio for this session
  public async streamAudio(audioData: Buffer): Promise<void> {
    // Update activity timestamp since we received audio data
    this.updateActivity();

    // Check queue size to avoid memory issues
    if (this.audioBufferQueue.length >= this.maxQueueSize) {
      // Queue is full, drop oldest chunk
      this.audioBufferQueue.shift();
      console.log('Audio queue full, dropping oldest chunk');
    }

    // Queue the audio chunk for streaming
    this.audioBufferQueue.push(audioData);
    this.processAudioQueue();
  }

  // Process audio queue for continuous streaming
  private async processAudioQueue() {
    if (this.isProcessingAudio || this.audioBufferQueue.length === 0 || !this.isActive) return;

    this.isProcessingAudio = true;
    try {
      // Process all chunks in the queue, up to a reasonable limit
      let processedChunks = 0;
      const maxChunksPerBatch = 5; // Process max 5 chunks at a time to avoid overload

      while (
        this.audioBufferQueue.length > 0 &&
        processedChunks < maxChunksPerBatch &&
        this.isActive
      ) {
        const audioChunk = this.audioBufferQueue.shift();
        if (audioChunk) {
          await this.client.streamAudioChunk(this.sessionId, audioChunk);
          processedChunks++;
        }
      }
    } finally {
      this.isProcessingAudio = false;

      // If there are still items in the queue, schedule the next processing using setTimeout
      if (this.audioBufferQueue.length > 0 && this.isActive) {
        setTimeout(() => this.processAudioQueue(), 0);
      }
    }
  }

  // Get session ID
  public getSessionId(): string {
    return this.sessionId;
  }

  public async endAudioContent(): Promise<void> {
    if (!this.isActive) return;

    // Clear the audio buffer queue to prevent overflow on restart
    this.audioBufferQueue = [];
    this.isProcessingAudio = false;

    // Stop the inactivity timer
    if (this.inactivityTimer) {
      clearTimeout(this.inactivityTimer);
      this.inactivityTimer = null;
    }

    await this.client.sendContentEnd(this.sessionId);
  }

  // Reset the session state for reuse
  public resetSessionState(): void {
    // Generate new IDs for the next session
    this.client.resetSessionState(this.sessionId);
  }

  public async endPrompt(): Promise<void> {
    if (!this.isActive) return;
    await this.client.sendPromptEnd(this.sessionId);
  }

  public async sendTextInput(text: string): Promise<void> {
    if (!this.isActive) return;
    await this.client.sendTextInput(this.sessionId, text);
  }

  public async close(): Promise<void> {
    if (!this.isActive) return;

    this.isActive = false;
    this.audioBufferQueue = []; // Clear any pending audio

    // Clean up timers on close
    if (this.inactivityTimer) {
      clearTimeout(this.inactivityTimer);
      this.inactivityTimer = null;
    }

    await this.client.sendSessionEnd(this.sessionId);
    console.log(`Session ${this.sessionId} close completed`);
  }
}

// client.ts - PART 2
export class NovaSonicBidirectionalStreamClient {
  private bedrockRuntimeClient: BedrockRuntimeClient;
  private inferenceConfig: InferenceConfig;
  private sessionManager: SessionManager;
  private errorHandler: SessionErrorHandler;
  private toolHandler: ToolHandler;

  constructor(config: NovaSonicBidirectionalStreamClientConfig) {
    const nodeHttp2Handler = new NodeHttp2Handler({
      requestTimeout: 300000,
      sessionTimeout: 300000,
      disableConcurrentStreams: false,
      maxConcurrentStreams: 20,
      ...config.requestHandlerConfig,
    });

    if (!config.clientConfig.credentials) {
      throw new Error('No credentials provided');
    }

    this.bedrockRuntimeClient = new BedrockRuntimeClient({
      ...config.clientConfig,
      credentials: config.clientConfig.credentials,
      region: config.clientConfig.region || 'us-east-1',
      requestHandler: nodeHttp2Handler,
    });

    this.inferenceConfig = config.inferenceConfig ?? {
      maxTokens: 1024,
      topP: 0.9,
      temperature: 0.7,
    };
    this.sessionManager = config.sessionManager || new SessionManager();
    this.errorHandler = config.errorHandler || new SessionErrorHandler();
    this.toolHandler = config.toolHandler || new ToolHandler();
  }

  public isSessionActive(sessionId: string): boolean {
    return this.sessionManager.isSessionActive(sessionId);
  }

  public getActiveSessions(): string[] {
    return this.sessionManager.getActiveSessions();
  }

  public getLastActivityTime(sessionId: string): number {
    return this.sessionManager.getLastActivityTime(sessionId);
  }

  // Create a new streaming session
  public createStreamSession(
    sessionId: string = randomUUID(),
    config?: NovaSonicBidirectionalStreamClientConfig,
  ): StreamSession {
    const inferenceConfig = config?.inferenceConfig ?? this.inferenceConfig;
    this.sessionManager.createSession(sessionId, inferenceConfig);
    return new StreamSession(sessionId, this);
  }

  // Dispatch events to handlers for a specific session
  public dispatchEvent(sessionId: string, eventType: string, data: any): void {
    this.sessionManager.dispatchEvent(sessionId, eventType, data);
  }

  // Register an event handler for a session
  public registerEventHandler(
    sessionId: string,
    eventType: string,
    handler: (data: any) => void,
  ): void {
    this.sessionManager.registerEventHandler(sessionId, eventType, handler);
  }

  // Stream an audio chunk for a session
  public async streamAudioChunk(sessionId: string, audioData: Buffer): Promise<void> {
    const session = this.sessionManager.getSession(sessionId);
    if (!session || !session.isActive || !session.audioContentId) {
      throw new Error(`Invalid session ${sessionId} for audio streaming`);
    }

    // Convert audio to base64
    const base64Data = audioData.toString('base64');

    this.sessionManager.addEventToSessionQueue(sessionId, {
      event: {
        audioInput: {
          promptName: session.promptName,
          contentName: session.audioContentId,
          content: base64Data,
        },
      },
    });
  }

  // Initiates a session with AWS Bedrock
  public async initiateSession(sessionId: string): Promise<void> {
    const session = this.sessionManager.getSession(sessionId);
    if (!session) {
      throw new Error(`Stream session ${sessionId} not found`);
    }

    try {
      // Generate new UUIDs for the session to avoid duplicate prompt name errors
      session.promptName = EnhancedUuidGenerator.generatePromptId(sessionId);
      session.audioContentId = EnhancedUuidGenerator.generateAudioContentId(sessionId);
      console.log(`Generated new prompt name for session ${sessionId}: ${session.promptName}`);
      console.log(
        `Generated new audio content ID for session ${sessionId}: ${session.audioContentId}`,
      );

      // Set up initial events for this session
      this.setupSessionStartEvent(sessionId);

      // Create the bidirectional stream with session-specific async iterator
      const asyncIterable = this.createSessionAsyncIterable(sessionId);

      console.log(`Starting bidirectional stream for session ${sessionId}...`);

      const response = await this.bedrockRuntimeClient.send(
        new InvokeModelWithBidirectionalStreamCommand({
          modelId: 'amazon.nova-sonic-v1:0',
          body: asyncIterable,
        }),
      );

      console.log(`Stream established for session ${sessionId}, processing responses...`);

      // Process responses for this session
      await this.processResponseStream(sessionId, response);
    } catch (error) {
      console.error(`Error in session ${sessionId}: `, error);

      // Check if this is a duplicate prompt name error
      const errorMessage = error instanceof Error ? error.message : String(error);
      if (this.errorHandler.isDuplicatePromptNameError(errorMessage)) {
        console.log(
          `Detected duplicate prompt name error for session ${sessionId}, resetting session state`,
        );

        // Reset the session state to generate new UUIDs
        this.resetSessionState(sessionId);

        // Dispatch a more user-friendly error
        this.dispatchEvent(
          sessionId,
          'error',
          this.errorHandler.createDuplicatePromptNameErrorResponse(sessionId),
        );
      } else {
        // For other errors, dispatch the original error
        this.dispatchEvent(sessionId, 'error', {
          source: 'bidirectionalStream',
          error,
        });
      }

      // Make sure to clean up if there's an error
      if (session.isActive) {
        this.closeSession(sessionId);
      }
    }
  }

  private createSessionAsyncIterable(
    sessionId: string,
  ): AsyncIterable<InvokeModelWithBidirectionalStreamInput> {
    if (!this.isSessionActive(sessionId)) {
      console.log(`Cannot create async iterable: Session ${sessionId} not active`);
      return {
        [Symbol.asyncIterator]: () => ({
          next: async () => ({ value: undefined, done: true }),
        }),
      };
    }

    const session = this.sessionManager.getSession(sessionId);
    if (!session) {
      throw new Error(`Cannot create async iterable: Session ${sessionId} not found`);
    }

    return {
      [Symbol.asyncIterator]: () => {
        console.log(`AsyncIterable iterator requested for session ${sessionId}`);

        return {
          next: async (): Promise<IteratorResult<InvokeModelWithBidirectionalStreamInput>> => {
            try {
              // Check if session is still active
              if (!session.isActive || !this.isSessionActive(sessionId)) {
                console.log(`Iterator closing for session ${sessionId}, done = true`);
                return { value: undefined, done: true };
              }

              // Wait for items in the queue or close signal
              if (session.queue.length === 0) {
                try {
                  await Promise.race([
                    firstValueFrom(session.queueSignal.pipe(take(1))),
                    firstValueFrom(session.closeSignal.pipe(take(1))).then(() => {
                      throw new Error('Stream closed');
                    }),
                  ]);
                } catch (error) {
                  if (error instanceof Error) {
                    if (error.message === 'Stream closed' || !session.isActive) {
                      // This is an expected condition when closing the session
                      if (this.isSessionActive(sessionId)) {
                        console.log(`Session ${sessionId} closed during wait`);
                      }
                      return { value: undefined, done: true };
                    }
                  } else {
                    console.error(`Error on event close`, error);
                  }
                }
              }

              // If queue is still empty or session is inactive, we're done
              if (session.queue.length === 0 || !session.isActive) {
                console.log(`Queue empty or session inactive: ${sessionId} `);
                return { value: undefined, done: true };
              }

              // Get next item from the session's queue
              const nextEvent = session.queue.shift();

              return {
                value: {
                  chunk: {
                    bytes: new TextEncoder().encode(JSON.stringify(nextEvent)),
                  },
                },
                done: false,
              };
            } catch (error) {
              console.error(`Error in session ${sessionId} iterator: `, error);
              session.isActive = false;
              return { value: undefined, done: true };
            }
          },

          return: async (): Promise<IteratorResult<InvokeModelWithBidirectionalStreamInput>> => {
            console.log(`Iterator return () called for session ${sessionId}`);
            session.isActive = false;
            return { value: undefined, done: true };
          },

          throw: async (
            error: any,
          ): Promise<IteratorResult<InvokeModelWithBidirectionalStreamInput>> => {
            console.log(`Iterator throw () called for session ${sessionId} with error: `, error);
            session.isActive = false;
            throw error;
          },
        };
      },
    };
  }

  // Process the response stream from AWS Bedrock - simplified for duplicates error handling
  private async processResponseStream(sessionId: string, response: any): Promise<void> {
    const session = this.sessionManager.getSession(sessionId);
    if (!session) return;

    try {
      let duplicateNameErrorDetected = false;

      for await (const event of response.body) {
        if (!session.isActive) {
          console.log(`Session ${sessionId} is no longer active, stopping response processing`);
          break;
        }

        if (event.chunk?.bytes) {
          try {
            this.sessionManager.updateSessionActivity(sessionId);
            const textResponse = new TextDecoder().decode(event.chunk.bytes);

            try {
              const jsonResponse = JSON.parse(textResponse);
              // Handle the event appropriately through the session manager
              this.handleResponseEvent(sessionId, jsonResponse);
            } catch (e) {
              // Check for duplicate name error in the raw response
              if (textResponse.includes('Duplicate prompt name')) {
                duplicateNameErrorDetected = true;
                console.log(
                  `Detected duplicate prompt name error in raw response for session ${sessionId}`,
                );
                break; // Exit the loop to handle the error
              }
            }
          } catch (e) {
            const errorMessage = e instanceof Error ? e.message : String(e);
            if (this.errorHandler.isDuplicatePromptNameError(errorMessage)) {
              duplicateNameErrorDetected = true;
              break; // Exit the loop to handle the error
            }
          }
        } else if (event.modelStreamErrorException || event.internalServerException) {
          // Handle model errors
          this.dispatchEvent(sessionId, 'error', {
            type: event.modelStreamErrorException
              ? 'modelStreamErrorException'
              : 'internalServerException',
            details: event.modelStreamErrorException || event.internalServerException,
          });

          // Check for duplicate name error
          if (JSON.stringify(event).includes('Duplicate prompt name')) {
            duplicateNameErrorDetected = true;
            break;
          }
        }
      }

      // Handle duplicate name error if detected
      if (duplicateNameErrorDetected) {
        // Add delay before reset to ensure AWS has cleared previous context
        await new Promise((resolve) => setTimeout(resolve, 1000));
        this.resetSessionState(sessionId);
        this.dispatchEvent(
          sessionId,
          'error',
          this.errorHandler.createDuplicatePromptNameErrorResponse(sessionId),
        );
        return;
      }

      this.dispatchEvent(sessionId, 'streamComplete', {
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      // Handle unexpected errors
      const errorMessage = error instanceof Error ? error.message : String(error);

      // Check for duplicate prompt name errors
      if (this.errorHandler.isDuplicatePromptNameError(errorMessage)) {
        await new Promise((resolve) => setTimeout(resolve, 1000));
        this.resetSessionState(sessionId);
        this.dispatchEvent(
          sessionId,
          'error',
          this.errorHandler.createDuplicatePromptNameErrorResponse(sessionId),
        );
      } else {
        this.dispatchEvent(
          sessionId,
          'error',
          this.errorHandler.createGenericErrorResponse('responseStream', error),
        );
      }
    }
  }

  // Simplified event handler that delegates to appropriate modules
  private handleResponseEvent(sessionId: string, jsonResponse: any): void {
    if (!jsonResponse.event) return;

    const eventType = this.getEventType(jsonResponse.event);
    if (eventType) {
      this.dispatchEvent(sessionId, eventType, jsonResponse.event[eventType]);

      // Special handling for tool use events
      if (eventType === 'toolUse') {
        this.processToolUseEvent(sessionId, jsonResponse.event.toolUse);
      }
    }
  }

  // Helper to determine event type
  private getEventType(event: any): string | null {
    if (event.contentStart) return 'contentStart';
    if (event.textOutput) return 'textOutput';
    if (event.audioOutput) return 'audioOutput';
    if (event.toolUse) return 'toolUse';
    if (event.contentEnd) return 'contentEnd';

    // For other events, use the first available key
    const keys = Object.keys(event);
    return keys.length > 0 ? keys[0] : null;
  }

  // Process tool use - simply delegates to tool handler
  private async processToolUseEvent(sessionId: string, toolUseEvent: any): Promise<void> {
    const session = this.sessionManager.getSession(sessionId);
    if (!session) return;

    try {
      // Store basic info in session
      session.toolUseId = toolUseEvent.toolUseId;
      session.toolName = toolUseEvent.toolName;
      session.toolUseContent = toolUseEvent;

      // Let the tool handler module handle the rest
      if (toolUseEvent.type === 'TOOL_END') {
        const toolResult = await this.toolHandler.processToolUse(
          session.toolName,
          session.toolUseContent,
        );
        await this.sendToolResult(sessionId, session.toolUseId, toolResult);
      }
    } catch (error) {
      this.dispatchEvent(sessionId, 'error', {
        source: 'toolUse',
        message: 'Error processing tool use',
        details: error instanceof Error ? error.message : String(error),
      });
    }
  }

  // Core API methods implemented using the session manager
  public setupPromptStartEvent(sessionId: string): void {
    console.log(`Setting up prompt start event for session ${sessionId}...`);
    const session = this.sessionManager.getSession(sessionId);
    if (!session) return;

    // Prompt start event
    this.sessionManager.addEventToSessionQueue(sessionId, {
      event: {
        promptStart: {
          promptName: session.promptName,
          textOutputConfiguration: {
            mediaType: 'text/plain',
          },
          audioOutputConfiguration: DefaultAudioOutputConfiguration,
          toolUseOutputConfiguration: {
            mediaType: 'application/json',
          },
          toolConfiguration: {
            tools: [
              {
                toolSpec: {
                  name: 'getDateAndTimeTool',
                  description: 'Get information about the current date and time.',
                  inputSchema: {
                    json: DefaultToolSchema,
                  },
                },
              },
              {
                toolSpec: {
                  name: 'getWeatherTool',
                  description:
                    'Get the current weather for a given location, based on its WGS84 coordinates.',
                  inputSchema: {
                    json: WeatherToolSchema,
                  },
                },
              },
              {
                toolSpec: {
                  name: 'generateUIMockup',
                  description: 'Generate a UI mockup based on the description provided.',
                  inputSchema: {
                    json: UIMockupToolSchema,
                  },
                },
              },
              {
                toolSpec: {
                  name: 'performCodeReview',
                  description: 'Perform a code review on the specified file or file section.',
                  inputSchema: {
                    json: CodeReviewToolSchema,
                  },
                },
              },
            ],
          },
        },
      },
    });

    if (session) {
      session.isPromptStartSent = true;
    }
  }

  public setupSystemPromptEvent(
    sessionId: string,
    textConfig: typeof DefaultTextConfiguration = DefaultTextConfiguration,
    systemPromptContent: string = DefaultSystemPrompt,
  ): void {
    console.log(`Setting up systemPrompt events for session ${sessionId}...`);
    const session = this.sessionManager.getSession(sessionId);
    if (!session) return;

    // Text content start
    const textPromptID = EnhancedUuidGenerator.generateContentId(sessionId, 'system');
    this.sessionManager.addEventToSessionQueue(sessionId, {
      event: {
        contentStart: {
          promptName: session.promptName,
          contentName: textPromptID,
          type: 'TEXT',
          interactive: true,
          role: 'SYSTEM',
          textInputConfiguration: textConfig,
        },
      },
    });

    // Text input content
    this.sessionManager.addEventToSessionQueue(sessionId, {
      event: {
        textInput: {
          promptName: session.promptName,
          contentName: textPromptID,
          content: systemPromptContent,
        },
      },
    });

    // Text content end
    this.sessionManager.addEventToSessionQueue(sessionId, {
      event: {
        contentEnd: {
          promptName: session.promptName,
          contentName: textPromptID,
        },
      },
    });
  }

  public setupStartAudioEvent(
    sessionId: string,
    audioConfig: typeof DefaultAudioInputConfiguration = DefaultAudioInputConfiguration,
  ): void {
    console.log(`Setting up startAudioContent event for session ${sessionId}...`);
    const session = this.sessionManager.getSession(sessionId);
    if (!session) return;

    console.log(`Using audio content ID: ${session.audioContentId}`);
    // Audio content start
    this.sessionManager.addEventToSessionQueue(sessionId, {
      event: {
        contentStart: {
          promptName: session.promptName,
          contentName: session.audioContentId,
          type: 'AUDIO',
          interactive: true,
          role: 'USER',
          audioInputConfiguration: audioConfig,
        },
      },
    });

    if (session) {
      session.isAudioContentStartSent = true;
    }
    console.log(`Initial events setup complete for session ${sessionId}`);
  }

  public async sendTextInput(sessionId: string, text: string): Promise<void> {
    const session = this.sessionManager.getSession(sessionId);
    if (!session || !session.isActive) {
      throw new Error(`Invalid session ${sessionId} for text input`);
    }

    console.log(`Sending text input for session ${sessionId}: "${text}"`);

    // Create a unique content ID for this text input
    const textContentId = EnhancedUuidGenerator.generateContentId(sessionId, 'text');

    // Text content start
    this.sessionManager.addEventToSessionQueue(sessionId, {
      event: {
        contentStart: {
          promptName: session.promptName,
          contentName: textContentId,
          type: 'TEXT',
          interactive: true,
          role: 'USER',
          textInputConfiguration: DefaultTextConfiguration,
        },
      },
    });

    // Text input content
    this.sessionManager.addEventToSessionQueue(sessionId, {
      event: {
        textInput: {
          promptName: session.promptName,
          contentName: textContentId,
          content: text,
        },
      },
    });

    // Text content end
    this.sessionManager.addEventToSessionQueue(sessionId, {
      event: {
        contentEnd: {
          promptName: session.promptName,
          contentName: textContentId,
        },
      },
    });

    console.log(`Text input sent for session ${sessionId}`);
  }

  public async sendContentEnd(sessionId: string): Promise<void> {
    const session = this.sessionManager.getSession(sessionId);
    if (!session || !session.isAudioContentStartSent) return;

    this.sessionManager.addEventToSessionQueue(sessionId, {
      event: {
        contentEnd: {
          promptName: session.promptName,
          contentName: session.audioContentId,
        },
      },
    });

    // Wait to ensure it's processed
    await new Promise((resolve) => setTimeout(resolve, 500));
  }

  public async sendPromptEnd(sessionId: string): Promise<void> {
    const session = this.sessionManager.getSession(sessionId);
    if (!session || !session.isPromptStartSent) return;

    this.sessionManager.addEventToSessionQueue(sessionId, {
      event: {
        promptEnd: {
          promptName: session.promptName,
        },
      },
    });

    // Wait to ensure it's processed
    await new Promise((resolve) => setTimeout(resolve, 300));
  }

  public async sendSessionEnd(sessionId: string): Promise<void> {
    const session = this.sessionManager.getSession(sessionId);
    if (!session) return;

    this.sessionManager.addEventToSessionQueue(sessionId, {
      event: {
        sessionEnd: {},
      },
    });

    // Wait to ensure it's processed
    await new Promise((resolve) => setTimeout(resolve, 300));
  }

  public resetSessionState(sessionId: string): boolean {
    return this.sessionManager.resetSessionState(sessionId);
  }

  public closeSession(sessionId: string): void {
    this.sessionManager.closeSession(sessionId);
  }

  /**
   * Force close a session (alias for closeSession)
   * @param sessionId The session ID to force close
   */
  public forceCloseSession(sessionId: string): void {
    console.log(`Force closing session ${sessionId}`);
    this.sessionManager.forceCloseSession(sessionId);
  }

  // Handle sending tool results back to model
  private async sendToolResult(sessionId: string, toolUseId: string, result: any): Promise<void> {
    const session = this.sessionManager.getSession(sessionId);
    if (!session || !session.isActive) return;

    console.log(`Sending tool result for session ${sessionId}, tool use ID: ${toolUseId}`);
    const contentId = EnhancedUuidGenerator.generateContentId(sessionId, 'tool');

    // Tool content start
    this.sessionManager.addEventToSessionQueue(sessionId, {
      event: {
        contentStart: {
          promptName: session.promptName,
          contentName: contentId,
          interactive: false,
          type: 'TOOL',
          role: 'TOOL',
          toolResultInputConfiguration: {
            toolUseId: toolUseId,
            type: 'TEXT',
            textInputConfiguration: {
              mediaType: 'text/plain',
            },
          },
        },
      },
    });

    // Tool content input
    const resultContent = typeof result === 'string' ? result : JSON.stringify(result);
    this.sessionManager.addEventToSessionQueue(sessionId, {
      event: {
        toolResult: {
          promptName: session.promptName,
          contentName: contentId,
          content: resultContent,
        },
      },
    });

    // Tool content end
    this.sessionManager.addEventToSessionQueue(sessionId, {
      event: {
        contentEnd: {
          promptName: session.promptName,
          contentName: contentId,
        },
      },
    });

    console.log(`Tool result sent for session ${sessionId}`);
  }

  // Set up session start
  private setupSessionStartEvent(sessionId: string): void {
    console.log(`Setting up initial events for session ${sessionId}...`);
    const session = this.sessionManager.getSession(sessionId);
    if (!session) return;

    // Session start event
    this.sessionManager.addEventToSessionQueue(sessionId, {
      event: {
        sessionStart: {
          inferenceConfiguration: session.inferenceConfig,
        },
      },
    });
  }
}
