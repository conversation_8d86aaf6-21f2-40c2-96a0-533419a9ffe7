import { AudioType, AudioMediaType, TextMediaType } from './types';

export const DefaultInferenceConfiguration = {
  maxTokens: 1024,
  topP: 0.9,
  temperature: 0.7,
};

export const DefaultAudioInputConfiguration = {
  audioType: 'SPEECH' as AudioType,
  encoding: 'base64',
  mediaType: 'audio/lpcm' as AudioMediaType,
  sampleRateHertz: 16000,
  sampleSizeBits: 16,
  channelCount: 1,
};

export const DefaultToolSchema = JSON.stringify({
  type: 'object',
  properties: {},
  required: [],
});

export const WeatherToolSchema = JSON.stringify({
  type: 'object',
  properties: {
    latitude: {
      type: 'string',
      description: 'Geographical WGS84 latitude of the location.',
    },
    longitude: {
      type: 'string',
      description: 'Geographical WGS84 longitude of the location.',
    },
  },
  required: ['latitude', 'longitude'],
});

export const UIMockupToolSchema = JSON.stringify({
  type: 'object',
  properties: {
    description: {
      type: 'string',
      description: 'Description of the UI component to be created',
    },
    elementType: {
      type: 'string',
      description: 'Type of UI element (e.g., button, card, form, navbar, etc.)',
    },
    colorScheme: {
      type: 'string',
      description: 'Optional color scheme for the UI component',
    },
    size: {
      type: 'string',
      description: 'Optional size specification for the UI component',
    },
  },
  required: ['description', 'elementType'],
});

export const CodeReviewToolSchema = JSON.stringify({
  type: 'object',
  properties: {
    filePath: {
      type: 'string',
      description: 'Path to the file to be reviewed',
    },
    focusArea: {
      type: 'string',
      description:
        "Optional specific area to focus on (e.g., 'performance', 'security', 'maintainability')",
    },
    startLine: {
      type: 'number',
      description: 'Optional starting line number for partial file review',
    },
    endLine: {
      type: 'number',
      description: 'Optional ending line number for partial file review',
    },
  },
  required: ['filePath'],
});

export const DefaultTextConfiguration = { mediaType: 'text/plain' as TextMediaType };

export const DefaultSystemPrompt =
  'You are KAPI, an AI assistant specializing in software development. ' +
  'You help with voice-based coding tasks like creating UI mockups and performing code reviews. ' +
  'Keep your responses concise and focused on the task at hand. ' +
  'For UI mockups, ask clarifying questions about design preferences. ' +
  'For code reviews, provide specific, actionable feedback.';

export const DefaultAudioOutputConfiguration = {
  ...DefaultAudioInputConfiguration,
  sampleRateHertz: 24000,
  voiceId: 'tiffany',
};
