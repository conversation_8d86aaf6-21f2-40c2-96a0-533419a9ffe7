// tool-handlers.ts
// Implementation of Nova Sonic tool handlers

import axios from 'axios';
import https from 'https';
import * as fs from 'fs/promises';

/**
 * Handles tool execution for Nova Sonic
 */
export class ToolHandler {
  /**
   * Process a tool use request and execute the appropriate tool
   * @param toolName Name of the tool to use
   * @param toolUseContent The content/parameters for the tool
   * @returns Result of the tool execution
   */
  public async processToolUse(toolName: string, toolUseContent: any): Promise<any> {
    const tool = toolName.toLowerCase();
    console.log(`Processing tool use for: ${tool}`);
    console.log(`Tool content: ${JSON.stringify(toolUseContent)}`);

    switch (tool) {
      case 'getdateandtimetool':
        return this.getDateAndTime();

      case 'getweathertool':
        console.log(`Weather tool called`);
        const weatherContent = await this.parseToolUseContentForWeather(toolUseContent);
        if (!weatherContent) {
          throw new Error('Weather content parsing failed');
        }
        return this.fetchWeatherData(weatherContent.latitude, weatherContent.longitude);

      case 'generateuimockup':
        console.log(`UI Mockup tool called`);
        const uiContent = await this.parseToolUseContentForUIMockup(toolUseContent);
        if (!uiContent) {
          throw new Error('UI mockup content parsing failed');
        }
        return this.generateUIMockup(uiContent);

      case 'performcodereview':
        console.log(`Code Review tool called`);
        const codeReviewContent = await this.parseToolUseContentForCodeReview(toolUseContent);
        if (!codeReviewContent) {
          throw new Error('Code review content parsing failed');
        }
        return this.performCodeReview(codeReviewContent);

      default:
        console.log(`Tool ${tool} not supported`);
        throw new Error(`Tool ${tool} not supported`);
    }
  }

  /**
   * Get the current date and time
   * @returns Date and time information
   */
  private getDateAndTime(): any {
    const date = new Date().toLocaleString('en-US', { timeZone: 'America/Los_Angeles' });
    const pstDate = new Date(date);
    return {
      date: pstDate.toISOString().split('T')[0],
      year: pstDate.getFullYear(),
      month: pstDate.getMonth() + 1,
      day: pstDate.getDate(),
      dayOfWeek: pstDate.toLocaleString('en-US', { weekday: 'long' }).toUpperCase(),
      timezone: 'PST',
      formattedTime: pstDate.toLocaleTimeString('en-US', {
        hour12: true,
        hour: '2-digit',
        minute: '2-digit',
      }),
    };
  }

  /**
   * Parse the content for weather tool
   * @param toolUseContent The content to parse
   * @returns Parsed latitude and longitude
   */
  private async parseToolUseContentForWeather(
    toolUseContent: any,
  ): Promise<{ latitude: number; longitude: number } | null> {
    try {
      // Check if the content field exists and is a string
      if (toolUseContent && typeof toolUseContent.content === 'string') {
        // Parse the JSON string into an object
        const parsedContent = JSON.parse(toolUseContent.content);
        console.log(`Weather parsed content: ${JSON.stringify(parsedContent)}`);
        // Return the parsed content
        return {
          latitude: parseFloat(parsedContent.latitude),
          longitude: parseFloat(parsedContent.longitude),
        };
      }
      return null;
    } catch (error) {
      console.error('Failed to parse weather tool content:', error);
      return null;
    }
  }

  /**
   * Parse the content for UI mockup tool
   * @param toolUseContent The content to parse
   * @returns Parsed UI mockup parameters
   */
  private async parseToolUseContentForUIMockup(toolUseContent: any): Promise<{
    description: string;
    elementType: string;
    colorScheme?: string;
    size?: string;
  } | null> {
    try {
      // Check if the content field exists and is a string
      if (toolUseContent && typeof toolUseContent.content === 'string') {
        // Parse the JSON string into an object
        const parsedContent = JSON.parse(toolUseContent.content);
        console.log(`UI Mockup parsed content: ${JSON.stringify(parsedContent)}`);
        // Return the parsed content
        return {
          description: parsedContent.description,
          elementType: parsedContent.elementType,
          colorScheme: parsedContent.colorScheme,
          size: parsedContent.size,
        };
      }
      return null;
    } catch (error) {
      console.error('Failed to parse UI mockup tool content:', error);
      return null;
    }
  }

  /**
   * Parse the content for code review tool
   * @param toolUseContent The content to parse
   * @returns Parsed code review parameters
   */
  private async parseToolUseContentForCodeReview(toolUseContent: any): Promise<{
    filePath: string;
    focusArea?: string;
    startLine?: number;
    endLine?: number;
  } | null> {
    try {
      // Check if the content field exists and is a string
      if (toolUseContent && typeof toolUseContent.content === 'string') {
        // Parse the JSON string into an object
        const parsedContent = JSON.parse(toolUseContent.content);
        console.log(`Code Review parsed content: ${JSON.stringify(parsedContent)}`);
        // Return the parsed content
        return {
          filePath: parsedContent.filePath,
          focusArea: parsedContent.focusArea,
          startLine: parsedContent.startLine ? parseInt(parsedContent.startLine, 10) : undefined,
          endLine: parsedContent.endLine ? parseInt(parsedContent.endLine, 10) : undefined,
        };
      }
      return null;
    } catch (error) {
      console.error('Failed to parse code review tool content:', error);
      return null;
    }
  }

  /**
   * Fetch weather data from Open Meteo API
   * @param latitude The latitude
   * @param longitude The longitude
   * @returns Weather data
   */
  private async fetchWeatherData(
    latitude: number,
    longitude: number,
  ): Promise<Record<string, any>> {
    const ipv4Agent = new https.Agent({ family: 4 });
    const url = `https://api.open-meteo.com/v1/forecast?latitude=${latitude}&longitude=${longitude}&current_weather=true`;

    try {
      const response = await axios.get(url, {
        httpsAgent: ipv4Agent,
        timeout: 5000,
        headers: {
          'User-Agent': 'MyApp/1.0',
          Accept: 'application/json',
        },
      });
      const weatherData = response.data;
      console.log('weatherData:', weatherData);

      return {
        weather_data: weatherData,
      };
    } catch (error) {
      if (axios.isAxiosError(error)) {
        console.error(`Error fetching weather data: ${error.message}`, error);
      } else {
        console.error(
          `Unexpected error: ${error instanceof Error ? error.message : String(error)} `,
          error,
        );
      }
      throw error;
    }
  }

  /**
   * Generate a UI mockup
   * @param content The UI mockup parameters
   * @returns UI mockup data
   */
  private async generateUIMockup(content: {
    description: string;
    elementType: string;
    colorScheme?: string;
    size?: string;
  }): Promise<Record<string, any>> {
    console.log('Generating UI mockup for:', content);

    try {
      // For MVP, we'll create a simple SVG mockup based on the element type
      // In a real implementation, this would call an external service or AI model
      const elementType = content.elementType.toLowerCase();
      const description = content.description;
      const colorScheme = content.colorScheme || 'blue';
      const size = content.size || 'medium';

      // Generate a simple SVG mockup
      let svgContent = '';
      let mockupTitle = '';

      // Generate different SVGs based on element type
      switch (elementType) {
        case 'button':
          mockupTitle = `Button Mockup: ${description}`;
          svgContent = this.generateButtonSVG(description, colorScheme, size);
          break;
        case 'card':
          mockupTitle = `Card Mockup: ${description}`;
          svgContent = this.generateCardSVG(description, colorScheme, size);
          break;
        case 'form':
          mockupTitle = `Form Mockup: ${description}`;
          svgContent = this.generateFormSVG(description, colorScheme, size);
          break;
        case 'navbar':
          mockupTitle = `Navbar Mockup: ${description}`;
          svgContent = this.generateNavbarSVG(description, colorScheme, size);
          break;
        default:
          mockupTitle = `Generic UI Mockup: ${description}`;
          svgContent = this.generateGenericSVG(description, colorScheme, size);
      }

      // Return the mockup data
      return {
        success: true,
        mockupTitle,
        svgContent,
        details: {
          elementType,
          description,
          colorScheme,
          size,
        },
      };
    } catch (error) {
      console.error('Error generating UI mockup:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
      };
    }
  }

  /**
   * Generate a button SVG
   */
  private generateButtonSVG(description: string, colorScheme: string, size: string): string {
    // Determine dimensions based on size
    let width = 120;
    let height = 40;
    let fontSize = 14;

    if (size === 'small') {
      width = 80;
      height = 30;
      fontSize = 12;
    } else if (size === 'large') {
      width = 160;
      height = 50;
      fontSize = 16;
    }

    // Determine colors based on color scheme
    let fillColor = '#3B82F6'; // Default blue
    let textColor = '#FFFFFF';

    if (colorScheme === 'red') {
      fillColor = '#EF4444';
    } else if (colorScheme === 'green') {
      fillColor = '#10B981';
    } else if (colorScheme === 'dark') {
      fillColor = '#1F2937';
    } else if (colorScheme === 'light') {
      fillColor = '#F3F4F6';
      textColor = '#1F2937';
    }

    // Truncate description if too long
    const buttonText = description.length > 20 ? description.substring(0, 17) + '...' : description;

    return `<svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
      <rect width="${width}" height="${height}" rx="6" fill="${fillColor}" />
      <text x="${width / 2}" y="${height / 2 + fontSize / 3}" font-family="Arial" font-size="${fontSize}" fill="${textColor}" text-anchor="middle">${buttonText}</text>
    </svg>`;
  }

  /**
   * Generate a card SVG
   */
  private generateCardSVG(description: string, colorScheme: string, size: string): string {
    // Determine dimensions based on size
    let width = 300;
    let height = 200;
    let fontSize = 16;

    if (size === 'small') {
      width = 200;
      height = 150;
      fontSize = 14;
    } else if (size === 'large') {
      width = 400;
      height = 300;
      fontSize = 18;
    }

    // Determine colors based on color scheme
    let borderColor = '#E5E7EB';
    let bgColor = '#FFFFFF';
    let textColor = '#1F2937';
    let headerColor = '#3B82F6';

    if (colorScheme === 'dark') {
      borderColor = '#374151';
      bgColor = '#1F2937';
      textColor = '#F9FAFB';
      headerColor = '#4B5563';
    } else if (colorScheme === 'red') {
      headerColor = '#EF4444';
    } else if (colorScheme === 'green') {
      headerColor = '#10B981';
    }

    // Split description into title and content
    let title = description;
    let content = 'Card content goes here';

    if (description.includes(':')) {
      const parts = description.split(':');
      title = parts[0].trim();
      content = parts[1].trim();
    }

    return `<svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
      <rect width="${width}" height="${height}" rx="8" fill="${bgColor}" stroke="${borderColor}" stroke-width="2" />
      <rect width="${width}" height="40" rx="8 8 0 0" fill="${headerColor}" />
      <text x="15" y="25" font-family="Arial" font-size="${fontSize}" font-weight="bold" fill="white">${title}</text>
      <text x="15" y="70" font-family="Arial" font-size="${fontSize - 2}" fill="${textColor}">${content}</text>
    </svg>`;
  }

  /**
   * Generate a form SVG
   */
  private generateFormSVG(description: string, colorScheme: string, size: string): string {
    // Implementation of form SVG generation
    // ... (keeping implementation brief for artifact size constraints)
    return `<svg width="400" height="300" xmlns="http://www.w3.org/2000/svg">
      <rect width="400" height="300" rx="8" fill="#FFFFFF" stroke="#E5E7EB" stroke-width="2" />
      <text x="200" y="30" font-family="Arial" font-size="18" font-weight="bold" fill="#1F2937" text-anchor="middle">${description}</text>
      <!-- Form fields -->
      <text x="20" y="70" font-family="Arial" font-size="14" fill="#1F2937">Name:</text>
      <rect x="20" y="80" width="360" height="30" rx="4" fill="#F3F4F6" stroke="#E5E7EB" />
      <text x="20" y="140" font-family="Arial" font-size="14" fill="#1F2937">Email:</text>
      <rect x="20" y="150" width="360" height="30" rx="4" fill="#F3F4F6" stroke="#E5E7EB" />
      <!-- Submit button -->
      <rect x="280" y="250" width="100" height="30" rx="4" fill="#3B82F6" />
      <text x="330" y="270" font-family="Arial" font-size="14" fill="white" text-anchor="middle">Submit</text>
    </svg>`;
  }

  /**
   * Generate a navbar SVG
   */
  private generateNavbarSVG(description: string, colorScheme: string, size: string): string {
    // Implementation of navbar SVG generation
    // ... (keeping implementation brief for artifact size constraints)
    return `<svg width="800" height="60" xmlns="http://www.w3.org/2000/svg">
      <rect width="800" height="60" fill="#3B82F6" />
      <text x="20" y="35" font-family="Arial" font-size="20" font-weight="bold" fill="white">${description}</text>
      <!-- Navigation items -->
      <text x="480" y="35" font-family="Arial" font-size="16" fill="white">Home</text>
      <text x="560" y="35" font-family="Arial" font-size="16" fill="white">Features</text>
      <text x="660" y="35" font-family="Arial" font-size="16" fill="white">Pricing</text>
      <text x="740" y="35" font-family="Arial" font-size="16" fill="white">Login</text>
    </svg>`;
  }

  /**
   * Generate a generic SVG
   */
  private generateGenericSVG(description: string, colorScheme: string, size: string): string {
    // Implementation of generic SVG generation
    // ... (keeping implementation brief for artifact size constraints)
    return `<svg width="400" height="300" xmlns="http://www.w3.org/2000/svg">
      <rect width="400" height="300" rx="8" fill="#FFFFFF" stroke="#E5E7EB" stroke-width="2" />
      <rect x="20" y="20" width="360" height="50" rx="4" fill="#3B82F6" />
      <text x="200" y="50" font-family="Arial" font-size="18" font-weight="bold" fill="white" text-anchor="middle">${description}</text>
      <!-- Placeholder content -->
      <rect x="20" y="90" width="360" height="190" rx="4" fill="#FFFFFF" stroke="#E5E7EB" stroke-width="1" stroke-dasharray="5,5" />
      <text x="200" y="180" font-family="Arial" font-size="16" fill="#1F2937" text-anchor="middle">UI Content Area</text>
    </svg>`;
  }

  /**
   * Perform a code review
   * @param content The code review parameters
   * @returns Code review results
   */
  private async performCodeReview(content: {
    filePath: string;
    focusArea?: string;
    startLine?: number;
    endLine?: number;
  }): Promise<Record<string, any>> {
    console.log('Performing code review for:', content);

    try {
      const { filePath, focusArea, startLine, endLine } = content;

      // Validate file path and check if file exists
      try {
        await fs.access(filePath);
      } catch (error) {
        return {
          success: false,
          error: `File not found: ${filePath}`,
        };
      }

      // Read file content
      const fileContent = await fs.readFile(filePath, 'utf8');
      const allLines = fileContent.split('\n');

      // Determine lines to review
      const start = startLine ? Math.max(0, startLine - 1) : 0;
      const end = endLine ? Math.min(allLines.length, endLine) : allLines.length;

      const codeToReview = allLines.slice(start, end).join('\n');

      // Analyze the code
      const fileExtension = filePath.split('.').pop()?.toLowerCase();
      const languageType = this.determineLanguageType(fileExtension || '');

      // Perform basic analysis
      const analysisResults = this.analyzeCode(codeToReview, languageType, focusArea);

      return {
        success: true,
        filePath,
        language: languageType,
        focusArea: focusArea || 'general',
        lineRange: {
          start: start + 1, // Convert back to 1-based for display
          end,
        },
        analysis: analysisResults,
      };
    } catch (error) {
      console.error('Error performing code review:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
      };
    }
  }

  /**
   * Determine language type from file extension
   */
  private determineLanguageType(fileExtension: string): string {
    const extensionMap: Record<string, string> = {
      js: 'JavaScript',
      jsx: 'JavaScript (React)',
      ts: 'TypeScript',
      tsx: 'TypeScript (React)',
      py: 'Python',
      java: 'Java',
      rb: 'Ruby',
      go: 'Go',
      php: 'PHP',
      c: 'C',
      cpp: 'C++',
      cs: 'C#',
      html: 'HTML',
      css: 'CSS',
      scss: 'SCSS',
      json: 'JSON',
      md: 'Markdown',
      sql: 'SQL',
      sh: 'Shell',
      yaml: 'YAML',
      yml: 'YAML',
      xml: 'XML',
    };

    return extensionMap[fileExtension] || 'Unknown';
  }

  /**
   * Analyze code for issues
   */
  private analyzeCode(code: string, language: string, focusArea?: string): Record<string, any> {
    // Simplified version of the code analysis
    // ... (keeping implementation brief for artifact size constraints)

    return {
      codeMetrics: {
        totalLines: code.split('\n').length,
        nonEmptyLines: code.split('\n').filter((line) => line.trim() !== '').length,
        commentLines: this.countCommentLines(code, language),
        averageLineLength: Math.round(
          code.split('\n').reduce((acc, line) => acc + line.length, 0) / code.split('\n').length,
        ),
      },
      issues: [],
      recommendations: [],
    };
  }

  /**
   * Count comment lines in code
   */
  private countCommentLines(code: string, language: string): number {
    // Simplified comment counting logic
    // ... (keeping implementation brief for artifact size constraints)
    return 0;
  }
}
