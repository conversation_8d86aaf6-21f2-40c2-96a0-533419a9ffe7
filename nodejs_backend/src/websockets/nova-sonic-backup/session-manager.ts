// session-manager.ts
// Manages session state and events for Nova Sonic bidirectional streaming

import { Subject } from 'rxjs';
import { randomUUID } from 'node:crypto';
import { SessionData, SessionStatus } from './session-data';
import { InferenceConfig } from './types';
import { EnhancedUuidGenerator } from './uuid-generator';

/**
 * Manages session state and events for Nova Sonic bidirectional streaming
 */
export class SessionManager {
  private activeSessions: Map<string, SessionData> = new Map();
  private sessionActivityTimes: Map<string, number> = new Map();
  private readonly SESSION_TIMEOUT_MS = 5 * 60 * 1000; // 5 minutes

  constructor() {
    console.log('SessionManager initialized');
  }

  /**
   * Check if a session is active
   * @param sessionId The session ID to check
   * @returns True if the session is active
   */
  public isSessionActive(sessionId: string): boolean {
    const session = this.activeSessions.get(sessionId);
    return !!session && session.isActive;
  }

  /**
   * Get a list of all active session IDs
   * @returns Array of active session IDs
   */
  public getActiveSessions(): string[] {
    return Array.from(this.activeSessions.keys()).filter((sessionId) =>
      this.isSessionActive(sessionId),
    );
  }

  /**
   * Get the last activity time for a session
   * @param sessionId The session ID
   * @returns Timestamp of last activity or 0 if session not found
   */
  public getLastActivityTime(sessionId: string): number {
    return this.sessionActivityTimes.get(sessionId) || 0;
  }

  /**
   * Update the activity timestamp for a session
   * @param sessionId The session ID
   */
  public updateSessionActivity(sessionId: string): void {
    this.sessionActivityTimes.set(sessionId, Date.now());
  }

  /**
   * Create a new session
   * @param sessionId The session ID
   * @param inferenceConfig Configuration for inference
   */
  public createSession(sessionId: string, inferenceConfig: InferenceConfig): void {
    console.log(`Creating session ${sessionId}`);

    const session: SessionData = {
      queue: [],
      queueSignal: new Subject<void>(),
      closeSignal: new Subject<void>(),
      responseSubject: new Subject<any>(),
      toolUseContent: null,
      toolUseId: '',
      toolName: '',
      responseHandlers: new Map(),
      promptName: EnhancedUuidGenerator.generatePromptId(sessionId),
      inferenceConfig: inferenceConfig,
      isActive: true,
      isPromptStartSent: false,
      isAudioContentStartSent: false,
      audioContentId: EnhancedUuidGenerator.generateAudioContentId(sessionId),
    };

    this.activeSessions.set(sessionId, session);
    this.updateSessionActivity(sessionId);
    console.log(`Session ${sessionId} created with prompt name ${session.promptName}`);
  }

  /**
   * Get a session by ID
   * @param sessionId The session ID
   * @returns The session data or undefined if not found
   */
  public getSession(sessionId: string): SessionData | undefined {
    return this.activeSessions.get(sessionId);
  }

  /**
   * Add an event to a session's queue
   * @param sessionId The session ID
   * @param event The event to add
   */
  public addEventToSessionQueue(sessionId: string, event: any): void {
    const session = this.activeSessions.get(sessionId);
    if (!session || !session.isActive) {
      console.log(`Cannot add event to inactive session ${sessionId}`);
      return;
    }

    session.queue.push(event);
    session.queueSignal.next();
    this.updateSessionActivity(sessionId);
  }

  /**
   * Dispatch an event to registered handlers
   * @param sessionId The session ID
   * @param eventType The event type
   * @param data The event data
   */
  public dispatchEvent(sessionId: string, eventType: string, data: any): void {
    const session = this.activeSessions.get(sessionId);
    if (!session) {
      console.log(`Cannot dispatch event: Session ${sessionId} not found`);
      return;
    }

    const handler = session.responseHandlers.get(eventType);
    if (handler) {
      try {
        handler(data);
      } catch (error) {
        console.error(`Error in event handler for ${eventType}:`, error);
      }
    }

    this.updateSessionActivity(sessionId);
  }

  /**
   * Register an event handler for a session
   * @param sessionId The session ID
   * @param eventType The event type
   * @param handler The handler function
   */
  public registerEventHandler(
    sessionId: string,
    eventType: string,
    handler: (data: any) => void,
  ): void {
    const session = this.activeSessions.get(sessionId);
    if (!session) {
      console.log(`Cannot register handler: Session ${sessionId} not found`);
      return;
    }

    session.responseHandlers.set(eventType, handler);
    this.updateSessionActivity(sessionId);
  }

  /**
   * Reset a session's state with new IDs
   * @param sessionId The session ID
   * @returns True if reset was successful
   */
  public resetSessionState(sessionId: string): boolean {
    const session = this.activeSessions.get(sessionId);
    if (!session) {
      console.log(`Cannot reset session state: Session ${sessionId} not found`);
      return false;
    }

    // Generate new IDs to avoid duplicate prompt name errors
    session.promptName = EnhancedUuidGenerator.generatePromptId(sessionId);
    session.audioContentId = EnhancedUuidGenerator.generateAudioContentId(sessionId);

    // Clear the queue
    session.queue = [];

    // Reset flags
    session.isPromptStartSent = false;
    session.isAudioContentStartSent = false;

    // Reset tool use data
    session.toolUseContent = null;
    session.toolUseId = '';
    session.toolName = '';

    console.log(`Session ${sessionId} state reset with new prompt name ${session.promptName}`);
    this.updateSessionActivity(sessionId);
    return true;
  }

  /**
   * Close a session
   * @param sessionId The session ID
   */
  public closeSession(sessionId: string): void {
    const session = this.activeSessions.get(sessionId);
    if (!session) {
      console.log(`Cannot close session: Session ${sessionId} not found`);
      return;
    }

    session.isActive = false;
    session.closeSignal.next();
    console.log(`Session ${sessionId} marked as inactive`);
  }

  /**
   * Force close a session (alias for closeSession to fix the build error)
   * @param sessionId The session ID
   */
  public forceCloseSession(sessionId: string): void {
    console.log(`Force closing session ${sessionId}`);
    this.closeSession(sessionId);
  }
}
