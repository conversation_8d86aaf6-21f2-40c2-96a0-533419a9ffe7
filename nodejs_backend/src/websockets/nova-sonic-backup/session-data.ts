// session-data.ts
// Session data interfaces and types

import { Subject } from 'rxjs';
import { InferenceConfig } from './types';

/**
 * Represents the state and data of an active session
 */
export interface SessionData {
  /** Queue for outgoing events */
  queue: Array<any>;
  /** Signal for new queue items */
  queueSignal: Subject<void>;
  /** Signal for session close */
  closeSignal: Subject<void>;
  /** Subject for response handling */
  responseSubject: Subject<any>;
  /** Content from the last tool use */
  toolUseContent: any;
  /** ID of the last tool use */
  toolUseId: string;
  /** Name of the last tool used */
  toolName: string;
  /** Map of event handlers */
  responseHandlers: Map<string, (data: any) => void>;
  /** Unique prompt name for this session */
  promptName: string;
  /** Configuration for inference */
  inferenceConfig: InferenceConfig;
  /** Whether the session is active */
  isActive: boolean;
  /** Whether prompt start has been sent */
  isPromptStartSent: boolean;
  /** Whether audio content start has been sent */
  isAudioContentStartSent: boolean;
  /** Unique audio content ID for this session */
  audioContentId: string;
}

/**
 * Session status constants
 */
export enum SessionStatus {
  ACTIVE = 'active',
  RESETTING = 'resetting',
  CLOSING = 'closing',
  CLOSED = 'closed',
  ERROR = 'error',
}
