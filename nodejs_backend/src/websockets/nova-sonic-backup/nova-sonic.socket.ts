import { Server, Socket } from 'socket.io';
import { fromIni } from '@aws-sdk/credential-providers';
import { Buffer } from 'node:buffer';
import config from '../../../config/config';
import { NovaSonicBidirectionalStreamClient, StreamSession } from './client';
import { DefaultSystemPrompt } from './constants';
import { SessionManager } from './session-manager';
import { SessionErrorHandler } from './error-handler';

/**
 * Handler for Nova Sonic bidirectional audio streaming WebSocket connections
 */
export class NovaSonicSocketHandler {
  private bedrockClient: NovaSonicBidirectionalStreamClient;
  private activeSessions: Map<string, StreamSession> = new Map();
  private sessionManager: SessionManager;
  private errorHandler: SessionErrorHandler;

  constructor(private io: Server) {
    // Create the Session Manager and Error Handler
    this.sessionManager = new SessionManager();
    this.errorHandler = new SessionErrorHandler();

    // Create the AWS Bedrock client
    // Use environment variables directly if available, otherwise fall back to profile
    const useEnvCredentials = process.env.AWS_ACCESS_KEY_ID && process.env.AWS_SECRET_ACCESS_KEY;

    this.bedrockClient = new NovaSonicBidirectionalStreamClient({
      requestHandlerConfig: {
        maxConcurrentStreams: 10,
      },
      clientConfig: {
        region: process.env.AWS_REGION || config.aws.region,
        credentials: useEnvCredentials
          ? {
              accessKeyId: process.env.AWS_ACCESS_KEY_ID!,
              secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY!,
              sessionToken: process.env.AWS_SESSION_TOKEN,
            }
          : fromIni({ profile: config.aws.profile }),
      },
      sessionManager: this.sessionManager,
      errorHandler: this.errorHandler,
    });

    // Set up WebSocket namespace
    this.io.of('/ws/nova-sonic-bidirectional').on('connection', this.handleConnection);

    // Periodically check for and close inactive sessions (every minute)
    setInterval(this.cleanupInactiveSessions, 60000);
  }

  private handleConnection = (socket: Socket) => {
    console.log('New Nova Sonic client connected:', socket.id);

    try {
      // Initialize socket data with default values
      socket.data = {
        provider: 'nova',
        model: 'nova-pro',
        promptSetup: false,
        systemPrompt: undefined,
        initialized: false,
      };

      // Create session with the client ID as session ID
      const sessionId = socket.id;
      console.log(`Creating stream session for ${sessionId}`);
      const session = this.bedrockClient.createStreamSession(sessionId);
      this.activeSessions.set(sessionId, session);

      // Initialize the session - done async, errors handled in the method
      this.bedrockClient.initiateSession(sessionId).catch((err) => {
        // Error handling improved with error handler
        const errorMessage = err instanceof Error ? err.message : String(err);
        if (this.errorHandler.isDuplicatePromptNameError(errorMessage)) {
          socket.emit('error', this.errorHandler.createDuplicatePromptNameErrorResponse(sessionId));
        } else {
          socket.emit('error', this.errorHandler.createGenericErrorResponse('initialization', err));
        }
      });

      // Set up event handlers in a more direct style like the sample application
      this.setupEventHandlers(socket, session);

      // Set up socket event handlers in a more direct style
      this.setupSocketEventHandlers(socket, session);
    } catch (error) {
      console.error('Error creating session:', error);
      socket.emit('error', this.errorHandler.createGenericErrorResponse('session_creation', error));
      socket.disconnect();
    }
  };

  private setupEventHandlers(socket: Socket, session: StreamSession) {
    // Simple, direct event handler setup like in the sample application
    session.onEvent('contentStart', (data) => {
      console.log(`Content start event for session ${socket.id}`);
      socket.emit('contentStart', data);
    });

    session.onEvent('textOutput', (data) => {
      console.log(`Text output event for session ${socket.id}`);
      socket.emit('textOutput', data);
    });

    session.onEvent('audioOutput', (data) => {
      console.log(
        `Audio output event for session ${socket.id} - data length: ${data?.content?.length || 0} bytes`,
      );
      socket.emit('audioOutput', data);
    });

    session.onEvent('error', (data) => {
      console.error(`Error event for session ${socket.id}:`, data);
      socket.emit('error', data);
    });

    session.onEvent('toolUse', (data) => {
      console.log(`Tool use event for session ${socket.id}`);
      socket.emit('toolUse', data);
    });

    session.onEvent('toolResult', (data) => {
      console.log(`Tool result event for session ${socket.id}`);
      socket.emit('toolResult', data);
    });

    session.onEvent('contentEnd', (data) => {
      console.log(`Content end event for session ${socket.id}`);
      socket.emit('contentEnd', data);
    });

    session.onEvent('streamComplete', () => {
      console.log(`Stream complete event for session ${socket.id}`);
      socket.emit('streamComplete');
    });

    session.onEvent('inactivityTimeout', (data) => {
      console.log(`Inactivity timeout event for session ${socket.id}:`, data);
      socket.emit('textOutput', {
        role: 'SYSTEM',
        content: 'Audio streaming automatically stopped due to inactivity',
      });
      socket.emit('audioProcessingComplete');
    });
  }

  private setupSocketEventHandlers(socket: Socket, session: StreamSession) {
    // Simplified event handler approach like in the sample application
    socket.on('promptStart', () => this.handlePromptStart(socket));
    socket.on('systemPrompt', (data) => this.handleSystemPrompt(socket, data));
    socket.on('audioStart', () => this.handleAudioStart(socket));
    socket.on('audioInput', (data) => this.handleAudioInput(socket, data));
    socket.on('stopAudio', () => this.handleStopAudio(socket));
    socket.on('textInput', (data) => this.handleTextInput(socket, data));
    socket.on('modelSelect', (data) => this.handleModelSelect(socket, data));
    socket.on('resetSession', () => this.handleResetSession(socket));
    socket.on('disconnect', () => this.handleDisconnect(socket));
  }

  // Event handler implementations (simplified examples)
  private handlePromptStart = async (socket: Socket) => {
    try {
      const session = this.activeSessions.get(socket.id);
      if (!session) throw new Error('Session not found');
      await session.setupPromptStart();
    } catch (error) {
      socket.emit('error', this.errorHandler.createGenericErrorResponse('prompt_start', error));
    }
  };

  private handleSystemPrompt = async (socket: Socket, data: string) => {
    try {
      console.log('System prompt received', data);
      const session = this.activeSessions.get(socket.id);
      if (!session) {
        throw new Error('Session not found');
      }
      await session.setupSystemPrompt(undefined, data || DefaultSystemPrompt);
    } catch (error) {
      console.error('Error processing system prompt:', error);
      socket.emit('error', {
        message: 'Error processing system prompt',
        details: error instanceof Error ? error.message : String(error),
      });
    }
  };

  private handleAudioStart = async (socket: Socket) => {
    try {
      console.log(`[${socket.id}] Audio start received`);
      const session = this.activeSessions.get(socket.id);
      if (!session) {
        console.error(`[${socket.id}] Session not found for audio start`);
        throw new Error('Session not found');
      }

      // Make sure we have a clean state before setting up audio
      // We need to ensure prompt start and system prompt are sent before audio start
      if (!socket.data.promptSetup) {
        console.log(`[${socket.id}] Setting up prompt before starting audio`);
        await session.setupPromptStart();
        console.log(`[${socket.id}] Prompt start setup completed`);

        // Wait a moment for the prompt start to be processed
        console.log(`[${socket.id}] Waiting for prompt start to be processed...`);
        await new Promise((resolve) => setTimeout(resolve, 300));

        // Send system prompt with default or provided content
        const systemPrompt = socket.data.systemPrompt || undefined;
        console.log(`[${socket.id}] Setting up system prompt: ${systemPrompt || 'using default'}`);
        await session.setupSystemPrompt(undefined, systemPrompt);
        console.log(`[${socket.id}] System prompt setup completed`);

        // Mark that we've set up the prompt
        socket.data.promptSetup = true;
        console.log(`[${socket.id}] Prompt setup marked as complete`);

        // Wait a moment for the system prompt to be processed
        console.log(`[${socket.id}] Waiting for system prompt to be processed...`);
        await new Promise((resolve) => setTimeout(resolve, 300));
      } else {
        console.log(`[${socket.id}] Prompt already set up, proceeding with audio start`);
      }

      console.log(`[${socket.id}] Setting up start audio...`);
      await session.setupStartAudio();
      console.log(`[${socket.id}] Audio setup completed successfully`);
    } catch (error) {
      console.error(`[${socket.id}] Error processing audio start:`, error);
      socket.emit('error', {
        message: 'Error processing audio start',
        details: error instanceof Error ? error.message : String(error),
      });
    }
  };

  private handleAudioInput = async (socket: Socket, audioData: string) => {
    try {
      const session = this.activeSessions.get(socket.id);
      if (!session) throw new Error('Session not found');

      // Convert base64 string to Buffer
      const audioBuffer =
        typeof audioData === 'string' ? Buffer.from(audioData, 'base64') : Buffer.from(audioData);

      // Stream the audio
      await session.streamAudio(audioBuffer);
    } catch (error) {
      socket.emit('error', this.errorHandler.createGenericErrorResponse('audio_input', error));
    }
  };

  private handleStopAudio = async (socket: Socket) => {
    try {
      console.log('Stop audio requested, beginning proper shutdown sequence');
      const session = this.activeSessions.get(socket.id);
      if (!session) {
        throw new Error('Session not found');
      }

      try {
        // Step 1: End audio content cleanly
        await session.endAudioContent();
        console.log('Audio content ended successfully');

        // Step 2: End the prompt
        await session.endPrompt();
        console.log('Prompt ended successfully');

        // Step 3: Reset the session state properly
        session.resetSessionState();
        console.log('Session state reset');

        // Step 4: Reset socket data to force proper reinitializing
        if (!socket.data) socket.data = {};
        socket.data.promptSetup = false; // Force new prompts on next start

        // Step 5: Send events to client
        // Emit an event to notify the client that audio processing is complete
        socket.emit('audioProcessingComplete');

        // Add a message to the chat
        socket.emit('textOutput', {
          role: 'SYSTEM',
          content: 'Audio streaming stopped',
        });

        console.log('Audio streaming stopped, session reset for reuse');
      } catch (innerError) {
        console.error('Error in audio shutdown sequence:', innerError);
        socket.emit('error', {
          message: 'Error in audio shutdown sequence',
          details: innerError instanceof Error ? innerError.message : String(innerError),
        });

        // Still try to notify the client even if there was an error
        socket.emit('audioProcessingComplete');
      }
    } catch (error) {
      console.error('Error processing streaming end events:', error);
      socket.emit('error', {
        message: 'Error processing streaming end events',
        details: error instanceof Error ? error.message : String(error),
      });

      // Still try to notify the client even if there was an error
      socket.emit('audioProcessingComplete');
    }
  };

  private handleTextInput = async (socket: Socket, text: string) => {
    try {
      console.log('Text input received:', text);
      const session = this.activeSessions.get(socket.id);
      if (!session) {
        throw new Error('Session not found');
      }

      // First, emit a text output event for the user's message
      socket.emit('textOutput', {
        role: 'USER',
        content: text,
      });

      // Get the current provider and model for this session
      // Initialize socket.data if it doesn't exist
      if (!socket.data) {
        socket.data = {};
        console.log('Initialized socket.data for text input');
      }

      const sessionData = socket.data;
      const provider = sessionData.provider || 'nova';
      const model = sessionData.model || 'nova-pro';

      console.log(`Using provider=${provider}, model=${model} for text input`);

      if (provider === 'nova') {
        try {
          // Import the Nova service
          const { default: novaService } = await import('../../services/ai/nova.service');

          // Show thinking indicator
          socket.emit('textOutput', {
            role: 'SYSTEM',
            content: 'Thinking...',
          });

          // Extract the model type (pro, lite, micro) from the full model name
          const modelType = model.replace('nova-', '');

          // Call the Nova service directly
          const response = await novaService.generateText({
            prompt: text,
            modelType: modelType,
            maxTokens: 1024,
            temperature: 0.7,
            topP: 0.9,
          });

          // Send the response back to the client
          socket.emit('textOutput', {
            role: 'ASSISTANT',
            content: response,
          });

          console.log(`Nova response sent for session ${socket.id}`);
        } catch (error) {
          console.error('Error calling Nova service:', error);
          socket.emit('error', {
            message: 'Error calling Nova service',
            details: error instanceof Error ? error.message : String(error),
          });
        }
      } else if (provider === 'gemini') {
        // Use the Gemini service for text input
        try {
          // Import the Gemini service
          const { default: geminiService } = await import('../../services/ai/gemini.service');

          // Extract the model name from the full model string (e.g., "gemini-2.5-flash-preview" -> "gemini-2.5-flash-preview")
          const modelName = model;

          // Show thinking indicator
          socket.emit('textOutput', {
            role: 'SYSTEM',
            content: 'Thinking...',
          });

          // Call the Gemini service
          const response = await geminiService.generateText({
            prompt: text,
            model: modelName,
            maxTokens: 1024,
            temperature: 0.7,
          });

          // Send the response back to the client
          socket.emit('textOutput', {
            role: 'ASSISTANT',
            content: response,
          });

          console.log(`Gemini response sent for session ${socket.id}`);
        } catch (error) {
          console.error('Error calling Gemini service:', error);
          socket.emit('error', {
            message: 'Error calling Gemini service',
            details: error instanceof Error ? error.message : String(error),
          });
        }
      } else if (provider === 'claude') {
        // Use the Claude service for text input
        try {
          // Import the Claude service
          const { default: claudeService } = await import('../../services/ai/claude.service');

          // Extract the model name from the full model string (e.g., "claude-3.7-sonnet" -> "claude-3.7-sonnet")
          const modelName = model;

          // Show thinking indicator
          socket.emit('textOutput', {
            role: 'SYSTEM',
            content: 'Thinking...',
          });

          // Map the model name to the format expected by Claude service
          let modelType = '3.7-sonnet'; // default
          if (modelName.includes('3-5-sonnet')) {
            modelType = '3.5-sonnet';
          } else if (modelName.includes('3-5-haiku')) {
            modelType = '3.5-haiku';
          } else if (modelName.includes('3-7-sonnet')) {
            modelType = '3.7-sonnet';
          }

          // Call the Claude service
          const response = await claudeService.generateText({
            prompt: text,
            model: modelName,
            maxTokens: 1024,
            temperature: 0.7,
          });

          // Send the response back to the client
          socket.emit('textOutput', {
            role: 'ASSISTANT',
            content: response,
          });

          console.log(`Claude response sent for session ${socket.id}`);
        } catch (error) {
          console.error('Error calling Claude service:', error);
          socket.emit('error', {
            message: 'Error calling Claude service',
            details: error instanceof Error ? error.message : String(error),
          });
        }
      } else if (provider === 'azure') {
        // Use the Azure service for text input
        try {
          // Import the Azure service
          const { default: azureService } = await import('../../services/ai/azure.service');

          // Extract the model type from the full model name (e.g., "azure-gpt-4.1" -> "gpt-4.1")
          const modelType = model.replace('azure-', '');

          // Show thinking indicator
          socket.emit('textOutput', {
            role: 'SYSTEM',
            content: 'Thinking...',
          });

          // Call the Azure service
          const response = await azureService.invokeModel(text, {
            modelType: modelType,
            maxTokens: 1024,
            temperature: 0.7,
          });

          // Send the response back to the client
          socket.emit('textOutput', {
            role: 'ASSISTANT',
            content: response || 'No response from Azure model',
          });

          console.log(`Azure response sent for session ${socket.id}`);
        } catch (error) {
          console.error('Error calling Azure service:', error);
          socket.emit('error', {
            message: 'Error calling Azure service',
            details: error instanceof Error ? error.message : String(error),
          });
        }
      } else {
        throw new Error(`Unsupported provider: ${provider}`);
      }
    } catch (error) {
      console.error('Error processing text input:', error);
      socket.emit('error', {
        message: 'Error processing text input',
        details: error instanceof Error ? error.message : String(error),
      });
    }
  };

  private handleModelSelect = async (socket: Socket, data: any) => {
    try {
      // Handle both string and object formats for backward compatibility
      const modelType = typeof data === 'string' ? data : data.model;
      let provider =
        typeof data === 'string'
          ? data.startsWith('nova-')
            ? 'nova'
            : data.startsWith('gemini-')
              ? 'gemini'
              : data.startsWith('claude-')
                ? 'claude'
                : data.startsWith('azure-')
                  ? 'azure'
                  : 'unknown'
          : data.provider;

      console.log(`Model selection received: ${modelType}, provider: ${provider}`);

      const session = this.activeSessions.get(socket.id);
      if (!session) {
        throw new Error('Session not found');
      }

      // Store the provider and model in the socket data for later use
      socket.data.provider = provider;
      socket.data.model = modelType;

      // Validate model type based on provider
      if (provider === 'nova') {
        // Extract the model part (pro, lite, micro) from nova-pro format
        const modelPart = modelType.replace('nova-', '');
        if (!['pro', 'lite', 'micro'].includes(modelPart)) {
          throw new Error(
            `Invalid Nova model type: ${modelType}. Must be one of: nova-pro, nova-lite, nova-micro`,
          );
        }

        // Currently, Nova Sonic only has one model type (amazon.nova-sonic-v1:0)
        // This is for future compatibility when more models become available
        console.log(`Nova model ${modelType} selected for session ${socket.id}`);

        // Format display name
        const displayName = `Nova ${modelPart.charAt(0).toUpperCase() + modelPart.slice(1)}`;

        // Send confirmation back to client
        socket.emit('modelSelected', {
          modelType,
          provider,
          status: 'success',
          message: `Model set to ${displayName}`,
        });

        // Also send a text output event to show in the chat
        socket.emit('textOutput', {
          role: 'SYSTEM',
          content: `Model set to ${displayName}`,
        });
      } else if (provider === 'gemini') {
        // Validate Gemini model
        const validGeminiModels = [
          'gemini-2.5-flash-preview',
          'gemini-2.5-pro-preview',
          'gemini-2.0-flash',
          'gemini-2.0-pro',
        ];

        if (!validGeminiModels.includes(modelType)) {
          throw new Error(
            `Invalid Gemini model type: ${modelType}. Must be one of: ${validGeminiModels.join(', ')}`,
          );
        }

        console.log(`Gemini model ${modelType} selected for session ${socket.id}`);

        // Format display name (Gemini 2.5 Pro)
        const displayName = modelType
          .split('-')
          .map((part: string) => part.charAt(0).toUpperCase() + part.slice(1))
          .join(' ');

        // Send confirmation back to client
        socket.emit('modelSelected', {
          modelType,
          provider,
          status: 'success',
          message: `Model set to ${displayName}`,
        });

        // Also send a text output event to show in the chat
        socket.emit('textOutput', {
          role: 'SYSTEM',
          content: `Model set to ${displayName}`,
        });
      } else if (provider === 'claude') {
        // Validate Claude model
        const validClaudeModels = ['claude-3.7-sonnet', 'claude-3.5-sonnet', 'claude-3.5-haiku'];

        if (!validClaudeModels.includes(modelType)) {
          throw new Error(
            `Invalid Claude model type: ${modelType}. Must be one of: ${validClaudeModels.join(', ')}`,
          );
        }

        console.log(`Claude model ${modelType} selected for session ${socket.id}`);

        // Format display name (Claude 3.7 Sonnet)
        const displayName = modelType
          .split('-')
          .map((part: string) => part.charAt(0).toUpperCase() + part.slice(1))
          .join(' ');

        // Send confirmation back to client
        socket.emit('modelSelected', {
          modelType,
          provider,
          status: 'success',
          message: `Model set to ${displayName}`,
        });

        // Also send a text output event to show in the chat
        socket.emit('textOutput', {
          role: 'SYSTEM',
          content: `Model set to ${displayName}`,
        });
      } else if (provider === 'azure') {
        // Validate Azure model
        const validAzureModels = ['azure-gpt-4.1', 'azure-gpt-4.1-mini', 'azure-o3'];

        if (!validAzureModels.includes(modelType)) {
          throw new Error(
            `Invalid Azure model type: ${modelType}. Must be one of: ${validAzureModels.join(', ')}`,
          );
        }

        console.log(`Azure model ${modelType} selected for session ${socket.id}`);

        // Extract the model part (gpt-4.1, o3) from azure-gpt-4.1 format
        const modelPart = modelType.replace('azure-', '');

        // Format display name (Azure GPT-4.1)
        const displayName = `Azure ${modelPart.toUpperCase()}`;

        // Send confirmation back to client
        socket.emit('modelSelected', {
          modelType,
          provider,
          status: 'success',
          message: `Model set to ${displayName}`,
        });

        // Also send a text output event to show in the chat
        socket.emit('textOutput', {
          role: 'SYSTEM',
          content: `Model set to ${displayName}`,
        });
      } else {
        throw new Error(`Unsupported provider: ${provider}`);
      }
    } catch (error) {
      console.error('Error processing model selection:', error);
      socket.emit('error', {
        message: 'Error processing model selection',
        details: error instanceof Error ? error.message : String(error),
      });
    }
  };

  private handleDisconnect = async (socket: Socket) => {
    console.log('Client disconnected abruptly:', socket.id);
    const sessionId = socket.id;

    if (this.bedrockClient.isSessionActive(sessionId)) {
      try {
        const session = this.activeSessions.get(sessionId);
        if (!session) return;

        // Add explicit timeouts to avoid hanging promises
        const cleanupPromise = Promise.race([
          (async () => {
            await session.endAudioContent();
            await session.endPrompt();
            await session.close();
          })(),
          new Promise((_, reject) =>
            setTimeout(() => reject(new Error('Session cleanup timeout')), 3000),
          ),
        ]);

        await cleanupPromise;
        console.log(`Successfully cleaned up session after abrupt disconnect: ${socket.id}`);
      } catch (error) {
        console.error(`Error cleaning up session after disconnect: ${socket.id}`, error);
        this.bedrockClient.forceCloseSession(sessionId);
      } finally {
        this.activeSessions.delete(sessionId);
        if (socket.connected) {
          socket.disconnect(true);
        }
      }
    }
  };

  private cleanupInactiveSessions = () => {
    console.log('Running session cleanup check');
    const now = Date.now();
    const INACTIVE_TIMEOUT = 5 * 60 * 1000; // 5 minutes

    // Check all active sessions
    this.bedrockClient.getActiveSessions().forEach((sessionId) => {
      const lastActivity = this.bedrockClient.getLastActivityTime(sessionId);

      // If no activity for 5 minutes, force close
      if (now - lastActivity > INACTIVE_TIMEOUT) {
        console.log(`Closing inactive session ${sessionId} after 5 minutes of inactivity`);
        try {
          this.bedrockClient.forceCloseSession(sessionId);
          this.activeSessions.delete(sessionId);
        } catch (error) {
          console.error(`Error force closing inactive session ${sessionId}:`, error);
        }
      }
    });
  };

  private handleResetSession = async (socket: Socket) => {
    console.log(`Session reset requested for ${socket.id}`);
    try {
      const session = this.activeSessions.get(socket.id);
      if (!session) {
        throw new Error('Session not found');
      }

      // Close any active prompts or content first
      try {
        await session.endAudioContent();
        await session.endPrompt();
      } catch (err) {
        console.warn('Error ending content during reset:', err);
        // Continue with reset even if there are errors with ending content
      }

      // Reset session state with new IDs
      session.resetSessionState();

      // Reset socket data to force proper initialization
      if (!socket.data) socket.data = {};
      socket.data.promptSetup = false;
      socket.data.initialized = false;

      // Send confirmation to client
      socket.emit('textOutput', {
        role: 'SYSTEM',
        content: 'Session reset complete',
      });

      console.log(`Session ${socket.id} reset complete`);
    } catch (error) {
      console.error('Error resetting session:', error);
      socket.emit('error', {
        message: 'Error resetting session',
        details: error instanceof Error ? error.message : String(error),
      });
    }
  };
}
