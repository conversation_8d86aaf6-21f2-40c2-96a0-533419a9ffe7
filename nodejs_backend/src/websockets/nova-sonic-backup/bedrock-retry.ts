/**
 * AWS Bedrock retry utilities
 *
 * This module provides utilities for handling AWS Bedrock connection issues,
 * particularly HTTP/2 stream errors that can occur with bidirectional streaming.
 */

import { BedrockRuntimeClient } from '@aws-sdk/client-bedrock-runtime';
import { NodeHttp2Handler } from '@smithy/node-http-handler';

/**
 * Creates a more resilient HTTP/2 handler for AWS Bedrock
 *
 * @param options Additional options to pass to the HTTP/2 handler
 * @returns A configured NodeHttp2Handler
 */
export function createResilientHttp2Handler(options: any = {}) {
  return new NodeHttp2Handler({
    // Longer timeouts for better reliability
    requestTimeout: 60000, // 60 seconds
    sessionTimeout: 300000, // 5 minutes
    connectTimeout: 15000, // 15 seconds

    // HTTP/2 specific settings
    disableConcurrentStreams: false,
    maxConcurrentStreams: 10, // Reduced from default 20 for stability

    // Enable keep-alive for better connection stability
    enableKeepAlive: true,
    keepAliveTimeout: 30000, // 30 seconds
    keepAliveTimeoutMs: 30000, // 30 seconds (alternative format)

    // Apply any additional options
    ...options,
  });
}

/**
 * Creates a BedrockRuntimeClient with retry capabilities
 *
 * @param config Client configuration
 * @param maxRetries Maximum number of retries
 * @returns Configured BedrockRuntimeClient
 */
export function createBedrockClientWithRetry(config: any, maxRetries = 3) {
  // Create a resilient HTTP/2 handler
  const http2Handler = createResilientHttp2Handler(config.requestHandlerConfig);

  // Create the Bedrock client with retry configuration
  return new BedrockRuntimeClient({
    ...config,
    region: config.region || 'us-east-1',
    maxAttempts: maxRetries,
    retryMode: 'adaptive',
    requestHandler: http2Handler,
  });
}

/**
 * Executes a function with retry logic
 *
 * @param fn Function to execute
 * @param maxRetries Maximum number of retries
 * @param retryableErrors Array of error message substrings that are retryable
 * @returns Promise resolving to the function result
 */
export async function withRetry<T>(
  fn: () => Promise<T>,
  maxRetries = 3,
  retryableErrors = [
    'HTTP/2 stream is abnormally aborted',
    'Timed out',
    'Connection reset',
    'ECONNRESET',
    'ETIMEDOUT',
    'ENOTFOUND',
    'ECONNREFUSED',
  ],
): Promise<T> {
  let lastError: Error | null = null;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error instanceof Error ? error : new Error(String(error));

      // Check if this error is retryable
      const isRetryable = retryableErrors.some((errMsg) => lastError!.message.includes(errMsg));

      if (!isRetryable || attempt >= maxRetries) {
        throw lastError;
      }

      // Calculate backoff time with jitter
      const baseDelay = Math.pow(2, attempt) * 100; // Exponential backoff
      const jitter = Math.random() * 100; // Random jitter
      const delay = baseDelay + jitter;

      console.log(
        `Retrying after error: ${lastError.message} (attempt ${attempt}/${maxRetries}, waiting ${delay}ms)`,
      );

      // Wait before retrying
      await new Promise((resolve) => setTimeout(resolve, delay));
    }
  }

  // This should never happen, but TypeScript needs it
  throw lastError || new Error('Unknown error during retry');
}

/**
 * Checks if an error is related to HTTP/2 stream issues
 *
 * @param error The error to check
 * @returns True if it's an HTTP/2 stream error
 */
export function isHttp2StreamError(error: any): boolean {
  if (!error) return false;

  const errorMessage = error instanceof Error ? error.message : String(error);

  return (
    errorMessage.includes('HTTP/2 stream') ||
    errorMessage.includes('stream error') ||
    errorMessage.includes('GOAWAY') ||
    errorMessage.includes('RST_STREAM')
  );
}
