// index.ts
// Main export file for Nova Sonic module

// index.ts
import { NovaSonicBidirectionalStreamClient, StreamSession } from './client';
import { SessionManager } from './session-manager';
import { SessionErrorHandler } from './error-handler';
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from './tool-handlers';
import { EnhancedUuidGenerator } from './uuid-generator';
import { NovaSonicSocketHandler } from './nova-sonic.socket';
import {
  DefaultInferenceConfiguration,
  DefaultAudioInputConfiguration,
  DefaultAudioOutputConfiguration,
  DefaultSystemPrompt,
  DefaultTextConfiguration,
  DefaultToolSchema,
  WeatherToolSchema,
  UIMockupToolSchema,
  CodeReviewToolSchema,
} from './constants';

export {
  NovaSonicBidirectionalStreamClient,
  StreamSession,
  SessionManager,
  SessionErrorHandler,
  ToolHandler,
  EnhancedUuidGenerator,
  NovaSonicSocketHandler,
  // Constants
  DefaultInferenceConfiguration,
  DefaultAudioInputConfiguration,
  DefaultAudioOutputConfiguration,
  DefaultSystemPrompt,
  DefaultTextConfiguration,
  DefaultToolSchema,
  WeatherToolSchema,
  UIMockupToolSchema,
  CodeReviewToolSchema,
};
