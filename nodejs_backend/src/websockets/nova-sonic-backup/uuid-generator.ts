// uuid-generator.ts
// Enhanced UUID generation with session context

import { randomUUID } from 'node:crypto';

/**
 * Generates enhanced UUIDs with session context to prevent collisions
 */
export class EnhancedUuidGenerator {
  /**
   * Generates a prompt ID with session context and timestamp
   * @param sessionId The current session ID
   * @returns A unique prompt ID
   */
  static generatePromptId(sessionId: string): string {
    return `${sessionId}-prompt-${Date.now()}-${randomUUID()}`;
  }

  /**
   * Generates an audio content ID with session context and timestamp
   * @param sessionId The current session ID
   * @returns A unique audio content ID
   */
  static generateAudioContentId(sessionId: string): string {
    return `${sessionId}-audio-${Date.now()}-${randomUUID()}`;
  }

  /**
   * Generates a tool use ID with session context
   * @param sessionId The current session ID
   * @param toolName Optional tool name for more context
   * @returns A unique tool use ID
   */
  static generateToolUseId(sessionId: string, toolName?: string): string {
    const toolContext = toolName ? `-${toolName}` : '';
    return `${sessionId}${toolContext}-tool-${Date.now()}-${randomUUID()}`;
  }

  /**
   * Generates a generic content ID with session context
   * @param sessionId The current session ID
   * @param contentType Type of content (text, tool, etc.)
   * @returns A unique content ID
   */
  static generateContentId(sessionId: string, contentType = 'content'): string {
    return `${sessionId}-${contentType}-${Date.now()}-${randomUUID()}`;
  }
}
