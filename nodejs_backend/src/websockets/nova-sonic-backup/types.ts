export interface InferenceConfig {
  readonly maxTokens: number;
  readonly topP: number;
  readonly temperature: number;
}

export type ContentType = 'AUDIO' | 'TEXT' | 'TOOL';
export type AudioType = 'SPEECH';
export type AudioMediaType = 'audio/lpcm';
export type TextMediaType = 'text/plain' | 'application/json';

export interface AudioConfiguration {
  readonly audioType: AudioType;
  readonly mediaType: AudioMediaType;
  readonly sampleRateHertz: number;
  readonly sampleSizeBits: number;
  readonly channelCount: number;
  readonly encoding: string;
  readonly voiceId?: string;
}

export interface TextConfiguration {
  readonly mediaType: TextMediaType;
}

export interface ToolConfiguration {
  readonly toolUseId: string;
  readonly type: 'TEXT';
  readonly textInputConfiguration: {
    readonly mediaType: 'text/plain';
  };
}
