// error-handler.ts
// Error handling and recovery logic

/**
 * <PERSON>les error detection and recovery for Nova Sonic sessions
 */
export class SessionErrorHandler {
  private sessionResetTimes: Map<string, number> = new Map();
  private readonly RESET_COOLDOWN_MS = 5000; // 5 second cooldown between resets

  /**
   * Checks if an error message contains a duplicate prompt name error
   * @param errorMessage The error message to check
   * @returns True if it's a duplicate prompt name error
   */
  public isDuplicatePromptNameError(errorMessage: string): boolean {
    return errorMessage.includes('Duplicate prompt name');
  }

  /**
   * Determines if a session can be reset based on cooldown period
   * @param sessionId The session ID
   * @returns True if the session can be reset, false if in cooldown
   */
  public canResetSession(sessionId: string): boolean {
    const lastResetTime = this.sessionResetTimes.get(sessionId) || 0;
    const now = Date.now();
    return now - lastResetTime >= this.RESET_COOLDOWN_MS;
  }

  /**
   * Records a session reset time
   * @param sessionId The session ID
   */
  public recordSessionReset(sessionId: string): void {
    this.sessionResetTimes.set(sessionId, Date.now());
  }

  /**
   * Gets the remaining cooldown time in milliseconds
   * @param sessionId The session ID
   * @returns Milliseconds remaining in cooldown, or 0 if not in cooldown
   */
  public getResetCooldownRemaining(sessionId: string): number {
    const lastResetTime = this.sessionResetTimes.get(sessionId) || 0;
    const now = Date.now();
    const elapsed = now - lastResetTime;
    return Math.max(0, this.RESET_COOLDOWN_MS - elapsed);
  }

  /**
   * Creates a user-friendly error object for duplicate prompt name errors
   * @param sessionId The session ID
   * @returns Error object to dispatch to the client
   */
  public createDuplicatePromptNameErrorResponse(sessionId: string): any {
    return {
      source: 'responseStream',
      message: 'Session error - please try again',
      details: 'The session needed to be reset due to a duplicate prompt name',
      sessionId,
    };
  }

  /**
   * Creates a generic error response object
   * @param source Error source
   * @param error The error object or message
   * @returns Error object to dispatch to the client
   */
  public createGenericErrorResponse(source: string, error: any): any {
    const errorMessage = error instanceof Error ? error.message : String(error);
    return {
      source,
      message: 'Error processing request',
      details: errorMessage,
    };
  }

  /**
   * Cleans up session reset tracking when a session is closed
   * @param sessionId The session ID
   */
  public cleanupSession(sessionId: string): void {
    this.sessionResetTimes.delete(sessionId);
  }
}
