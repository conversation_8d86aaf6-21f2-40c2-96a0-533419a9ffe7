{% extends "./base.html" %}

{% block content %}
<div class="p-4">
    <h1 class="text-2xl font-bold mb-4">API Health Status</h1>
    
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
        <!-- Overall Status -->
        <div class="bg-white p-4 rounded shadow">
            <h2 class="text-lg font-semibold mb-2">Overall Status</h2>
            <div class="text-2xl font-bold {% if healthStatus.status == 'ok' %}text-green-500{% elif healthStatus.status == 'degraded' %}text-yellow-500{% else %}text-red-500{% endif %}">
                {{ healthStatus.status | upper }}
            </div>
            <p class="text-gray-600 text-sm mt-1">{{ healthStatus.timestamp }}</p>
        </div>
        
        <!-- Uptime -->
        <div class="bg-white p-4 rounded shadow">
            <h2 class="text-lg font-semibold mb-2">Uptime</h2>
            <div class="text-2xl font-bold">
                {{ (healthStatus.uptime / 3600) | round(2) }} hours
            </div>
            <p class="text-gray-600 text-sm mt-1">{{ (healthStatus.uptime / 60) | round(0) }} minutes total</p>
        </div>
        
        <!-- Environment -->
        <div class="bg-white p-4 rounded shadow">
            <h2 class="text-lg font-semibold mb-2">Environment</h2>
            <div class="text-2xl font-bold">
                {{ healthStatus.environment | upper }}
            </div>
            <p class="text-gray-600 text-sm mt-1">Node {{ healthStatus.node.version }}</p>
        </div>
    </div>
    
    <!-- Services Status -->
    <div class="bg-white p-4 rounded shadow mb-6">
        <h2 class="text-xl font-semibold mb-4">Services Status</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <!-- Database -->
            <div class="border p-3 rounded">
                <h3 class="font-semibold">Database</h3>
                <p class="{% if healthStatus.services.database.status == 'connected' %}text-green-500{% else %}text-red-500{% endif %}">
                    {{ healthStatus.services.database.status }}
                </p>
                {% if healthStatus.services.database.error %}
                    <p class="text-red-500 text-sm">{{ healthStatus.services.database.error }}</p>
                {% endif %}
            </div>
            
            <!-- Session -->
            <div class="border p-3 rounded">
                <h3 class="font-semibold">Session</h3>
                <p class="{% if healthStatus.services.session.status == 'active' %}text-green-500{% else %}text-yellow-500{% endif %}">
                    {{ healthStatus.services.session.status }}
                </p>
                <p class="text-sm text-gray-600">{{ healthStatus.services.session.adminUser }}</p>
            </div>
            
            <!-- Clerk -->
            <div class="border p-3 rounded">
                <h3 class="font-semibold">Clerk Auth</h3>
                <p class="text-sm">Publishable Key: <span class="{% if healthStatus.services.clerk.publishableKey == 'configured' %}text-green-500{% else %}text-red-500{% endif %}">{{ healthStatus.services.clerk.publishableKey }}</span></p>
                <p class="text-sm">Secret Key: <span class="{% if healthStatus.services.clerk.secretKey == 'configured' %}text-green-500{% else %}text-red-500{% endif %}">{{ healthStatus.services.clerk.secretKey }}</span></p>
            </div>
            
            <!-- AI Providers -->
            <div class="border p-3 rounded">
                <h3 class="font-semibold">AI Providers</h3>
                {% for provider, status in healthStatus.services.aiProviders %}
                    <p class="text-sm">{{ provider }}: <span class="{% if status == 'configured' %}text-green-500{% else %}text-gray-400{% endif %}">{{ status }}</span></p>
                {% endfor %}
            </div>
        </div>
    </div>
    
    <!-- System Resources -->
    <div class="bg-white p-4 rounded shadow mb-6">
        <h2 class="text-xl font-semibold mb-4">System Resources</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
                <h3 class="font-semibold">Memory Usage</h3>
                <p class="text-sm">System: {{ ((healthStatus.system.memory.used / healthStatus.system.memory.total) * 100) | round(1) }}% used</p>
                <p class="text-sm">Process: {{ (healthStatus.node.memory.heapUsed / 1024 / 1024) | round(1) }} MB</p>
            </div>
            <div>
                <h3 class="font-semibold">System Info</h3>
                <p class="text-sm">Platform: {{ healthStatus.system.platform }}</p>
                <p class="text-sm">CPUs: {{ healthStatus.system.cpus }}</p>
                <p class="text-sm">Load Average: {{ healthStatus.system.loadAverage[0] | round(2) }}</p>
            </div>
        </div>
    </div>
    
    <!-- API Routes -->
    <div class="bg-white p-4 rounded shadow mb-6">
        <h2 class="text-xl font-semibold mb-4">Admin API Routes</h2>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Path</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Methods</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% for route in healthStatus.routes.admin.api %}
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-mono">{{ route.path }}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm">{{ route.methods.join(', ') | upper }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
    
    <!-- Raw JSON -->
    <div class="bg-white p-4 rounded shadow">
        <h2 class="text-xl font-semibold mb-4">Raw JSON Response</h2>
        <pre class="bg-gray-100 p-4 rounded overflow-x-auto"><code>{{ healthData }}</code></pre>
    </div>
    
    <!-- Quick Actions -->
    <div class="mt-6 flex gap-4">
        <a href="/api/admin/api-health" target="_blank" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
            View Raw API Response
        </a>
        <a href="/api/admin/api-health/routes" target="_blank" class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600">
            View All Routes
        </a>
        <button onclick="location.reload()" class="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600">
            Refresh
        </button>
    </div>
</div>
{% endblock %}
