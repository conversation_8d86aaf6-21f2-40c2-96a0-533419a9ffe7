{% extends "admin/base.html" %}

{% block title %}Model Priorities Management{% endblock %}

{% block head_extra %}
<style>
    .priority-card {
        margin-bottom: 15px;
        border-radius: 5px;
    }
    .model-badge {
        display: inline-block;
        padding: 6px 12px;
        margin: 5px;
        border-radius: 20px;
        font-size: 0.85rem;
        font-weight: 500;
        cursor: grab;
    }
    .model-badge.gpt-4 {
        background-color: #28a745;
        color: white;
    }
    .model-badge.gpt-3 {
        background-color: #17a2b8;
        color: white;
    }
    .model-badge.nova {
        background-color: #fd7e14;
        color: white;
    }
    .model-badge.gemini {
        background-color: #007bff;
        color: white;
    }
    .model-badge.claude {
        background-color: #6f42c1;
        color: white;
    }
    .model-badge.o4 {
        background-color: #6c757d;
        color: white;
    }
    .alert-info {
        background-color: #e3f2fd;
        border-color: #b3e5fc;
    }
    .task-row {
        background-color: #f8f9fa;
        padding: 15px;
        margin-bottom: 10px;
        border-radius: 5px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <h1 class="mb-4">Model Priorities Management</h1>
    
    <p class="mb-4">This page allows you to manage the model priorities for different use cases. The priorities determine which model is selected for each task type.</p>
    
    <div class="alert alert-info mb-4">
        <i class="fas fa-info-circle mr-2"></i> Changes to priorities will be saved to <code>config.yaml</code>. Models are tried in order until a working one is found.
    </div>
    
    <div class="mb-4">
        <a href="/admin/models" class="btn btn-secondary mr-2">
            <i class="fas fa-list mr-1"></i> View All Models
        </a>
        <a href="/admin/api/models/test" class="btn btn-success">
            <i class="fas fa-vial mr-1"></i> Test Models
        </a>
    </div>
    
    <h2 class="mb-3">Model Priorities</h2>
    
    <div id="priorities-container" class="mb-5">
        <!-- Loading indicator shown initially -->
        <div class="text-center mt-4" id="loading-indicator">
            <div class="spinner-border text-primary" role="status">
                <span class="sr-only">Loading...</span>
            </div>
            <p class="mt-2">Loading model priorities...</p>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/sortablejs@1.14.0/Sortable.min.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Fetch all priorities from API
        fetch('/admin/api/models/priorities')
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    const priorities = data.data.priorities;
                    const descriptions = data.data.descriptions;
                    const prioritiesContainer = document.getElementById('priorities-container');
                    
                    // Clear existing content (including loading indicator)
                    prioritiesContainer.innerHTML = '';
                    
                    // If no priorities found, show a message
                    if (Object.keys(priorities).length === 0) {
                        prioritiesContainer.innerHTML = `
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle mr-2"></i> No model priorities found in configuration.
                                Please check your config.yaml file.
                            </div>
                        `;
                        return;
                    }
                    
                    // Add each priority category
                    for (const [taskType, modelList] of Object.entries(priorities)) {
                        // Create task row element
                        const taskRow = document.createElement('div');
                        taskRow.className = 'task-row card mb-4';
                        
                        // Add description if available
                        const description = descriptions[taskType] || `Priority settings for ${taskType}`;
                        
                        // Create card header
                        taskRow.innerHTML = `
                            <div class="card-header bg-light">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h5 class="mb-0">${taskType}</h5>
                                    <button class="btn btn-sm btn-primary save-priority" data-task="${taskType}">
                                        <i class="fas fa-save mr-1"></i> Save
                                    </button>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="small text-muted mb-3">${description}</div>
                                <div class="priority-table-container">
                                    <table class="table table-bordered">
                                        <thead>
                                            <tr>
                                                <th style="width: 80px" class="text-center">Priority</th>
                                                <th>Model</th>
                                                <th style="width: 120px" class="text-center">Action</th>
                                            </tr>
                                        </thead>
                                        <tbody id="${taskType}-priority-table" class="priority-list">
                                        </tbody>
                                    </table>
                                </div>
                                <p class="text-muted mt-2 mb-0"><small>Models will be tried in order of priority (lower number = higher priority)</small></p>
                            </div>
                        `;
                        
                        // Add to container
                        prioritiesContainer.appendChild(taskRow);
                        
                        // Add models to the table
                        const priorityTable = document.getElementById(`${taskType}-priority-table`);
                        if (Array.isArray(modelList)) {
                            modelList.forEach((modelId, index) => {
                                const row = document.createElement('tr');
                                row.dataset.modelId = modelId;
                                
                                // Determine model badge class based on model prefix
                                let badgeClass = 'gpt-3';
                                if (modelId.startsWith('gpt-4')) {
                                    badgeClass = 'gpt-4';
                                } else if (modelId.startsWith('nova')) {
                                    badgeClass = 'nova';
                                } else if (modelId.startsWith('gemini')) {
                                    badgeClass = 'gemini';
                                } else if (modelId.startsWith('claude')) {
                                    badgeClass = 'claude';
                                } else if (modelId.startsWith('o')) {
                                    badgeClass = 'o4';
                                }
                                
                                row.innerHTML = `
                                    <td class="text-center">${index + 1}</td>
                                    <td><span class="model-badge ${badgeClass}">${modelId}</span></td>
                                    <td class="text-center">
                                        <button class="btn btn-sm btn-outline-secondary move-up" title="Move Up"><i class="fas fa-arrow-up"></i></button>
                                        <button class="btn btn-sm btn-outline-secondary move-down" title="Move Down"><i class="fas fa-arrow-down"></i></button>
                                    </td>
                                `;
                                
                                priorityTable.appendChild(row);
                            });
                        }
                    }
                    
                    // Initialize all event handlers after DOM is updated
                    initializeEventHandlers();
                } else {
                    console.error('Failed to load priorities:', data);
                    showError('Failed to load model priorities. See console for details.');
                }
            })
            .catch(error => {
                console.error('Error fetching priorities:', error);
                showError('Failed to load model priorities. See console for details.');
            });
        
        // Function to show error message
        function showError(message) {
            // Remove loading indicator if present
            const loadingIndicator = document.getElementById('loading-indicator');
            if (loadingIndicator) {
                loadingIndicator.remove();
            }
            
            const notification = document.createElement('div');
            notification.className = 'alert alert-danger alert-dismissible fade show';
            notification.innerHTML = `
                <strong>Error!</strong> ${message}
                <button type="button" class="close" data-dismiss="alert">
                    <span aria-hidden="true">&times;</span>
                </button>
            `;
            document.querySelector('.container-fluid').insertBefore(
                notification,
                document.querySelector('h2')
            );
        }
        
        // Function to show success message
        function showSuccess(message) {
            const notification = document.createElement('div');
            notification.className = 'alert alert-success alert-dismissible fade show';
            notification.innerHTML = `
                <strong>Success!</strong> ${message}
                <button type="button" class="close" data-dismiss="alert">
                    <span aria-hidden="true">&times;</span>
                </button>
            `;
            document.querySelector('.container-fluid').insertBefore(
                notification,
                document.querySelector('h2')
            );
            
            // Auto-dismiss after 3 seconds
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => notification.remove(), 150);
            }, 3000);
        }
        
        // Initialize all event handlers for priority tables
        function initializeEventHandlers() {
            // Function to update priority numbers
            function updatePriorityNumbers(tableId) {
                const rows = document.querySelectorAll(`#${tableId} tr`);
                rows.forEach((row, index) => {
                    // Update the priority number in the first cell
                    const priorityCell = row.querySelector('td:first-child');
                    if (priorityCell) {
                        priorityCell.textContent = index + 1;
                    }
                });
            }
            
            // Function to move a row up
            function moveRowUp(button) {
                const row = button.closest('tr');
                const prevRow = row.previousElementSibling;
                if (prevRow) {
                    row.parentNode.insertBefore(row, prevRow);
                    updatePriorityNumbers(row.parentNode.id);
                }
            }
            
            // Function to move a row down
            function moveRowDown(button) {
                const row = button.closest('tr');
                const nextRow = row.nextElementSibling;
                if (nextRow) {
                    row.parentNode.insertBefore(nextRow, row);
                    updatePriorityNumbers(row.parentNode.id);
                }
            }
            
            // Attach event listeners to move up/down buttons
            document.querySelectorAll('.move-up').forEach(button => {
                button.addEventListener('click', function() {
                    moveRowUp(this);
                });
            });
            
            document.querySelectorAll('.move-down').forEach(button => {
                button.addEventListener('click', function() {
                    moveRowDown(this);
                });
            });
            
            // Handle save buttons
            document.querySelectorAll('.save-priority').forEach(button => {
                button.addEventListener('click', function() {
                    const taskType = this.dataset.task;
                    const priorityTable = document.getElementById(`${taskType}-priority-table`);
                    const models = Array.from(priorityTable.querySelectorAll('tr'))
                        .map(row => row.dataset.modelId)
                        .filter(id => id); // Filter out any undefined values
                    
                    console.log(`Saving priorities for ${taskType}:`, models);
                    
                    // Send the updated priority to the server
                    fetch('/admin/api/models/priorities/update', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            taskType: taskType,
                            priorities: models
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            showSuccess(`Priorities for ${taskType} saved successfully.`);
                        } else {
                            showError(`Failed to save priorities for ${taskType}. ${data.error || ''}`);
                        }
                    })
                    .catch(error => {
                        console.error('Error saving priorities:', error);
                        showError(`Failed to save priorities for ${taskType}. See console for details.`);
                    });
                });
            });
            
            // Optional: Initialize SortableJS if you want drag and drop functionality
            document.querySelectorAll('.priority-list').forEach(table => {
                new Sortable(table, {
                    animation: 150,
                    onEnd: function() {
                        updatePriorityNumbers(table.id);
                    }
                });
            });
        }
    });
</script>
{% endblock %}
