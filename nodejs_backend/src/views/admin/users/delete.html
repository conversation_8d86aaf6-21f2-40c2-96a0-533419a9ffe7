{% extends "admin/base.html" %}

{% block title %}Delete User: {{ user.email }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="mb-0">Delete User</h1>
        <div>
            <a href="/admin/api/users/{{ user.id }}" class="btn btn-secondary mr-2">
                <i class="fas fa-arrow-left"></i> Back to Details
            </a>
            <a href="/admin/api/users/list" class="btn btn-secondary">
                <i class="fas fa-list"></i> Back to List
            </a>
        </div>
    </div>

    {% if error %}
    <div class="alert alert-danger" role="alert">
        <i class="fas fa-exclamation-triangle mr-2"></i> {{ error }}
    </div>
    {% endif %}

    <div class="card mb-4">
        <div class="card-header bg-danger text-white">
            <h5 class="mb-0">Confirm Deletion</h5>
        </div>
        <div class="card-body">
            <div class="alert alert-warning mb-4">
                <i class="fas fa-exclamation-triangle mr-2"></i> <strong>Warning:</strong> This action cannot be undone. The user will be permanently deleted from both the database and Clerk.
            </div>

            <h5>User Information</h5>
            <div class="table-responsive mb-4">
                <table class="table table-bordered">
                    <tr>
                        <th style="width: 150px;">ID</th>
                        <td>{{ user.id }}</td>
                    </tr>
                    <tr>
                        <th>Email</th>
                        <td>{{ user.email }}</td>
                    </tr>
                    <tr>
                        <th>Name</th>
                        <td>{{ user.first_name }} {{ user.last_name }}</td>
                    </tr>
                    <tr>
                        <th>Username</th>
                        <td>{{ user.username }}</td>
                    </tr>
                    <tr>
                        <th>Role</th>
                        <td>
                            {% if user.role == 'ADMIN' %}
                            <span class="badge badge-danger">Admin</span>
                            {% elif user.role == 'DEVELOPER' %}
                            <span class="badge badge-success">Developer</span>
                            {% else %}
                            <span class="badge badge-secondary">Free</span>
                            {% endif %}
                        </td>
                    </tr>
                    <tr>
                        <th>Created At</th>
                        <td>{{ user.created_at }}</td>
                    </tr>
                </table>
            </div>

            {% if !error %}
            <form method="post" action="/admin/api/users/{{ user.id }}/delete">
                <div class="d-flex justify-content-end">
                    <a href="/admin/api/users/{{ user.id }}" class="btn btn-secondary mr-2">Cancel</a>
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash mr-1"></i> Permanently Delete User
                    </button>
                </div>
            </form>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
