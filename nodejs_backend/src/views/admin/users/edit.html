{% extends "admin/base.html" %}

{% block title %}Edit User: {{ user.email }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="mb-0">Edit User</h1>
        <div>
            <a href="/admin/api/users/{{ user.id }}" class="btn btn-secondary mr-2">
                <i class="fas fa-arrow-left"></i> Back to Details
            </a>
            <a href="/admin/api/users/list" class="btn btn-secondary">
                <i class="fas fa-list"></i> Back to List
            </a>
        </div>
    </div>

    {% if error %}
    <div class="alert alert-danger" role="alert">
        <i class="fas fa-exclamation-triangle mr-2"></i> {{ error }}
    </div>
    {% endif %}

    <div class="card">
        <div class="card-header bg-light">
            <h5 class="mb-0">User Information</h5>
        </div>
        <div class="card-body">
            <form method="post" action="/admin/api/users/{{ user.id }}/edit">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label for="email" class="form-label">Email <span class="text-danger">*</span></label>
                            <input type="email" class="form-control" id="email" name="email" value="{{ user.email }}" required>
                        </div>
                        
                        <div class="form-group mb-3">
                            <label for="first_name" class="form-label">First Name</label>
                            <input type="text" class="form-control" id="first_name" name="first_name" value="{{ user.first_name }}">
                        </div>
                        
                        <div class="form-group mb-3">
                            <label for="last_name" class="form-label">Last Name</label>
                            <input type="text" class="form-control" id="last_name" name="last_name" value="{{ user.last_name }}">
                        </div>
                        
                        <div class="form-group mb-3">
                            <label for="username" class="form-label">Username</label>
                            <input type="text" class="form-control" id="username" name="username" value="{{ user.username }}">
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label for="role" class="form-label">Role</label>
                            <select class="form-control" id="role" name="role">
                                <option value="FREE" {% if user.role == 'FREE' %}selected{% endif %}>Free User</option>
                                <option value="DEVELOPER" {% if user.role == 'DEVELOPER' %}selected{% endif %}>Developer</option>
                                <option value="ADMIN" {% if user.role == 'ADMIN' %}selected{% endif %}>Admin</option>
                            </select>
                        </div>
                        
                        <div class="form-group mb-3">
                            <label for="is_active" class="form-label">Status</label>
                            <select class="form-control" id="is_active" name="is_active">
                                <option value="true" {% if user.is_active %}selected{% endif %}>Active</option>
                                <option value="false" {% if !user.is_active %}selected{% endif %}>Inactive</option>
                            </select>
                        </div>
                        
                        <div class="form-group mb-3">
                            <label for="bio" class="form-label">Bio</label>
                            <textarea class="form-control" id="bio" name="bio" rows="4">{{ user.bio }}</textarea>
                        </div>
                        
                        <div class="form-group mb-3">
                            <label for="profile_image_url" class="form-label">Profile Image URL</label>
                            <input type="url" class="form-control" id="profile_image_url" name="profile_image_url" value="{{ user.profile_image_url }}">
                        </div>
                    </div>
                </div>
                
                <div class="alert alert-info mb-4">
                    <i class="fas fa-info-circle mr-2"></i> Changes to the user information will also be synchronized with their Clerk account.
                </div>
                
                <div class="d-flex justify-content-end">
                    <a href="/admin/api/users/{{ user.id }}" class="btn btn-secondary mr-2">Cancel</a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save mr-1"></i> Save Changes
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
