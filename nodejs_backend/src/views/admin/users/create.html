{% extends "admin/base.html" %}

{% block title %}Create User{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="mb-0">Create User</h1>
        <a href="/admin/users" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to List
        </a>
    </div>

    {% if error %}
    <div class="alert alert-danger" role="alert">
        <i class="fas fa-exclamation-triangle mr-2"></i> {{ error }}
    </div>
    {% endif %}

    <div class="card">
        <div class="card-header bg-light">
            <h5 class="mb-0">User Information</h5>
        </div>
        <div class="card-body">
            <form method="post" action="/admin/api/users/create">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label for="email" class="form-label">Email <span class="text-danger">*</span></label>
                            <input type="email" class="form-control" id="email" name="email" value="{{ formData.email }}" required>
                            <small class="text-muted">The user's email address (required)</small>
                        </div>
                        
                        <div class="form-group mb-3">
                            <label for="first_name" class="form-label">First Name</label>
                            <input type="text" class="form-control" id="first_name" name="first_name" value="{{ formData.first_name }}">
                        </div>
                        
                        <div class="form-group mb-3">
                            <label for="last_name" class="form-label">Last Name</label>
                            <input type="text" class="form-control" id="last_name" name="last_name" value="{{ formData.last_name }}">
                        </div>
                        
                        <div class="form-group mb-3">
                            <label for="username" class="form-label">Username</label>
                            <input type="text" class="form-control" id="username" name="username" value="{{ formData.username }}">
                            <small class="text-muted">If not provided, will be generated from email</small>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label for="role" class="form-label">Role</label>
                            <select class="form-control" id="role" name="role">
                                <option value="FREE" {% if formData.role == 'FREE' %}selected{% endif %}>Free User</option>
                                <option value="DEVELOPER" {% if formData.role == 'DEVELOPER' %}selected{% endif %}>Developer</option>
                                <option value="ADMIN" {% if formData.role == 'ADMIN' %}selected{% endif %}>Admin</option>
                            </select>
                        </div>
                        
                        <div class="form-group mb-3">
                            <label for="is_active" class="form-label">Status</label>
                            <select class="form-control" id="is_active" name="is_active">
                                <option value="true" {% if formData.is_active == 'true' or not formData.is_active %}selected{% endif %}>Active</option>
                                <option value="false" {% if formData.is_active == 'false' %}selected{% endif %}>Inactive</option>
                            </select>
                            <small class="text-muted">Controls whether the user can log in</small>
                        </div>
                        
                        <div class="form-group mb-3">
                            <label for="send_welcome_email" class="form-label">Send Welcome Email</label>
                            <select class="form-control" id="send_welcome_email" name="send_welcome_email">
                                <option value="true" {% if formData.send_welcome_email == 'true' or not formData.send_welcome_email %}selected{% endif %}>Yes</option>
                                <option value="false" {% if formData.send_welcome_email == 'false' %}selected{% endif %}>No</option>
                            </select>
                            <small class="text-muted">If yes, Clerk will send an email with account verification and setup instructions</small>
                        </div>
                        
                        <div class="form-group mb-3">
                            <label for="bio" class="form-label">Bio</label>
                            <textarea class="form-control" id="bio" name="bio" rows="4">{{ formData.bio }}</textarea>
                        </div>
                    </div>
                </div>
                
                <div class="alert alert-info mb-4">
                    <i class="fas fa-info-circle mr-2"></i> When you create a user, a Clerk account will be automatically created with the provided email. The user will receive an email to set their password.
                </div>
                
                <div class="d-flex justify-content-end">
                    <a href="/admin/api/users/list" class="btn btn-secondary mr-2">Cancel</a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-plus-circle mr-1"></i> Create User
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
