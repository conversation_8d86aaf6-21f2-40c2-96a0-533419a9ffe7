{% extends "admin/base.html" %}

{% block title %}User Details: {{ user.email }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="mb-0">User Details</h1>
        <div>
            <a href="/admin/api/users/list" class="btn btn-secondary mr-2">
                <i class="fas fa-arrow-left"></i> Back to List
            </a>
            <a href="/admin/api/users/{{ user.id }}/edit" class="btn btn-primary mr-2">
                <i class="fas fa-edit"></i> Edit
            </a>
            <a href="/admin/api/users/{{ user.id }}/delete" class="btn btn-danger">
                <i class="fas fa-trash"></i> Delete
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">Basic Information</h5>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">ID:</div>
                        <div class="col-md-8">{{ user.id }}</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">Email:</div>
                        <div class="col-md-8">{{ user.email }}</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">Name:</div>
                        <div class="col-md-8">{{ user.first_name }} {{ user.last_name }}</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">Username:</div>
                        <div class="col-md-8">{{ user.username }}</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">Clerk ID:</div>
                        <div class="col-md-8">{{ user.clerk_id }}</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">Role:</div>
                        <div class="col-md-8">
                            {% if user.role == 'ADMIN' %}
                            <span class="badge badge-danger">Admin</span>
                            {% elif user.role == 'DEVELOPER' %}
                            <span class="badge badge-success">Developer</span>
                            {% else %}
                            <span class="badge badge-secondary">Free</span>
                            {% endif %}
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">Status:</div>
                        <div class="col-md-8">
                            {% if user.is_active %}
                            <span class="badge badge-success">Active</span>
                            {% else %}
                            <span class="badge badge-secondary">Inactive</span>
                            {% endif %}
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">Created At:</div>
                        <div class="col-md-8">{{ user.created_at }}</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">Last Updated:</div>
                        <div class="col-md-8">{{ user.updated_at }}</div>
                    </div>
                </div>
            </div>

            {% if user.bio %}
            <div class="card mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">Bio</h5>
                </div>
                <div class="card-body">
                    <p>{{ user.bio }}</p>
                </div>
            </div>
            {% endif %}
        </div>

        <div class="col-md-6">
            {% if user.projects && user.projects.length > 0 %}
            <div class="card mb-4">
                <div class="card-header bg-light d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Recent Projects</h5>
                </div>
                <div class="card-body p-0">
                    <div class="list-group list-group-flush">
                        {% for project in user.projects %}
                        <div class="list-group-item">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-1">{{ project.name }}</h6>
                                    <small class="text-muted">Created: {{ project.created_at }}</small>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
            {% endif %}

            {% if user.conversations && user.conversations.length > 0 %}
            <div class="card mb-4">
                <div class="card-header bg-light d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Recent Conversations</h5>
                </div>
                <div class="card-body p-0">
                    <div class="list-group list-group-flush">
                        {% for conversation in user.conversations %}
                        <div class="list-group-item">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-1">{{ conversation.title || 'Untitled Conversation' }}</h6>
                                    <small class="text-muted">Created: {{ conversation.created_at }}</small>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
            {% endif %}
            
            <div class="card mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">Stats</h5>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-6 fw-bold">ELO Rating:</div>
                        <div class="col-md-6">{{ user.elo_rating }}</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6 fw-bold">Karma:</div>
                        <div class="col-md-6">{{ user.karma }}</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6 fw-bold">Challenges Completed:</div>
                        <div class="col-md-6">{{ user.total_challenges_completed }}</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6 fw-bold">Streak Count:</div>
                        <div class="col-md-6">{{ user.streak_count }}</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6 fw-bold">Daily Token Quota:</div>
                        <div class="col-md-6">{{ user.daily_llm_token_quota }}</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6 fw-bold">Current Token Usage:</div>
                        <div class="col-md-6">{{ user.daily_llm_token_usage }}</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6 fw-bold">Last Active:</div>
                        <div class="col-md-6">{{ user.last_active_date || 'Never' }}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
