<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nova Sonic Debug</title>
    <style>
        body {
            font-family: monospace;
            padding: 20px;
            background: #f5f5f5;
        }
        .section {
            background: white;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        pre {
            background: #f0f0f0;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <h1>Nova Sonic Debug Page</h1>
    
    <div class="section">
        <h2>1. Check Socket.IO Loading</h2>
        <button onclick="checkSocketIO()">Check Socket.IO</button>
        <pre id="socketio-result"></pre>
    </div>
    
    <div class="section">
        <h2>2. Test API Endpoints</h2>
        <button onclick="testEndpoints()">Test All Endpoints</button>
        <pre id="api-result"></pre>
    </div>
    
    <div class="section">
        <h2>3. Test Socket Connection</h2>
        <button onclick="testSocketConnection()">Test Connection</button>
        <pre id="socket-result"></pre>
    </div>
    
    <div class="section">
        <h2>4. Server Logs</h2>
        <pre id="logs"></pre>
    </div>

    <script src="/socket.io/socket.io.js"></script>
    <script>
        function log(section, message, type = 'info') {
            const element = document.getElementById(section);
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : 'info';
            element.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
        }

        function checkSocketIO() {
            const result = document.getElementById('socketio-result');
            result.innerHTML = '';
            
            if (typeof io === 'undefined') {
                log('socketio-result', 'Socket.IO is NOT loaded!', 'error');
            } else {
                log('socketio-result', 'Socket.IO is loaded', 'success');
                log('socketio-result', 'Socket.IO object properties:');
                for (let prop in io) {
                    if (typeof io[prop] !== 'function') {
                        log('socketio-result', `  ${prop}: ${io[prop]}`);
                    }
                }
            }
        }

        async function testEndpoints() {
            const result = document.getElementById('api-result');
            result.innerHTML = '';
            
            const endpoints = [
                '/api/socket-test',
                '/api/admin/nova-sonic',
                '/api/admin/nova-sonic/status',
                '/api/admin/nova-sonic/debug'
            ];
            
            for (const endpoint of endpoints) {
                try {
                    log('api-result', `Testing ${endpoint}...`);
                    const response = await fetch(endpoint);
                    const data = await response.json();
                    
                    if (response.ok) {
                        log('api-result', `✅ ${endpoint}: ${response.status}`, 'success');
                        log('api-result', JSON.stringify(data, null, 2));
                    } else {
                        log('api-result', `❌ ${endpoint}: ${response.status}`, 'error');
                        log('api-result', JSON.stringify(data, null, 2));
                    }
                } catch (error) {
                    log('api-result', `❌ ${endpoint}: ${error.message}`, 'error');
                }
                log('api-result', '---');
            }
        }

        function testSocketConnection() {
            const result = document.getElementById('socket-result');
            result.innerHTML = '';
            
            log('socket-result', 'Testing Socket.IO connection...');
            
            // Test 1: Basic connection
            const socket = io({
                transports: ['polling'],
                reconnection: false,
                timeout: 5000
            });
            
            socket.on('connect', () => {
                log('socket-result', '✅ Connected to default namespace', 'success');
                log('socket-result', `Socket ID: ${socket.id}`);
                log('socket-result', `Transport: ${socket.io.engine.transport.name}`);
                
                // Test 2: Nova Sonic namespace
                log('socket-result', '\nTesting Nova Sonic namespace...');
                
                const nsSocket = io('/ws/nova-sonic', {
                    transports: ['polling'],
                    reconnection: false
                });
                
                nsSocket.on('connect', () => {
                    log('socket-result', '✅ Connected to Nova Sonic namespace', 'success');
                    log('socket-result', `NS Socket ID: ${nsSocket.id}`);
                    
                    nsSocket.emit('ping');
                    nsSocket.disconnect();
                });
                
                nsSocket.on('connect_error', (error) => {
                    log('socket-result', `❌ Nova Sonic connection error: ${error.message}`, 'error');
                    log('socket-result', `Error type: ${error.type}`);
                });
                
                nsSocket.on('connected', (data) => {
                    log('socket-result', '✅ Received connected event:', 'success');
                    log('socket-result', JSON.stringify(data, null, 2));
                });
                
                setTimeout(() => {
                    socket.disconnect();
                }, 5000);
            });
            
            socket.on('connect_error', (error) => {
                log('socket-result', `❌ Connection error: ${error.message}`, 'error');
                log('socket-result', `Error type: ${error.type}`);
                
                // Try to get more info
                if (error.data) {
                    log('socket-result', `Error data: ${JSON.stringify(error.data)}`);
                }
            });
            
            // Monitor engine events
            socket.io.on('upgrade', (transport) => {
                log('socket-result', `📡 Upgraded to ${transport.name}`, 'info');
            });
            
            socket.io.on('packet', (packet) => {
                log('logs', `Packet: ${packet.type}`, 'info');
            });
        }
        
        // Auto-run checks on load
        window.addEventListener('load', () => {
            checkSocketIO();
        });
    </script>
</body>
</html>
