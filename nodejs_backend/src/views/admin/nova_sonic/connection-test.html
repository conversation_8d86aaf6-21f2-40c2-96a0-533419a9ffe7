<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nova Sonic Connection Test</title>
    <script src="/socket.io/socket.io.js"></script>
    <style>
        body {
            font-family: monospace;
            padding: 20px;
            background: #1e1e1e;
            color: #d4d4d4;
        }
        #console {
            background: #252526;
            border: 1px solid #464647;
            border-radius: 4px;
            padding: 15px;
            height: 600px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .error { color: #f48771; }
        .success { color: #89d185; }
        .info { color: #9cdcfe; }
        .warn { color: #dcdcaa; }
        button {
            margin: 10px 0;
            padding: 10px 20px;
            background: #0e639c;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #1177bb;
        }
    </style>
</head>
<body>
    <h1>Nova Sonic Connection Test</h1>
    <button onclick="runTest()">Run Connection Test</button>
    <button onclick="clearConsole()">Clear Console</button>
    <div id="console"></div>

    <script>
        const consoleDiv = document.getElementById('console');
        
        // Override console methods to display in div
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        function addToConsole(message, className = '') {
            const timestamp = new Date().toLocaleTimeString();
            const line = document.createElement('div');
            line.className = className;
            line.textContent = `[${timestamp}] ${message}`;
            consoleDiv.appendChild(line);
            consoleDiv.scrollTop = consoleDiv.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            const message = args.map(arg => 
                typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
            ).join(' ');
            
            if (message.includes('✅')) {
                addToConsole(message, 'success');
            } else if (message.includes('📡') || message.includes('📤') || message.includes('📨')) {
                addToConsole(message, 'info');
            } else {
                addToConsole(message);
            }
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            const message = args.map(arg => 
                typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
            ).join(' ');
            addToConsole(message, 'error');
        };
        
        console.warn = function(...args) {
            originalWarn.apply(console, args);
            const message = args.map(arg => 
                typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
            ).join(' ');
            addToConsole(message, 'warn');
        };
        
        function clearConsole() {
            consoleDiv.innerHTML = '';
        }
        
        function runTest() {
            clearConsole();
            const script = document.createElement('script');
            script.src = '/js/nova-sonic-test.js?' + Date.now(); // Cache bust
            document.body.appendChild(script);
        }
        
        // Auto-run on load
        window.addEventListener('load', runTest);
    </script>
</body>
</html>
