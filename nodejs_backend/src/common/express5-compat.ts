/**
 * Express 5 Compatibility Layer
 * 
 * This module provides compatibility fixes for Express 5.x
 */

import { Router } from 'express';

// Store the original Router constructor
const OriginalRouter = Router;

// Create a wrapper that catches path-to-regexp errors
export function createRouter(): Router {
  const router = OriginalRouter();
  
  // Wrap the use method to catch errors
  const originalUse = router.use.bind(router);
  router.use = function(...args: any[]) {
    try {
      // Log the path being registered
      if (typeof args[0] === 'string') {
        console.log(`[Router] Registering path: ${args[0]}`);
        
        // Check for common Express 5 incompatible patterns
        const path = args[0];
        if (path.includes('/:') && !path.match(/:\w+/)) {
          console.error(`[ERROR] Invalid route pattern detected: "${path}" - missing parameter name after colon`);
          throw new Error(`Invalid route pattern: "${path}" - Express 5 requires parameter names after colons`);
        }
      }
      
      return originalUse(...args);
    } catch (error) {
      console.error('[Router] Error in router.use:', error);
      console.error('[Router] Arguments:', args);
      throw error;
    }
  };
  
  // Wrap other methods that might use path-to-regexp
  ['get', 'post', 'put', 'delete', 'patch'].forEach(method => {
    const original = (router as any)[method].bind(router);
    (router as any)[method] = function(...args: any[]) {
      try {
        if (typeof args[0] === 'string') {
          console.log(`[Router] Registering ${method.toUpperCase()} ${args[0]}`);
          
          // Check for invalid patterns
          const path = args[0];
          if (path.includes('/:') && !path.match(/:\w+/)) {
            console.error(`[ERROR] Invalid route pattern detected: "${path}" - missing parameter name after colon`);
            throw new Error(`Invalid route pattern: "${path}" - Express 5 requires parameter names after colons`);
          }
        }
        
        return original(...args);
      } catch (error) {
        console.error(`[Router] Error in router.${method}:`, error);
        console.error('[Router] Arguments:', args);
        throw error;
      }
    };
  });
  
  return router;
}

// Export a function to patch Express globally
export function patchExpress() {
  const express = require('express');
  const originalRouter = express.Router;
  
  express.Router = function() {
    return createRouter();
  };
  
  // Preserve static properties
  Object.setPrototypeOf(express.Router, originalRouter);
  Object.keys(originalRouter).forEach(key => {
    (express.Router as any)[key] = (originalRouter as any)[key];
  });
}
