import { Request, Response, NextFunction } from 'express';
import { logger } from '../logger';

export interface ApiError extends Error {
  statusCode?: number;
  isOperational?: boolean;
}

/**
 * Secure error handler middleware
 * Prevents sensitive information leakage in production
 */
export const secureErrorHandler = (
  err: ApiError,
  req: Request,
  res: Response,
  next: NextFunction
) => {
  // Default to 500 if no status code is set
  const statusCode = err.statusCode || 500;
  
  // Log the full error internally
  logger.error(`Error ${statusCode}: ${err.message}`, {
    stack: err.stack,
    statusCode,
    url: req.url,
    method: req.method,
    ip: req.ip,
    userId: (req as any).user?.id
  });

  // Determine if we're in development
  const isDevelopment = process.env.NODE_ENV === 'development';

  // Prepare error response
  const errorResponse: any = {
    success: false,
    error: {
      message: err.isOperational || isDevelopment 
        ? err.message 
        : 'An error occurred processing your request',
      statusCode
    }
  };

  // Only include stack trace in development
  if (isDevelopment && err.stack) {
    errorResponse.error.stack = err.stack.split('\n');
  }

  // Add request ID if available for tracking
  if ((req as any).id) {
    errorResponse.error.requestId = (req as any).id;
  }

  res.status(statusCode).json(errorResponse);
};

/**
 * 404 handler
 */
export const notFoundHandler = (req: Request, res: Response) => {
  res.status(404).json({
    success: false,
    error: {
      message: 'Resource not found',
      statusCode: 404,
      path: req.path
    }
  });
};

/**
 * Create operational error
 */
export const createError = (message: string, statusCode: number = 500): ApiError => {
  const error: ApiError = new Error(message);
  error.statusCode = statusCode;
  error.isOperational = true;
  return error;
};
