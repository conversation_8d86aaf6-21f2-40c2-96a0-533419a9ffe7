import { Request, Response, NextFunction, RequestHandler } from 'express';

/**
 * Wraps controller methods to handle async operations and provide
 * consistent error handling across all route handlers.
 *
 * This utility function:
 * 1. Converts async controller methods to proper Express RequestHandlers
 * 2. Provides centralized error handling
 * 3. Ensures TypeScript compatibility
 *
 * @param controllerMethod - The async controller method to wrap
 * @returns Express RequestHandler
 */
export function wrapController(
  controllerMethod: (req: Request, res: Response, next?: NextFunction) => Promise<any>,
): RequestHandler {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      await controllerMethod(req, res, next);
    } catch (error) {
      // Pass errors to Express error handling middleware
      next(error);
    }
  };
}

/**
 * Alternative wrapper for controller methods that don't need next function
 */
export function wrapAsyncHandler(
  handler: (req: Request, res: Response) => Promise<any>,
): RequestHandler {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      await handler(req, res);
    } catch (error) {
      next(error);
    }
  };
}

export default wrapController;
