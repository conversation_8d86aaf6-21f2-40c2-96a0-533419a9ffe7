#!/usr/bin/env node

/**
 * Test Canvas Mode flow to see what's happening
 */

const axios = require('axios');

async function testCanvasFlow() {
  console.log('🔧 Testing Canvas Mode flow...\n');
  
  try {
    const BASE_URL = 'http://localhost:3000';
    
    // Simulate what Canvas Mode should send
    console.log('✅ Testing conversation stream endpoint...');
    
    // Create a conversation first
    const createResponse = await axios.post(`${BASE_URL}/conversations`, {
      title: "Canvas Task",
      taskType: "slide_generation"
    }, {
      headers: {
        'Content-Type': 'application/json',
        // Mock authorization - in real app this would be a valid JWT
        'Authorization': 'Bearer mock-token'
      }
    });
    
    console.log('✅ Create conversation response:', createResponse.status);
    const conversationId = createResponse.data.id;
    
    // Now send a message with slide generation task type
    const streamResponse = await axios.post(`${BASE_URL}/conversations/${conversationId}/stream`, {
      prompt: "Generate a modern banking application slide deck with reveal.js",
      model: "claude-3.7-sonnet",
      temperature: 0.7,
      maxTokens: 8192,
      taskType: "slide_generation",
      memoryCount: 0
    }, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer mock-token'
      },
      timeout: 60000
    });
    
    console.log('✅ Stream response status:', streamResponse.status);
    console.log('✅ Stream response headers:', streamResponse.headers['content-type']);
    
    // This would be streaming data in real scenario
    console.log('✅ Response preview:', streamResponse.data.toString().substring(0, 500));
    
  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
    
    if (error.response?.status === 401) {
      console.log('💡 Note: Authentication required. This is expected.');
      console.log('💡 The important thing is to verify the request structure.');
    } else if (error.response?.status === 500) {
      console.log('💡 Server error. Check backend logs for details.');
      console.log('💡 Error details:', error.response?.data);
    }
  }
}

testCanvasFlow();