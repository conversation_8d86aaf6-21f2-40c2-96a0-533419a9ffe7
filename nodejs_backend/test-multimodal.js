/**
 * Test script for multimodal AI endpoints
 * Run with: node test-multimodal.js
 */

const API_BASE = 'http://localhost:3000/api/ai';

// Test image URL (publicly accessible)
const TEST_IMAGE_URL = 'https://via.placeholder.com/800x600/0066cc/ffffff?text=Sample+Mockup';

async function testHealthEndpoint() {
  console.log('🏥 Testing health endpoint...');
  try {
    const response = await fetch(`${API_BASE}/multimodal/health`);
    const data = await response.json();
    console.log('✅ Health check:', data);
    return true;
  } catch (error) {
    console.error('❌ Health check failed:', error.message);
    return false;
  }
}

async function testImageAnalysis() {
  console.log('🖼️ Testing image analysis endpoint...');
  try {
    const response = await fetch(`${API_BASE}/image/analyze`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer dev-token' // Development token
      },
      body: JSON.stringify({
        imageUrl: TEST_IMAGE_URL,
        prompt: 'Describe this placeholder image and suggest how it could be improved as a mockup',
        modelType: 'gpt-4.1-mini',
        maxTokens: 500,
        temperature: 0.7
      })
    });

    const data = await response.json();
    
    if (response.ok) {
      console.log('✅ Image analysis successful');
      console.log('📊 Usage:', data.usage);
      console.log('🔍 Analysis preview:', data.content.substring(0, 200) + '...');
      return true;
    } else {
      console.error('❌ Image analysis failed:', data);
      return false;
    }
  } catch (error) {
    console.error('❌ Image analysis error:', error.message);
    return false;
  }
}

async function testMockupProcessing() {
  console.log('🎨 Testing mockup processing endpoint...');
  try {
    const response = await fetch(`${API_BASE}/mockups/process`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer dev-token' // Development token
      },
      body: JSON.stringify({
        images: [
          {
            url: TEST_IMAGE_URL,
            description: 'Sample placeholder mockup for testing'
          }
        ],
        instructions: 'Make this mockup more modern and user-friendly. Add proper navigation and improve the visual hierarchy.',
        modelType: 'gpt-4.1-mini',
        maxTokens: 1000,
        temperature: 0.7,
        outputFormat: 'both',
        designStyle: 'modern',
        targetPlatform: 'web'
      })
    });

    const data = await response.json();
    
    if (response.ok) {
      console.log('✅ Mockup processing successful');
      console.log('📊 Usage:', data.usage);
      console.log('🔍 Analysis preview:', data.analysis.substring(0, 200) + '...');
      console.log('💡 Recommendations count:', data.recommendations.length);
      return true;
    } else {
      console.error('❌ Mockup processing failed:', data);
      return false;
    }
  } catch (error) {
    console.error('❌ Mockup processing error:', error.message);
    return false;
  }
}

async function runTests() {
  console.log('🚀 Starting multimodal AI endpoint tests...\n');
  
  const healthOk = await testHealthEndpoint();
  console.log('');
  
  if (healthOk) {
    const imageAnalysisOk = await testImageAnalysis();
    console.log('');
    
    const mockupProcessingOk = await testMockupProcessing();
    console.log('');
    
    console.log('📋 Test Results Summary:');
    console.log(`   Health: ${healthOk ? '✅' : '❌'}`);
    console.log(`   Image Analysis: ${imageAnalysisOk ? '✅' : '❌'}`);
    console.log(`   Mockup Processing: ${mockupProcessingOk ? '✅' : '❌'}`);
    
    if (healthOk && imageAnalysisOk && mockupProcessingOk) {
      console.log('\n🎉 All tests passed! Multimodal AI endpoints are working correctly.');
    } else {
      console.log('\n⚠️ Some tests failed. Check the logs above for details.');
    }
  } else {
    console.log('❌ Health check failed - service may not be running or configured properly.');
  }
}

// Add fetch polyfill for Node.js if needed
if (typeof fetch === 'undefined') {
  console.log('Installing fetch polyfill...');
  global.fetch = require('node-fetch');
}

runTests().catch(console.error);