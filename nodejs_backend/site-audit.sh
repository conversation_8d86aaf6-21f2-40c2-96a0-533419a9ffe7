#!/bin/bash

# Complete Site Audit Script
# Combines link checking and screenshot capture

URL=${1:-"http://localhost:3001"}
OUTPUT_DIR=${2:-"./audit-results"}
MODE=${3:-"full"} # "links", "screenshots", or "full"

echo "🚀 Starting site audit for: $URL"
echo "📁 Output directory: $OUTPUT_DIR"
echo "🔧 Mode: $MODE"
echo "============================================"

# Create output directory
mkdir -p "$OUTPUT_DIR"
mkdir -p "$OUTPUT_DIR/screenshots"
mkdir -p "$OUTPUT_DIR/link-reports"

if [ "$MODE" = "links" ] || [ "$MODE" = "full" ]; then
    echo ""
    echo "🔍 STEP 1: Checking for broken links..."
    echo "========================================"
    
    # Basic link check - save to file
    echo "Running comprehensive link check..."
    blc "$URL" --recursive --filter-level 2 --ordered > "$OUTPUT_DIR/link-reports/full-report.txt" 2>&1
    
    # Quick internal-only check
    echo "Running internal links check..."
    blc "$URL" --recursive --exclude-external --filter-level 2 > "$OUTPUT_DIR/link-reports/internal-only.txt" 2>&1
    
    echo "✅ Link check complete! Reports saved to:"
    echo "   - $OUTPUT_DIR/link-reports/full-report.txt"
    echo "   - $OUTPUT_DIR/link-reports/internal-only.txt"
fi

if [ "$MODE" = "screenshots" ] || [ "$MODE" = "full" ]; then
    echo ""
    echo "📸 STEP 2: Taking full-page screenshots..."
    echo "=========================================="
    
    # Check if we're in the right directory for Playwright
    if [ -f "../new_ide/package.json" ]; then
        cd ../new_ide
        echo "📦 Using Playwright from new_ide directory..."
        npx playwright install chromium --force > /dev/null 2>&1
        cd ../nodejs_backend
    fi
    
    # Take screenshots using our custom script
    node screenshot-pages.js "$URL" "$OUTPUT_DIR/screenshots"
    
    echo "✅ Screenshots complete! Saved to: $OUTPUT_DIR/screenshots/"
fi

echo ""
echo "🎉 Site audit complete!"
echo "========================================"
echo "📊 Results summary:"
echo "   🔗 Link reports: $OUTPUT_DIR/link-reports/"
echo "   📸 Screenshots: $OUTPUT_DIR/screenshots/"
echo ""
echo "💡 Quick commands for next time:"
echo "   Links only:      ./site-audit.sh http://localhost:3001 $OUTPUT_DIR links"
echo "   Screenshots only: ./site-audit.sh http://localhost:3001 $OUTPUT_DIR screenshots"
echo "   Full audit:       ./site-audit.sh http://localhost:3001 $OUTPUT_DIR full"