/**
 * Test script for conversation multimodal task integration
 * Tests the complete flow from frontend VoiceAgent to backend multimodal task processing
 */

const API_BASE = 'http://localhost:3000/api';

// Test image URL (publicly accessible)
const TEST_IMAGE_URL = 'https://via.placeholder.com/800x600/0066cc/ffffff?text=Dashboard+Mockup';

// Test authentication token
const AUTH_TOKEN = 'dev-token'; // Use development token

async function testCreateConversation() {
  console.log('📝 Creating new conversation...');
  try {
    const response = await fetch(`${API_BASE}/conversations`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${AUTH_TOKEN}`
      },
      body: JSON.stringify({
        title: 'Multimodal Test Conversation',
        strategy: 'multimodal'
      })
    });

    const data = await response.json();
    
    if (response.ok) {
      console.log('✅ Conversation created:', data.id);
      return data.id;
    } else {
      console.error('❌ Failed to create conversation:', data);
      return null;
    }
  } catch (error) {
    console.error('❌ Error creating conversation:', error.message);
    return null;
  }
}

async function testMultimodalTask(conversationId) {
  console.log('🖼️ Testing multimodal task endpoint...');
  try {
    const response = await fetch(`${API_BASE}/tasks/multimodal`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${AUTH_TOKEN}`
      },
      body: JSON.stringify({
        prompt: 'I created this dashboard mockup and would like your feedback on the design.',
        conversationId: conversationId,
        images: [{
          url: TEST_IMAGE_URL,
          description: 'Dashboard mockup created in KAPI IDE'
        }],
        instructions: 'Please analyze this dashboard mockup and provide specific recommendations for improving the user experience and visual design.',
        outputFormat: 'both',
        designStyle: 'modern',
        targetPlatform: 'web',
        model: 'gpt-4.1-mini', // Use smaller model for testing
        maxTokens: 4000,
        temperature: 0.7
      })
    });

    const data = await response.json();
    
    if (response.ok) {
      console.log('✅ Multimodal task successful!');
      console.log('📊 Response data:');
      console.log('   - Status:', data.status);
      console.log('   - Model:', data.model);
      console.log('   - Processing time:', data.processingTime, 'ms');
      console.log('   - Tokens:', data.promptTokens, '/', data.completionTokens);
      console.log('   - Cost: $', data.cost?.toFixed(6) || 'N/A');
      console.log('   - Message preview:', data.message?.content?.substring(0, 200) + '...');
      
      if (data.structuredData) {
        console.log('   - Structured data available:', !!data.structuredData);
        console.log('   - Recommendations count:', data.structuredData.recommendations?.length || 0);
      }
      
      return data;
    } else {
      console.error('❌ Multimodal task failed:', data);
      return null;
    }
  } catch (error) {
    console.error('❌ Error in multimodal task:', error.message);
    return null;
  }
}

async function testConversationContext(conversationId) {
  console.log('💬 Testing conversation context with follow-up...');
  try {
    // Send a follow-up message that references the previous mockup analysis
    const response = await fetch(`${API_BASE}/tasks/multimodal`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${AUTH_TOKEN}`
      },
      body: JSON.stringify({
        prompt: 'Based on your previous analysis, can you create a more detailed breakdown of the navigation improvements?',
        conversationId: conversationId,
        images: [{
          url: TEST_IMAGE_URL,
          description: 'Same dashboard mockup for follow-up analysis'
        }],
        instructions: 'Focus specifically on navigation and user flow improvements based on our previous discussion.',
        outputFormat: 'description',
        designStyle: 'modern',
        targetPlatform: 'web',
        model: 'gpt-4.1-mini',
        maxTokens: 2000,
        temperature: 0.7
      })
    });

    const data = await response.json();
    
    if (response.ok) {
      console.log('✅ Follow-up analysis successful!');
      console.log('   - Contextual response length:', data.message?.content?.length || 0, 'chars');
      console.log('   - Response preview:', data.message?.content?.substring(0, 150) + '...');
      return true;
    } else {
      console.error('❌ Follow-up analysis failed:', data);
      return false;
    }
  } catch (error) {
    console.error('❌ Error in follow-up analysis:', error.message);
    return false;
  }
}

async function testConversationHistory(conversationId) {
  console.log('📜 Checking conversation history...');
  try {
    const response = await fetch(`${API_BASE}/conversations/${conversationId}`, {
      headers: {
        'Authorization': `Bearer ${AUTH_TOKEN}`
      }
    });

    const data = await response.json();
    
    if (response.ok) {
      console.log('✅ Conversation history retrieved:');
      console.log('   - Total messages:', data.messages?.length || 0);
      console.log('   - Conversation title:', data.conversation?.title);
      
      if (data.messages) {
        data.messages.forEach((msg, index) => {
          console.log(`   - Message ${index + 1} (${msg.role}): ${msg.content.substring(0, 100)}...`);
        });
      }
      
      return true;
    } else {
      console.error('❌ Failed to get conversation history:', data);
      return false;
    }
  } catch (error) {
    console.error('❌ Error getting conversation history:', error.message);
    return false;
  }
}

async function testHealthEndpoints() {
  console.log('🏥 Testing health endpoints...');
  
  const endpoints = [
    '/api/ai/multimodal/health',
    '/health'
  ];
  
  for (const endpoint of endpoints) {
    try {
      const response = await fetch(`${API_BASE}${endpoint}`);
      const data = await response.json();
      
      if (response.ok) {
        console.log(`✅ ${endpoint}: Healthy`);
      } else {
        console.log(`⚠️ ${endpoint}: ${data.error || 'Unknown error'}`);
      }
    } catch (error) {
      console.log(`❌ ${endpoint}: ${error.message}`);
    }
  }
}

async function runFullIntegrationTest() {
  console.log('🚀 Starting Conversation Multimodal Integration Test...\n');
  
  // Test health endpoints first
  await testHealthEndpoints();
  console.log('');
  
  // Create a new conversation
  const conversationId = await testCreateConversation();
  if (!conversationId) {
    console.log('❌ Cannot continue without a conversation. Exiting.');
    return;
  }
  console.log('');
  
  // Test multimodal analysis
  const analysisResult = await testMultimodalTask(conversationId);
  if (!analysisResult) {
    console.log('❌ Multimodal analysis failed. Exiting.');
    return;
  }
  console.log('');
  
  // Test conversation context with follow-up
  const contextResult = await testConversationContext(conversationId);
  console.log('');
  
  // Check conversation history
  const historyResult = await testConversationHistory(conversationId);
  console.log('');
  
  // Summary
  console.log('📋 Integration Test Results:');
  console.log(`   ✅ Conversation Creation: Success`);
  console.log(`   ${analysisResult ? '✅' : '❌'} Multimodal Analysis: ${analysisResult ? 'Success' : 'Failed'}`);
  console.log(`   ${contextResult ? '✅' : '❌'} Context Awareness: ${contextResult ? 'Success' : 'Failed'}`);
  console.log(`   ${historyResult ? '✅' : '❌'} History Retrieval: ${historyResult ? 'Success' : 'Failed'}`);
  
  if (analysisResult && contextResult && historyResult) {
    console.log('\n🎉 All integration tests passed! The VoiceAgent multimodal integration is working correctly.');
    console.log('\n🔧 Ready for use:');
    console.log('   - Sketch in VoiceAgent → Export → Auto-analyze with conversation context');
    console.log('   - Follow-up questions build on previous analysis');
    console.log('   - Full conversation history maintained');
  } else {
    console.log('\n⚠️ Some tests failed. Check the logs above for details.');
  }
}

// Add fetch polyfill for Node.js if needed
if (typeof fetch === 'undefined') {
  console.log('Installing fetch polyfill...');
  global.fetch = require('node-fetch');
}

runFullIntegrationTest().catch(console.error);