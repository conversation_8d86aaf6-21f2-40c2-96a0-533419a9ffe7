#!/usr/bin/env node

// Try to use <PERSON><PERSON> from new_ide directory if available
let playwright;
try {
  playwright = require('playwright');
} catch (e) {
  try {
    playwright = require('../new_ide/node_modules/playwright');
  } catch (e2) {
    console.error('❌ Playwright not found. Please install it:');
    console.error('   npm install playwright');
    console.error('   npx playwright install chromium');
    process.exit(1);
  }
}

const { chromium } = playwright;
const fs = require('fs');
const path = require('path');

async function takeFullPageScreenshots() {
  const baseUrl = process.argv[2] || 'http://localhost:3001';
  const outputDir = process.argv[3] || './screenshots';
  
  // Create output directory
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
  }

  console.log(`📸 Taking full-page screenshots of: ${baseUrl}`);
  console.log(`📁 Output directory: ${outputDir}`);
  
  const browser = await chromium.launch();
  const context = await browser.newContext({
    viewport: { width: 1920, height: 1080 }
  });
  
  const pages = [
    { name: 'home', path: '/' },
    { name: 'features', path: '/features' },
    { name: 'benefits', path: '/benefits' },
    // Add more pages as needed
  ];

  for (const page of pages) {
    console.log(`📷 Capturing: ${page.name} (${page.path})`);
    
    const webPage = await context.newPage();
    
    try {
      // Navigate to page
      await webPage.goto(`${baseUrl}${page.path}`, { 
        waitUntil: 'networkidle',
        timeout: 30000 
      });
      
      // Wait for initial page load
      await webPage.waitForTimeout(2000);
      
      // Wait for Lottie animations to load (they need time to initialize)
      console.log(`   ⏳ Waiting for Lottie animations to load...`);
      
      // Try to wait for Lottie iframes to load, fallback to timeout
      try {
        await webPage.waitForSelector('iframe[src*="lottie.host"]', { 
          timeout: 8000, 
          state: 'attached' 
        });
        console.log(`   🎬 Lottie iframes detected, waiting for content...`);
        await webPage.waitForTimeout(3000); // Additional time for animation content
      } catch (e) {
        console.log(`   ⏰ Using fallback timeout for animations...`);
        await webPage.waitForTimeout(5000);
      }
      
      // Scroll slowly and wait for animations at each section
      console.log(`   📜 Scrolling through page to load all animations...`);
      await webPage.evaluate(() => {
        return new Promise((resolve) => {
          let totalHeight = 0;
          const distance = 200; // Larger steps
          const scrollHeight = document.body.scrollHeight;
          
          const scrollStep = () => {
            if (totalHeight >= scrollHeight) {
              // Scroll back to top
              window.scrollTo(0, 0);
              resolve();
              return;
            }
            
            window.scrollBy(0, distance);
            totalHeight += distance;
            
            // Wait longer at each step to allow animations to initialize
            setTimeout(scrollStep, 500); // 500ms between scroll steps
          };
          
          scrollStep();
        });
      });
      
      // Wait for all Lottie animations that may have loaded during scroll
      console.log(`   🎬 Waiting for all animations to fully load...`);
      
      // Wait for any additional iframes that loaded during scroll
      await webPage.waitForTimeout(2000);
      
      // Check if more Lottie iframes appeared and wait for them
      try {
        const iframeCount = await webPage.$$eval('iframe[src*="lottie.host"]', iframes => iframes.length);
        console.log(`   📊 Found ${iframeCount} Lottie animations, waiting for full load...`);
        await webPage.waitForTimeout(Math.max(3000, iframeCount * 1000)); // Scale wait time with number of animations
      } catch (e) {
        await webPage.waitForTimeout(5000);
      }
      
      // Take full page screenshot
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const filename = `${page.name}-fullpage-${timestamp}.png`;
      const filepath = path.join(outputDir, filename);
      
      await webPage.screenshot({
        path: filepath,
        fullPage: true,
        animations: 'disabled' // Disable animations for consistent screenshots
      });
      
      console.log(`✅ Saved: ${filename}`);
      
    } catch (error) {
      console.error(`❌ Error capturing ${page.name}:`, error.message);
    } finally {
      await webPage.close();
    }
  }

  await browser.close();
  console.log('🎉 Screenshot capture complete!');
}

// Add mobile screenshots function
async function takeMobileScreenshots() {
  const baseUrl = process.argv[2] || 'http://localhost:3001';
  const outputDir = process.argv[3] || './screenshots';
  
  console.log(`📱 Taking mobile screenshots of: ${baseUrl}`);
  
  const browser = await chromium.launch();
  const context = await browser.newContext({
    viewport: { width: 390, height: 844 }, // iPhone 12 Pro size
    userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15'
  });
  
  const pages = [
    { name: 'home-mobile', path: '/' },
    { name: 'features-mobile', path: '/features' },
    { name: 'benefits-mobile', path: '/benefits' },
  ];

  for (const page of pages) {
    console.log(`📱 Capturing mobile: ${page.name} (${page.path})`);
    
    const webPage = await context.newPage();
    
    try {
      await webPage.goto(`${baseUrl}${page.path}`, { 
        waitUntil: 'networkidle',
        timeout: 30000 
      });
      
      // Wait for initial page load
      await webPage.waitForTimeout(2000);
      
      // Wait for Lottie animations to load (mobile)
      console.log(`   ⏳ Waiting for Lottie animations to load...`);
      
      // Try to wait for Lottie iframes to load, fallback to timeout
      try {
        await webPage.waitForSelector('iframe[src*="lottie.host"]', { 
          timeout: 8000, 
          state: 'attached' 
        });
        console.log(`   🎬 Lottie iframes detected, waiting for content...`);
        await webPage.waitForTimeout(3000); // Additional time for animation content
      } catch (e) {
        console.log(`   ⏰ Using fallback timeout for animations...`);
        await webPage.waitForTimeout(5000);
      }
      
      // Scroll slowly for mobile to load all animations
      console.log(`   📜 Scrolling through page to load all animations...`);
      await webPage.evaluate(() => {
        return new Promise((resolve) => {
          let totalHeight = 0;
          const distance = 200; // Larger steps
          const scrollHeight = document.body.scrollHeight;
          
          const scrollStep = () => {
            if (totalHeight >= scrollHeight) {
              // Scroll back to top
              window.scrollTo(0, 0);
              resolve();
              return;
            }
            
            window.scrollBy(0, distance);
            totalHeight += distance;
            
            // Wait longer at each step to allow animations to initialize
            setTimeout(scrollStep, 500); // 500ms between scroll steps
          };
          
          scrollStep();
        });
      });
      
      // Wait for all mobile animations
      console.log(`   🎬 Waiting for all mobile animations to fully load...`);
      
      // Wait for any additional iframes that loaded during scroll
      await webPage.waitForTimeout(2000);
      
      // Check if more Lottie iframes appeared and wait for them
      try {
        const iframeCount = await webPage.$$eval('iframe[src*="lottie.host"]', iframes => iframes.length);
        console.log(`   📊 Found ${iframeCount} Lottie animations, waiting for full load...`);
        await webPage.waitForTimeout(Math.max(3000, iframeCount * 1000)); // Scale wait time with number of animations
      } catch (e) {
        await webPage.waitForTimeout(5000);
      }
      
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const filename = `${page.name}-${timestamp}.png`;
      const filepath = path.join(outputDir, filename);
      
      await webPage.screenshot({
        path: filepath,
        fullPage: true,
        animations: 'disabled'
      });
      
      console.log(`✅ Saved mobile: ${filename}`);
      
    } catch (error) {
      console.error(`❌ Error capturing mobile ${page.name}:`, error.message);
    } finally {
      await webPage.close();
    }
  }

  await browser.close();
}

// Main execution
async function main() {
  const mode = process.argv[4] || 'both'; // 'desktop', 'mobile', or 'both'
  
  if (mode === 'desktop' || mode === 'both') {
    await takeFullPageScreenshots();
  }
  
  if (mode === 'mobile' || mode === 'both') {
    await takeMobileScreenshots();
  }
}

main().catch(console.error);