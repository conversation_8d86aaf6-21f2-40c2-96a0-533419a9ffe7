import axios from 'axios';
import * as https from 'https';
import { promises as fs } from 'fs';
import path from 'path';

// Interface for tool handlers
export interface ToolHandler {
  name: string;
  description: string;
  schema: string;
  execute: (content: any) => Promise<any>;
}

// Interface for tool registry
export interface ToolRegistry {
  registerTool: (tool: ToolHandler) => void;
  getTool: (name: string) => ToolHandler | undefined;
  getAllTools: () => ToolHandler[];
  processToolUse: (toolName: string, toolUseContent: any) => Promise<any>;
}

// Implementation of the tool registry
export class DefaultToolRegistry implements ToolRegistry {
  private tools: Map<string, ToolHandler> = new Map();

  constructor() {
    // Register default tools
    this.registerTool(getDateAndTimeTool);
    this.registerTool(getWeatherTool);
    
    // Register Demo 1 tools
    this.registerTool(generateProductIdeaTool);
    this.registerTool(generateSlideDeckTool);
    this.registerTool(generateLofiMockupTool);
    this.registerTool(createProjectScaffoldTool);
    this.registerTool(demo1WorkflowOrchestratorTool);
    this.registerTool(conversationalProductBuilderTool);
  }

  registerTool(tool: ToolHandler): void {
    this.tools.set(tool.name.toLowerCase(), tool);
  }

  getTool(name: string): ToolHandler | undefined {
    return this.tools.get(name.toLowerCase());
  }

  getAllTools(): ToolHandler[] {
    return Array.from(this.tools.values());
  }

  async processToolUse(toolName: string, toolUseContent: any): Promise<any> {
    console.log(`🔧 [TOOL-REGISTRY] Processing tool use: ${toolName}`);
    
    const tool = this.getTool(toolName.toLowerCase());
    
    if (!tool) {
      console.error(`🔧 [TOOL-REGISTRY] Tool ${toolName} not found in registry`);
      throw new Error(`Tool ${toolName} not supported`);
    }
    
    console.log(`🔧 [TOOL-REGISTRY] Found tool ${toolName}, executing...`);
    
    try {
      const result = await tool.execute(toolUseContent);
      console.log(`🔧 [TOOL-REGISTRY] Tool ${toolName} execution completed:`, {
        success: result.success,
        hasResult: !!result,
        resultType: typeof result
      });
      return result;
    } catch (error) {
      console.error(`🔧 [TOOL-REGISTRY] Error executing tool ${toolName}:`, error);
      throw error;
    }
  }
}

// Date and Time Tool
export const getDateAndTimeTool: ToolHandler = {
  name: "getDateAndTimeTool",
  description: "Get information about the current date and time.",
  schema: JSON.stringify({
    "type": "object",
    "properties": {},
    "required": []
  }),
  execute: async () => {
    const date = new Date().toLocaleString("en-US", { timeZone: "America/Los_Angeles" });
    const pstDate = new Date(date);
    return {
      date: pstDate.toISOString().split('T')[0],
      year: pstDate.getFullYear(),
      month: pstDate.getMonth() + 1,
      day: pstDate.getDate(),
      dayOfWeek: pstDate.toLocaleString('en-US', { weekday: 'long' }).toUpperCase(),
      timezone: "PST",
      formattedTime: pstDate.toLocaleTimeString('en-US', {
        hour12: true,
        hour: '2-digit',
        minute: '2-digit'
      })
    };
  }
};

// Weather Tool
export const getWeatherTool: ToolHandler = {
  name: "getWeatherTool",
  description: "Get the current weather for a given location, based on its WGS84 coordinates.",
  schema: JSON.stringify({
    "type": "object",
    "properties": {
      "latitude": {
        "type": "string",
        "description": "Geographical WGS84 latitude of the location."
      },
      "longitude": {
        "type": "string",
        "description": "Geographical WGS84 longitude of the location."
      }
    },
    "required": ["latitude", "longitude"]
  }),
  execute: async (toolUseContent: any) => {
    try {
      console.log('Weather tool received content:', JSON.stringify(toolUseContent));
      const parsedContent = await parseToolUseContentForWeather(toolUseContent);
      if (!parsedContent) {
        return {
          error: 'Failed to parse weather tool content. Please provide latitude and longitude.',
          success: false
        };
      }
      return await fetchWeatherData(parsedContent.latitude, parsedContent.longitude);
    } catch (error) {
      console.error('Weather tool execution error:', error);
      return {
        error: `Weather data unavailable: ${error instanceof Error ? error.message : 'Unknown error'}`,
        success: false
      };
    }
  }
};

// Helper function to parse weather tool content
async function parseToolUseContentForWeather(toolUseContent: any): Promise<{ latitude: number; longitude: number; } | null> {
  try {
    // Check if the content field exists and is a string
    if (toolUseContent && typeof toolUseContent.content === 'string') {
      // Parse the JSON string into an object
      const parsedContent = JSON.parse(toolUseContent.content);
      console.log(`parsedContent ${parsedContent}`);
      // Return the parsed content
      return {
        latitude: parsedContent.latitude,
        longitude: parsedContent.longitude
      };
    }
    return null;
  } catch (error) {
    console.error("Failed to parse tool use content:", error);
    return null;
  }
}

// Helper function to fetch weather data
async function fetchWeatherData(
  latitude: number,
  longitude: number
): Promise<Record<string, any>> {
  const ipv4Agent = new https.Agent({ family: 4 });
  const url = `https://api.open-meteo.com/v1/forecast?latitude=${latitude}&longitude=${longitude}&current_weather=true`;

  try {
    const response = await axios.get(url, {
      httpsAgent: ipv4Agent,
      timeout: 5000,
      headers: {
        'User-Agent': 'MyApp/1.0',
        'Accept': 'application/json'
      }
    });
    const weatherData = response.data;
    console.log("weatherData:", weatherData);

    return {
      weather_data: weatherData
    };
  } catch (error) {
    if (axios.isAxiosError(error)) {
      console.error(`Error fetching weather data: ${error.message}`, error);
    } else {
      console.error(`Unexpected error: ${error instanceof Error ? error.message : String(error)} `, error);
    }
    throw error;
  }
}

// Demo 1 Tools Implementation

// Product Idea Document Generation Tool
export const generateProductIdeaTool: ToolHandler = {
  name: "generateProductIdeaTool",
  description: "Generate a product idea document based on user requirements and conversation context",
  schema: JSON.stringify({
    "type": "object",
    "properties": {
      "productName": {
        "type": "string",
        "description": "Name of the product"
      },
      "description": {
        "type": "string", 
        "description": "Brief description of the product idea"
      },
      "targetAudience": {
        "type": "string",
        "description": "Primary target audience for the product"
      },
      "keyFeatures": {
        "type": "array",
        "items": { "type": "string" },
        "description": "List of key features the product should have"
      },
      "useCase": {
        "type": "string",
        "description": "Primary use case or problem the product solves"
      }
    },
    "required": ["productName", "description"]
  }),
  execute: async (toolUseContent: any) => {
    try {
      console.log('Product idea generation tool received:', JSON.stringify(toolUseContent));
      const parsedContent = await parseToolUseContent(toolUseContent);
      
      if (!parsedContent) {
        return {
          error: 'Failed to parse product idea tool content',
          success: false
        };
      }

      // Call the nodejs backend to generate the product idea document
      const result = await callBackendService('/api/tools/generate-product-idea', parsedContent);
      
      return {
        success: true,
        productIdeaDocument: result.document,
        filePath: result.filePath,
        message: 'Product idea document generated successfully'
      };
    } catch (error) {
      console.error('Product idea generation error:', error);
      return {
        error: `Product idea generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        success: false
      };
    }
  }
};

// Slide Deck Generation Tool
export const generateSlideDeckTool: ToolHandler = {
  name: "generateSlideDeckTool", 
  description: "Help users create slide deck presentations for their product ideas. When a user mentions wanting to create slides, a presentation, or pitch deck for a product/app/tool, use this tool. First gather essential information through conversation (product name, description, target audience, key features), then generate the slides. Ask clarifying questions if information is missing.",
  schema: JSON.stringify({
    "type": "object",
    "properties": {
      "action": {
        "type": "string",
        "enum": ["gather_info", "generate_slides"],
        "description": "Whether to ask for more information or generate slides"
      },
      "productName": {
        "type": "string",
        "description": "Name of the product (required for generation)"
      },
      "productDescription": {
        "type": "string", 
        "description": "Brief description of what the product does"
      },
      "targetAudience": {
        "type": "string",
        "description": "Who the product is for"
      },
      "keyFeatures": {
        "type": "array",
        "items": { "type": "string" },
        "description": "Main features or capabilities"
      },
      "problemSolved": {
        "type": "string",
        "description": "What problem this product solves"
      },
      "missingInfo": {
        "type": "array",
        "items": { "type": "string" },
        "description": "List of information still needed from the user"
      },
      "theme": {
        "type": "string",
        "description": "Visual theme for the slides (modern, minimal, corporate)",
        "default": "modern"
      }
    },
    "required": ["action"]
  }),
  execute: async (toolUseContent: any) => {
    try {
      console.log('🔧 [SLIDE-TOOL] Slide deck generation tool received:', JSON.stringify(toolUseContent));
      const parsedContent = await parseToolUseContent(toolUseContent);
      
      if (!parsedContent) {
        console.log('🔧 [SLIDE-TOOL] Failed to parse tool content, returning error');
        return {
          error: 'Failed to parse slide deck tool content - content may be missing or malformed',
          success: false,
          debug: {
            originalContent: toolUseContent,
            contentType: typeof toolUseContent
          }
        };
      }
      
      console.log('🔧 [SLIDE-TOOL] Parsed content successfully:', JSON.stringify(parsedContent, null, 2));

      // If no action is specified, treat as information gathering for conversational flow
      if (!parsedContent.action) {
        console.log('🔧 [SLIDE-TOOL] No action specified, defaulting to gather_info');
        parsedContent.action = 'gather_info';
      }

      // Check if this is information gathering or slide generation
      if (parsedContent.action === 'gather_info') {
        // Return questions to ask the user
        const questions = [];
        
        if (!parsedContent.productName) {
          questions.push("What's the name of your product or app?");
        }
        if (!parsedContent.productDescription) {
          questions.push("Can you briefly describe what your product does?");
        }
        if (!parsedContent.targetAudience) {
          questions.push("Who is your target audience or primary users?");
        }
        if (!parsedContent.keyFeatures || parsedContent.keyFeatures.length === 0) {
          questions.push("What are the main features or capabilities you want to highlight?");
        }
        if (!parsedContent.problemSolved) {
          questions.push("What problem does this product solve for users?");
        }

        return {
          success: true,
          needsMoreInfo: true,
          questions: questions,
          message: "I need a few more details to create great slides for you. " + questions[0]
        };
      }

      // If action is 'generate_slides', check if we have enough information
      const requiredFields = ['productName', 'productDescription', 'targetAudience'];
      const missingFields = requiredFields.filter(field => !parsedContent[field]);
      
      if (missingFields.length > 0) {
        return {
          success: false,
          needsMoreInfo: true,
          message: `I still need some information: ${missingFields.join(', ')}. Can you provide these details?`
        };
      }

      // We have enough information - generate the slides!
      const slideData = {
        productName: parsedContent.productName,
        slides: [
          { title: parsedContent.productName, content: parsedContent.productDescription },
          { title: 'Problem Statement', content: parsedContent.problemSolved || 'Key challenges addressed' },
          { title: 'Target Audience', content: parsedContent.targetAudience },
          { title: 'Key Features', content: parsedContent.keyFeatures?.join(', ') || 'Core functionality' },
          { title: 'Next Steps', content: 'Development roadmap and implementation plan' }
        ],
        theme: parsedContent.theme || 'modern'
      };

      // Call the nodejs backend to generate the slide deck
      const result = await callBackendService('/api/tools/generate-slide-deck', slideData);
      
      return {
        success: true,
        slidesGenerated: true,
        slideDeckHtml: result.html,
        filePath: result.filePath,
        message: 'Slide deck generated successfully'
      };
    } catch (error) {
      console.error('Slide deck generation error:', error);
      return {
        error: `Slide deck generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        success: false
      };
    }
  }
};

// Lo-Fi Mockup Generation Tool
export const generateLofiMockupTool: ToolHandler = {
  name: "generateLofiMockupTool",
  description: "Generate a lo-fi SVG mockup based on product requirements",
  schema: JSON.stringify({
    "type": "object", 
    "properties": {
      "productType": {
        "type": "string",
        "description": "Type of product (web app, mobile app, dashboard, etc.)"
      },
      "keyFeatures": {
        "type": "array",
        "items": { "type": "string" },
        "description": "Key features to include in the mockup"
      },
      "layoutStyle": {
        "type": "string",
        "description": "Layout style preference (simple, detailed, modern, classic)"
      },
      "targetPlatform": {
        "type": "string",
        "description": "Target platform (web, mobile, desktop)"
      }
    },
    "required": ["productType", "keyFeatures"]
  }),
  execute: async (toolUseContent: any) => {
    try {
      console.log('Lo-fi mockup generation tool received:', JSON.stringify(toolUseContent));
      const parsedContent = await parseToolUseContent(toolUseContent);
      
      if (!parsedContent) {
        return {
          error: 'Failed to parse lo-fi mockup tool content',
          success: false
        };
      }

      // Call the nodejs backend to generate the SVG mockup
      const result = await callBackendService('/api/tools/generate-lofi-mockup', parsedContent);
      
      return {
        success: true,
        svgContent: result.svg,
        filePath: result.filePath,
        message: 'Lo-fi mockup generated successfully'
      };
    } catch (error) {
      console.error('Lo-fi mockup generation error:', error);
      return {
        error: `Lo-fi mockup generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        success: false
      };
    }
  }
};

// Project Scaffolding Tool
export const createProjectScaffoldTool: ToolHandler = {
  name: "createProjectScaffoldTool",
  description: "Generate a complete project scaffold with frontend and backend files",
  schema: JSON.stringify({
    "type": "object",
    "properties": {
      "projectName": {
        "type": "string",
        "description": "Name of the project"
      },
      "tech": {
        "type": "object",
        "properties": {
          "frontend": { "type": "string" },
          "backend": { "type": "string" },
          "database": { "type": "string" }
        },
        "description": "Technology stack preferences"
      },
      "features": {
        "type": "array",
        "items": { "type": "string" },
        "description": "Features to include in the scaffold"
      },
      "projectType": {
        "type": "string",
        "description": "Type of project (saas, ecommerce, portfolio, etc.)"
      }
    },
    "required": ["projectName", "projectType"]
  }),
  execute: async (toolUseContent: any) => {
    try {
      console.log('Project scaffold generation tool received:', JSON.stringify(toolUseContent));
      const parsedContent = await parseToolUseContent(toolUseContent);
      
      if (!parsedContent) {
        return {
          error: 'Failed to parse project scaffold tool content',
          success: false
        };
      }

      // Call the nodejs backend to generate the project scaffold
      const result = await callBackendService('/api/tools/create-project-scaffold', parsedContent);
      
      return {
        success: true,
        generatedFiles: result.files,
        projectPath: result.projectPath,
        message: 'Project scaffold created successfully'
      };
    } catch (error) {
      console.error('Project scaffold generation error:', error);
      return {
        error: `Project scaffold generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        success: false
      };
    }
  }
};

// Helper function to parse tool use content (generic for all Demo 1 tools)
async function parseToolUseContent(toolUseContent: any): Promise<any | null> {
  try {
    console.log('🔧 [PARSER] Parsing tool use content:', JSON.stringify(toolUseContent, null, 2));
    
    // Handle different possible formats
    if (!toolUseContent) {
      console.log('🔧 [PARSER] No tool use content provided');
      return null;
    }
    
    // If it's already an object with expected properties, return as-is
    if (typeof toolUseContent === 'object' && !toolUseContent.content) {
      console.log('🔧 [PARSER] Tool content is already an object, using directly');
      return toolUseContent;
    }
    
    // If it has a content property that's a string, parse it
    if (toolUseContent.content && typeof toolUseContent.content === 'string') {
      console.log('🔧 [PARSER] Parsing JSON content string');
      return JSON.parse(toolUseContent.content);
    }
    
    // If it has a content property that's an object, use it
    if (toolUseContent.content && typeof toolUseContent.content === 'object') {
      console.log('🔧 [PARSER] Using content object directly');
      return toolUseContent.content;
    }
    
    // If it's a string, try to parse as JSON
    if (typeof toolUseContent === 'string') {
      console.log('🔧 [PARSER] Parsing string as JSON');
      return JSON.parse(toolUseContent);
    }
    
    console.log('🔧 [PARSER] Using tool content as-is');
    return toolUseContent;
  } catch (error) {
    console.error("🔧 [PARSER] Failed to parse tool use content:", error);
    console.error("🔧 [PARSER] Original content:", toolUseContent);
    return null;
  }
}

// Demo 1 Workflow Orchestrator Tool
export const demo1WorkflowOrchestratorTool: ToolHandler = {
  name: "demo1WorkflowOrchestratorTool",
  description: "Orchestrate the complete Demo 1 workflow: voice input → product idea → slides → mockup → scaffold",
  schema: JSON.stringify({
    "type": "object",
    "properties": {
      "productIdea": {
        "type": "string",
        "description": "The initial product idea from voice input"
      },
      "details": {
        "type": "object",
        "properties": {
          "targetAudience": { "type": "string" },
          "keyFeatures": { "type": "array", "items": { "type": "string" } },
          "projectType": { "type": "string" }
        },
        "description": "Additional product details gathered from conversation"
      },
      "workflowStep": {
        "type": "string",
        "enum": ["start", "idea_ready", "slides_ready", "mockup_ready", "complete"],
        "description": "Current step in the workflow"
      }
    },
    "required": ["productIdea"]
  }),
  execute: async (toolUseContent: any) => {
    try {
      console.log('Demo 1 workflow orchestrator received:', JSON.stringify(toolUseContent));
      const parsedContent = await parseToolUseContent(toolUseContent);
      
      if (!parsedContent) {
        return {
          error: 'Failed to parse workflow orchestrator content',
          success: false
        };
      }

      const { productIdea, details, workflowStep = 'start' } = parsedContent;
      
      // Call the nodejs backend to orchestrate the complete workflow
      const result = await callBackendService('/api/tools/demo1-workflow', {
        productIdea,
        details,
        workflowStep
      });
      
      return {
        success: true,
        workflowResult: result,
        nextStep: result.nextStep,
        generatedAssets: result.assets,
        message: `Demo 1 workflow ${workflowStep} completed successfully`
      };
    } catch (error) {
      console.error('Demo 1 workflow orchestrator error:', error);
      return {
        error: `Demo 1 workflow failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        success: false
      };
    }
  }
};

// Helper function to call nodejs backend services
async function callBackendService(endpoint: string, data: any): Promise<any> {
  const backendUrl = process.env.BACKEND_URL || 'http://localhost:3000';
  
  try {
    console.log(`🔧 [BACKEND-CALL] Calling ${endpoint}`);
    console.log(`🔧 [BACKEND-CALL] URL: ${backendUrl}${endpoint}`);
    console.log(`🔧 [BACKEND-CALL] Request data:`, JSON.stringify(data, null, 2));
    
    const response = await axios.post(`${backendUrl}${endpoint}`, data, {
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'NovaSonic/1.0'
      },
      timeout: 30000
    });
    
    console.log(`🔧 [BACKEND-CALL] Response status: ${response.status}`);
    console.log(`🔧 [BACKEND-CALL] Response data:`, JSON.stringify(response.data, null, 2));
    
    return response.data;
  } catch (error) {
    console.error(`🔧 [BACKEND-CALL] Backend service call failed for ${endpoint}:`, error);
    throw error;
  }
}

// Conversational Product Builder Tool (Demo 1 Complete Flow)
export const conversationalProductBuilderTool: ToolHandler = {
  name: "conversationalProductBuilderTool",
  description: "Help users build a complete product from voice or chat input. When users mention wanting to create, build, develop, or make a product/app/tool/service, use this tool. It guides them through the complete Demo 1 workflow: gathering product info, generating slides, creating mockups, and building project scaffolds. Ask questions conversationally to gather all needed information.",
  schema: JSON.stringify({
    "type": "object",
    "properties": {
      "action": {
        "type": "string", 
        "enum": ["gather_info", "build_product"],
        "description": "Whether to ask for more info or start building"
      },
      "productName": {
        "type": "string",
        "description": "Name of the product/app/tool"
      },
      "productDescription": {
        "type": "string",
        "description": "What the product does"
      },
      "targetAudience": {
        "type": "string", 
        "description": "Primary users or customers"
      },
      "keyFeatures": {
        "type": "array",
        "items": { "type": "string" },
        "description": "Main features and capabilities"
      },
      "problemSolved": {
        "type": "string",
        "description": "What problem this solves"
      },
      "projectType": {
        "type": "string",
        "description": "Type of project (web app, mobile app, SaaS, etc.)"
      },
      "currentStep": {
        "type": "string",
        "enum": ["info_gathering", "slides", "mockup", "scaffold", "complete"],
        "description": "Current step in the workflow"
      }
    },
    "required": ["action"]
  }),
  execute: async (toolUseContent: any) => {
    try {
      console.log('🔧 [PRODUCT-BUILDER] Conversational product builder tool called');
      console.log('🔧 [PRODUCT-BUILDER] Raw content:', JSON.stringify(toolUseContent, null, 2));
      const parsedContent = await parseToolUseContent(toolUseContent);
      
      if (!parsedContent) {
        return {
          error: 'Failed to parse conversational product builder content',
          success: false
        };
      }

      // Information gathering phase - default behavior if no action or action is gather_info
      if (parsedContent.action === 'gather_info' || !parsedContent.action) {
        console.log('🔧 [PRODUCT-BUILDER] Gathering information phase');
        const questions = [];
        
        if (!parsedContent.productName) {
          questions.push("What would you like to call your product or app?");
        } else if (!parsedContent.productDescription) {
          questions.push("Can you describe what your product does in a sentence or two?");
        } else if (!parsedContent.targetAudience) {
          questions.push("Who is your target audience? Who would use this product?");
        } else if (!parsedContent.keyFeatures || parsedContent.keyFeatures.length === 0) {
          questions.push("What are the main features or capabilities you want to include?");
        } else if (!parsedContent.problemSolved) {
          questions.push("What problem does this product solve for users?");
        } else if (!parsedContent.projectType) {
          questions.push("What type of project is this? (web app, mobile app, desktop software, etc.)");
        }

        if (questions.length > 0) {
          console.log(`🔧 [PRODUCT-BUILDER] Need more info, asking: ${questions[0]}`);
          return {
            success: true,
            needsMoreInfo: true,
            questions: questions,
            message: questions[0]
          };
        } else {
          console.log('🔧 [PRODUCT-BUILDER] All info gathered, ready to build!');
          return {
            success: true,
            readyToBuild: true,
            message: "Perfect! I have all the information I need. Should I start building your product? I'll create slides, mockups, and project scaffolding for you."
          };
        }
      }

      // Building phase - execute the complete Demo 1 workflow
      if (parsedContent.action === 'build_product') {
        console.log('🔧 [PRODUCT-BUILDER] Building product phase started');
        const workflowData = {
          productIdea: `${parsedContent.productName}: ${parsedContent.productDescription}`,
          details: {
            targetAudience: parsedContent.targetAudience,
            keyFeatures: parsedContent.keyFeatures || [],
            projectType: parsedContent.projectType || 'web application'
          }
        };

        console.log('🔧 [PRODUCT-BUILDER] Calling backend service with data:', workflowData);
        
        // Call the Demo 1 workflow orchestrator
        const result = await callBackendService('/api/tools/demo1-workflow', workflowData);
        
        console.log('🔧 [PRODUCT-BUILDER] Backend service response:', {
          success: result.success,
          progress: result.progress,
          assetsCount: Object.keys(result.assets || {}).length
        });
        
        return {
          success: true,
          workflowComplete: true,
          progress: result.progress,
          assets: result.assets,
          message: `🎉 Your product "${parsedContent.productName}" has been built! Generated ${result.summary.completedSteps}/${result.summary.totalSteps} assets including slides, documentation, and mockups.`,
          projectPath: result.assets.productDocument?.filePath || 'Project files created'
        };
      }

      return {
        success: false,
        message: "I need you to specify whether you want to gather more info or build the product."
      };

    } catch (error) {
      console.error('Conversational product builder error:', error);
      return {
        error: `Product building failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        success: false
      };
    }
  }
};
