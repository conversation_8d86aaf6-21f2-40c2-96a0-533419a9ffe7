import express from 'express';
import http from 'http';
import path from 'path';
import { Server } from 'socket.io';
import { fromIni } from "@aws-sdk/credential-providers";
import { NovaSonicBidirectionalStreamClient } from './client';
import { Buffer } from 'node:buffer';
import cors from 'cors';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Configure AWS credentials
const useEnvCredentials = process.env.AWS_ACCESS_KEY_ID && process.env.AWS_SECRET_ACCESS_KEY;
const AWS_PROFILE_NAME = process.env.AWS_PROFILE || 'bedrock-test';

// Create Express app and HTTP server
const app = express();
const server = http.createServer(app);

// Configure security middleware
// CORS configuration - simplified for internal service
const corsOptions = {
    origin: process.env.ALLOWED_ORIGINS ? process.env.ALLOWED_ORIGINS.split(',') : ['http://localhost:3000', 'http://127.0.0.1:3000'],
    methods: ['GET', 'POST'],
    credentials: true
};
app.use(cors(corsOptions));

// Basic middleware
app.use(express.json());

// Serve static files from the public directory
app.use(express.static(path.join(__dirname, '../public')));

// Create Socket.IO server with CORS configuration
const io = new Server(server, {
    cors: corsOptions,
    pingTimeout: 60000,
    pingInterval: 25000
});

// Create the AWS Bedrock client
console.log('Initializing Nova Sonic Bedrock client...');
console.log(`Using ${useEnvCredentials ? 'environment' : 'profile'} credentials`);
if (!useEnvCredentials) {
    console.log(`AWS Profile: ${AWS_PROFILE_NAME}`);
}
console.log(`AWS Region: ${process.env.AWS_REGION || 'us-east-1'}`);

const bedrockClient = new NovaSonicBidirectionalStreamClient({
    requestHandlerConfig: {
        maxConcurrentStreams: 10,
    },
    clientConfig: {
        region: process.env.AWS_REGION || "us-east-1",
        credentials: useEnvCredentials
            ? {
                accessKeyId: process.env.AWS_ACCESS_KEY_ID!,
                secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY!,
                sessionToken: process.env.AWS_SESSION_TOKEN,
              }
            : fromIni({ profile: AWS_PROFILE_NAME })
    }
});

console.log('Nova Sonic Bedrock client initialized successfully');

// Session cleanup interval
setInterval(() => {
    const now = Date.now();
    const activeSessions = bedrockClient.getActiveSessions();
    
    if (process.env.LOG_LEVEL === 'debug') {
        console.log(`[CLEANUP] Checking ${activeSessions.length} active sessions`);
    }

    activeSessions.forEach(sessionId => {
        const lastActivity = bedrockClient.getLastActivityTime(sessionId);

        // If no activity for 5 minutes, force close
        if (now - lastActivity > 5 * 60 * 1000) {
            console.log(`[CLEANUP] Closing inactive session ${sessionId} after 5 minutes of inactivity`);
            try {
                bedrockClient.forceCloseSession(sessionId);
            } catch (error) {
                console.error(`[CLEANUP] Error force closing inactive session ${sessionId}:`, error);
            }
        }
    });
}, 60000);

// Socket.IO connection handler
io.on('connection', (socket) => {
    console.log(`[SOCKET] New client connected: ${socket.id}`);

    // Create a unique session ID for this client
    const sessionId = socket.id;
    
    // Send initial connected event
    socket.emit('connected', { 
        sessionId,
        status: 'awaiting_initialization',
        message: 'Connected to Nova Sonic WebSocket. Send "initializeSession" to start voice streaming.'
    });

    // Handler for explicit session initialization
    socket.on('initializeSession', async (data?: { systemPrompt?: string }) => {
        console.log(`[${sessionId}] Received initializeSession request`);
        
        try {
            // Check if session already exists and clean it up first
            if (bedrockClient.isSessionActive(sessionId)) {
                console.log(`[${sessionId}] Session already exists, cleaning up before reinitializing`);
                try {
                    await bedrockClient.closeSession(sessionId);
                } catch (error) {
                    console.warn(`[${sessionId}] Error cleaning up existing session:`, error);
                    bedrockClient.forceCloseSession(sessionId);
                }
                // Wait a bit for cleanup to complete
                await new Promise(resolve => setTimeout(resolve, 500));
            }
            
            // Create session with the new API
            const session = bedrockClient.createStreamSession(sessionId);
            
            // Set up event handlers
            session.onEvent('contentStart', (data) => {
                if (process.env.LOG_LEVEL === 'debug') {
                    console.log(`[${sessionId}] contentStart:`, data);
                }
                socket.emit('contentStart', data);
            });

            session.onEvent('textOutput', (data) => {
                console.log(`[${sessionId}] Text output:`, data);
                socket.emit('textOutput', data);
            });

            session.onEvent('audioOutput', (data) => {
                if (process.env.LOG_LEVEL === 'debug') {
                    console.log(`[${sessionId}] Audio output received, size: ${data?.content?.length || 0}`);
                }
                socket.emit('audioOutput', data);
            });

            session.onEvent('error', (data) => {
                console.error(`[${sessionId}] Error in session:`, data);
                socket.emit('error', data);
            });

            session.onEvent('sessionTimeout', (data) => {
                console.log(`[${sessionId}] Session timeout:`, data);
                socket.emit('sessionTimeout', data);
            });

            session.onEvent('toolUse', (data) => {
                console.log(`[${sessionId}] Tool use detected:`, data.toolName);
                socket.emit('toolUse', data);
            });

            session.onEvent('toolResult', (data) => {
                console.log(`[${sessionId}] Tool result received`);
                socket.emit('toolResult', data);
            });

            session.onEvent('contentEnd', (data) => {
                if (process.env.LOG_LEVEL === 'debug') {
                    console.log(`[${sessionId}] Content end:`, data);
                }
                socket.emit('contentEnd', data);
            });

            session.onEvent('streamComplete', () => {
                console.log(`[${sessionId}] Stream completed`);
                socket.emit('streamComplete');
            });
            
            // Initiate the AWS Bedrock session
            bedrockClient.initiateSession(sessionId).catch(error => {
                console.error(`[SESSION] Failed to initiate session ${sessionId}:`, error);
                socket.emit('error', {
                    message: 'Failed to initiate AWS Bedrock session',
                    details: error instanceof Error ? error.message : String(error),
                    type: 'bedrock_session_error'
                });
            });

            // Send success confirmation
            socket.emit('sessionInitialized', {
                sessionId,
                status: 'ready',
                message: 'Session initialized successfully. You can now start streaming.'
            });

            // Set up socket handlers for this session
            setupSocketHandlers(socket, session, sessionId);

        } catch (error) {
            console.error(`[${sessionId}] Error initializing session:`, error);
            socket.emit('error', {
                message: 'Failed to initialize session',
                details: error instanceof Error ? error.message : String(error),
                type: 'initialization_error'
            });
        }
    });

    // Handler for checking session status
    socket.on('getSessionStatus', () => {
        const isActive = bedrockClient.isSessionActive(sessionId);
        
        socket.emit('sessionStatus', {
            sessionId,
            initialized: isActive,
            active: isActive,
            timestamp: new Date().toISOString()
        });
    });

    // Add disconnect handler for non-initialized sockets
    socket.on('disconnect', (reason) => {
        console.log(`[${socket.id}] Client disconnected: ${reason}`);
        
        // Check if session exists and clean up
        if (bedrockClient.isSessionActive(sessionId)) {
            console.log(`[${sessionId}] Cleaning up active session on disconnect`);
            bedrockClient.forceCloseSession(sessionId);
        }
    });
});

// Function to set up socket handlers for an initialized session
function setupSocketHandlers(socket: any, session: any, sessionId: string) {

        // Socket event handlers
        socket.on('audioInput', async (audioData: any) => {
            try {
                if (!bedrockClient.isSessionActive(sessionId)) {
                    console.log(`[${sessionId}] Session not active, ignoring audio input`);
                    return;
                }

                // Convert base64 string to Buffer
                const audioBuffer = typeof audioData === 'string'
                    ? Buffer.from(audioData, 'base64')
                    : Buffer.from(audioData);

                // Stream the audio
                await session.streamAudio(audioBuffer);

            } catch (error) {
                console.error(`[${sessionId}] Error processing audio:`, error);
                socket.emit('error', {
                    message: 'Error processing audio',
                    details: error instanceof Error ? error.message : String(error)
                });
            }
        });

        socket.on('promptStart', async () => {
            try {
                console.log(`[${sessionId}] Prompt start received`);
                await session.setupPromptStart();
                socket.emit('promptStarted');
            } catch (error) {
                console.error(`[${sessionId}] Error processing prompt start:`, error);
                socket.emit('error', {
                    message: 'Error processing prompt start',
                    details: error instanceof Error ? error.message : String(error)
                });
            }
        });

        socket.on('systemPrompt', async (data: any) => {
            try {
                console.log(`[${sessionId}] System prompt received:`, data);
                await session.setupSystemPrompt(undefined, data);
                socket.emit('systemPromptSet');
            } catch (error) {
                console.error(`[${sessionId}] Error processing system prompt:`, error);
                socket.emit('error', {
                    message: 'Error processing system prompt',
                    details: error instanceof Error ? error.message : String(error)
                });
            }
        });

        socket.on('audioStart', async () => {
            try {
                console.log(`[${sessionId}] Audio start received`);

                // Reset session state if needed
                if (bedrockClient.isSessionActive(sessionId)) {
                    bedrockClient.resetSessionState(sessionId);
                }

                await session.setupStartAudio();
                console.log(`[${sessionId}] Audio start setup complete`);
                socket.emit('audioStarted');
            } catch (error) {
                console.error(`[${sessionId}] Error processing audio start:`, error);
                socket.emit('error', {
                    message: 'Error processing audio start',
                    details: error instanceof Error ? error.message : String(error)
                });
            }
        });

        socket.on('stopAudio', async () => {
            try {
                console.log(`[${sessionId}] Stop audio requested`);

                // Temporarily disable the session to stop audio processing
                const sessionDisabler = session.temporarilyDisable();

                try {
                    await session.endAudioContent();
                    await session.endPrompt();
                    session.resetState();
                    bedrockClient.resetSessionState(sessionId);
                    console.log(`[${sessionId}] Audio stopped, session reset`);
                } finally {
                    sessionDisabler.restore();
                }

                socket.emit('sessionReadyForNewStream', {
                    sessionId: sessionId,
                    timestamp: new Date().toISOString()
                });
            } catch (error) {
                console.error(`[${sessionId}] Error processing stop audio:`, error);
                socket.emit('error', {
                    message: 'Error processing stop audio',
                    details: error instanceof Error ? error.message : String(error)
                });
            }
        });

        // Handle disconnection
        socket.on('disconnect', async (reason: any) => {
            console.log(`[${sessionId}] Client disconnected: ${reason}`);

            if (bedrockClient.isSessionActive(sessionId)) {
                try {
                    const cleanupPromise = Promise.race([
                        (async () => {
                            const sessionDisabler = session.temporarilyDisable();
                            try {
                                session.clearAudioQueue();
                                if (bedrockClient.isSessionActive(sessionId)) {
                                    bedrockClient.resetSessionState(sessionId);
                                }
                                await new Promise(resolve => setTimeout(resolve, 200));
                                await session.endAudioContent();
                                await new Promise(resolve => setTimeout(resolve, 500));
                                await session.endPrompt();
                                await new Promise(resolve => setTimeout(resolve, 500));
                                await session.close();
                            } finally {
                                sessionDisabler.restore();
                            }
                        })(),
                        new Promise((_, reject) =>
                            setTimeout(() => reject(new Error('Session cleanup timeout')), 15000)
                        )
                    ]);

                    await cleanupPromise;
                    console.log(`[${sessionId}] Successfully cleaned up session after disconnect`);
                } catch (error) {
                    console.error(`[${sessionId}] Error during cleanup after disconnect:`, error);
                    try {
                        bedrockClient.forceCloseSession(sessionId);
                        console.log(`[${sessionId}] Force closed session`);
                    } catch (e) {
                        console.error(`[${sessionId}] Failed even force close:`, e);
                    }
                } finally {
                    if (socket.connected) {
                        socket.disconnect(true);
                    }
                }
            }
        });

        // Health check handler
        socket.on('health', () => {
            socket.emit('health-response', {
                status: 'ok',
                timestamp: new Date().toISOString(),
                sessionId: sessionId
            });
        });
}

// Health check endpoint
app.get('/health', (_, res) => {
    const activeSessions = bedrockClient.getActiveSessions();
    res.status(200).json({ 
        status: 'healthy', 
        service: 'nova-sonic',
        port: PORT,
        activeSessions: activeSessions.length,
        uptime: process.uptime(),
        timestamp: new Date().toISOString()
    });
});

// Session status endpoint
app.get('/sessions', (_, res) => {
    const activeSessions = bedrockClient.getActiveSessions();
    const sessionDetails = activeSessions.map(sessionId => ({
        sessionId,
        lastActivity: bedrockClient.getLastActivityTime(sessionId),
        age: Date.now() - bedrockClient.getLastActivityTime(sessionId)
    }));
    
    res.json({
        count: activeSessions.length,
        sessions: sessionDetails
    });
});

// Start the server
const PORT = process.env.PORT || 3005;
server.listen(PORT, () => {
    console.log(`Nova Sonic service listening on port ${PORT}`);
    console.log(`Health check available at http://localhost:${PORT}/health`);
});

// Graceful shutdown
process.on('SIGINT', async () => {
    console.log('Shutting down Nova Sonic service...');

    const forceExitTimer = setTimeout(() => {
        console.error('Forcing service shutdown after timeout');
        process.exit(1);
    }, 5000);

    try {
        // Close Socket.IO server
        await new Promise(resolve => io.close(resolve));
        console.log('Socket.IO server closed');

        // Close all active sessions
        const activeSessions = bedrockClient.getActiveSessions();
        console.log(`Closing ${activeSessions.length} active sessions...`);

        await Promise.all(activeSessions.map(async (sessionId) => {
            try {
                await bedrockClient.closeSession(sessionId);
                console.log(`Closed session ${sessionId} during shutdown`);
            } catch (error) {
                console.error(`Error closing session ${sessionId} during shutdown:`, error);
                bedrockClient.forceCloseSession(sessionId);
            }
        }));

        // Close HTTP server
        await new Promise(resolve => server.close(resolve));
        clearTimeout(forceExitTimer);
        console.log('Service shut down');
        process.exit(0);
    } catch (error) {
        console.error('Error during service shutdown:', error);
        process.exit(1);
    }
});
