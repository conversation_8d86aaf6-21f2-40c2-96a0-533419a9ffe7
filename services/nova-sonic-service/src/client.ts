import {
  BedrockRuntimeClient,
  BedrockRuntimeClientConfig,
  InvokeModelWithBidirectionalStreamCommand,
  InvokeModelWithBidirectionalStreamInput,
} from "@aws-sdk/client-bedrock-runtime";
import {
  NodeHttp2Handler,
  NodeHttp2HandlerOptions,
} from "@smithy/node-http-handler";
import { Provider } from "@smithy/types";
import { Buffer } from "node:buffer";
import { randomUUID } from "node:crypto";
import { InferenceConfig } from "./types";
import { Subject } from 'rxjs';
import { take } from 'rxjs/operators';
import { firstValueFrom } from 'rxjs';
import axios from 'axios';
import {
  DefaultAudioInputConfiguration,
  DefaultAudioOutputConfiguration,
  DefaultSystemPrompt,
  DefaultTextConfiguration
} from "./constants";
import { DefaultToolRegistry, ToolRegistry } from "./tools";

export interface NovaSonicBidirectionalStreamClientConfig {
  requestHandlerConfig?:
  | NodeHttp2HandlerOptions
  | Provider<NodeHttp2HandlerOptions | void>;
  clientConfig: Partial<BedrockRuntimeClientConfig>;
  inferenceConfig?: InferenceConfig;
  toolRegistry?: ToolRegistry;
}

export class StreamSession {
  private audioBufferQueue: Buffer[] = [];
  private maxQueueSize = 200; // Maximum number of audio chunks to queue
  private isProcessingAudio = false;
  private isActive = true;
  private lastActivityTimestamp = Date.now();
  private inactivityTimer: NodeJS.Timeout | null = null;
  private readonly INACTIVITY_TIMEOUT_MS = 30000; // 30 seconds inactivity timeout

  constructor(
    private sessionId: string,
    private client: NovaSonicBidirectionalStreamClient
  ) {
    // Start the inactivity timer when session is created
    this.startInactivityTimer();
  }

  // Register event handlers for this specific session
  public onEvent(eventType: string, handler: (data: any) => void): StreamSession {
    this.client.registerEventHandler(this.sessionId, eventType, handler);
    return this; // For chaining
  }

  public async setupPromptStart(): Promise<void> {
    this.client.setupPromptStartEvent(this.sessionId);
  }

  public async setupSystemPrompt(
    textConfig: typeof DefaultTextConfiguration = DefaultTextConfiguration,
    systemPromptContent: string = DefaultSystemPrompt): Promise<void> {
    this.client.setupSystemPromptEvent(this.sessionId, textConfig, systemPromptContent);
  }

  public async setupStartAudio(
    audioConfig: typeof DefaultAudioInputConfiguration = DefaultAudioInputConfiguration
  ): Promise<void> {
    this.client.setupStartAudioEvent(this.sessionId, audioConfig);
  }


  // Starts or resets the inactivity timer
  private startInactivityTimer(): void {
    // Clear any existing timer
    if (this.inactivityTimer) {
      clearTimeout(this.inactivityTimer);
    }

    // Start a new timer
    this.inactivityTimer = setTimeout(() => {
      this.handleInactivityTimeout();
    }, this.INACTIVITY_TIMEOUT_MS);

    // Update the timestamp
    this.lastActivityTimestamp = Date.now();
  }

  // Handles what happens when inactivity timeout is reached
  private async handleInactivityTimeout(): Promise<void> {
    if (!this.isActive) return;

    const timeSinceLastActivity = Date.now() - this.lastActivityTimestamp;

    if (timeSinceLastActivity >= this.INACTIVITY_TIMEOUT_MS) {
      console.log(`Session ${this.sessionId} has been inactive for ${timeSinceLastActivity}ms, allowing natural timeout`);

      // We're not sending keepalive - let the connection naturally time out
      // This prevents overbilling by allowing inactive sessions to disconnect

      // Don't restart the timer - we want the session to time out
      this.inactivityTimer = null;
    }
  }

  // Update activity timestamp and reset timer
  private updateActivity(): void {
    this.lastActivityTimestamp = Date.now();
    this.startInactivityTimer();
  }

  // Stream audio for this session
  public async streamAudio(audioData: Buffer): Promise<void> {
    // First check if session is active before processing
    if (!this.isActive) {
      console.log(`[${this.sessionId}] Ignoring audio data for inactive session`);
      return;
    }

    // Update activity timestamp since we received audio data
    this.updateActivity();

    // Check queue size to avoid memory issues
    if (this.audioBufferQueue.length >= this.maxQueueSize) {
      // Queue is full, drop oldest chunk
      this.audioBufferQueue.shift();
      console.log("Audio queue full, dropping oldest chunk");
    }

    // Queue the audio chunk for streaming
    this.audioBufferQueue.push(audioData);
    this.processAudioQueue();
  }

  // Process audio queue for continuous streaming
  private async processAudioQueue() {
    // First check if we should be processing at all
    if (this.isProcessingAudio || this.audioBufferQueue.length === 0) return;

    // Double check if session is active
    if (!this.isActive) {
      if (this.audioBufferQueue.length > 0) {
        // If session became inactive but queue still has items, clear it.
        console.log(`Session ${this.sessionId} inactive, clearing ${this.audioBufferQueue.length} pending audio chunks.`);
        this.audioBufferQueue = [];
      }
      return;
    }

    this.isProcessingAudio = true;
    try {
      // Process all chunks in the queue, up to a reasonable limit
      let processedChunks = 0;
      const maxChunksPerBatch = 5;

      // Check session active state before each chunk
      while (this.audioBufferQueue.length > 0 && processedChunks < maxChunksPerBatch && this.isActive) {
        // Double check session is still active before processing each chunk
        if (!this.isActive) {
          console.log(`Session ${this.sessionId} became inactive during processing, stopping`);
          this.audioBufferQueue = []; // Clear remaining queue
          break;
        }

        const audioChunk = this.audioBufferQueue.shift();
        if (audioChunk) {
          await this.client.streamAudioChunk(this.sessionId, audioChunk);
          processedChunks++;
        }
      }
    } finally {
      this.isProcessingAudio = false;

      // Only schedule next processing if session is still active
      if (this.audioBufferQueue.length > 0 && this.isActive) {
        setTimeout(() => {
          // Check again if session is active before scheduling next processing
          if (this.isActive) {
            this.processAudioQueue();
          } else {
            // Clear queue if session became inactive
            if (this.audioBufferQueue.length > 0) {
              console.log(`Session ${this.sessionId} inactive before next processing, clearing ${this.audioBufferQueue.length} chunks`);
              this.audioBufferQueue = [];
            }
          }
        }, 0);
      }
    }
  }

  public clearAudioQueue(): void {
    console.log(`[${this.sessionId}] Clearing audio buffer queue (${this.audioBufferQueue.length} items)`);
    this.audioBufferQueue = [];
  }

  // Get session ID
  public getSessionId(): string {
    return this.sessionId;
  }

  public async endAudioContent(): Promise<void> {
    if (!this.isActive) return;

    // Clear the audio buffer queue to prevent overflow on restart
    this.audioBufferQueue = [];
    this.isProcessingAudio = false;

    // Update activity timestamp
    this.updateActivity();

    await this.client.sendContentEnd(this.sessionId);
  }

  public async endPrompt(): Promise<void> {
    if (!this.isActive) return;
    await this.client.sendPromptEnd(this.sessionId);
  }

  public async close(): Promise<void> {
    if (!this.isActive) return;

    this.isActive = false;
    this.audioBufferQueue = []; // Clear any pending audio

    // Clean up timers on close
    if (this.inactivityTimer) {
      clearTimeout(this.inactivityTimer);
      this.inactivityTimer = null;
    }

    await this.client.sendSessionEnd(this.sessionId);
    console.log(`Session ${this.sessionId} close completed`);
  }

  // Reset the session state without closing it
  public resetState(): void {
    // Clear any pending audio data
    this.clearAudioQueue();

    // Reset internal state flags but keep the session active
    this.isProcessingAudio = false;

    // Reset the inactivity timer
    this.startInactivityTimer();

    // Force a pause in audio processing to ensure any in-flight processing completes
    setTimeout(() => {
      // Double-check that the queue is empty after a short delay
      if (this.audioBufferQueue.length > 0) {
        console.log(`[${this.sessionId}] Found ${this.audioBufferQueue.length} items still in queue after reset, clearing again`);
        this.audioBufferQueue = [];
      }
    }, 100);

    console.log(`Session ${this.sessionId} state reset`);
  }

  // Temporarily disable the session (for use during reset operations)
  public temporarilyDisable(): { restore: () => void } {
    // Store the current active state
    const originalActiveState = this.isActive;

    // Pause the inactivity timer
    if (this.inactivityTimer) {
      clearTimeout(this.inactivityTimer);
      this.inactivityTimer = null;
    }

    // Set to inactive
    this.isActive = false;

    // Clear the queue immediately
    this.clearAudioQueue();

    // Return a function to restore the original state
    return {
      restore: () => {
        this.isActive = originalActiveState;

        // If we're restoring to active state, restart the inactivity timer
        if (originalActiveState) {
          this.startInactivityTimer();
        }

        console.log(`[${this.sessionId}] Session active state restored to ${originalActiveState}`);
      }
    };
  }
}

// Session data type
interface SessionData {
  queue: Array<any>;
  queueSignal: Subject<void>;
  closeSignal: Subject<void>;
  responseSubject: Subject<any>;
  toolUseContent: any;
  toolUseId: string;
  toolName: string;
  responseHandlers: Map<string, (data: any) => void>;
  promptName: string;
  inferenceConfig: InferenceConfig;
  isActive: boolean;
  isPromptStartSent: boolean;
  isAudioContentStartSent: boolean;
  currentAudioContentId: string | null;
  processingToolCall: boolean;
}

export class NovaSonicBidirectionalStreamClient {
  private bedrockRuntimeClient: BedrockRuntimeClient;
  private inferenceConfig: InferenceConfig;
  private activeSessions: Map<string, SessionData> = new Map();
  private sessionLastActivity: Map<string, number> = new Map();
  private sessionCleanupInProgress = new Set<string>();
  private toolRegistry: ToolRegistry;


  constructor(config: NovaSonicBidirectionalStreamClientConfig) {
    const nodeHttp2Handler = new NodeHttp2Handler({
      requestTimeout: 300000,
      sessionTimeout: 300000,
      disableConcurrentStreams: false,
      maxConcurrentStreams: 20,
      ...config.requestHandlerConfig,
    });

    if (!config.clientConfig.credentials) {
      throw new Error("No credentials provided");
    }

    this.bedrockRuntimeClient = new BedrockRuntimeClient({
      ...config.clientConfig,
      credentials: config.clientConfig.credentials,
      region: config.clientConfig.region || "us-east-1",
      requestHandler: nodeHttp2Handler
    });

    this.inferenceConfig = config.inferenceConfig ?? {
      maxTokens: 1024,
      topP: 0.9,
      temperature: 0.7,
    };

    // Initialize the tool registry
    this.toolRegistry = config.toolRegistry ?? new DefaultToolRegistry();
  }

  public isSessionActive(sessionId: string): boolean {
    const session = this.activeSessions.get(sessionId);
    return !!session && session.isActive;
  }

  public getActiveSessions(): string[] {
    return Array.from(this.activeSessions.keys());
  }

  public getLastActivityTime(sessionId: string): number {
    return this.sessionLastActivity.get(sessionId) || 0;
  }

  private updateSessionActivity(sessionId: string): void {
    this.sessionLastActivity.set(sessionId, Date.now());
  }

  public isCleanupInProgress(sessionId: string): boolean {
    return this.sessionCleanupInProgress.has(sessionId);
  }


  // Create a new streaming session
  public createStreamSession(sessionId: string = randomUUID(), config?: NovaSonicBidirectionalStreamClientConfig): StreamSession {
    if (this.activeSessions.has(sessionId)) {
      throw new Error(`Stream session with ID ${sessionId} already exists`);
    }

    const session: SessionData = {
      queue: [],
      queueSignal: new Subject<void>(),
      closeSignal: new Subject<void>(),
      responseSubject: new Subject<any>(),
      toolUseContent: null,
      toolUseId: "",
      toolName: "",
      responseHandlers: new Map(),
      promptName: randomUUID(),
      inferenceConfig: config?.inferenceConfig ?? this.inferenceConfig,
      isActive: true,
      isPromptStartSent: false,
      isAudioContentStartSent: false,
      currentAudioContentId: null,
      processingToolCall: false
    };

    this.activeSessions.set(sessionId, session);

    return new StreamSession(sessionId, this);
  }

  private async processToolUse(toolName: string, toolUseContent: object): Promise<any> {
    try {
      console.log(`🔧 [NOVA-SONIC] Processing tool: ${toolName}`);
      console.log(`🔧 [NOVA-SONIC] Tool content:`, JSON.stringify(toolUseContent, null, 2));
      
      const result = await this.toolRegistry.processToolUse(toolName, toolUseContent);
      
      console.log(`🔧 [NOVA-SONIC] Tool ${toolName} result:`, {
        success: result?.success,
        hasFilePath: result?.filePath ? 'Yes' : 'No',
        hasProjectPath: result?.projectPath ? 'Yes' : 'No',
        resultKeys: Object.keys(result || {})
      });
      
      return result;
    } catch (error) {
      console.error(`🔧 [NOVA-SONIC] Error processing tool ${toolName}:`, error);
      throw error;
    }
  }

  // Stream audio for a specific session
  public async initiateSession(sessionId: string): Promise<void> {
    const session = this.activeSessions.get(sessionId);
    if (!session) {
      throw new Error(`Stream session ${sessionId} not found`);
    }

    try {
      // Set up initial events for this session
      this.setupSessionStartEvent(sessionId);

      // Create the bidirectional stream with session-specific async iterator
      const asyncIterable = this.createSessionAsyncIterable(sessionId);

      console.log(`Starting bidirectional stream for session ${sessionId}...`);

      const response = await this.bedrockRuntimeClient.send(
        new InvokeModelWithBidirectionalStreamCommand({
          modelId: "amazon.nova-sonic-v1:0",
          body: asyncIterable,
        })
      );

      console.log(`Stream established for session ${sessionId}, processing responses...`);

      // Process responses for this session
      await this.processResponseStream(sessionId, response);

    } catch (error) {
      if (axios.isAxiosError(error) && error.code === 'ECONNABORTED') {
        console.warn(`Timeout occurred in session ${sessionId}: `, error.message);
        this.dispatchEventForSession(sessionId, 'timeout', {
          source: 'bidirectionalStream',
          message: 'Session timed out, please try again.'
        });
      } else {
        console.error(`Error in session ${sessionId}: `, error);
        this.dispatchEventForSession(sessionId, 'error', {
          source: 'bidirectionalStream',
          error
        });
      }

      // Make sure to clean up if there's an error
      if (session.isActive) {
        this.closeSession(sessionId);
      }
    }
  }

  // Dispatch events to handlers for a specific session
  private dispatchEventForSession(sessionId: string, eventType: string, data: any): void {
    const session = this.activeSessions.get(sessionId);
    if (!session) return;

    const handler = session.responseHandlers.get(eventType);
    if (handler) {
      try {
        handler(data);
      } catch (e) {
        console.error(`Error in ${eventType} handler for session ${sessionId}: `, e);
      }
    }

    // Also dispatch to "any" handlers
    const anyHandler = session.responseHandlers.get('any');
    if (anyHandler) {
      try {
        anyHandler({ type: eventType, data });
      } catch (e) {
        console.error(`Error in 'any' handler for session ${sessionId}: `, e);
      }
    }
  }

  private createSessionAsyncIterable(sessionId: string): AsyncIterable<InvokeModelWithBidirectionalStreamInput> {

    if (!this.isSessionActive(sessionId)) {
      console.log(`Cannot create async iterable: Session ${sessionId} not active`);
      return {
        [Symbol.asyncIterator]: () => ({
          next: async () => ({ value: undefined, done: true })
        })
      };
    }

    const session = this.activeSessions.get(sessionId);
    if (!session) {
      throw new Error(`Cannot create async iterable: Session ${sessionId} not found`);
    }

    let eventCount = 0;

    return {
      [Symbol.asyncIterator]: () => {
        console.log(`AsyncIterable iterator requested for session ${sessionId}`);

        return {
          next: async (): Promise<IteratorResult<InvokeModelWithBidirectionalStreamInput>> => {
            try {
              // Check if session is still active
              if (!session.isActive || !this.activeSessions.has(sessionId)) {
                console.log(`Iterator closing for session ${sessionId}, done = true`);
                return { value: undefined, done: true };
              }
              // Wait for items in the queue or close signal
              if (session.queue.length === 0) {
                try {
                  await Promise.race([
                    firstValueFrom(session.queueSignal.pipe(take(1))),
                    firstValueFrom(session.closeSignal.pipe(take(1))).then(() => {
                      throw new Error("Stream closed signal received");
                    })
                  ]);
                } catch (error) {
                  if (error instanceof Error) {
                    if (error.message === "Stream closed" || !session.isActive) {
                      // This is an expected condition when closing the session
                      if (this.activeSessions.has(sessionId)) {
                        console.log(`Session ${sessionId} closed during wait`);
                      }
                      return { value: undefined, done: true };
                    }
                  }
                  else {
                    console.error(`Error on event close`, error)
                  }
                }
              }

              // If queue is still empty or session is inactive, we're done
              if (session.queue.length === 0 || !session.isActive) {
                console.log(`Queue empty or session inactive: ${sessionId} `);
                return { value: undefined, done: true };
              }

              // Get next item from the session's queue
              const nextEvent = session.queue.shift();
              eventCount++;

              //console.log(`Sending event #${ eventCount } for session ${ sessionId }: ${ JSON.stringify(nextEvent).substring(0, 100) }...`);

              return {
                value: {
                  chunk: {
                    bytes: new TextEncoder().encode(JSON.stringify(nextEvent))
                  }
                },
                done: false
              };
            } catch (error) {
              console.error(`Error in session ${sessionId} iterator: `, error);
              session.isActive = false;
              return { value: undefined, done: true };
            }
          },

          return: async (): Promise<IteratorResult<InvokeModelWithBidirectionalStreamInput>> => {
            console.log(`Iterator return () called for session ${sessionId}`);
            session.isActive = false;
            return { value: undefined, done: true };
          },

          throw: async (error: any): Promise<IteratorResult<InvokeModelWithBidirectionalStreamInput>> => {
            console.log(`Iterator throw () called for session ${sessionId} with error: `, error);
            session.isActive = false;
            throw error;
          }
        };
      }
    };
  }

  // Process the response stream from AWS Bedrock
  private async processResponseStream(sessionId: string, response: any): Promise<void> {
    const session = this.activeSessions.get(sessionId);
    if (!session) return;

    try {
      for await (const event of response.body) {
        if (!session.isActive) {
          console.log(`Session ${sessionId} is no longer active, stopping response processing`);
          break;
        }
        if (event.chunk?.bytes) {
          try {
            this.updateSessionActivity(sessionId);
            const textResponse = new TextDecoder().decode(event.chunk.bytes);

            try {
              // Log all responses for debugging
              console.log(`[${sessionId}] Raw response: ${textResponse.substring(0, 200)}${textResponse.length > 200 ? '...' : ''}`);

              const jsonResponse = JSON.parse(textResponse);
              console.log(`[${sessionId}] Parsed response event type:`, Object.keys(jsonResponse.event || {}).join(', '));

              if (jsonResponse.event?.contentStart) {
                console.log(`[${sessionId}] contentStart event:`, JSON.stringify(jsonResponse.event.contentStart).substring(0, 200));
                this.dispatchEvent(sessionId, 'contentStart', jsonResponse.event.contentStart);
              } else if (jsonResponse.event?.textOutput) {
                console.log(`[${sessionId}] textOutput event: "${jsonResponse.event.textOutput.content}"`);
                this.dispatchEvent(sessionId, 'textOutput', jsonResponse.event.textOutput);
              } else if (jsonResponse.event?.audioOutput) {
                console.log(`[${sessionId}] audioOutput event received`);
                this.dispatchEvent(sessionId, 'audioOutput', jsonResponse.event.audioOutput);
              } else if (jsonResponse.event?.toolUse) {
                console.log(`🔧 [NOVA-SONIC] Tool use event received for session ${sessionId}:`, {
                  toolName: jsonResponse.event.toolUse.toolName,
                  toolUseId: jsonResponse.event.toolUse.toolUseId,
                  contentSize: JSON.stringify(jsonResponse.event.toolUse.content || {}).length
                });
                this.dispatchEvent(sessionId, 'toolUse', jsonResponse.event.toolUse);

                // Store tool use information for later
                session.toolUseContent = jsonResponse.event.toolUse;
                session.toolUseId = jsonResponse.event.toolUse.toolUseId;
                session.toolName = jsonResponse.event.toolUse.toolName;
              } else if (jsonResponse.event?.contentEnd &&
                jsonResponse.event?.contentEnd?.type === 'TOOL') {

                // Process tool use
                console.log(`🔧 [NOVA-SONIC] Processing tool use for session ${sessionId}:`, {
                  toolName: session.toolName,
                  toolUseId: session.toolUseId,
                  hasContent: session.toolUseContent ? 'Yes' : 'No'
                });
                this.dispatchEvent(sessionId, 'toolEnd', {
                  toolUseContent: session.toolUseContent,
                  toolUseId: session.toolUseId,
                  toolName: session.toolName
                });

                console.log(`🔧 [NOVA-SONIC] Calling tool execution for ${session.toolName}`);
                console.log(`🔧 [NOVA-SONIC] Tool use content:`, session.toolUseContent)
                
                try {
                  // Mark that we're processing a tool call
                  session.processingToolCall = true;
                  
                  // function calling
                  const toolResult = await this.processToolUse(session.toolName, session.toolUseContent);

                  // Only send tool result if session is still active
                  if (session.isActive) {
                    console.log(`🔧 [NOVA-SONIC] Tool execution completed for ${session.toolName}:`, {
                      success: toolResult?.success,
                      resultType: typeof toolResult,
                      hasFilePath: toolResult?.filePath ? 'Yes' : 'No',
                      hasProjectPath: toolResult?.projectPath ? 'Yes' : 'No'
                    });
                    
                    // Send tool result
                    this.sendToolResult(sessionId, session.toolUseId, toolResult);

                    // Also dispatch event about tool result
                    this.dispatchEvent(sessionId, 'toolResult', {
                      toolUseId: session.toolUseId,
                      result: toolResult
                    });
                  } else {
                    console.log(`🔧 [NOVA-SONIC] Session ${sessionId} became inactive during tool processing, skipping result`);
                  }
                } catch (toolError) {
                  console.error(`🔧 [NOVA-SONIC] Tool processing error for session ${sessionId}:`, {
                    toolName: session.toolName,
                    toolUseId: session.toolUseId,
                    error: toolError instanceof Error ? toolError.message : String(toolError)
                  });
                  
                  // Send error result if session is still active
                  if (session.isActive) {
                    const errorResult = {
                      success: false,
                      error: 'Tool processing failed',
                      details: toolError instanceof Error ? toolError.message : 'Unknown error'
                    };
                    console.log(`🔧 [NOVA-SONIC] Sending error result for ${session.toolName}:`, errorResult);
                    this.sendToolResult(sessionId, session.toolUseId, errorResult);
                  }
                } finally {
                  // Mark tool call as complete
                  console.log(`🔧 [NOVA-SONIC] Tool call processing finished for ${session.toolName} (session: ${sessionId})`);
                  session.processingToolCall = false;
                }
              } else if (jsonResponse.event?.contentEnd) {
                console.log(`[${sessionId}] contentEnd event:`, JSON.stringify(jsonResponse.event.contentEnd).substring(0, 200));
                this.dispatchEvent(sessionId, 'contentEnd', jsonResponse.event.contentEnd);
              }
              else {
                // Handle other events
                const eventKeys = Object.keys(jsonResponse.event || {});
                console.log(`[${sessionId}] Other event keys: ${eventKeys.join(', ')}`);
                console.log(`[${sessionId}] Full event data:`, JSON.stringify(jsonResponse.event).substring(0, 300));

                if (eventKeys.length > 0) {
                  this.dispatchEvent(sessionId, eventKeys[0], jsonResponse.event);
                } else if (Object.keys(jsonResponse).length > 0) {
                  console.log(`[${sessionId}] Unknown response structure:`, JSON.stringify(jsonResponse).substring(0, 300));
                  this.dispatchEvent(sessionId, 'unknown', jsonResponse);
                }
              }
            } catch (e) {
              console.error(`[${sessionId}] Error parsing response:`, e);
              console.log(`[${sessionId}] Raw text response (parse error): ${textResponse.substring(0, 500)}${textResponse.length > 500 ? '...' : ''}`);
            }
          } catch (e) {
            console.error(`Error processing response chunk for session ${sessionId}: `, e);
          }
        } else if (event.modelStreamErrorException) {
          console.error(`Model stream error for session ${sessionId}: `, event.modelStreamErrorException);
          this.dispatchEvent(sessionId, 'error', {
            type: 'modelStreamErrorException',
            details: event.modelStreamErrorException
          });
        } else if (event.internalServerException) {
          console.error(`Internal server error for session ${sessionId}: `, event.internalServerException);
          this.dispatchEvent(sessionId, 'error', {
            type: 'internalServerException',
            details: event.internalServerException
          });
        }
      }

      console.log(`Response stream processing complete for session ${sessionId}`);
      this.dispatchEvent(sessionId, 'streamComplete', {
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      console.error(`Error processing response stream for session ${sessionId}: `, error);

      // Check if this is a timeout error or a "No open prompt" error
      const errorMessage = error instanceof Error ? error.message : String(error);

      if (errorMessage.includes('Timed out waiting for input events')) {
        console.log(`Detected natural timeout for session ${sessionId}, notifying client`);

        // Don't treat this as an error - it's a natural timeout
        // Send a specific timeout event instead
        this.dispatchEvent(sessionId, 'sessionTimeout', {
          type: 'inactivityTimeout',
          message: 'Connection timed out due to inactivity',
          details: 'The session was idle for too long and has timed out naturally.',
          timestamp: new Date().toISOString()
        });

        // Mark the session as inactive
        const session = this.activeSessions.get(sessionId);
        if (session) {
          session.isActive = false;
        }

        return;
      }
      // Handle "No open prompt found" errors
      else if (errorMessage.includes('No open prompt found for prompt name')) {
        console.log(`Detected "No open prompt" error for session ${sessionId}, notifying client`);

        // This happens when the session has timed out on AWS side but we're still trying to use it
        // Send a specific timeout event instead of an error
        this.dispatchEvent(sessionId, 'sessionTimeout', {
          type: 'serverTimeout',
          message: 'Session expired on server',
          details: 'The session has expired on the server side.',
          timestamp: new Date().toISOString()
        });

        // Mark the session as inactive
        const session = this.activeSessions.get(sessionId);
        if (session) {
          session.isActive = false;
        }

        return;
      }

      // For other errors or if recovery failed, dispatch the original error
      this.dispatchEvent(sessionId, 'error', {
        source: 'responseStream',
        message: 'Error processing response stream',
        details: errorMessage
      });
    }
  }

  // Add an event to a session's queue
  private addEventToSessionQueue(sessionId: string, event: any): void {
    const session = this.activeSessions.get(sessionId);
    if (!session || !session.isActive) return;

    this.updateSessionActivity(sessionId);
    session.queue.push(event);
    session.queueSignal.next();
  }


  // Set up initial events for a session
  private setupSessionStartEvent(sessionId: string): void {
    console.log(`Setting up initial events for session ${sessionId}...`);
    const session = this.activeSessions.get(sessionId);
    if (!session) return;

    // Session start event
    this.addEventToSessionQueue(sessionId, {
      event: {
        sessionStart: {
          inferenceConfiguration: session.inferenceConfig
        }
      }
    });
  }
  public setupPromptStartEvent(sessionId: string): void {
    console.log(`Setting up prompt start event for session ${sessionId}...`);
    const session = this.activeSessions.get(sessionId);
    if (!session) return;

    // Get all available tools from the registry
    const tools = this.toolRegistry.getAllTools();

    // Prompt start event
    this.addEventToSessionQueue(sessionId, {
      event: {
        promptStart: {
          promptName: session.promptName,
          textOutputConfiguration: {
            mediaType: "text/plain",
          },
          audioOutputConfiguration: DefaultAudioOutputConfiguration,
          toolUseOutputConfiguration: {
            mediaType: "application/json",
          },
          toolConfiguration: {
            tools: tools.map(tool => ({
              toolSpec: {
                name: tool.name,
                description: tool.description,
                inputSchema: {
                  json: tool.schema
                }
              }
            }))
          },
        },
      }
    });
    session.isPromptStartSent = true;
  }

  public setupSystemPromptEvent(sessionId: string,
    textConfig: typeof DefaultTextConfiguration = DefaultTextConfiguration,
    systemPromptContent: string = DefaultSystemPrompt
  ): void {
    console.log(`Setting up systemPrompt events for session ${sessionId}...`);
    const session = this.activeSessions.get(sessionId);
    if (!session) return;
    // Text content start
    const textPromptID = randomUUID();
    this.addEventToSessionQueue(sessionId, {
      event: {
        contentStart: {
          promptName: session.promptName,
          contentName: textPromptID,
          type: "TEXT",
          interactive: true,
          role: "SYSTEM",
          textInputConfiguration: textConfig,
        },
      }
    });

    // Text input content
    this.addEventToSessionQueue(sessionId, {
      event: {
        textInput: {
          promptName: session.promptName,
          contentName: textPromptID,
          content: systemPromptContent,
        },
      }
    });

    // Text content end
    this.addEventToSessionQueue(sessionId, {
      event: {
        contentEnd: {
          promptName: session.promptName,
          contentName: textPromptID,
        },
      }
    });
  }

  public setupStartAudioEvent(
    sessionId: string,
    audioConfig: typeof DefaultAudioInputConfiguration = DefaultAudioInputConfiguration
  ): void {
    console.log(`Setting up startAudioContent event for session ${sessionId}...`);
    const session = this.activeSessions.get(sessionId);
    if (!session) return;

    session.currentAudioContentId = randomUUID(); // Generate new ID for this audio content
    console.log(`Using audio content ID: ${session.currentAudioContentId}`);
    // Audio content start
    this.addEventToSessionQueue(sessionId, {
      event: {
        contentStart: {
          promptName: session.promptName,
          contentName: session.currentAudioContentId,
          type: "AUDIO",
          interactive: true,
          role: "USER",
          audioInputConfiguration: audioConfig,
        },
      }
    });
    session.isAudioContentStartSent = true;
    console.log(`Initial events setup complete for session ${sessionId}`);
  }

  // Stream an audio chunk for a session
  public async streamAudioChunk(sessionId: string, audioData: Buffer): Promise<void> {
    const session = this.activeSessions.get(sessionId);

    // Check if session exists and is valid for streaming
    if (!session) {
      console.log(`Session ${sessionId} not found for audio streaming`);
      return; // Just return instead of throwing
    }

    // Check if session is active
    if (!session.isActive) {
      console.log(`Session ${sessionId} is inactive, ignoring audio chunk`);
      return; // Just return instead of throwing
    }

    // Check if audio content ID exists
    if (!session.currentAudioContentId) {
      console.log(`Session ${sessionId} has no audio content ID, ignoring audio chunk`);
      return; // Just return instead of throwing
    }

    // Convert audio to base64
    const base64Data = audioData.toString('base64');

    this.addEventToSessionQueue(sessionId, {
      event: {
        audioInput: {
          promptName: session.promptName,
          contentName: session.currentAudioContentId,
          content: base64Data,
        },
      }
    });
  }


  // Send tool result back to the model
  private async sendToolResult(sessionId: string, toolUseId: string, result: any): Promise<void> {
    const session = this.activeSessions.get(sessionId);
    console.log("inside tool result")
    if (!session || !session.isActive) return;

    console.log(`Sending tool result for session ${sessionId}, tool use ID: ${toolUseId}`);
    const contentId = randomUUID();

    // Tool content start
    this.addEventToSessionQueue(sessionId, {
      event: {
        contentStart: {
          promptName: session.promptName,
          contentName: contentId,
          interactive: false,
          type: "TOOL",
          role: "TOOL",
          toolResultInputConfiguration: {
            toolUseId: toolUseId,
            type: "TEXT",
            textInputConfiguration: {
              mediaType: "text/plain"
            }
          }
        }
      }
    });

    // Tool content input
    const resultContent = typeof result === 'string' ? result : JSON.stringify(result);
    this.addEventToSessionQueue(sessionId, {
      event: {
        toolResult: {
          promptName: session.promptName,
          contentName: contentId,
          content: resultContent
        }
      }
    });

    // Tool content end
    this.addEventToSessionQueue(sessionId, {
      event: {
        contentEnd: {
          promptName: session.promptName,
          contentName: contentId
        }
      }
    });

    console.log(`Tool result sent for session ${sessionId}`);
  }

  public async sendContentEnd(sessionId: string): Promise<void> {
    const session = this.activeSessions.get(sessionId);
    if (!session) {
      console.log(`sendContentEnd: Session ${sessionId} not found.`);
      return;
    }

    // If we're in cleanup mode and the session doesn't have audio content,
    // we'll create a dummy content end event to ensure proper sequencing
    if (this.sessionCleanupInProgress.has(sessionId) &&
        (!session.isAudioContentStartSent || !session.currentAudioContentId)) {
      console.log(`sendContentEnd: Creating dummy content end for cleanup of session ${sessionId}`);

      // Use the prompt name as the content name for the dummy event
      const dummyContentId = session.promptName + "-dummy";

      // Send a dummy content start event first
      this.addEventToSessionQueue(sessionId, {
        event: {
          contentStart: {
            promptName: session.promptName,
            contentName: dummyContentId,
            type: "TEXT",
            interactive: false,
            role: "SYSTEM"
          }
        }
      });

      // Wait a bit to ensure content start is processed
      await new Promise(resolve => setTimeout(resolve, 300));

      // Then send the content end event
      this.addEventToSessionQueue(sessionId, {
        event: {
          contentEnd: {
            promptName: session.promptName,
            contentName: dummyContentId,
          }
        }
      });

      // Wait a bit to ensure content end is processed
      await new Promise(resolve => setTimeout(resolve, 300));

      return;
    }

    // Normal case - we have active audio content to end
    if (!session.isAudioContentStartSent || !session.currentAudioContentId) {
      console.log(`sendContentEnd: No active audio content for session ${sessionId} to end.`);
      return;
    }

    // Store the current audio content ID before sending the event
    const contentId = session.currentAudioContentId;

    console.log(`Sending contentEnd for session ${sessionId} with contentId ${contentId}`);

    this.addEventToSessionQueue(sessionId, {
      event: {
        contentEnd: {
          promptName: session.promptName,
          contentName: contentId,
        }
      }
    });

    // Only reset audio content state if the session is still active
    // This prevents issues when called during cleanup
    if (session.isActive && !this.sessionCleanupInProgress.has(sessionId)) {
      console.log(`Resetting audio content state for session ${sessionId}`);
      session.isAudioContentStartSent = false;
      session.currentAudioContentId = null;
    } else {
      console.log(`Skipping audio state reset for session ${sessionId} - cleanup in progress`);
    }

    await new Promise(resolve => setTimeout(resolve, 500));
  }

  // Reset a session's state without closing it
  public resetSessionState(sessionId: string): void {
    const session = this.activeSessions.get(sessionId);
    if (!session) {
      console.log(`Cannot reset session ${sessionId}: not found`);
      return;
    }

    if (!session.isActive) {
      console.log(`Cannot reset session ${sessionId}: not active`);
      return;
    }

    console.log(`Resetting session state for ${sessionId}`);

    // Reset session state flags
    session.isAudioContentStartSent = false;
    session.currentAudioContentId = null;

    // Keep the session active and ready for new content
    this.updateSessionActivity(sessionId);

    console.log(`Session ${sessionId} state reset complete`);
  }

  public async sendPromptEnd(sessionId: string): Promise<void> {
    const session = this.activeSessions.get(sessionId);
    if (!session || !session.isPromptStartSent) return;

    this.addEventToSessionQueue(sessionId, {
      event: {
        promptEnd: {
          promptName: session.promptName
        }
      }
    });

    // Wait to ensure it's processed
    await new Promise(resolve => setTimeout(resolve, 300));
  }



  public async sendSessionEnd(sessionId: string): Promise<void> {
    const session = this.activeSessions.get(sessionId);
    if (!session) return;

    this.addEventToSessionQueue(sessionId, {
      event: {
        sessionEnd: {}
      }
    });

    // Wait to ensure it's processed
    await new Promise(resolve => setTimeout(resolve, 300));

    // Now it's safe to clean up
    session.isActive = false;
    session.closeSignal.next();
    session.closeSignal.complete();
    this.activeSessions.delete(sessionId);
    this.sessionLastActivity.delete(sessionId);
    console.log(`Session ${sessionId} closed and removed from active sessions`);
  }

  // Register an event handler for a session
  public registerEventHandler(sessionId: string, eventType: string, handler: (data: any) => void): void {
    const session = this.activeSessions.get(sessionId);
    if (!session) {
      throw new Error(`Session ${sessionId} not found`);
    }
    session.responseHandlers.set(eventType, handler);
  }

  // Dispatch an event to registered handlers
  private dispatchEvent(sessionId: string, eventType: string, data: any): void {
    const session = this.activeSessions.get(sessionId);
    if (!session) return;

    const handler = session.responseHandlers.get(eventType);
    if (handler) {
      try {
        handler(data);
      } catch (e) {
        console.error(`Error in ${eventType} handler for session ${sessionId}:`, e);
      }
    }

    // Also dispatch to "any" handlers
    const anyHandler = session.responseHandlers.get('any');
    if (anyHandler) {
      try {
        anyHandler({ type: eventType, data });
      } catch (e) {
        console.error(`Error in 'any' handler for session ${sessionId}:`, e);
      }
    }
  }

  public async closeSession(sessionId: string): Promise<void> {
    if (this.sessionCleanupInProgress.has(sessionId)) {
      console.log(`Cleanup already in progress for session ${sessionId}, skipping`);
      return;
    }

    this.sessionCleanupInProgress.add(sessionId);

    try {
      console.log(`Starting close process for session ${sessionId}`);

      const session = this.activeSessions.get(sessionId);
      if (!session) {
        console.log(`Session ${sessionId} not found, skipping cleanup`);
        return;
      }

      // Mark session as inactive before sending end events to prevent race conditions
      // This ensures other methods know we're in cleanup mode
      session.isActive = false;

      // First, make sure we have a clean state by resetting session flags
      // This helps prevent the "All contents must be closed before ending prompt" error
      session.isAudioContentStartSent = false;
      session.currentAudioContentId = null;

      // Wait for tool calls to complete before closing
      let toolWaitCount = 0;
      while (session.processingToolCall && toolWaitCount < 20) {
        console.log(`[${sessionId}] Waiting for tool call to complete... (${toolWaitCount + 1}/20)`);
        await new Promise(resolve => setTimeout(resolve, 500));
        toolWaitCount++;
      }
      
      if (session.processingToolCall) {
        console.warn(`[${sessionId}] Tool call still in progress after timeout, proceeding with cleanup`);
      }

      // Wait a bit to ensure any in-flight events are processed
      await new Promise(resolve => setTimeout(resolve, 300));

      // Only send content end if we have an active audio content
      // We've already reset the flags, but we'll check the original values
      if (session.isAudioContentStartSent && session.currentAudioContentId) {
        console.log(`Sending contentEnd during cleanup for session ${sessionId}`);
        await this.sendContentEnd(sessionId);

        // Wait a bit to ensure content end is processed
        await new Promise(resolve => setTimeout(resolve, 300));
      } else {
        console.log(`No active audio content for session ${sessionId} during cleanup`);
      }

      // Only send prompt end if prompt was started
      if (session.isPromptStartSent) {
        console.log(`Sending promptEnd during cleanup for session ${sessionId}`);
        await this.sendPromptEnd(sessionId);

        // Wait a bit to ensure prompt end is processed
        await new Promise(resolve => setTimeout(resolve, 300));
      }

      // Finally, end the session
      await this.sendSessionEnd(sessionId);
      console.log(`Session ${sessionId} cleanup complete`);
    } catch (error) {
      console.error(`Error during closing sequence for session ${sessionId}:`, error);

      // Ensure cleanup happens even if there's an error
      const session = this.activeSessions.get(sessionId);
      if (session) {
        session.isActive = false;
        this.activeSessions.delete(sessionId);
        this.sessionLastActivity.delete(sessionId);
      }
    } finally {
      // Always clean up the tracking set
      this.sessionCleanupInProgress.delete(sessionId);
    }
  }

  // Same for forceCloseSession:
  public forceCloseSession(sessionId: string): void {
    if (this.sessionCleanupInProgress.has(sessionId) || !this.activeSessions.has(sessionId)) {
      console.log(`Session ${sessionId} already being cleaned up or not active`);
      return;
    }

    this.sessionCleanupInProgress.add(sessionId);
    try {
      const session = this.activeSessions.get(sessionId);
      if (!session) return;

      console.log(`Force closing session ${sessionId}`);
      
      // If there's a tool call in progress, mark it as interrupted
      if (session.processingToolCall) {
        console.log(`Session ${sessionId} has tool call in progress during force close`);
        session.processingToolCall = false;
      }

      // Immediately mark as inactive and clean up resources
      session.isActive = false;
      session.closeSignal.next();
      session.closeSignal.complete();
      this.activeSessions.delete(sessionId);
      this.sessionLastActivity.delete(sessionId);

      console.log(`Session ${sessionId} force closed`);
    } finally {
      this.sessionCleanupInProgress.delete(sessionId);
    }
  }

}