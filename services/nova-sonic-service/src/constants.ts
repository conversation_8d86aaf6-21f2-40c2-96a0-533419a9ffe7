import { AudioType, AudioMediaType, TextMediaType } from "./types";

export const DefaultInferenceConfiguration = {
  maxTokens: 1024,
  topP: 0.9,
  temperature: 0.7,
};

export const DefaultAudioInputConfiguration = {
  audioType: "SPEECH" as AudioType,
  encoding: "base64",
  mediaType: "audio/lpcm" as AudioMediaType,
  sampleRateHertz: 16000,
  sampleSizeBits: 16,
  channelCount: 1,
};

export const DefaultTextConfiguration = { mediaType: "text/plain" as TextMediaType };

export const DefaultSystemPrompt = "You are a friendly AI assistant specialized in helping users build products from ideas. " +
  "When users mention wanting to create, build, develop, or make ANY product/app/tool/service (including slides, presentations, mockups, or applications), ALWAYS use the conversationalProductBuilderTool first to guide them through the complete process. " +
  "The conversationalProductBuilderTool handles the full workflow including slides, mockups, documentation, and project scaffolding. " +
  "Do NOT use individual tools like generateSlideDeckTool directly - always use conversationalProductBuilderTool for product creation requests. " +
  "Always ask clarifying questions when you need more information about their product idea. " +
  "Keep your responses conversational and engaging, usually 1-3 sentences. " +
  "Be enthusiastic about helping them build their ideas into reality!";

export const DefaultAudioOutputConfiguration = {
  ...DefaultAudioInputConfiguration,
  sampleRateHertz: 24000,
  voiceId: "tiffany",
};
