module.exports = {
  apps: [{
    name: 'nova-sonic-service',
    script: 'npm',
    args: 'start',
    cwd: '/Users/<USER>/Code/kapi-fresh/services/nova-sonic-service',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      PORT: 3005,
      NODE_ENV: 'production',
      AWS_PROFILE: 'bedrock-test'
    },
    error_file: 'logs/err.log',
    out_file: 'logs/out.log',
    log_file: 'logs/combined.log',
    time: true
  }]
};
