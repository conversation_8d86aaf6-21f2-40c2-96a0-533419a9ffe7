<!DOCTYPE html>
<html>

<head>
    <title>Nova Sonic Service Test</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="/src/style.css">
    <script src="/socket.io/socket.io.js"></script>
</head>

<body>
    <div id="app">
        <h1>Nova Sonic Microservice Test</h1>
        <div id="status" class="disconnected">Disconnected</div>
        <div id="chat-container"></div>
        <div id="controls">
            <button id="connect" class="button" style="background-color: #4CAF50;">Connect</button>
            <button id="start" class="button" disabled>Start Streaming</button>
            <button id="stop" class="button" disabled>Stop Streaming</button>
            <button id="disconnect" class="button" style="background-color: #f44336;" disabled>Disconnect</button>
        </div>
    </div>
    <script type="module" src="/src/main.js"></script>
</body>

</html>
</parameter>
</invoke>