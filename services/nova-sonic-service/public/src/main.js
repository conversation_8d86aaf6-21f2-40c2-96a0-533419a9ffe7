import { AudioPlayer } from './lib/play/AudioPlayer.js';
import { AudioRecorder } from './lib/record/AudioRecorder.js';
import { ChatHistoryManager } from "./lib/util/ChatHistoryManager.js";

// Connect to the server - specify port 3005 for the microservice
// Configure socket options for better reconnection control
const socket = io('http://localhost:3005', {
    autoConnect: false,  // Don't auto-connect on page load
    reconnection: false  // Don't auto-reconnect - we'll handle it manually
});

// DOM elements
const connectButton = document.getElementById('connect');
const startButton = document.getElementById('start');
const stopButton = document.getElementById('stop');
const disconnectButton = document.getElementById('disconnect');
const statusElement = document.getElementById('status');
const chatContainer = document.getElementById('chat-container');

// Chat history management
let chat = { history: [] };
const chatRef = { current: chat };
const chatHistoryManager = ChatHistoryManager.getInstance(
    chatRef,
    (newChat) => {
        chat = { ...newChat };
        chatRef.current = chat;
        updateChatUI();
    }
);

// Audio processing variables
let audioStream;
let isStreaming = false;
let waitingForAssistantResponse = false;
let waitingForUserTranscription = false;
let userThinkingIndicator = null;
let assistantThinkingIndicator = null;
let displayAssistantText = false;
let role;
const audioPlayer = new AudioPlayer();
const audioRecorder = new AudioRecorder();
let sessionInitialized = false;

const TARGET_SAMPLE_RATE = 16000;

// Custom system prompt - you can modify this
let SYSTEM_PROMPT = "You are a friend. The user and you will engage in a spoken " +
    "dialog exchanging the transcripts of a natural real-time conversation. Keep your responses short, " +
    "generally two or three sentences for chatty scenarios.";

// Initialize WebSocket audio
async function initAudio() {
    try {
        statusElement.textContent = "Requesting microphone access...";
        statusElement.className = "connecting";

        // Request microphone access
        audioStream = await navigator.mediaDevices.getUserMedia({
            audio: {
                echoCancellation: true,
                noiseSuppression: true,
                autoGainControl: true
            }
        });

        // Initialize audio player
        await audioPlayer.start();

        // Initialize audio recorder
        await audioRecorder.init(audioStream, TARGET_SAMPLE_RATE);

        // Set up audio data callback
        audioRecorder.setAudioDataCallback((pcmData) => {
            if (isStreaming) {
                // Convert to base64 (browser-safe way)
                const base64Data = arrayBufferToBase64(pcmData.buffer);

                // Send to server
                socket.emit('audioInput', base64Data);
            }
        });

        statusElement.textContent = "Microphone ready. Click Connect to begin.";
        statusElement.className = "ready";
        connectButton.disabled = false;
    } catch (error) {
        console.error("Error accessing microphone:", error);
        statusElement.textContent = "Error: " + error.message;
        statusElement.className = "error";
    }
}

// Connect to the Nova Sonic service and initialize session
async function connectToService() {
    // Always reset session state on new connection attempt
    sessionInitialized = false;
    
    statusElement.textContent = "Connecting to Nova Sonic service...";
    statusElement.className = "connecting";
    
    connectButton.disabled = true;
    
    // If socket is disconnected, reconnect it first
    if (!socket.connected) {
        console.log('Socket disconnected, reconnecting...');
        socket.connect();
        // Wait a bit for connection to establish
        await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    // The microservice requires an explicit initializeSession call
    console.log('Sending initializeSession request...');
    socket.emit('initializeSession', { systemPrompt: SYSTEM_PROMPT });
}

// Initialize the session with Bedrock (after receiving sessionInitialized event)
async function setupStreamingSession() {
    try {
        // Send events in sequence
        socket.emit('promptStart');
        socket.emit('systemPrompt', SYSTEM_PROMPT);
        socket.emit('audioStart');

        statusElement.textContent = "Session ready. Click Start to begin streaming.";
        statusElement.className = "connected";
        
        startButton.disabled = false;
        disconnectButton.disabled = false;
    } catch (error) {
        console.error("Failed to setup streaming session:", error);
        statusElement.textContent = "Error setting up session";
        statusElement.className = "error";
    }
}

async function startStreaming() {
    if (isStreaming) return;

    try {
        // If session needs to be re-initialized, set up audio start event
        socket.emit('audioStart');
        console.log('Sent audioStart event');

        // Restart audio player if it was stopped
        if (!audioPlayer.initialized) {
            await audioPlayer.start();
            console.log('Restarted audio player');
        }

        // Start the audio recorder
        audioRecorder.start();
        console.log('Started audio recorder');

        isStreaming = true;
        startButton.disabled = true;
        stopButton.disabled = false;
        statusElement.textContent = "Streaming... Speak now";
        statusElement.className = "recording";

        // Show user thinking indicator when starting to record
        showUserThinkingIndicator();

    } catch (error) {
        console.error("Error starting recording:", error);
        statusElement.textContent = "Error: " + error.message;
        statusElement.className = "error";
    }
}

// Convert ArrayBuffer to base64 string
function arrayBufferToBase64(buffer) {
    const binary = [];
    const bytes = new Uint8Array(buffer);
    for (let i = 0; i < bytes.byteLength; i++) {
        binary.push(String.fromCharCode(bytes[i]));
    }
    return btoa(binary.join(''));
}

function stopStreaming() {
    if (!isStreaming) return;

    isStreaming = false;

    // Stop the audio recorder
    audioRecorder.stop();
    console.log('Stopped audio recorder');

    // Make sure to hide the user thinking indicator when stopping
    hideUserThinkingIndicator();

    startButton.disabled = false;
    stopButton.disabled = true;
    statusElement.textContent = "Processing...";
    statusElement.className = "processing";

    // Don't completely stop the audio player, just clear its buffer
    // This allows it to be reused for the next streaming attempt
    if (audioPlayer.initialized && audioPlayer.workletNode) {
        audioPlayer.workletNode.port.postMessage({
            type: "barge-in" // This clears the buffer without stopping the player
        });
        console.log('Cleared audio player buffer');
    }

    // Tell server to finalize processing
    socket.emit('stopAudio');

    // End the current turn in chat history
    chatHistoryManager.endTurn();
}

// Disconnect from service
function disconnectFromService() {
    console.log('Disconnecting from Nova Sonic service...');
    
    // Reset session state
    sessionInitialized = false;
    isStreaming = false;
    
    // Force disconnect the socket
    if (socket.connected) {
        socket.disconnect();
    }
    
    // Stop any ongoing audio
    if (audioRecorder.isRecording) {
        audioRecorder.stop();
    }
    
    // Hide any indicators
    hideUserThinkingIndicator();
    hideAssistantThinkingIndicator();
    
    // Reset UI
    connectButton.disabled = false;
    startButton.disabled = true;
    stopButton.disabled = true;
    disconnectButton.disabled = true;
    
    statusElement.textContent = "Disconnected. Click Connect to restart.";
    statusElement.className = "disconnected";
    
    // Add system message
    chatHistoryManager.addTextMessage({
        role: 'SYSTEM',
        message: 'Disconnected from Nova Sonic service.'
    });
}

// Base64 to Float32Array conversion
function base64ToFloat32Array(base64String) {
    try {
        const binaryString = window.atob(base64String);
        const bytes = new Uint8Array(binaryString.length);
        for (let i = 0; i < binaryString.length; i++) {
            bytes[i] = binaryString.charCodeAt(i);
        }

        const int16Array = new Int16Array(bytes.buffer);
        const float32Array = new Float32Array(int16Array.length);
        for (let i = 0; i < int16Array.length; i++) {
            float32Array[i] = int16Array[i] / 32768.0;
        }

        return float32Array;
    } catch (error) {
        console.error('Error in base64ToFloat32Array:', error);
        throw error;
    }
}

// Process message data and add to chat history
function handleTextOutput(data) {
    console.log("Processing text output:", data);
    if (data.content) {
        const messageData = {
            role: data.role,
            message: data.content
        };
        chatHistoryManager.addTextMessage(messageData);
    }
}

// Update the UI based on the current chat history
function updateChatUI() {
    if (!chatContainer) {
        console.error("Chat container not found");
        return;
    }

    // Clear existing chat messages
    chatContainer.innerHTML = '';

    // Add all messages from history
    chat.history.forEach(item => {
        if (item.endOfConversation) {
            const endDiv = document.createElement('div');
            endDiv.className = 'message system';
            endDiv.textContent = "Conversation ended";
            chatContainer.appendChild(endDiv);
            return;
        }

        if (item.role) {
            const messageDiv = document.createElement('div');
            const roleLowerCase = item.role.toLowerCase();
            messageDiv.className = `message ${roleLowerCase}`;

            const roleLabel = document.createElement('div');
            roleLabel.className = 'role-label';
            roleLabel.textContent = item.role;
            messageDiv.appendChild(roleLabel);

            const content = document.createElement('div');
            content.textContent = item.message || "No content";
            messageDiv.appendChild(content);

            chatContainer.appendChild(messageDiv);
        }
    });

    // Re-add thinking indicators if we're still waiting
    if (waitingForUserTranscription) {
        showUserThinkingIndicator();
    }

    if (waitingForAssistantResponse) {
        showAssistantThinkingIndicator();
    }

    // Scroll to bottom
    chatContainer.scrollTop = chatContainer.scrollHeight;
}

// Show the "Listening" indicator for user
function showUserThinkingIndicator() {
    hideUserThinkingIndicator();

    waitingForUserTranscription = true;
    userThinkingIndicator = document.createElement('div');
    userThinkingIndicator.className = 'message user thinking';

    const roleLabel = document.createElement('div');
    roleLabel.className = 'role-label';
    roleLabel.textContent = 'USER';
    userThinkingIndicator.appendChild(roleLabel);

    const listeningText = document.createElement('div');
    listeningText.className = 'thinking-text';
    listeningText.textContent = 'Listening';
    userThinkingIndicator.appendChild(listeningText);

    const dotContainer = document.createElement('div');
    dotContainer.className = 'thinking-dots';

    for (let i = 0; i < 3; i++) {
        const dot = document.createElement('span');
        dot.className = 'dot';
        dotContainer.appendChild(dot);
    }

    userThinkingIndicator.appendChild(dotContainer);
    chatContainer.appendChild(userThinkingIndicator);
    chatContainer.scrollTop = chatContainer.scrollHeight;
}

// Show the "Thinking" indicator for assistant
function showAssistantThinkingIndicator() {
    hideAssistantThinkingIndicator();

    waitingForAssistantResponse = true;
    assistantThinkingIndicator = document.createElement('div');
    assistantThinkingIndicator.className = 'message assistant thinking';

    const roleLabel = document.createElement('div');
    roleLabel.className = 'role-label';
    roleLabel.textContent = 'ASSISTANT';
    assistantThinkingIndicator.appendChild(roleLabel);

    const thinkingText = document.createElement('div');
    thinkingText.className = 'thinking-text';
    thinkingText.textContent = 'Thinking';
    assistantThinkingIndicator.appendChild(thinkingText);

    const dotContainer = document.createElement('div');
    dotContainer.className = 'thinking-dots';

    for (let i = 0; i < 3; i++) {
        const dot = document.createElement('span');
        dot.className = 'dot';
        dotContainer.appendChild(dot);
    }

    assistantThinkingIndicator.appendChild(dotContainer);
    chatContainer.appendChild(assistantThinkingIndicator);
    chatContainer.scrollTop = chatContainer.scrollHeight;
}

// Hide the user thinking indicator
function hideUserThinkingIndicator() {
    waitingForUserTranscription = false;
    if (userThinkingIndicator && userThinkingIndicator.parentNode) {
        userThinkingIndicator.parentNode.removeChild(userThinkingIndicator);
    }
    userThinkingIndicator = null;
}

// Hide the assistant thinking indicator
function hideAssistantThinkingIndicator() {
    waitingForAssistantResponse = false;
    if (assistantThinkingIndicator && assistantThinkingIndicator.parentNode) {
        assistantThinkingIndicator.parentNode.removeChild(assistantThinkingIndicator);
    }
    assistantThinkingIndicator = null;
}

// EVENT HANDLERS
// --------------

// Handle initial connection acknowledgment from microservice
socket.on('connected', (data) => {
    console.log('Connected to Nova Sonic service:', data);
    statusElement.textContent = data.message || "Connected. Awaiting initialization.";
    statusElement.className = "connected";
});

// Handle session initialization confirmation from microservice
socket.on('sessionInitialized', (data) => {
    console.log('Session initialized:', data);
    sessionInitialized = true;
    setupStreamingSession();
});

// Handle content start from the server
socket.on('contentStart', (data) => {
    console.log('Content start received:', data);

    if (data.type === 'TEXT') {
        // Below update will be enabled when role is moved to the contentStart
        role = data.role;
        if (data.role === 'USER') {
            // When user's text content starts, hide user thinking indicator
            hideUserThinkingIndicator();
        }
        else if (data.role === 'ASSISTANT') {
            // When assistant's text content starts, hide assistant thinking indicator
            hideAssistantThinkingIndicator();
            let isSpeculative = false;
            try {
                if (data.additionalModelFields) {
                    const additionalFields = JSON.parse(data.additionalModelFields);
                    isSpeculative = additionalFields.generationStage === "SPECULATIVE";
                    if (isSpeculative) {
                        console.log("Received speculative content");
                        displayAssistantText = true;
                    }
                    else {
                        displayAssistantText = false;
                    }
                }
            } catch (e) {
                console.error("Error parsing additionalModelFields:", e);
            }
        }
    }
    else if (data.type === 'AUDIO') {
        // When audio content starts, we may need to show user thinking indicator
        if (isStreaming) {
            showUserThinkingIndicator();
        }
    }
});

// Handle text output from the server
socket.on('textOutput', (data) => {
    console.log('Received text output:', data);

    if (role === 'USER') {
        // When user text is received, hide user thinking indicator and show assistant thinking indicator
        hideUserThinkingIndicator();

        // Add user message to chat
        handleTextOutput({
            role: data.role,
            content: data.content
        });

        // Show assistant thinking indicator after user text appears
        showAssistantThinkingIndicator();
    }
    else if (role === 'ASSISTANT') {
        //hideAssistantThinkingIndicator();
        if (displayAssistantText) {
            handleTextOutput({
                role: data.role,
                content: data.content
            });
        }
    }
});

// Handle audio output
socket.on('audioOutput', (data) => {
    if (data.content) {
        try {
            console.log('Received audio output data, length:', data.content.length);

            // Check if audio player is initialized
            if (!audioPlayer.initialized) {
                console.warn('Audio player not initialized, attempting to restart');
                audioPlayer.start().then(() => {
                    console.log('Audio player restarted, playing audio');
                    const audioData = base64ToFloat32Array(data.content);
                    audioPlayer.playAudio(audioData);
                });
            } else {
                const audioData = base64ToFloat32Array(data.content);
                audioPlayer.playAudio(audioData);
                console.log('Playing audio, samples:', audioData.length);
            }
        } catch (error) {
            console.error('Error processing audio data:', error);
        }
    }
});

// Handle content end events
socket.on('contentEnd', (data) => {
    console.log('Content end received:', data);

    if (data.type === 'TEXT') {
        if (role === 'USER') {
            // When user's text content ends, make sure assistant thinking is shown
            hideUserThinkingIndicator();
            showAssistantThinkingIndicator();
        }
        else if (role === 'ASSISTANT') {
            // When assistant's text content ends, prepare for user input in next turn
            hideAssistantThinkingIndicator();
        }

        // Handle stop reasons
        if (data.stopReason && data.stopReason.toUpperCase() === 'END_TURN') {
            chatHistoryManager.endTurn();
        } else if (data.stopReason && data.stopReason.toUpperCase() === 'INTERRUPTED') {
            console.log("Interrupted by user");
            audioPlayer.bargeIn();
        }
    }
    else if (data.type === 'AUDIO') {
        // When audio content ends, we may need to show user thinking indicator
        if (isStreaming) {
            showUserThinkingIndicator();
        }
    }
});

// Stream completion event
socket.on('streamComplete', () => {
    if (isStreaming) {
        stopStreaming();
    }
    statusElement.textContent = "Ready";
    statusElement.className = "ready";
});

// Handle session ready for new stream event
socket.on('sessionReadyForNewStream', (data) => {
    console.log('Session ready for new stream:', data);
    
    statusElement.textContent = "Ready for new recording";
    statusElement.className = "ready";

    // Enable start button, disable stop button
    startButton.disabled = false;
    stopButton.disabled = true;

    // Make sure audio recorder is stopped
    if (audioRecorder.isRecording) {
        audioRecorder.stop();
        console.log('Stopped audio recorder after session reset');
    }

    console.log('Audio system ready for new streaming attempt');
});

// Handle connection status updates
socket.on('connect', () => {
    console.log('Socket connected to Nova Sonic service');
    // Don't automatically disable connect button - wait for user action
    // This prevents issues when socket auto-reconnects
    if (sessionInitialized) {
        connectButton.disabled = true;
    }
});

socket.on('disconnect', () => {
    statusElement.textContent = "Disconnected from service";
    statusElement.className = "disconnected";
    sessionInitialized = false;
    
    // Reset buttons
    connectButton.disabled = false;
    startButton.disabled = true;
    stopButton.disabled = true;
    disconnectButton.disabled = true;
    
    hideUserThinkingIndicator();
    hideAssistantThinkingIndicator();

    // Add a system message to the chat about the disconnection
    chatHistoryManager.addTextMessage({
        role: 'SYSTEM',
        message: 'Disconnected from Nova Sonic service.'
    });
});

// Handle session timeout events
socket.on('sessionTimeout', (data) => {
    console.log("Session timeout:", data);

    statusElement.textContent = data.type === 'inactivityTimeout' 
        ? "Session timed out due to inactivity"
        : "Session expired on server";
    
    statusElement.className = "timeout";

    // Clean up UI
    hideUserThinkingIndicator();
    hideAssistantThinkingIndicator();

    // Reset buttons
    startButton.disabled = true;
    stopButton.disabled = true;

    // Add system message
    chatHistoryManager.addTextMessage({
        role: 'SYSTEM',
        message: data.details || data.message || 'Session timed out. Please reconnect to continue.'
    });
});

// Handle errors
socket.on('error', (error) => {
    console.error("Server error:", error);
    statusElement.textContent = "Error: " + (error.message || JSON.stringify(error).substring(0, 100));
    statusElement.className = "error";
    hideUserThinkingIndicator();
    hideAssistantThinkingIndicator();
});

// Button event listeners
connectButton.addEventListener('click', connectToService);
startButton.addEventListener('click', startStreaming);
stopButton.addEventListener('click', stopStreaming);
disconnectButton.addEventListener('click', disconnectFromService);

// Initialize the app when the page loads
document.addEventListener('DOMContentLoaded', () => {
    // Initialize audio
    initAudio();
    
    // Since we disabled autoConnect, the socket won't be connected initially
    // This ensures clean state on page load
    console.log('App initialized. Socket auto-connect disabled.');
});
