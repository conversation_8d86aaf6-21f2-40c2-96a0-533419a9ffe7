# Nova Sonic Microservice Setup and Integration

## Overview

This guide helps you set up the Nova Sonic voice streaming as a separate microservice to resolve the "Duplicate prompt name" errors and improve system stability.

## Benefits of Microservice Architecture

1. **Process Isolation**: Nova Sonic runs independently, preventing AWS session conflicts
2. **Independent Restarts**: Can restart Nova service without affecting main app
3. **Clean Debugging**: Separate logs and error tracking
4. **Better Performance**: Dedicated resources for real-time audio processing
5. **Easier Scaling**: Can scale voice service independently

## Setup Instructions

### 1. Install Dependencies

```bash
cd /Users/<USER>/Code/kapi-fresh/services/nova-sonic-service
npm install
```

### 2. Copy Client Implementation

Copy the working client.ts from your legacy implementation:

```bash
cp /Users/<USER>/Code/kapi-fresh/legacy/test/nova-nodejs/src/client.ts ./src/client.ts
```

### 3. Build the Service

```bash
npm run build
```

### 4. Start the Service

#### Option A: Direct Start (for testing)
```bash
npm start
```

#### Option B: Using PM2 (recommended for production)
```bash
# Install PM2 globally if not already installed
npm install -g pm2

# Start the service
pm2 start ecosystem.config.js

# View logs
pm2 logs nova-sonic-service

# Monitor
pm2 monit
```

### 5. Verify Service Health

```bash
curl http://localhost:3005/health
```

Expected response:
```json
{
  "status": "healthy",
  "service": "nova-sonic",
  "port": 3005,
  "activeSessions": 0,
  "uptime": 10.5,
  "timestamp": "2025-05-27T..."
}
```

## Main Application Integration

### 1. Install Socket.IO Client

In your main application:

```bash
cd /Users/<USER>/Code/kapi-fresh/nodejs_backend
npm install socket.io-client
```

### 2. Update Your Main Server

Replace your current Nova Sonic handler with the proxy:

```typescript
// In your main server.ts or app.ts
import { NovaSonicProxy } from './websockets/nova-sonic-proxy';

// Instead of the current NovaSonicSocketHandler
const novaSonicProxy = new NovaSonicProxy(io, {
  serviceUrl: process.env.NOVA_SONIC_SERVICE_URL || 'http://localhost:3005'
});

// Add to your environment variables
// NOVA_SONIC_SERVICE_URL=http://localhost:3005
```

### 3. Update Environment Variables

Add to your main app's .env:

```env
NOVA_SONIC_SERVICE_URL=http://localhost:3005
```

### 4. Update Admin Routes (Optional)

If you have admin endpoints for Nova Sonic, update them to query the proxy:

```typescript
// In your admin routes
router.get('/nova-sonic/status', async (req, res) => {
  try {
    const status = await novaSonicProxy.getStatus();
    res.json(status);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});
```

## Testing the Integration

1. **Start Nova Sonic Service**:
   ```bash
   cd services/nova-sonic-service
   npm start
   ```

2. **Start Main Application**:
   ```bash
   cd nodejs_backend
   npm run dev
   ```

3. **Test WebSocket Connection**:
   - Navigate to your admin test page
   - Click "Connect"
   - You should see "Connected to Nova Sonic WebSocket via proxy"

4. **Monitor Both Services**:
   - Nova Service logs: Check for session creation
   - Main app logs: Check for proxy connections

## Production Deployment

### 1. Create Systemd Service (Linux)

```bash
sudo nano /etc/systemd/system/nova-sonic.service
```

```ini
[Unit]
Description=Nova Sonic Voice Streaming Service
After=network.target

[Service]
Type=simple
User=your-user
WorkingDirectory=/Users/<USER>/Code/kapi-fresh/services/nova-sonic-service
ExecStart=/usr/bin/node dist/server.js
Restart=on-failure
RestartSec=10
Environment=NODE_ENV=production
Environment=PORT=3005
Environment=AWS_PROFILE=bedrock-test

[Install]
WantedBy=multi-user.target
```

### 2. Using Docker (Alternative)

Create `Dockerfile` in nova-sonic-service:

```dockerfile
FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci --only=production

COPY dist ./dist
COPY .env ./

EXPOSE 3005

CMD ["node", "dist/server.js"]
```

## Monitoring and Maintenance

### Health Checks

1. **Service Health**: `http://localhost:3005/health`
2. **Active Sessions**: `http://localhost:3005/sessions`
3. **PM2 Status**: `pm2 status`

### Logs

- **PM2 Logs**: `pm2 logs nova-sonic-service`
- **Service Logs**: Check `logs/` directory in service folder
- **Main App Logs**: Check proxy connection logs

### Common Issues and Solutions

1. **Service Won't Start**
   - Check AWS credentials: `aws configure list`
   - Verify port 3005 is available
   - Check Node.js version (requires 16+)

2. **Connection Timeouts**
   - Increase timeout in proxy configuration
   - Check network connectivity between services
   - Verify AWS Bedrock access

3. **Session Errors**
   - Service automatically cleans up stale sessions
   - Can manually check: `curl http://localhost:3005/sessions`
   - Force restart service if needed: `pm2 restart nova-sonic-service`

## Rollback Plan

If you need to revert to the integrated version:

1. Stop the Nova Sonic service
2. Comment out the proxy initialization
3. Uncomment the original NovaSonicSocketHandler
4. Restart your main application

## Next Steps

1. Add authentication between services (JWT tokens)
2. Implement service discovery (Consul, etcd)
3. Add metrics collection (Prometheus)
4. Set up centralized logging (ELK stack)
5. Implement circuit breaker pattern for resilience
