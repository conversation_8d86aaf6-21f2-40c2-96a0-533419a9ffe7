{"name": "nova-sonic-service", "version": "1.0.0", "description": "Standalone Nova Sonic voice streaming service", "main": "dist/server.js", "type": "commonjs", "scripts": {"build": "tsc", "start": "node dist/server.js", "dev": "ts-node src/server.ts", "clean": "rm -rf dist", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"@aws-sdk/client-bedrock-runtime": "^3.785", "@aws-sdk/credential-providers": "^3.782", "@smithy/node-http-handler": "^4.0.4", "@smithy/types": "^4.1.0", "axios": "^1.6.2", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.21.2", "rxjs": "^7.8.2", "socket.io": "^4.8.1", "uuid": "^11.1.0"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^5.0.0", "@types/node": "^22.13.9", "ts-node": "^10.9.2", "typescript": "^5.7.3"}}