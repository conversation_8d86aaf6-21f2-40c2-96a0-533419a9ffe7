/**
 * React Hook for Nova Sonic Integration
 * 
 * Provides easy access to Nova Sonic service in React components
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { novaSonicService, NovaSonicCallbacks } from '../services/NovaSonicService';

export interface UseNovaSonicOptions {
  systemPrompt?: string;
  autoConnect?: boolean;
  autoInitialize?: boolean;
}

export interface UseNovaSonicReturn {
  // Connection state
  isConnected: boolean;
  isSessionInitialized: boolean;
  isStreaming: boolean;
  error: string | null;
  
  // Actions
  connect: () => Promise<void>;
  disconnect: () => void;
  initializeSession: (prompt?: string) => Promise<void>;
  startStreaming: () => Promise<void>;
  stopStreaming: () => void;
  sendMessage: (message: string) => void;
  
  // Audio responses
  textOutput: string[];
  audioOutputs: string[];
  
  // Utility
  clearOutput: () => void;
  status: any;
}

export const useNovaSonic = (options: UseNovaSonicOptions = {}): UseNovaSonicReturn => {
  const {
    systemPrompt = 'You are a helpful AI assistant for a code editor.',
    autoConnect = false,
    autoInitialize = false
  } = options;

  // State
  const [isConnected, setIsConnected] = useState(false);
  const [isSessionInitialized, setIsSessionInitialized] = useState(false);
  const [isStreaming, setIsStreaming] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [textOutput, setTextOutput] = useState<string[]>([]);
  const [audioOutputs, setAudioOutputs] = useState<string[]>([]);

  // Refs to avoid stale closures
  const callbacksRef = useRef<NovaSonicCallbacks>({});

  // Update callbacks when state changes
  useEffect(() => {
    callbacksRef.current = {
      onConnect: () => {
        console.log('🎯 useNovaSonic: Connected');
        setIsConnected(true);
        setError(null);
      },

      onDisconnect: () => {
        console.log('🎯 useNovaSonic: Disconnected');
        setIsConnected(false);
        setIsSessionInitialized(false);
        setIsStreaming(false);
      },

      onSessionInitialized: (data) => {
        console.log('🎯 useNovaSonic: Session initialized', data);
        setIsSessionInitialized(true);
        setError(null);
      },

      onTextOutput: (data) => {
        // console.log('🎯 useNovaSonic: Text output', data);
        
        // Handle different Nova Sonic response formats
        let text: string;
        if (typeof data === 'string') {
          text = data;
        } else if (data && typeof data === 'object') {
          if (data.text) {
            text = data.text;
          } else if (data.content) {
            text = data.content;
          } else {
            // If it's a complex object, try to stringify
            text = JSON.stringify(data);
          }
        } else {
          text = String(data);
        }
        
        setTextOutput(prev => [...prev, text]);
      },

      onAudioOutput: (audioData) => {
        // console.log('🎯 useNovaSonic: Audio output', audioData.length, 'bytes');
        setAudioOutputs(prev => [...prev, audioData]);
      },

      onContentStart: () => {
        // console.log('🎯 useNovaSonic: Content start');
      },

      onContentEnd: () => {
        // console.log('🎯 useNovaSonic: Content end');
      },

      onStreamComplete: () => {
        // console.log('🎯 useNovaSonic: Stream complete');
        setIsStreaming(false);
      },

      onError: (err) => {
        console.error('🎯 useNovaSonic: Error', err);
        setError(err.message || 'Unknown error');
        setIsStreaming(false);
      },

      onSessionReady: () => {
        // console.log('🎯 useNovaSonic: Session ready for new stream');
      }
    };
  }, []);

  // Connect function
  const connect = useCallback(async () => {
    try {
      setError(null);
      console.log('🔌 useNovaSonic: Connecting...');
      await novaSonicService.connect(callbacksRef.current);
      
      if (autoInitialize) {
        await initializeSession(systemPrompt);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Connection failed';
      setError(errorMessage);
      console.error('❌ useNovaSonic: Connection failed', err);
    }
  }, [autoInitialize, systemPrompt]);

  // Disconnect function
  const disconnect = useCallback(() => {
    console.log('🔌 useNovaSonic: Disconnecting...');
    novaSonicService.disconnect();
    setIsConnected(false);
    setIsSessionInitialized(false);
    setIsStreaming(false);
    setError(null);
  }, []);

  // Initialize session function
  const initializeSession = useCallback(async (prompt?: string) => {
    try {
      setError(null);
      console.log('🎯 useNovaSonic: Initializing session...');
      await novaSonicService.initializeSession(prompt || systemPrompt);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Session initialization failed';
      setError(errorMessage);
      console.error('❌ useNovaSonic: Session initialization failed', err);
    }
  }, [systemPrompt]);

  // Start streaming function
  const startStreaming = useCallback(async () => {
    try {
      setError(null);
      console.log('🎙️ useNovaSonic: Starting streaming...');
      await novaSonicService.startAudioStreaming();
      setIsStreaming(true);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to start streaming';
      setError(errorMessage);
      console.error('❌ useNovaSonic: Failed to start streaming', err);
    }
  }, []);

  // Stop streaming function
  const stopStreaming = useCallback(() => {
    console.log('⏹️ useNovaSonic: Stopping streaming...');
    novaSonicService.stopAudioStreaming();
    setIsStreaming(false);
  }, []);

  // Send message function
  const sendMessage = useCallback((message: string) => {
    try {
      console.log('💬 useNovaSonic: Sending message', message);
      novaSonicService.sendTextMessage(message);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to send message';
      setError(errorMessage);
      console.error('❌ useNovaSonic: Failed to send message', err);
    }
  }, []);

  // Clear output function
  const clearOutput = useCallback(() => {
    setTextOutput([]);
    setAudioOutputs([]);
  }, []);

  // Get status
  const status = novaSonicService.getStatus();

  // Auto-connect on mount if enabled
  useEffect(() => {
    if (autoConnect) {
      connect();
    }

    // Cleanup on unmount
    return () => {
      if (isConnected) {
        disconnect();
      }
    };
  }, [autoConnect]); // Only run on mount

  return {
    // State
    isConnected,
    isSessionInitialized,
    isStreaming,
    error,
    
    // Actions
    connect,
    disconnect,
    initializeSession,
    startStreaming,
    stopStreaming,
    sendMessage,
    
    // Data
    textOutput,
    audioOutputs,
    
    // Utility
    clearOutput,
    status
  };
};

export default useNovaSonic;