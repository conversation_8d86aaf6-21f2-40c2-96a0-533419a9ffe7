interface ClipboardItem {
  path: string;
  name: string;
  isDirectory: boolean;
  operation: 'copy' | 'cut';
}

class FileClipboardService {
  private clipboardItem: ClipboardItem | null = null;
  private listeners: Array<(item: ClipboardItem | null) => void> = [];

  copy(path: string, name: string, isDirectory: boolean) {
    this.clipboardItem = {
      path,
      name,
      isDirectory,
      operation: 'copy'
    };
    this.notifyListeners();
    console.log(`File copied to clipboard: ${name}`);
  }

  cut(path: string, name: string, isDirectory: boolean) {
    this.clipboardItem = {
      path,
      name,
      isDirectory,
      operation: 'cut'
    };
    this.notifyListeners();
    console.log(`File cut to clipboard: ${name}`);
  }

  getClipboardItem(): ClipboardItem | null {
    return this.clipboardItem;
  }

  hasItem(): boolean {
    return this.clipboardItem !== null;
  }

  clear() {
    this.clipboardItem = null;
    this.notifyListeners();
  }

  subscribe(callback: (item: ClipboardItem | null) => void) {
    this.listeners.push(callback);
    
    // Return unsubscribe function
    return () => {
      const index = this.listeners.indexOf(callback);
      if (index > -1) {
        this.listeners.splice(index, 1);
      }
    };
  }

  private notifyListeners() {
    this.listeners.forEach(callback => callback(this.clipboardItem));
  }

  async paste(destinationDir: string): Promise<{ success: boolean; error?: string; newPath?: string }> {
    if (!this.clipboardItem) {
      return { success: false, error: 'No item in clipboard' };
    }

    try {
      const fileExplorer = window.electronAPI?.fileExplorer;
      if (!fileExplorer) {
        return { success: false, error: 'File system API not available' };
      }

      const sourcePath = this.clipboardItem.path;
      const fileName = this.clipboardItem.name;
      let newPath = `${destinationDir}/${fileName}`;

      // Handle naming conflicts by adding numbers
      let counter = 1;
      while (true) {
        try {
          // Check if destination already exists by trying to read it
          // For directories, we'll try listDirectory, for files readFile
          if (this.clipboardItem.isDirectory) {
            await fileExplorer.listDirectory({ path: newPath });
          } else {
            await fileExplorer.readFile({ path: newPath });
          }
          // If we get here, item exists, so try a new name
          const nameWithoutExt = fileName.includes('.') 
            ? fileName.substring(0, fileName.lastIndexOf('.'))
            : fileName;
          const extension = fileName.includes('.') 
            ? fileName.substring(fileName.lastIndexOf('.'))
            : '';
          newPath = `${destinationDir}/${nameWithoutExt} (${counter})${extension}`;
          counter++;
        } catch {
          // Item doesn't exist, we can use this path
          break;
        }
      }

      if (this.clipboardItem.operation === 'copy') {
        // Copy operation
        const result = await fileExplorer.copy(sourcePath, newPath);
        
        if (result.success) {
          return { success: true, newPath };
        } else {
          return { success: false, error: result.error };
        }
      } else {
        // Cut operation (move)
        const result = await fileExplorer.move(sourcePath, newPath);
        
        if (result.success) {
          // Clear clipboard after successful cut operation
          this.clear();
          return { success: true, newPath };
        } else {
          return { success: false, error: result.error };
        }
      }
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }
}

// Create singleton instance
const fileClipboardService = new FileClipboardService();

export default fileClipboardService;
export type { ClipboardItem };