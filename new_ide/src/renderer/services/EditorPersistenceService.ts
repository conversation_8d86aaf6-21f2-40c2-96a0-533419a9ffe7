interface TabSessionState {
  id: string;
  filePath: string;
  fileName: string;
  content: string;
  isModified: boolean;
  cursorPosition: {
    line: number;
    column: number;
  };
  scrollPosition: {
    scrollTop: number;
    scrollLeft: number;
  };
}

interface EditorSessionState {
  currentFile: string | null;
  cursorPosition?: {
    line: number;
    column: number;
  };
  scrollPosition?: {
    scrollTop: number;
    scrollLeft: number;
  };
  openTabs?: TabSessionState[];
  activeTabId?: string | null;
  lastModified: number;
}

class EditorPersistenceService {
  private static readonly STORAGE_KEY = 'kapi_editor_session_state';

  /**
   * Save current editor session state to localStorage
   */
  static saveSessionState(state: Partial<EditorSessionState>): void {
    try {
      const currentState = this.loadSessionState() || {};
      const newState = {
        ...currentState,
        ...state,
        lastModified: Date.now()
      };
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(newState));
    } catch (error) {
      console.warn('Failed to save editor session state:', error);
    }
  }

  /**
   * Load editor session state from localStorage
   */
  static loadSessionState(): EditorSessionState | null {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      if (!stored) return null;

      const state = JSON.parse(stored);
      
      // Check if state is too old (older than 7 days)
      const sevenDaysAgo = Date.now() - (7 * 24 * 60 * 60 * 1000);
      if (state.lastModified && state.lastModified < sevenDaysAgo) {
        this.clearSessionState();
        return null;
      }

      return state;
    } catch (error) {
      console.warn('Failed to load editor session state:', error);
      return null;
    }
  }

  /**
   * Clear stored session state
   */
  static clearSessionState(): void {
    try {
      localStorage.removeItem(this.STORAGE_KEY);
    } catch (error) {
      console.warn('Failed to clear editor session state:', error);
    }
  }

  /**
   * Update cursor position in stored state
   */
  static updateCursorPosition(line: number, column: number): void {
    this.saveSessionState({
      cursorPosition: { line, column }
    });
  }

  /**
   * Update scroll position in stored state
   */
  static updateScrollPosition(scrollTop: number, scrollLeft: number): void {
    this.saveSessionState({
      scrollPosition: { scrollTop, scrollLeft }
    });
  }

  /**
   * Save current file without changing other state
   */
  static saveCurrentFile(filePath: string | null): void {
    this.saveSessionState({
      currentFile: filePath
    });
  }

  /**
   * Save the full editor state including tabs
   */
  static saveFullEditorState(state: {
    currentFile: string | null;
    openTabs: TabSessionState[];
    activeTabId: string | null;
    cursorPosition?: { line: number; column: number };
    scrollPosition?: { scrollTop: number; scrollLeft: number };
  }): void {
    this.saveSessionState({
      currentFile: state.currentFile,
      openTabs: state.openTabs,
      activeTabId: state.activeTabId,
      cursorPosition: state.cursorPosition,
      scrollPosition: state.scrollPosition
    });
  }

  /**
   * Update tabs in stored state
   */
  static saveTabsState(openTabs: TabSessionState[], activeTabId: string | null): void {
    this.saveSessionState({
      openTabs,
      activeTabId
    });
  }
}

export default EditorPersistenceService;
export type { EditorSessionState, TabSessionState };