import { fontFamilies } from '../utils/fontConfig';

// Mermaid configuration interface
export interface MermaidConfig {
  theme: 'dark' | 'light';
  securityLevel: 'strict' | 'loose';
  fontFamily?: string;
  logLevel?: 'debug' | 'info' | 'warn' | 'error' | 'fatal';
}

// Render result interface
export interface MermaidRenderResult {
  success: boolean;
  svg?: string;
  error?: string;
  id?: string;
}

// Error types for better error handling
export enum MermaidErrorType {
  LIBRARY_NOT_AVAILABLE = 'LIBRARY_NOT_AVAILABLE',
  INITIALIZATION_FAILED = 'INITIALIZATION_FAILED',
  RENDER_FAILED = 'RENDER_FAILED',
  INVALID_SYNTAX = 'INVALID_SYNTAX',
  SECURITY_ERROR = 'SECURITY_ERROR'
}

export class MermaidError extends Error {
  constructor(
    public type: MermaidErrorType,
    message: string,
    public originalError?: Error
  ) {
    super(message);
    this.name = 'MermaidError';
  }
}

// Global state for mermaid instance
let mermaidInstance: any = null;
let mermaidInitialized = false;
const renderedDiagrams = new Map<string, { code: string; svg: string; timestamp: number }>();

// Cache expiry time (1 hour)
const CACHE_EXPIRY_MS = 60 * 60 * 1000;

/**
 * MermaidService - Centralized service for mermaid diagram rendering
 * 
 * This service handles:
 * - Dynamic mermaid library loading
 * - Initialization with consistent configuration
 * - Secure diagram rendering
 * - Error handling and recovery
 * - Dark theme integration
 */
class MermaidService {
  private defaultConfig: MermaidConfig = {
    theme: 'dark',
    securityLevel: 'strict',
    fontFamily: fontFamilies.ui,
    logLevel: 'error'
  };

  /**
   * Dynamically load the mermaid library
   */
  private async loadMermaid(): Promise<any> {
    if (mermaidInstance) {
      return mermaidInstance;
    }

    try {
      console.log('[MermaidService] Loading mermaid library dynamically...');
      const mermaidModule = await import('mermaid');
      mermaidInstance = mermaidModule.default;
      console.log('[MermaidService] Mermaid library loaded successfully');
      return mermaidInstance;
    } catch (error) {
      console.error('[MermaidService] Failed to load mermaid library:', error);
      throw new MermaidError(
        MermaidErrorType.LIBRARY_NOT_AVAILABLE,
        'Mermaid library not available. Please install it with "npm install mermaid"',
        error instanceof Error ? error : undefined
      );
    }
  }

  /**
   * Initialize mermaid with configuration optimized for dark theme
   */
  async initialize(config: Partial<MermaidConfig> = {}): Promise<void> {
    if (mermaidInitialized) {
      return;
    }

    try {
      const mermaid = await this.loadMermaid();
      const finalConfig = { ...this.defaultConfig, ...config };

      mermaid.initialize({
        startOnLoad: false,
        theme: finalConfig.theme,
        securityLevel: finalConfig.securityLevel,
        logLevel: finalConfig.logLevel,
        deterministicIds: true,
        fontFamily: finalConfig.fontFamily,
        
        // Flowchart configuration
        flowchart: {
          htmlLabels: true,
          curve: 'basis',
          padding: 20,
          nodeSpacing: 50,
          rankSpacing: 50,
          useMaxWidth: true,
          diagramPadding: 20,
        },
        
        // Sequence diagram configuration
        sequence: {
          diagramMarginX: 50,
          diagramMarginY: 10,
          useMaxWidth: true,
          boxMargin: 10,
          boxTextMargin: 5,
          noteMargin: 10,
          messageMargin: 35,
        },
        
        // Gantt chart configuration
        gantt: {
          numberSectionStyles: 4,
          axisFormat: '%Y-%m-%d',
          useMaxWidth: true,
          fontSize: 11,
          fontFamily: finalConfig.fontFamily,
        },
        
        // Class diagram configuration
        class: {
          useMaxWidth: true,
        },
        
        // Dark theme variables optimized for KAPI IDE
        themeVariables: {
          darkMode: true,
          background: '#2d2d30',
          primaryColor: '#1a0933',
          primaryTextColor: '#e6d9ff',
          primaryBorderColor: '#6C5CE7',
          lineColor: '#6b7280',
          secondaryColor: '#252526',
          tertiaryColor: '#3e3e42',
          mainBkg: '#1a0933',
          secondBkg: '#252526',
          arrowheadColor: '#6C5CE7',
          fontFamily: finalConfig.fontFamily,
          fontSize: '12px',
          
          // Node styling
          nodeBkg: '#1a0933',
          nodeTextColor: '#e6d9ff',
          classTitleColor: '#e6d9ff',
          classText: '#e6d9ff',
          
          // Fill types for different elements
          fillType0: '#1a0933',
          fillType1: '#2a1854',
          fillType2: '#3a2775',
          fillType3: '#4a3696',
          
          // Sequence diagram specific
          actorBkg: '#1a0933',
          actorTextColor: '#e6d9ff',
          actorLineColor: '#6C5CE7',
          activationBkgColor: '#2a1854',
          activationBorderColor: '#6C5CE7',
        },
      });

      mermaidInitialized = true;
      console.log('[MermaidService] Mermaid initialized successfully');
    } catch (error) {
      console.error('[MermaidService] Failed to initialize mermaid:', error);
      throw new MermaidError(
        MermaidErrorType.INITIALIZATION_FAILED,
        'Failed to initialize mermaid library',
        error instanceof Error ? error : undefined
      );
    }
  }

  /**
   * Convert light colors to dark theme equivalents
   */
  private convertToDarkColors(input: string): string {
    // Map of light colors to dark equivalents
    const colorMap = new Map([
      // Light greens to dark purple variants
      ['#e8f5e9', '#1a0933'],
      ['#c8e6c9', '#2a1854'], 
      ['#a5d6a7', '#3a2775'],
      ['#81c784', '#4a3696'],
      ['#66bb6a', '#5a46b7'],
      
      // Light yellows/oranges to dark purple variants
      ['#fff9c4', '#1a0933'],
      ['#fff59d', '#2a1854'],
      ['#ffeb3b', '#3a2775'],
      ['#fdd835', '#4a3696'],
      ['#fff3e0', '#1a0933'],
      ['#ffccbc', '#2a1854'],
      
      // Light blues to dark purple variants
      ['#e3f2fd', '#1a0933'],
      ['#bbdefb', '#2a1854'],
      ['#90caf9', '#3a2775'],
      ['#64b5f6', '#4a3696'],
      ['#42a5f5', '#5a46b7'],
      ['#e1f5fe', '#1a0933'],
      ['#b3e5fc', '#2a1854'],
      ['#81d4fa', '#3a2775'],
      
      // Light purples/pinks
      ['#f3e5f5', '#1a0933'],
      ['#e1bee7', '#2a1854'],
      ['#ce93d8', '#3a2775'],
      ['#f8bbd0', '#2a1854'],
      
      // Light reds to dark red variants
      ['#ffcdd2', '#4a1827'],
      ['#ffebee', '#2d1b1b'],
      
      // Generic light colors
      ['#ffffff', '#1a0933'],
      ['#fff', '#1a0933'],
      ['white', '#1a0933'],
    ]);

    let processed = input;
    
    // Replace fill colors in style statements
    colorMap.forEach((darkColor, lightColor) => {
      // Match patterns like: style NodeName fill:#e8f5e9
      const fillPattern = new RegExp(`(style\\s+\\w+\\s+fill:)${lightColor.replace('#', '#?')}`, 'gi');
      processed = processed.replace(fillPattern, `$1${darkColor}`);
      
      // Match patterns like: fill:#e8f5e9 in other contexts
      const genericFillPattern = new RegExp(`fill:${lightColor.replace('#', '#?')}`, 'gi');
      processed = processed.replace(genericFillPattern, `fill:${darkColor}`);
    });
    
    // Also handle any remaining light hex colors that start with common light prefixes
    const lightColorPatterns = [
      /fill:#[f-f][0-9a-f]{5}/gi, // Colors starting with f (very light)
      /fill:#[e-e][0-9a-f]{5}/gi, // Colors starting with e (light)
      /fill:#[d-d][0-9a-f]{5}/gi, // Colors starting with d (light-medium)
      /fill:#[c-c][0-9a-f]{5}/gi, // Colors starting with c (light-medium)
    ];
    
    lightColorPatterns.forEach(pattern => {
      processed = processed.replace(pattern, 'fill:#1a0933');
    });
    
    return processed;
  }

  /**
   * Sanitize mermaid input to prevent XSS and other security issues
   */
  private sanitizeMermaidInput(input: string): string {
    if (typeof input !== 'string') {
      throw new MermaidError(
        MermaidErrorType.INVALID_SYNTAX,
        'Mermaid input must be a string'
      );
    }

    // Trim whitespace
    let sanitized = input.trim();
    
    // Remove potential XSS attempts
    sanitized = sanitized.replace(/<script[^>]*>.*?<\/script>/gi, '');
    sanitized = sanitized.replace(/javascript:/gi, 'removed:');
    sanitized = sanitized.replace(/on\w+=/gi, 'data-removed=');
    
    // Convert light colors to dark theme equivalents
    sanitized = this.convertToDarkColors(sanitized);
    
    return sanitized;
  }

  /**
   * Generate a unique ID for diagram rendering
   */
  generateDiagramId(): string {
    return `mermaid-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
  }

  /**
   * Render a mermaid diagram and return the SVG
   */
  async renderDiagram(
    code: string, 
    id?: string,
    options: { 
      applyDarkTheme?: boolean;
      maxRetries?: number;
      timeout?: number;
      useCache?: boolean;
    } = {}
  ): Promise<MermaidRenderResult> {
    const { applyDarkTheme = true, maxRetries = 1, timeout = 10000, useCache = true } = options;
    const diagramId = id || this.generateDiagramId();

    // Check cache first if enabled
    if (useCache) {
      const cacheKey = `${diagramId}_${code.length}_${code.substring(0, 50)}`;
      const cached = renderedDiagrams.get(cacheKey);
      
      if (cached && cached.code === code) {
        const now = Date.now();
        if (now - cached.timestamp < CACHE_EXPIRY_MS) {
          console.log(`[MermaidService] Using cached diagram for ${diagramId}`);
          return {
            success: true,
            svg: cached.svg,
            id: diagramId
          };
        } else {
          // Cache expired, remove it
          renderedDiagrams.delete(cacheKey);
        }
      }
    }

    // Wrap the entire rendering process in a timeout
    return Promise.race([
      this.doRenderDiagram(code, diagramId, { applyDarkTheme, maxRetries, useCache }),
      new Promise<MermaidRenderResult>((_, reject) =>
        setTimeout(() => reject(new Error(`Rendering timeout after ${timeout}ms`)), timeout)
      )
    ]);
  }

  /**
   * Internal rendering method without timeout
   */
  private async doRenderDiagram(
    code: string,
    diagramId: string,
    options: { applyDarkTheme: boolean; maxRetries: number; useCache?: boolean }
  ): Promise<MermaidRenderResult> {
    const { applyDarkTheme, maxRetries, useCache = true } = options;

    try {
      // Ensure mermaid is initialized
      await this.initialize();
      const mermaid = await this.loadMermaid();

      // Sanitize input
      const sanitizedCode = this.sanitizeMermaidInput(code);
      
      if (!sanitizedCode.trim()) {
        throw new MermaidError(
          MermaidErrorType.INVALID_SYNTAX,
          'Empty diagram content'
        );
      }

      // Attempt rendering with retries
      let lastError: Error | null = null;
      
      for (let attempt = 0; attempt <= maxRetries; attempt++) {
        try {
          let svgOutput: string;
          
          // Use modern mermaid v11.x API with fallbacks
          if (typeof mermaid.renderAsync === 'function') {
            const result = await mermaid.renderAsync(diagramId, sanitizedCode);
            svgOutput = result.svg || result;
          } else if (typeof mermaid.render === 'function') {
            const result = await mermaid.render(diagramId, sanitizedCode);
            svgOutput = result.svg || result;
          } else {
            // Fallback for older versions
            const tempDiv = document.createElement('div');
            tempDiv.className = 'mermaid';
            tempDiv.textContent = sanitizedCode;
            document.body.appendChild(tempDiv);
            
            await mermaid.run({
              nodes: [tempDiv],
              suppressErrors: false
            });
            
            svgOutput = tempDiv.innerHTML;
            document.body.removeChild(tempDiv);
          }

          // Clean and secure the SVG
          const cleanedSvg = this.cleanSvgOutput(svgOutput, applyDarkTheme);
          
          // Cache the result if caching is enabled
          if (useCache) {
            const cacheKey = `${diagramId}_${code.length}_${code.substring(0, 50)}`;
            renderedDiagrams.set(cacheKey, {
              code,
              svg: cleanedSvg,
              timestamp: Date.now()
            });
            
            // Clean up old cache entries
            this.cleanupOldCacheEntries();
          }
          
          return {
            success: true,
            svg: cleanedSvg,
            id: diagramId
          };
          
        } catch (error) {
          lastError = error instanceof Error ? error : new Error(String(error));
          console.warn(`[MermaidService] Render attempt ${attempt + 1} failed:`, lastError);
          
          // If this is not the last attempt, clear any partial state and retry
          if (attempt < maxRetries) {
            // Longer delay before retry for complex diagrams
            const delay = Math.min(500 + (attempt * 200), 1500); // 500ms, 700ms, 900ms...
            console.warn(`[MermaidService] Retrying in ${delay}ms...`);
            await new Promise(resolve => setTimeout(resolve, delay));
          }
        }
      }

      // All attempts failed
      throw lastError || new Error('Rendering failed');
      
    } catch (error) {
      console.error('[MermaidService] Failed to render diagram:', error);
      
      const mermaidError = error instanceof MermaidError 
        ? error 
        : new MermaidError(
            MermaidErrorType.RENDER_FAILED,
            error instanceof Error ? error.message : 'Unknown rendering error',
            error instanceof Error ? error : undefined
          );

      return {
        success: false,
        error: mermaidError.message,
        id: diagramId
      };
    }
  }

  /**
   * Clean SVG output and apply dark theme styling
   */
  private cleanSvgOutput(svg: string, applyDarkTheme: boolean): string {
    try {
      // Parse SVG safely
      const parser = new DOMParser();
      const svgDoc = parser.parseFromString(svg, 'image/svg+xml');
      
      // Remove any script tags for security
      const scriptTags = svgDoc.querySelectorAll('script');
      scriptTags.forEach(tag => tag.remove());
      
      if (applyDarkTheme) {
        // Apply dark theme styling
        const svgElement = svgDoc.documentElement;
        
        if (svgElement) {
          svgElement.style.maxWidth = '100%';
          svgElement.style.height = 'auto';
          svgElement.style.backgroundColor = 'transparent';
          
          // Style text elements
          const textElements = svgElement.querySelectorAll('text');
          textElements.forEach(text => {
            text.setAttribute('fill', '#e6d9ff');
          });

          // Style rectangles/boxes
          const rectElements = svgElement.querySelectorAll('rect');
          rectElements.forEach(rect => {
            const currentFill = rect.getAttribute('fill');
            if (currentFill && currentFill !== 'none') {
              rect.setAttribute('fill', '#1a0933');
              rect.setAttribute('stroke', '#6C5CE7');
            }
          });

          // Style polygons (flowchart shapes)
          const polygonElements = svgElement.querySelectorAll('polygon');
          polygonElements.forEach(polygon => {
            const currentFill = polygon.getAttribute('fill');
            if (currentFill && currentFill !== 'none') {
              polygon.setAttribute('fill', '#1a0933');
              polygon.setAttribute('stroke', '#6C5CE7');
            }
          });
        }
      }
      
      return new XMLSerializer().serializeToString(svgDoc);
      
    } catch (error) {
      console.warn('[MermaidService] Failed to clean SVG, returning original:', error);
      return svg;
    }
  }

  /**
   * Check if mermaid library is available
   */
  async isAvailable(): Promise<boolean> {
    try {
      await this.loadMermaid();
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Get the current initialization status
   */
  isInitialized(): boolean {
    return mermaidInitialized;
  }

  /**
   * Clear rendered diagrams cache
   */
  clearCache(): void {
    renderedDiagrams.clear();
  }

  /**
   * Clean up old cache entries
   */
  private cleanupOldCacheEntries(): void {
    const now = Date.now();
    const entriesToDelete: string[] = [];
    
    for (const [key, value] of renderedDiagrams) {
      if (now - value.timestamp > CACHE_EXPIRY_MS) {
        entriesToDelete.push(key);
      }
    }
    
    entriesToDelete.forEach(key => renderedDiagrams.delete(key));
    
    // Also limit total cache size
    if (renderedDiagrams.size > 100) {
      // Remove oldest entries
      const entries = Array.from(renderedDiagrams.entries())
        .sort((a, b) => a[1].timestamp - b[1].timestamp);
      
      // Keep only the 50 most recent
      const toRemove = entries.slice(0, entries.length - 50);
      toRemove.forEach(([key]) => renderedDiagrams.delete(key));
    }
  }

  /**
   * Reset the service (useful for testing)
   */
  reset(): void {
    mermaidInstance = null;
    mermaidInitialized = false;
    this.clearCache();
  }
}

// Export singleton instance
export const mermaidService = new MermaidService();
export default mermaidService;