/**
 * Global keyboard shortcut manager for the IDE
 * Handles application-wide keyboard shortcuts and command registration
 */

export interface KeyboardShortcut {
  key: string;
  metaKey?: boolean;
  ctrlKey?: boolean;
  shiftKey?: boolean;
  altKey?: boolean;
  action: () => void | Promise<void>;
  description: string;
  enabled?: boolean;
  context?: string; // Optional context for scoped shortcuts
}

export interface CommandHistoryEntry {
  action: () => void | Promise<void>;
  undo: () => void | Promise<void>;
  description: string;
  timestamp: number;
}

class KeyboardManager {
  private shortcuts: Map<string, KeyboardShortcut> = new Map();
  private commandHistory: CommandHistoryEntry[] = [];
  private currentHistoryIndex: number = -1;
  private maxHistorySize: number = 100;
  private isListening: boolean = false;

  /**
   * Initialize the keyboard manager and start listening for events
   */
  initialize() {
    if (this.isListening) return;

    document.addEventListener('keydown', this.handleKeyDown.bind(this));
    this.isListening = true;
    console.log('[KeyboardManager] Initialized and listening for keyboard events');
  }

  /**
   * Cleanup and stop listening for events
   */
  destroy() {
    document.removeEventListener('keydown', this.handleKeyDown.bind(this));
    this.isListening = false;
    console.log('[KeyboardManager] Destroyed and stopped listening');
  }

  /**
   * Register a keyboard shortcut
   */
  registerShortcut(shortcut: KeyboardShortcut): void {
    const key = this.getShortcutKey(shortcut);
    const wasAlreadyRegistered = this.shortcuts.has(key);
    this.shortcuts.set(key, { ...shortcut, enabled: shortcut.enabled !== false });

    // Only log if this is a new registration (not a re-registration)
    if (!wasAlreadyRegistered && process.env.NODE_ENV === 'development') {
      // console.log(`[KeyboardManager] Registered shortcut: ${key} - ${shortcut.description}`);
    }
  }

  /**
   * Unregister a keyboard shortcut
   */
  unregisterShortcut(key: string, metaKey?: boolean, ctrlKey?: boolean, shiftKey?: boolean, altKey?: boolean): void {
    const shortcutKey = this.getShortcutKeyFromParams(key, metaKey, ctrlKey, shiftKey, altKey);
    const removed = this.shortcuts.delete(shortcutKey);
    // Reduce logging noise - only log if we actually removed something meaningful
    if (removed && process.env.NODE_ENV === 'development') {
      console.log(`[KeyboardManager] Unregistered shortcut: ${shortcutKey}`);
    }
  }

  /**
   * Enable or disable a shortcut
   */
  setShortcutEnabled(key: string, enabled: boolean, metaKey?: boolean, ctrlKey?: boolean, shiftKey?: boolean, altKey?: boolean): void {
    const shortcutKey = this.getShortcutKeyFromParams(key, metaKey, ctrlKey, shiftKey, altKey);
    const shortcut = this.shortcuts.get(shortcutKey);
    if (shortcut) {
      shortcut.enabled = enabled;
      console.log(`[KeyboardManager] ${enabled ? 'Enabled' : 'Disabled'} shortcut: ${shortcutKey}`);
    }
  }

  /**
   * Execute a command with undo/redo support
   */
  async executeCommand(entry: CommandHistoryEntry): Promise<void> {
    try {
      await entry.action();

      // Add to history
      this.addToHistory(entry);

      console.log(`[KeyboardManager] Executed command: ${entry.description}`);
    } catch (error) {
      console.error(`[KeyboardManager] Error executing command: ${entry.description}`, error);
    }
  }

  /**
   * Undo the last command
   */
  async undo(): Promise<boolean> {
    if (this.currentHistoryIndex < 0 || this.commandHistory.length === 0) {
      console.log('[KeyboardManager] Nothing to undo');
      return false;
    }

    const entry = this.commandHistory[this.currentHistoryIndex];
    try {
      await entry.undo();
      this.currentHistoryIndex--;
      console.log(`[KeyboardManager] Undid command: ${entry.description}`);
      return true;
    } catch (error) {
      console.error(`[KeyboardManager] Error undoing command: ${entry.description}`, error);
      return false;
    }
  }

  /**
   * Redo the next command
   */
  async redo(): Promise<boolean> {
    if (this.currentHistoryIndex >= this.commandHistory.length - 1) {
      console.log('[KeyboardManager] Nothing to redo');
      return false;
    }

    this.currentHistoryIndex++;
    const entry = this.commandHistory[this.currentHistoryIndex];
    try {
      await entry.action();
      console.log(`[KeyboardManager] Redid command: ${entry.description}`);
      return true;
    } catch (error) {
      console.error(`[KeyboardManager] Error redoing command: ${entry.description}`, error);
      this.currentHistoryIndex--;
      return false;
    }
  }

  /**
   * Clear command history
   */
  clearHistory(): void {
    this.commandHistory = [];
    this.currentHistoryIndex = -1;
    console.log('[KeyboardManager] Cleared command history');
  }

  /**
   * Get all registered shortcuts
   */
  getShortcuts(): KeyboardShortcut[] {
    return Array.from(this.shortcuts.values());
  }

  /**
   * Get command history
   */
  getHistory(): CommandHistoryEntry[] {
    return [...this.commandHistory];
  }

  /**
   * Private method to handle keydown events
   */
  private handleKeyDown(event: KeyboardEvent): void {
    // Don't handle shortcuts if typing in input/textarea (unless specifically allowed)
    const target = event.target as HTMLElement;
    const isInputElement = target.tagName === 'INPUT' || target.tagName === 'TEXTAREA' || target.contentEditable === 'true';

    // Special handling for Monaco editor
    if (target.closest('.monaco-editor')) {
      // Allow Monaco's built-in shortcuts to pass through uninterrupted
      if (this.isMonacoBuiltinShortcut(event)) {
        console.log(`[KeyboardManager] Allowing Monaco built-in shortcut to pass through: ${event.key}`);
        return;
      }
      
      // For other shortcuts in Monaco editor, only allow global shortcuts
      const shortcutKey = this.getShortcutKeyFromEvent(event);
      const shortcut = this.shortcuts.get(shortcutKey);
      
      if (shortcut && !this.isGlobalShortcut(shortcut)) {
        return;
      }
    }

    const shortcutKey = this.getShortcutKeyFromEvent(event);
    const shortcut = this.shortcuts.get(shortcutKey);

    if (shortcut && shortcut.enabled !== false) {
      // Check if this shortcut should work in input elements
      if (isInputElement && !this.isGlobalShortcut(shortcut)) {
        return;
      }

      event.preventDefault();
      event.stopPropagation();

      try {
        shortcut.action();
        console.log(`[KeyboardManager] Executed shortcut: ${shortcutKey} - ${shortcut.description}`);
      } catch (error) {
        console.error(`[KeyboardManager] Error executing shortcut: ${shortcutKey}`, error);
      }
    }
  }

  /**
   * Check if a shortcut should work globally (even in input elements)
   */
  private isGlobalShortcut(shortcut: KeyboardShortcut): boolean {
    // These shortcuts should work everywhere
    const globalShortcuts = ['save', 'undo', 'redo', 'copy', 'paste', 'cut'];
    return globalShortcuts.some(global => shortcut.description.toLowerCase().includes(global));
  }

  /**
   * Check if a shortcut is a Monaco Editor built-in shortcut that should not be blocked
   */
  private isMonacoBuiltinShortcut(event: KeyboardEvent): boolean {
    // Monaco Editor built-in shortcuts that we should never intercept
    const isMac = /Mac|iPod|iPhone|iPad/.test(navigator.userAgent);
    
    // Find shortcuts: Cmd+F (Mac) or Ctrl+F (Windows/Linux)
    if (event.key.toLowerCase() === 'f' && 
        ((isMac && event.metaKey && !event.ctrlKey) || 
         (!isMac && event.ctrlKey && !event.metaKey)) &&
        !event.shiftKey && !event.altKey) {
      return true;
    }
    
    // Replace shortcuts: Cmd+Option+F (Mac) or Ctrl+H (Windows/Linux)
    if ((isMac && event.key.toLowerCase() === 'f' && event.metaKey && event.altKey && !event.shiftKey && !event.ctrlKey) ||
        (!isMac && event.key.toLowerCase() === 'h' && event.ctrlKey && !event.metaKey && !event.shiftKey && !event.altKey)) {
      return true;
    }
    
    return false;
  }

  /**
   * Generate a unique key for a shortcut
   */
  private getShortcutKey(shortcut: KeyboardShortcut): string {
    return this.getShortcutKeyFromParams(
      shortcut.key,
      shortcut.metaKey,
      shortcut.ctrlKey,
      shortcut.shiftKey,
      shortcut.altKey,
    );
  }

  /**
   * Generate a unique key from parameters
   */
  private getShortcutKeyFromParams(key: string, metaKey?: boolean, ctrlKey?: boolean, shiftKey?: boolean, altKey?: boolean): string {
    const modifiers = [];
    if (metaKey) modifiers.push('meta');
    if (ctrlKey) modifiers.push('ctrl');
    if (shiftKey) modifiers.push('shift');
    if (altKey) modifiers.push('alt');

    return `${modifiers.join('+')}_${key.toLowerCase()}`;
  }

  /**
   * Generate a unique key from a keyboard event
   */
  private getShortcutKeyFromEvent(event: KeyboardEvent): string {
    return this.getShortcutKeyFromParams(
      event.key,
      event.metaKey,
      event.ctrlKey,
      event.shiftKey,
      event.altKey,
    );
  }

  /**
   * Add a command to history
   */
  private addToHistory(entry: CommandHistoryEntry): void {
    // Remove any entries after current index (when user does something after undo)
    this.commandHistory = this.commandHistory.slice(0, this.currentHistoryIndex + 1);

    // Add new entry
    this.commandHistory.push(entry);
    this.currentHistoryIndex++;

    // Limit history size
    if (this.commandHistory.length > this.maxHistorySize) {
      this.commandHistory.shift();
      this.currentHistoryIndex--;
    }
  }
}

// Export singleton instance
export const keyboardManager = new KeyboardManager();

// Export utility functions for common shortcuts
export const createPlatformShortcut = (key: string, action: () => void | Promise<void>, description: string) => {
  const isMac = /Mac|iPod|iPhone|iPad/.test(navigator.userAgent);

  return {
    key,
    [isMac ? 'metaKey' : 'ctrlKey']: true,
    action,
    description,
  } as KeyboardShortcut;
};

export const createShiftShortcut = (key: string, action: () => void | Promise<void>, description: string) => {
  const isMac = /Mac|iPod|iPhone|iPad/.test(navigator.userAgent);

  return {
    key,
    [isMac ? 'metaKey' : 'ctrlKey']: true,
    shiftKey: true,
    action,
    description,
  } as KeyboardShortcut;
};

export default KeyboardManager;
