import { ApiClient } from './ApiClient';
import { logger } from '../utils/index';

export interface Message {
  id: number;
  role: 'user' | 'assistant';
  content: string;
  model?: string;
  createdAt: string;
  metrics?: MessageMetrics;
}

export interface MessageMetrics {
  promptTokens?: number;
  completionTokens?: number;
  cost?: number;
  duration?: number; // in seconds
}

export interface Conversation {
  id: number;
  userId: number;
  title: string;
  status: 'active' | 'completed' | 'archived';
  createdAt: string;
  updatedAt: string;
  messages?: Message[];
}

export interface CreateConversationOptions {
  title?: string;
  strategy?: TaskStrategy;
  projectContext?: ProjectContext;
}

export interface ProjectContext {
  projectId?: string;
  currentFile?: string;
  fileContent?: string;
  projectStructure?: string[];
}

export interface StreamOptions {
  modelId: string;
  temperature?: number;
  maxTokens?: number;
  memoryCount?: number;
  strategy?: TaskStrategy;
  projectContext?: ProjectContext;
}

export interface StreamChunk {
  type: 'conversation_id' | 'content' | 'metadata' | 'done' | 'error';
  conversation_id?: number;
  content?: string;
  metadata?: MessageMetrics;
  message?: string;
}

export type TaskStrategy = 
  | 'chat'           // General conversational AI
  | 'code_generation' // Creating code from specifications  
  | 'code_planning'   // Architectural planning and design
  | 'slide_generation' // Creating presentation slides
  | 'svg_mockup'      // Generating visual mockups
  | 'test_cases'      // Automated test generation
  | 'multimodal';     // Multimodal image analysis

export interface ConversationFilter {
  status?: 'active' | 'completed' | 'archived';
  strategy?: TaskStrategy;
  skip?: number;
  limit?: number;
}

export interface MultimodalMessageOptions {
  conversationId: number;
  prompt: string;
  images: Array<{
    url: string;
    description?: string;
  }>;
  instructions: string;
  outputFormat?: 'description' | 'code' | 'both';
  designStyle?: string;
  targetPlatform?: 'web' | 'mobile' | 'desktop';
  model?: string;
  maxTokens?: number;
  temperature?: number;
}

/**
 * ConversationService - Unified conversation management for Kapi IDE
 * 
 * Provides base-level conversation management with LLMs including:
 * - Conversation CRUD operations
 * - Real-time streaming responses
 * - Task strategy routing
 * - Context-aware interactions
 * - Cost tracking and optimization
 */
export class ConversationService {
  private apiClient: ApiClient;

  constructor() {
    this.apiClient = new ApiClient();
  }

  /**
   * Get all conversations for the current user
   */
  async getConversations(filter: ConversationFilter = {}): Promise<{
    conversations: Conversation[];
    total: number;
  }> {
    try {
      const params: Record<string, any> = {};
      
      if (filter.status) params.status = filter.status;
      if (filter.skip) params.skip = filter.skip;
      if (filter.limit) params.limit = filter.limit;

      const data = await this.apiClient.get('/conversations', params);

      return {
        conversations: data.conversations || data,
        total: data.total || data.length || 0,
      };
    } catch (error) {
      logger.error('Error fetching conversations:', error);
      throw error;
    }
  }

  /**
   * Get a specific conversation by ID with messages
   */
  async getConversation(conversationId: number): Promise<{
    conversation: Conversation;
    messages: Message[];
  } | null> {
    try {
      const data = await this.apiClient.get(`/conversations/${conversationId}`);

      return {
        conversation: data.conversation || data,
        messages: data.messages || [],
      };
    } catch (error: any) {
      if (error.statusCode === 404) {
        return null;
      }
      logger.error(`Error fetching conversation ${conversationId}:`, error);
      throw error;
    }
  }

  /**
   * Create a new conversation
   */
  async createConversation(options: CreateConversationOptions = {}): Promise<Conversation> {
    try {
      const requestBody = {
        title: options.title || `New ${options.strategy || 'chat'} conversation`,
        taskType: options.strategy || 'chat',
        projectContext: options.projectContext,
      };

      const data = await this.apiClient.post('/conversations', requestBody);

      return data.conversation || data;
    } catch (error) {
      logger.error('Error creating conversation:', error);
      throw error;
    }
  }

  /**
   * Send a message and get a streaming response
   */
  async sendMessage(
    conversationId: number | undefined,
    message: string,
    options: StreamOptions
  ): Promise<AsyncGenerator<StreamChunk, void, unknown>> {
    const controller = new AbortController();
    
    try {
      // If no conversation ID, we need to create one first
      if (!conversationId) {
        throw new Error('Conversation ID is required for streaming');
      }

      const requestBody = {
        prompt: message,
        model: options.modelId,
        temperature: options.temperature || 0.7,
        maxTokens: options.maxTokens || 1000,
        taskType: options.strategy || 'chat',
        memoryCount: options.memoryCount,
      };
      
      console.log('🔧 [ConversationService] Sending message with options:', JSON.stringify(options, null, 2));
      console.log('🔧 [ConversationService] Request body taskType:', requestBody.taskType);
      console.log('🔧 [ConversationService] options.strategy:', options.strategy);

      // Get auth headers from ApiClient
      const token = localStorage.getItem('token');
      const headers: HeadersInit = {
        'Content-Type': 'application/json',
        'Accept': 'text/event-stream',
        'Cache-Control': 'no-cache',
      };

      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      const response = await fetch(`${this.apiClient.baseUrl}/conversations/${conversationId}/stream`, {
        method: 'POST',
        headers,
        body: JSON.stringify(requestBody),
        signal: controller.signal,
      });

      if (!response.ok) {
        throw new Error(`Failed to send message: ${response.statusText}`);
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('Failed to get response reader');
      }

      const decoder = new TextDecoder();

      async function* streamGenerator(): AsyncGenerator<StreamChunk, void, unknown> {
        try {
          while (true) {
            const { done, value } = await reader!.read();
            
            if (done) break;

            const chunk = decoder.decode(value, { stream: true });
            const lines = chunk.split('\n');

            for (const line of lines) {
              if (line.startsWith('data: ')) {
                try {
                  const data = JSON.parse(line.slice(6));
                  yield data as StreamChunk;
                } catch (e) {
                  // Skip malformed JSON
                  continue;
                }
              }
            }
          }
        } finally {
          reader!.releaseLock();
        }
      }

      return streamGenerator();
    } catch (error) {
      controller.abort();
      logger.error('Error sending message:', error);
      throw error;
    }
  }

  /**
   * Get strategy-specific prompt templates and optimizations
   */
  getStrategyConfig(strategy: TaskStrategy): {
    systemPrompt: string;
    defaultModel: string;
    maxTokens: number;
    temperature: number;
  } {
    const configs = {
      chat: {
        systemPrompt: 'You are a helpful coding assistant. Provide clear, concise answers and code examples when appropriate.',
        defaultModel: 'gemini-2.0-flash-lite', // Cost-effective for general chat
        maxTokens: 1000,
        temperature: 0.7,
      },
      code_generation: {
        systemPrompt: 'You are a code generation expert. Generate clean, well-documented code following best practices. Include comments and error handling.',
        defaultModel: 'claude-3.5-haiku', // Better for structured code
        maxTokens: 2000,
        temperature: 0.3,
      },
      code_planning: {
        systemPrompt: 'You are a software architect. Focus on high-level design, scalability, and best practices. Provide architectural guidance and technology recommendations.',
        defaultModel: 'claude-3.5-haiku', // Good for reasoning
        maxTokens: 1500,
        temperature: 0.4,
      },
      slide_generation: {
        systemPrompt: 'You are a presentation expert. Create clear, structured slides with bullet points and visual hierarchy. Focus on audience-appropriate language.',
        defaultModel: 'claude-3.7-sonnet', // Use Claude for better HTML/reveal.js generation
        maxTokens: 8192, // Increased for complete slide generation
        temperature: 0.6,
      },
      svg_mockup: {
        systemPrompt: 'You are a UI/UX designer. Generate clean SVG mockups and wireframes. Focus on responsive design and accessibility.',
        defaultModel: 'claude-3.5-haiku', // Better for structured SVG
        maxTokens: 2000,
        temperature: 0.4,
      },
      test_cases: {
        systemPrompt: 'You are a testing expert. Generate comprehensive test cases including unit tests, edge cases, and integration tests. Follow testing best practices.',
        defaultModel: 'claude-3.5-haiku', // Good for systematic testing
        maxTokens: 2000,
        temperature: 0.2,
      },
      multimodal: {
        systemPrompt: 'You are an expert UI/UX designer and developer specializing in mockup analysis and enhancement. Analyze provided images and provide detailed design recommendations.',
        defaultModel: 'gpt-4.1', // Best for multimodal analysis
        maxTokens: 8000,
        temperature: 0.7,
      },
    };

    return configs[strategy] || configs.chat;
  }

  /**
   * Send multimodal message with images for analysis
   */
  async sendMultimodalMessage(options: MultimodalMessageOptions): Promise<{
    success: boolean;
    message?: Message;
    error?: string;
  }> {
    try {
      logger.info('Sending multimodal message for conversation:', options.conversationId);

      const requestBody = {
        prompt: options.prompt,
        conversationId: options.conversationId,
        images: options.images,
        instructions: options.instructions,
        outputFormat: options.outputFormat || 'both',
        designStyle: options.designStyle || 'modern',
        targetPlatform: options.targetPlatform || 'web',
        model: options.model || 'gpt-4.1-mini',
        maxTokens: options.maxTokens || 4000,
        temperature: options.temperature || 0.7,
      };

      const response = await this.apiClient.post('/tasks/multimodal', requestBody);

      if (response.status === 'success') {
        // Use message content from the actual response structure
        const messageContent = response.message?.content || response.content || '';
        
        console.log('[ConversationService] Multimodal response message content length:', messageContent.length);
        console.log('[ConversationService] First 200 chars:', messageContent.substring(0, 200));
        
        const message: Message = {
          id: Date.now(), // Temporary ID
          role: 'assistant',
          content: messageContent,
          model: response.model,
          createdAt: new Date().toISOString(),
          metrics: {
            promptTokens: response.promptTokens,
            completionTokens: response.completionTokens,
            cost: response.cost,
            duration: response.processingTime / 1000, // Convert to seconds
          },
        };

        return {
          success: true,
          message,
        };
      } else {
        return {
          success: false,
          error: response.message || 'Multimodal analysis failed',
        };
      }
    } catch (error) {
      logger.error('Error sending multimodal message:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Get available AI models for conversation
   */
  getAvailableModels(): Array<{
    id: string;
    name: string;
    provider: string;
    costTier: 'low' | 'medium' | 'high';
    capabilities: string[];
  }> {
    return [
      {
        id: 'gemini-2.0-flash-lite',
        name: 'Gemini 2.0 Flash Lite',
        provider: 'Google',
        costTier: 'low',
        capabilities: ['chat', 'code', 'reasoning'],
      },
      {
        id: 'claude-3.5-haiku',
        name: 'Claude 3.5 Haiku',
        provider: 'Anthropic',
        costTier: 'medium',
        capabilities: ['code', 'reasoning', 'analysis'],
      },
      {
        id: 'nova-micro',
        name: 'Nova Micro',
        provider: 'Amazon',
        costTier: 'low',
        capabilities: ['chat', 'simple-tasks'],
      },
      {
        id: 'gpt-4.1-nano',
        name: 'GPT-4.1 Nano',
        provider: 'Azure',
        costTier: 'medium',
        capabilities: ['chat', 'code', 'reasoning'],
      },
    ];
  }

  /**
   * Estimate cost for a conversation based on message length and model
   */
  estimateCost(message: string, modelId: string): {
    estimatedTokens: number;
    estimatedCost: number;
  } {
    // Rough token estimation (1 token ≈ 4 characters)
    const estimatedTokens = Math.ceil(message.length / 4);
    
    // Cost per 1K tokens (rough estimates)
    const costPer1KTokens = {
      'gemini-2.0-flash-lite': 0.001,
      'claude-3.5-haiku': 0.0025,
      'nova-micro': 0.0008,
      'gpt-4.1-nano': 0.002,
    };

    const modelCost = costPer1KTokens[modelId as keyof typeof costPer1KTokens] || 0.001;
    const estimatedCost = (estimatedTokens / 1000) * modelCost;

    return {
      estimatedTokens,
      estimatedCost,
    };
  }

  /**
   * Add project context to enhance conversations
   */
  private buildContextualPrompt(
    message: string,
    strategy: TaskStrategy,
    projectContext?: ProjectContext
  ): string {
    const config = this.getStrategyConfig(strategy);
    let prompt = config.systemPrompt + '\n\n';

    if (projectContext) {
      if (projectContext.currentFile) {
        prompt += `Current file: ${projectContext.currentFile}\n`;
      }
      
      if (projectContext.fileContent) {
        prompt += `File content:\n\`\`\`\n${projectContext.fileContent}\n\`\`\`\n\n`;
      }
      
      if (projectContext.projectStructure) {
        prompt += `Project structure:\n${projectContext.projectStructure.join('\n')}\n\n`;
      }
    }

    prompt += `User request: ${message}`;
    return prompt;
  }
}

// Export singleton instance
export const conversationService = new ConversationService();
export default conversationService;