/**
 * Semantic Search Service
 * Orchestrates document processing and ChromaDB for semantic search
 */

import ChromaDbService, { SemanticSearchResult, SearchOptions } from './ChromaDbService';
import DocumentProcessingService, { ProcessingOptions } from './DocumentProcessingService';
import { ApiClient } from './ApiClient';
import { FileOperationResult } from '../types/FileTypes';

export interface IndexingProgress {
  stage: 'initializing' | 'scanning' | 'processing' | 'embedding' | 'storing' | 'completed' | 'error';
  progress: number; // 0-100
  message: string;
  filesProcessed?: number;
  totalFiles?: number;
  error?: string;
}

export interface IndexingOptions extends ProcessingOptions {
  onProgress?: (progress: IndexingProgress) => void;
  forceReindex?: boolean;
}

export interface SemanticSearchOptions extends SearchOptions {
  hybrid?: boolean; // Combine with traditional search
  rerank?: boolean; // Apply AI-based reranking
}

/**
 * High-level semantic search service that manages indexing and searching
 */
export class SemanticSearchService {
  private chromaDb: ChromaDbService;
  private docProcessor: DocumentProcessingService;
  private apiClient: ApiClient;
  private isIndexing = false;
  private indexingProgress: IndexingProgress | null = null;

  constructor(projectId: string, apiClient: ApiClient) {
    this.apiClient = apiClient;
    this.chromaDb = new ChromaDbService(projectId, apiClient);
    this.docProcessor = new DocumentProcessingService();
  }

  /**
   * Initialize the semantic search service
   */
  async initialize(): Promise<void> {
    console.log('[SemanticSearchService] Initializing...');
    await this.chromaDb.initialize();
    console.log('[SemanticSearchService] Initialization complete');
  }

  /**
   * Index a project for semantic search
   */
  async indexProject(projectPath: string, options: IndexingOptions = {}): Promise<void> {
    if (this.isIndexing) {
      throw new Error('Indexing already in progress');
    }

    const { onProgress, forceReindex = false, ...processingOptions } = options;
    
    this.isIndexing = true;
    this.indexingProgress = {
      stage: 'initializing',
      progress: 0,
      message: 'Initializing indexing process...'
    };

    try {
      // Report progress
      onProgress?.(this.indexingProgress);

      // Check if already indexed and not forcing reindex
      if (!forceReindex) {
        const stats = await this.chromaDb.getStats();
        if (stats.documentCount > 0) {
          console.log(`[SemanticSearchService] Project already indexed with ${stats.documentCount} documents`);
          this.indexingProgress = {
            stage: 'completed',
            progress: 100,
            message: `Project already indexed (${stats.documentCount} documents)`
          };
          onProgress?.(this.indexingProgress);
          return;
        }
      }

      // Clear existing documents if force reindexing
      if (forceReindex) {
        await this.chromaDb.clearDocuments();
      }

      // Stage 1: Scan project files
      this.indexingProgress = {
        stage: 'scanning',
        progress: 10,
        message: 'Scanning project files...'
      };
      onProgress?.(this.indexingProgress);

      // Stage 2: Process documents
      this.indexingProgress = {
        stage: 'processing',
        progress: 30,
        message: 'Processing documents and extracting content...'
      };
      onProgress?.(this.indexingProgress);

      const chunks = await this.docProcessor.processProjectDirectory(projectPath, processingOptions);
      
      if (chunks.length === 0) {
        this.indexingProgress = {
          stage: 'completed',
          progress: 100,
          message: 'No documents found to index'
        };
        onProgress?.(this.indexingProgress);
        return;
      }

      // Stage 3: Generate embeddings and store
      this.indexingProgress = {
        stage: 'embedding',
        progress: 60,
        message: `Generating embeddings for ${chunks.length} document chunks...`,
        filesProcessed: 0,
        totalFiles: chunks.length
      };
      onProgress?.(this.indexingProgress);

      // Process in batches to avoid overwhelming the API
      const batchSize = 20;
      const batches = this.createBatches(chunks, batchSize);
      
      for (let i = 0; i < batches.length; i++) {
        const batch = batches[i];
        
        // Update progress
        const progressPercent = 60 + Math.round((i / batches.length) * 30);
        this.indexingProgress = {
          stage: 'storing',
          progress: progressPercent,
          message: `Processing batch ${i + 1}/${batches.length}...`,
          filesProcessed: i * batchSize,
          totalFiles: chunks.length
        };
        onProgress?.(this.indexingProgress);

        // Process batch
        await this.chromaDb.processDocuments(batch);
        
        // Small delay to avoid overwhelming the API
        if (i < batches.length - 1) {
          await this.delay(100);
        }
      }

      // Stage 4: Complete
      const finalStats = await this.chromaDb.getStats();
      this.indexingProgress = {
        stage: 'completed',
        progress: 100,
        message: `Indexing complete! ${finalStats.documentCount} documents indexed.`,
        filesProcessed: chunks.length,
        totalFiles: chunks.length
      };
      onProgress?.(this.indexingProgress);

      console.log(`[SemanticSearchService] Successfully indexed ${finalStats.documentCount} documents`);
    } catch (error) {
      console.error('[SemanticSearchService] Indexing error:', error);
      this.indexingProgress = {
        stage: 'error',
        progress: 0,
        message: 'Indexing failed',
        error: error instanceof Error ? error.message : String(error)
      };
      onProgress?.(this.indexingProgress);
      throw error;
    } finally {
      this.isIndexing = false;
    }
  }

  /**
   * Perform semantic search
   */
  async search(query: string, options: SemanticSearchOptions = {}): Promise<SemanticSearchResult[]> {
    if (!this.chromaDb.isReady()) {
      throw new Error('ChromaDB not initialized. Call initialize() first.');
    }

    console.log(`[SemanticSearchService] Searching for: "${query}"`);

    try {
      // Check if there are any documents indexed
      const stats = await this.chromaDb.getStats();
      if (stats.documentCount === 0) {
        console.log('[SemanticSearchService] No documents indexed for search');
        return [];
      }

      // Perform semantic search
      const results = await this.chromaDb.search(query, options);

      // Apply reranking if requested
      if (options.rerank && results.length > 1) {
        return await this.rerankResults(query, results);
      }

      return results;
    } catch (error) {
      console.error('[SemanticSearchService] Search error:', error);
      throw error;
    }
  }

  /**
   * Clear the entire search index
   */
  async clearIndex(): Promise<void> {
    if (!this.chromaDb.isReady()) {
      console.log('[SemanticSearchService] ChromaDB not ready, nothing to clear');
      return;
    }

    console.log('[SemanticSearchService] Clearing search index');
    
    try {
      await this.chromaDb.clearDocuments();
      console.log('[SemanticSearchService] Search index cleared successfully');
    } catch (error) {
      console.error('[SemanticSearchService] Error clearing index:', error);
      throw error;
    }
  }

  /**
   * Get indexing status and statistics
   */
  async getStatus(): Promise<{
    isReady: boolean;
    isIndexing: boolean;
    documentCount: number;
    lastUpdated: string;
    indexingProgress?: IndexingProgress;
  }> {
    const isReady = this.chromaDb.isReady();
    let documentCount = 0;
    let lastUpdated = new Date().toISOString();

    if (isReady) {
      try {
        const stats = await this.chromaDb.getStats();
        documentCount = stats.documentCount;
        lastUpdated = stats.lastUpdated;
      } catch (error) {
        console.error('[SemanticSearchService] Error getting stats:', error);
      }
    }

    return {
      isReady,
      isIndexing: this.isIndexing,
      documentCount,
      lastUpdated,
      indexingProgress: this.indexingProgress || undefined
    };
  }

  /**
   * Update index for specific files
   */
  async updateFiles(filePaths: string[], projectPath: string): Promise<void> {
    if (!this.chromaDb.isReady()) {
      await this.initialize();
    }

    console.log(`[SemanticSearchService] Updating index for ${filePaths.length} files`);

    try {
      // Get file contents
      const files = await Promise.all(
        filePaths.map(async (filePath) => {
          try {
            const result = await window.electronAPI?.fileExplorer?.readFile?.({ path: filePath }) as unknown as FileOperationResult | undefined;
            if (result?.success && result.content) {
              return {
                filePath,
                content: result.content,
                lastModified: new Date().toISOString(),
                type: this.docProcessor['determineFileType'](filePath)
              };
            }
          } catch (error) {
            console.error(`[SemanticSearchService] Error reading file ${filePath}:`, error);
          }
          return null;
        })
      );

      const validFiles = files.filter(f => f !== null);
      
      if (validFiles.length === 0) {
        console.log('[SemanticSearchService] No valid files to update');
        return;
      }

      // Process files into chunks
      const chunks = await this.docProcessor.processFiles(validFiles);

      // Update in ChromaDB (will delete old and add new)
      for (const filePath of filePaths) {
        const fileChunks = chunks.filter(chunk => chunk.filePath === filePath);
        if (fileChunks.length > 0) {
          await this.chromaDb.updateDocuments(filePath, fileChunks);
        }
      }

      console.log(`[SemanticSearchService] Updated ${filePaths.length} files in index`);
    } catch (error) {
      console.error('[SemanticSearchService] Error updating files:', error);
      throw error;
    }
  }

  /**
   * Remove files from index
   */
  async removeFiles(filePaths: string[]): Promise<void> {
    if (!this.chromaDb.isReady()) {
      return; // Nothing to remove if not initialized
    }

    console.log(`[SemanticSearchService] Removing ${filePaths.length} files from index`);

    try {
      for (const filePath of filePaths) {
        await this.chromaDb.deleteDocumentsByPath(filePath);
      }
      console.log(`[SemanticSearchService] Removed ${filePaths.length} files from index`);
    } catch (error) {
      console.error('[SemanticSearchService] Error removing files:', error);
      throw error;
    }
  }


  /**
   * Cleanup resources
   */
  async cleanup(): Promise<void> {
    await this.chromaDb.cleanup();
    this.isIndexing = false;
    this.indexingProgress = null;
  }

  /**
   * Create batches from array
   */
  private createBatches<T>(array: T[], batchSize: number): T[][] {
    const batches: T[][] = [];
    for (let i = 0; i < array.length; i += batchSize) {
      batches.push(array.slice(i, i + batchSize));
    }
    return batches;
  }

  /**
   * Apply AI-based reranking to search results
   */
  private async rerankResults(query: string, results: SemanticSearchResult[]): Promise<SemanticSearchResult[]> {
    try {
      // Use a simple scoring approach based on content relevance
      // In a more advanced implementation, this could use a reranking model
      const scoredResults = results.map(result => {
        let score = result.confidence;
        
        // Boost results that have query terms in the matched content
        const queryTerms = query.toLowerCase().split(/\s+/);
        const content = result.matchedContent.toLowerCase();
        
        const termMatches = queryTerms.filter(term => content.includes(term)).length;
        const termBoost = (termMatches / queryTerms.length) * 10;
        
        // Boost documentation over code for conceptual queries
        if (query.includes('how') || query.includes('what') || query.includes('why')) {
          if (result.documentType === 'documentation' || result.documentType === 'markdown') {
            score += 5;
          }
        }

        return {
          ...result,
          confidence: Math.min(100, score + termBoost)
        };
      });

      // Sort by new confidence scores
      return scoredResults.sort((a, b) => b.confidence - a.confidence);
    } catch (error) {
      console.error('[SemanticSearchService] Reranking error:', error);
      return results; // Return original results if reranking fails
    }
  }

  /**
   * Simple delay utility
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

export default SemanticSearchService;