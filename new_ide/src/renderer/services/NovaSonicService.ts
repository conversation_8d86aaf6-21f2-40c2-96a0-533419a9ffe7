/**
 * Nova Sonic Service for IDE Integration
 * 
 * Connects to the existing Nova Sonic WebSocket endpoint at /ws/nova-sonic
 * Following the same pattern as the admin interface
 */

import { io, Socket } from 'socket.io-client';
import { API_BASE_URL } from '../../config';

export interface NovaSonicCallbacks {
  onConnect?: () => void;
  onDisconnect?: () => void;
  onSessionInitialized?: (data: any) => void;
  onTextOutput?: (data: { text?: string; content?: string; type?: string } | string) => void;
  onAudioOutput?: (audioData: string) => void; // base64 encoded
  onContentStart?: () => void;
  onContentEnd?: () => void;
  onStreamComplete?: () => void;
  onError?: (error: any) => void;
  onSessionReady?: () => void;
  onToolUse?: (data: any) => void;
  onToolEnd?: (data: any) => void;
  onToolResult?: (data: any) => void;
}

export interface AudioMetrics {
  latency: number;
  packetsLost: number;
  audioLevel: number;
  noiseLevel: number;
}

export class NovaSonicService {
  private socket: Socket | null = null;
  private isConnected = false;
  private isSessionInitialized = false;
  private isStreaming = false;
  private callbacks: NovaSonicCallbacks = {};
  private audioRecorder: AudioRecorder | null = null;
  private audioPlayer: AudioPlayer | null = null;

  constructor() {
    // NovaSonicService initialized
  }

  /**
   * Connect to Nova Sonic WebSocket (same endpoint as admin interface)
   */
  async connect(callbacks: NovaSonicCallbacks = {}): Promise<void> {
    if (this.socket && this.isConnected) {
      console.log('Already connected to Nova Sonic');
      return;
    }

    this.callbacks = callbacks;
    console.log('🔌 Connecting to Nova Sonic WebSocket...');

    return new Promise((resolve, reject) => {
      // Connect through NodeJS backend proxy (not directly to port 3005)
      const baseUrl = API_BASE_URL;

      const socketOptions = {
        transports: ['polling'], // Start with polling like admin interface
        forceNew: true,
        reconnection: true,
        reconnectionDelay: 1000,
        reconnectionAttempts: 5,
        autoConnect: false,
        timeout: 20000,
        path: '/socket.io/',
        upgrade: false, // Disable upgrade to WebSocket initially
        auth: {
          // Include auth token for backend verification - use same token as API calls
          token: localStorage.getItem('token') || 'kapi_dev_ide_token_2024'
        }
      };

      this.socket = io(`${baseUrl}/ws/nova-sonic`, socketOptions);

      // Set up event listeners
      this.socket.on('connect', () => {
        console.log('✅ Connected to Nova Sonic WebSocket');
        this.isConnected = true;
        this.callbacks.onConnect?.();
      });

      this.socket.on('connected', (data) => {
        console.log('🎯 Nova Sonic connected event:', data);
        resolve();
      });

      this.socket.on('disconnect', (reason) => {
        console.log('❌ Disconnected from Nova Sonic:', reason);
        this.isConnected = false;
        this.isSessionInitialized = false;
        this.isStreaming = false;
        this.callbacks.onDisconnect?.();
      });

      this.socket.on('connect_error', (error) => {
        console.error('❌ Nova Sonic connection error:', error);
        this.isConnected = false;
        this.callbacks.onError?.(error);
        reject(error);
      });

      this.socket.on('sessionInitialized', (data) => {
        console.log('🚀 Nova Sonic session initialized:', data);
        this.isSessionInitialized = true;
        this.callbacks.onSessionInitialized?.(data);
      });

      this.socket.on('textOutput', (data) => {
        console.log('💬 Nova Sonic text output:', data);
        
        // Check if the text output contains an interruption signal
        if (data && typeof data === 'object') {
          // Handle different possible formats for interruption signals
          const text = data.text || data.content || data;
          
          // Check for JSON-formatted interruption signal
          if (typeof text === 'string') {
            try {
              const parsed = JSON.parse(text.trim());
              if (parsed.interrupted === true) {
                console.log('🔴 Interruption detected in text output, stopping audio playback');
                if (this.audioPlayer) {
                  this.audioPlayer.bargeIn();
                }
                return; // Don't forward interruption signals to the UI
              }
            } catch (e) {
              // Not JSON, continue normally
            }
          }
          
          // Check for direct interrupted property
          if (data.interrupted === true) {
            console.log('🔴 Direct interruption signal detected, stopping audio playback');
            if (this.audioPlayer) {
              this.audioPlayer.bargeIn();
            }
            return; // Don't forward interruption signals to the UI
          }
        }
        
        this.callbacks.onTextOutput?.(data);
      });

      this.socket.on('audioOutput', (data) => {
        // console.log('🔊 Nova Sonic audio output raw data:', { type: typeof data, data });
        
        // Handle different data formats from Nova Sonic
        let audioData: string | null = null;
        
        if (typeof data === 'string') {
          audioData = data;
        } else if (data && typeof data === 'object') {
          // Check for various possible field names
          audioData = data.content || data.audio || data.audioData || data.data;
        }
        
        if (!audioData) {
          console.log('🔊 Nova Sonic audio output: no valid audio data found');
          return;
        }
        
        // console.log('🔊 Nova Sonic audio output:', audioData.length, 'bytes');
        this.callbacks.onAudioOutput?.(audioData);
        
        // Play audio if player is available and data is valid
        if (this.audioPlayer && audioData) {
          this.audioPlayer.playAudio(audioData);
        }
      });

      this.socket.on('audioStarted', (data) => {
        console.log('🎙️ Audio streaming started:', data);
        this.isStreaming = true;
      });

      this.socket.on('contentStart', () => {
        // console.log('📝 Content start');
        this.callbacks.onContentStart?.();
      });

      this.socket.on('contentEnd', () => {
        // console.log('📝 Content end');
        this.callbacks.onContentEnd?.();
      });

      // Tool-related event handlers
      this.socket.on('toolUse', (data) => {
        console.log('🔧 [IDE] Tool use event received:', {
          toolName: data.toolName,
          toolUseId: data.toolUseId,
          content: data.toolUseContent ? 'Present' : 'Missing'
        });
        this.callbacks.onToolUse?.(data);
      });

      this.socket.on('toolEnd', (data) => {
        console.log('🔧 [IDE] Tool end event received:', {
          toolName: data.toolName,
          toolUseId: data.toolUseId,
          contentPresent: data.toolUseContent ? 'Yes' : 'No'
        });
        this.callbacks.onToolEnd?.(data);
      });

      this.socket.on('toolResult', (data) => {
        console.log('🔧 [IDE] Tool result event received:', {
          toolUseId: data.toolUseId,
          success: data.result?.success,
          resultType: typeof data.result,
          hasFilePath: data.result?.filePath ? 'Yes' : 'No',
          hasProjectPath: data.result?.projectPath ? 'Yes' : 'No'
        });
        this.callbacks.onToolResult?.(data);
      });

      this.socket.on('streamComplete', () => {
        // console.log('✅ Stream complete');
        this.isStreaming = false;
        this.callbacks.onStreamComplete?.();
      });

      this.socket.on('sessionReadyForNewStream', () => {
        // console.log('🔄 Session ready for new stream - interruption detected, stopping audio playback');
        // This event is sent when the server detects VAD interruption and resets the session
        // We need to immediately stop any audio playback to handle barge-in
        if (this.audioPlayer) {
          this.audioPlayer.bargeIn();
        }
        this.callbacks.onSessionReady?.();
      });

      // Also handle any direct stop signals that might indicate interruption
      this.socket.on('audioStopped', () => {
        // console.log('⏹️ Audio stopped - interruption detected, stopping audio playback');
        if (this.audioPlayer) {
          this.audioPlayer.bargeIn();
        }
        this.isStreaming = false;
      });

      this.socket.on('error', (error) => {
        console.error('❌ Nova Sonic error:', error);
        this.callbacks.onError?.(error);
      });

      // Start connection
      this.socket.connect();

      // Connection timeout
      setTimeout(() => {
        if (!this.isConnected) {
          reject(new Error('Nova Sonic connection timeout'));
        }
      }, 20000);
    });
  }

  /**
   * Initialize Nova Sonic session with system prompt
   */
  async initializeSession(systemPrompt?: string): Promise<void> {
    if (!this.socket || !this.isConnected) {
      throw new Error('Not connected to Nova Sonic');
    }

    if (this.isSessionInitialized) {
      console.log('Session already initialized');
      return;
    }

    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('Session initialization timeout'));
      }, 15000);

      this.socket!.once('sessionInitialized', () => {
        clearTimeout(timeout);
        
        // Send prompt setup (following admin pattern)
        this.socket!.emit('promptStart');
        if (systemPrompt) {
          this.socket!.emit('systemPrompt', systemPrompt);
        }
        
        resolve();
      });

      // Initialize session
      console.log('🎯 Initializing Nova Sonic session...');
      this.socket!.emit('initializeSession', {
        systemPrompt: systemPrompt || 'You are a helpful AI assistant for a code editor.'
      });
    });
  }

  /**
   * Start audio streaming
   */
  async startAudioStreaming(): Promise<void> {
    if (!this.socket || !this.isConnected || !this.isSessionInitialized) {
      throw new Error('Nova Sonic not ready for audio streaming');
    }

    if (this.isStreaming) {
      console.log('Audio streaming already active');
      return;
    }

    console.log('🎙️ Starting audio streaming...');
    
    // Initialize audio recorder if not already done
    if (!this.audioRecorder) {
      this.audioRecorder = new AudioRecorder();
      await this.audioRecorder.initialize();
    }

    // Initialize audio player if not already done
    if (!this.audioPlayer) {
      this.audioPlayer = new AudioPlayer();
      await this.audioPlayer.initialize();
    }

    // Start audio streaming
    this.socket.emit('audioStart');

    // Start capturing audio
    await this.audioRecorder.startRecording((audioData: string) => {
      if (this.socket && this.isStreaming) {
        this.socket.emit('audioInput', audioData);
      }
    });
  }

  /**
   * Stop audio streaming
   */
  stopAudioStreaming(): void {
    if (!this.isStreaming) {
      return;
    }

    console.log('⏹️ Stopping audio streaming...');
    
    if (this.audioRecorder) {
      this.audioRecorder.stopRecording();
    }

    if (this.socket) {
      this.socket.emit('stopAudio');
    }

    this.isStreaming = false;
  }

  /**
   * Send text message to Nova Sonic
   */
  sendTextMessage(message: string): void {
    if (!this.socket || !this.isConnected || !this.isSessionInitialized) {
      throw new Error('Nova Sonic not ready');
    }

    console.log('💬 Sending text message:', message);
    this.socket.emit('textInput', message); // Send as string like admin page
  }

  /**
   * Disconnect from Nova Sonic
   */
  disconnect(): void {
    if (this.isStreaming) {
      this.stopAudioStreaming();
    }

    if (this.audioRecorder) {
      this.audioRecorder.cleanup();
      this.audioRecorder = null;
    }

    if (this.audioPlayer) {
      this.audioPlayer.cleanup();
      this.audioPlayer = null;
    }

    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }

    this.isConnected = false;
    this.isSessionInitialized = false;
    this.isStreaming = false;

    console.log('👋 Disconnected from Nova Sonic');
  }

  /**
   * Get connection status
   */
  getStatus() {
    return {
      connected: this.isConnected,
      sessionInitialized: this.isSessionInitialized,
      streaming: this.isStreaming,
      hasAudioRecorder: !!this.audioRecorder,
      hasAudioPlayer: !!this.audioPlayer
    };
  }
}

/**
 * Audio Recorder Class (simplified version of admin interface)
 */
class AudioRecorder {
  private audioContext: AudioContext | null = null;
  private mediaStream: MediaStream | null = null;
  private workletNode: AudioWorkletNode | null = null;
  private isRecording = false;
  private onAudioData: ((data: string) => void) | null = null;

  async initialize(): Promise<void> {
    try {
      // Get microphone access - use 48kHz like admin page, then downsample
      this.mediaStream = await navigator.mediaDevices.getUserMedia({
        audio: {
          sampleRate: 48000,  // Admin uses 48kHz, then downsamples to 16kHz
          channelCount: 1,
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true
        }
      });

      // Create audio context with 48kHz like admin page
      this.audioContext = new AudioContext({ sampleRate: 48000 });
      
      // Load the same audio worklet as admin page
      await this.audioContext.audioWorklet.addModule('/js/audio-recorder.worklet.js');
      
      console.log('🎤 Audio recorder initialized with 48kHz sample rate + worklet');
    } catch (error) {
      console.error('❌ Failed to initialize audio recorder:', error);
      throw error;
    }
  }

  async startRecording(onAudioData: (data: string) => void): Promise<void> {
    if (!this.audioContext || !this.mediaStream) {
      throw new Error('Audio recorder not initialized');
    }

    if (this.isRecording) {
      console.warn('Recording already in progress');
      return;
    }

    this.onAudioData = onAudioData;
    this.isRecording = true;

    try {
      // Use the same AudioWorklet approach as admin page
      const source = this.audioContext.createMediaStreamSource(this.mediaStream);
      
      // Create AudioWorkletNode with the same processor as admin
      this.workletNode = new AudioWorkletNode(this.audioContext, 'audio-recorder-processor', {
        processorOptions: {
          targetSampleRate: 16000, // Match admin page options
        },
      });
      
      // Handle messages from the worklet
      this.workletNode.port.onmessage = (event) => {
        if (event.data.type === 'audio-data') {
          // Convert ArrayBuffer to base64 exactly like admin page
          const base64 = this.arrayBufferToBase64(event.data.audioData);
          
          // console.log('🎤 Worklet PCM audio data:', event.data.audioData.byteLength, 'bytes');
          this.onAudioData?.(base64);
        } else if (event.data.type === 'voice-detected') {
          // console.log('🎤 Voice detected, level:', event.data.audioLevel);
        } else if (event.data.type === 'silence-detected') {
          // console.log('🔇 Silence detected, level:', event.data.audioLevel);
        } else if (event.data.type === 'processing-error') {
          console.error('❌ Audio processing error:', event.data.error);
        }
      };
      
      // Connect the audio processing chain
      source.connect(this.workletNode);
      
      // Start recording via worklet message
      this.workletNode.port.postMessage({ type: 'start' });
      
      console.log('🎙️ Recording started with AudioWorklet (48kHz→16kHz PCM)');
      
    } catch (error) {
      console.error('❌ Failed to start audio recording:', error);
      this.isRecording = false;
      throw error;
    }
  }

  stopRecording(): void {
    this.isRecording = false;
    
    if (this.workletNode) {
      // Stop recording via worklet message
      this.workletNode.port.postMessage({ type: 'stop' });
      this.workletNode.disconnect();
      console.log('⏹️ AudioWorklet stopped and disconnected');
    }
    
    console.log('⏹️ Recording stopped');
  }

  // Convert ArrayBuffer to base64 (matching admin page implementation)
  private arrayBufferToBase64(buffer: ArrayBuffer): string {
    const binary = [];
    const bytes = new Uint8Array(buffer);
    for (let i = 0; i < bytes.byteLength; i++) {
      binary.push(String.fromCharCode(bytes[i]));
    }
    return btoa(binary.join(''));
  }

  cleanup(): void {
    if (this.isRecording) {
      this.stopRecording();
    }

    if (this.workletNode) {
      this.workletNode.disconnect();
      this.workletNode = null;
    }

    if (this.mediaStream) {
      this.mediaStream.getTracks().forEach(track => track.stop());
      this.mediaStream = null;
    }

    if (this.audioContext) {
      this.audioContext.close();
      this.audioContext = null;
    }

    this.isRecording = false;
    console.log('🧹 Audio recorder cleaned up');
  }
}

/**
 * Audio Player Class (full implementation matching admin interface)
 */
class AudioPlayer {
  private audioContext: AudioContext | null = null;
  private workletNode: AudioWorkletNode | null = null;
  private initialized = false;

  async initialize(): Promise<void> {
    try {
      this.audioContext = new AudioContext({
        sampleRate: 24000, // Match Nova Sonic output sample rate
      });

      // Load the audio worklet
      await this.audioContext.audioWorklet.addModule('/js/audio-player.worklet.js');
      
      // Create the worklet node
      this.workletNode = new AudioWorkletNode(this.audioContext, 'audio-player-processor');
      
      // Connect the worklet to the destination
      this.workletNode.connect(this.audioContext.destination);
      
      this.initialized = true;
      console.log('🔊 Audio player initialized with AudioWorklet');
    } catch (error) {
      console.error('❌ Failed to initialize audio player:', error);
      throw error;
    }
  }

  playAudio(base64Data: string): void {
    if (!this.initialized || !this.workletNode) {
      console.error('🔊 Audio player not initialized');
      return;
    }

    if (!base64Data || typeof base64Data !== 'string') {
      console.warn('🔊 Invalid audio data:', typeof base64Data);
      return;
    }

    try {
      // Convert base64 to Float32Array exactly like admin page
      const audioData = this.base64ToFloat32Array(base64Data);
      
      // console.log('🔊 Playing audio:', audioData.length, 'samples');
      
      this.workletNode.port.postMessage({
        type: 'audio',
        audioData: audioData
      });
    } catch (error) {
      console.error('❌ Failed to play audio:', error);
    }
  }

  bargeIn(): void {
    if (!this.initialized || !this.workletNode) {
      console.error('🔊 Audio player not initialized');
      return;
    }

    this.workletNode.port.postMessage({
      type: 'barge-in'
    });
  }

  // Convert base64 to Float32Array (matching admin page implementation)
  private base64ToFloat32Array(base64String: string): Float32Array {
    try {
      const binaryString = atob(base64String);
      const bytes = new Uint8Array(binaryString.length);
      for (let i = 0; i < binaryString.length; i++) {
        bytes[i] = binaryString.charCodeAt(i);
      }

      const int16Array = new Int16Array(bytes.buffer);
      const float32Array = new Float32Array(int16Array.length);
      for (let i = 0; i < int16Array.length; i++) {
        float32Array[i] = int16Array[i] / 32768.0;
      }

      return float32Array;
    } catch (error) {
      console.error('❌ Error in base64ToFloat32Array:', error);
      throw error;
    }
  }

  cleanup(): void {
    if (this.workletNode) {
      this.workletNode.disconnect();
      this.workletNode = null;
    }
    if (this.audioContext) {
      this.audioContext.close();
      this.audioContext = null;
    }
    this.initialized = false;
    console.log('🧹 Audio player cleaned up');
  }
}

// Export singleton instance
export const novaSonicService = new NovaSonicService();
export default novaSonicService;