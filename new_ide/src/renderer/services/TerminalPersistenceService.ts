interface TerminalSessionState {
  lastWorkingDirectory: string | null;
  lastModified: number;
}

class TerminalPersistenceService {
  private static readonly STORAGE_KEY = 'kapi_terminal_session_state';

  /**
   * Save terminal session state to localStorage
   */
  static saveSessionState(state: Partial<TerminalSessionState>): void {
    try {
      const currentState = this.loadSessionState() || {};
      const newState = {
        ...currentState,
        ...state,
        lastModified: Date.now()
      };
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(newState));
    } catch (error) {
      console.warn('Failed to save terminal session state:', error);
    }
  }

  /**
   * Load terminal session state from localStorage
   */
  static loadSessionState(): TerminalSessionState | null {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      if (!stored) return null;

      const state = JSON.parse(stored);
      
      // Check if state is too old (older than 7 days)
      const sevenDaysAgo = Date.now() - (7 * 24 * 60 * 60 * 1000);
      if (state.lastModified && state.lastModified < sevenDaysAgo) {
        this.clearSessionState();
        return null;
      }

      return state;
    } catch (error) {
      console.warn('Failed to load terminal session state:', error);
      return null;
    }
  }

  /**
   * Clear stored session state
   */
  static clearSessionState(): void {
    try {
      localStorage.removeItem(this.STORAGE_KEY);
    } catch (error) {
      console.warn('Failed to clear terminal session state:', error);
    }
  }

  /**
   * Save last working directory
   */
  static saveLastWorkingDirectory(directory: string): void {
    this.saveSessionState({
      lastWorkingDirectory: directory
    });
  }

  /**
   * Get last working directory
   */
  static getLastWorkingDirectory(): string | null {
    const state = this.loadSessionState();
    return state?.lastWorkingDirectory || null;
  }
}

export default TerminalPersistenceService;
export type { TerminalSessionState };