import React, { createContext, useContext, useState, useCallback, useRef, ReactNode } from 'react';
import { 
  conversationService, 
  Conversation, 
  Message, 
  TaskStrategy, 
  StreamOptions, 
  StreamChunk,
  ProjectContext,
  ConversationFilter,
  MultimodalMessageOptions
} from '../services/ConversationService';
import { useProject } from './ProjectContext';
import { logger } from '../utils/index';

export interface ConversationState {
  conversations: Conversation[];
  currentConversation: Conversation | null;
  messages: Message[];
  isLoading: boolean;
  isStreaming: boolean;
  error: string | null;
  totalConversations: number;
}

export interface ConversationContextProps {
  state: ConversationState;
  // Conversation management
  loadConversations: (filter?: ConversationFilter) => Promise<void>;
  createConversation: (title?: string, strategy?: TaskStrategy) => Promise<Conversation>;
  selectConversation: (conversationId: number) => Promise<void>;
  // Messaging
  sendMessage: (message: string, options?: Partial<StreamOptions>) => Promise<void>;
  sendMultimodalMessage: (options: MultimodalMessageOptions) => Promise<{
    success: boolean;
    message?: Message;
    error?: string;
  }>;
  // Real-time streaming
  streamingContent: string;
  streamingMetadata: any;
  // Utilities
  clearError: () => void;
  refreshCurrentConversation: () => Promise<void>;
  // Strategy management
  setCurrentStrategy: (strategy: TaskStrategy) => void;
  currentStrategy: TaskStrategy;
  // Context shortcuts
  currentConversationId: number | null;
}

const defaultState: ConversationState = {
  conversations: [],
  currentConversation: null,
  messages: [],
  isLoading: false,
  isStreaming: false,
  error: null,
  totalConversations: 0,
};

const ConversationContext = createContext<ConversationContextProps | undefined>(undefined);

export const useConversation = (): ConversationContextProps => {
  const context = useContext(ConversationContext);
  if (!context) {
    throw new Error('useConversation must be used within a ConversationProvider');
  }
  return context;
};

interface ConversationProviderProps {
  children: ReactNode;
}

export const ConversationProvider: React.FC<ConversationProviderProps> = ({ children }) => {
  const [state, setState] = useState<ConversationState>(defaultState);
  const [streamingContent, setStreamingContent] = useState<string>('');
  const [streamingMetadata, setStreamingMetadata] = useState<any>(null);
  const [currentStrategy, setCurrentStrategy] = useState<TaskStrategy>('chat');
  
  const { projectState } = useProject();
  const abortControllerRef = useRef<AbortController | null>(null);

  // Build project context from current project state
  const buildProjectContext = useCallback((): ProjectContext | undefined => {
    if (!projectState) return undefined;

    return {
      projectId: projectState.name,
      currentFile: projectState.lastOpenedFiles ? Object.keys(projectState.lastOpenedFiles)[0] : undefined,
      fileContent: projectState.lastOpenedFiles ? Object.values(projectState.lastOpenedFiles)[0] : undefined,
      projectStructure: projectState.recentFiles,
    };
  }, [projectState]);

  // Load conversations with filtering
  const loadConversations = useCallback(async (filter: ConversationFilter = {}) => {
    setState(prev => ({ ...prev, isLoading: true, error: null }));
    
    try {
      const result = await conversationService.getConversations(filter);
      setState(prev => ({
        ...prev,
        conversations: result.conversations,
        totalConversations: result.total,
        isLoading: false,
      }));
    } catch (error) {
      logger.error('Error loading conversations:', error);
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to load conversations',
        isLoading: false,
      }));
    }
  }, []);

  // Create a new conversation
  const createConversation = useCallback(async (
    title?: string,
    strategy: TaskStrategy = 'chat'
  ): Promise<Conversation> => {
    setState(prev => ({ ...prev, isLoading: true, error: null }));
    
    try {
      const projectContext = buildProjectContext();
      const conversation = await conversationService.createConversation({
        title,
        strategy,
        projectContext,
      });

      setState(prev => ({
        ...prev,
        conversations: [conversation, ...prev.conversations],
        currentConversation: conversation,
        messages: [],
        isLoading: false,
      }));

      setCurrentStrategy(strategy);
      return conversation;
    } catch (error) {
      logger.error('Error creating conversation:', error);
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to create conversation',
        isLoading: false,
      }));
      throw error;
    }
  }, [buildProjectContext]);

  // Select and load a conversation
  const selectConversation = useCallback(async (conversationId: number) => {
    setState(prev => ({ ...prev, isLoading: true, error: null }));
    
    try {
      const result = await conversationService.getConversation(conversationId);
      
      if (!result) {
        throw new Error('Conversation not found');
      }

      setState(prev => ({
        ...prev,
        currentConversation: result.conversation,
        messages: result.messages,
        isLoading: false,
      }));
    } catch (error) {
      logger.error('Error selecting conversation:', error);
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to load conversation',
        isLoading: false,
      }));
    }
  }, []);

  // Send a message with streaming response
  const sendMessage = useCallback(async (
    message: string,
    options: Partial<StreamOptions> = {}
  ) => {
    // Cancel any existing stream
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    setState(prev => ({ ...prev, isStreaming: true, error: null }));
    setStreamingContent('');
    setStreamingMetadata(null);

    try {
      let conversationId = state.currentConversation?.id;
      
      // Create conversation if none exists
      if (!conversationId) {
        const newConversation = await createConversation(
          message.substring(0, 50) + '...',
          currentStrategy
        );
        conversationId = newConversation.id;
        
        // Update state with new conversation immediately
        setState(prev => ({
          ...prev,
          currentConversation: newConversation,
          conversations: [newConversation, ...prev.conversations],
        }));
      }

      // Get strategy config
      const strategyConfig = conversationService.getStrategyConfig(currentStrategy);
      
      // Build stream options
      const streamOptions: StreamOptions = {
        modelId: options.modelId || strategyConfig.defaultModel,
        temperature: options.temperature || strategyConfig.temperature,
        maxTokens: options.maxTokens || strategyConfig.maxTokens,
        memoryCount: options.memoryCount || 10, // Default to including recent context
        strategy: currentStrategy,
        projectContext: options.projectContext || buildProjectContext(),
      };

      // Add user message to state immediately
      const userMessage: Message = {
        id: Date.now(), // Temporary ID
        role: 'user',
        content: message,
        createdAt: new Date().toISOString(),
      };

      setState(prev => ({
        ...prev,
        messages: [...prev.messages, userMessage],
      }));

      // Start streaming
      abortControllerRef.current = new AbortController();
      const stream = await conversationService.sendMessage(conversationId, message, streamOptions);

      let fullContent = '';
      let metadata: any = null;
      let actualConversationId = conversationId;

      for await (const chunk of stream) {
        if (abortControllerRef.current?.signal.aborted) {
          break;
        }

        switch (chunk.type) {
          case 'conversation_id':
            actualConversationId = chunk.conversation_id!;
            break;
            
          case 'content':
            if (chunk.content) {
              fullContent += chunk.content;
              setStreamingContent(fullContent);
            }
            break;
            
          case 'metadata':
            metadata = chunk.metadata;
            setStreamingMetadata(metadata);
            break;
            
          case 'done':
            // Add assistant message to state
            const assistantMessage: Message = {
              id: Date.now() + 1, // Temporary ID
              role: 'assistant',
              content: fullContent,
              createdAt: new Date().toISOString(),
              metrics: metadata,
            };

            setState(prev => ({
              ...prev,
              messages: [...prev.messages, assistantMessage],
              isStreaming: false,
              currentConversation: prev.currentConversation ? {
                ...prev.currentConversation,
                id: actualConversationId,
              } : null,
            }));

            setStreamingContent('');
            setStreamingMetadata(null);
            break;
            
          case 'error':
            throw new Error(chunk.message || 'Streaming error');
        }
      }
    } catch (error) {
      logger.error('Error sending message:', error);
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to send message',
        isStreaming: false,
      }));
      setStreamingContent('');
      setStreamingMetadata(null);
    }
  }, [state.currentConversation, currentStrategy, buildProjectContext, createConversation]);

  // Send multimodal message
  const sendMultimodalMessage = useCallback(async (options: MultimodalMessageOptions) => {
    setState(prev => ({ ...prev, isLoading: true, error: null }));
    
    try {
      const result = await conversationService.sendMultimodalMessage(options);
      
      if (result.success && result.message) {
        // Add the AI response to messages if we have a current conversation
        if (state.currentConversation && state.currentConversation.id === options.conversationId) {
          setState(prev => ({
            ...prev,
            messages: [...prev.messages, result.message!],
            isLoading: false,
          }));
        }
      }
      
      return result;
    } catch (error) {
      logger.error('Error sending multimodal message:', error);
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to send multimodal message',
        isLoading: false,
      }));
      
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }, [state.currentConversation]);

  // Refresh current conversation
  const refreshCurrentConversation = useCallback(async () => {
    if (state.currentConversation) {
      await selectConversation(state.currentConversation.id);
    }
  }, [state.currentConversation, selectConversation]);

  // Clear error
  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: null }));
  }, []);

  // Cleanup on unmount
  React.useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  const value: ConversationContextProps = {
    state,
    loadConversations,
    createConversation,
    selectConversation,
    sendMessage,
    sendMultimodalMessage,
    streamingContent,
    streamingMetadata,
    clearError,
    refreshCurrentConversation,
    setCurrentStrategy,
    currentStrategy,
    currentConversationId: state.currentConversation?.id || null,
  };

  return (
    <ConversationContext.Provider value={value}>
      {children}
    </ConversationContext.Provider>
  );
};

export default ConversationContext;