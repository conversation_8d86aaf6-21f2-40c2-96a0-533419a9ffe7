/**
 * Terminal Tab Context Provider
 * Manages state and actions for terminal tabs
 */

import React, { createContext, useContext, useState, useCallback, useRef, useEffect } from 'react';
import { v4 as uuidv4 } from 'uuid';
import TerminalPersistenceService from '../services/TerminalPersistenceService';
import {
  TerminalTab,
  TerminalTabState,
  TerminalTabActions,
  TerminalTabContextType,
  CreateTabOptions,
  TerminalTabHistory,
  DEFAULT_TAB_CONFIG,
  TerminalTabConfig,
  TabEvents
} from '../types/TerminalTypes';
import { TerminalRef } from '../features/terminal/Terminal';
import { useProject } from './ProjectContext';

// Create context
const TerminalTabContext = createContext<TerminalTabContextType | undefined>(undefined);

// Initial state
const createInitialState = (): TerminalTabState => ({
  tabs: [],
  activeTabId: '',
  nextTabNumber: 1,
  tabOrder: [],
  maxTabs: DEFAULT_TAB_CONFIG.maxTabs,
  recentlyClosedTabs: []
});

// Provider component
export const TerminalTabProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [state, setState] = useState<TerminalTabState>(createInitialState);
  const [config] = useState<TerminalTabConfig>(DEFAULT_TAB_CONFIG);
  const { projectState, isProjectOpen } = useProject();
  
  // Events
  const [events] = useState<TabEvents>({});
  
  // Ref to track mounted tabs for cleanup
  const mountedTabsRef = useRef<Set<string>>(new Set());

  // Utility functions
  const generateTabId = useCallback(() => uuidv4(), []);
  
  const generateTabTitle = useCallback((number: number, directory?: string) => {
    if (directory) {
      const dirName = directory.split('/').pop() || 'Terminal';
      return `${number}: ${dirName}`;
    }
    return `Terminal ${number}`;
  }, []);

  const getActiveTab = useCallback((): TerminalTab | null => {
    return state.tabs.find(tab => tab.id === state.activeTabId) || null;
  }, [state.tabs, state.activeTabId]);

  const getTabByPtyId = useCallback((ptyId: string): TerminalTab | null => {
    return state.tabs.find(tab => tab.ptyId === ptyId) || null;
  }, [state.tabs]);

  // Actions
  const createTab = useCallback((options?: CreateTabOptions): string => {
    const tabId = generateTabId();
    const now = new Date();
    
    // Determine working directory
    let workingDirectory = options?.workingDirectory;
    if (!workingDirectory && isProjectOpen && projectState.path) {
      workingDirectory = projectState.path;
    } else if (!workingDirectory) {
      // Try to get last working directory from persistence
      const lastWorkingDirectory = TerminalPersistenceService.getLastWorkingDirectory();
      if (lastWorkingDirectory && isProjectOpen && projectState.path && lastWorkingDirectory.startsWith(projectState.path)) {
        // Only use last directory if it's within the current project
        workingDirectory = lastWorkingDirectory;
      } else if (isProjectOpen && projectState.path) {
        // Fallback to project root
        workingDirectory = projectState.path;
      } else {
        // Final fallback to home directory
        workingDirectory = process.env.HOME || '/';
      }
    }

    // Generate title
    const title = options?.title || generateTabTitle(state.nextTabNumber, workingDirectory);

    const newTab: TerminalTab = {
      id: tabId,
      title,
      ptyId: null, // Will be set when terminal connects
      workingDirectory,
      isActive: false, // Will be activated after creation
      createdAt: now,
      lastActivity: now,
      hasRunningProcess: false,
      exitCode: null,
      ref: React.createRef<TerminalRef>(),
      customTitle: options?.title ? title : undefined
    };

    setState(prevState => {
      // Check if we've reached max tabs
      if (prevState.tabs.length >= prevState.maxTabs) {
        console.warn(`Maximum number of tabs (${prevState.maxTabs}) reached`);
        return prevState;
      }

      const updatedTabs = [...prevState.tabs, newTab];
      const updatedTabOrder = [...prevState.tabOrder, tabId];

      return {
        ...prevState,
        tabs: updatedTabs,
        tabOrder: updatedTabOrder,
        nextTabNumber: prevState.nextTabNumber + 1,
        activeTabId: prevState.activeTabId || tabId // Only activate if no active tab exists
      };
    });

    // Track mounted tab
    mountedTabsRef.current.add(tabId);

    // Save working directory for future terminals
    if (workingDirectory) {
      TerminalPersistenceService.saveLastWorkingDirectory(workingDirectory);
    }

    // Fire event
    events.onTabCreate?.(newTab);

    // console.log(`[TerminalTabs] Created tab: ${tabId} (${title}), total tabs: ${state.tabs.length + 1}`);
    return tabId;
  }, [generateTabId, generateTabTitle, state.nextTabNumber, isProjectOpen, projectState.path, events]);

  const closeTab = useCallback((tabId: string, force = false): boolean => {
    const tabToClose = state.tabs.find(tab => tab.id === tabId);
    if (!tabToClose) {
      console.warn(`[TerminalTabs] Tab not found: ${tabId}`);
      return false;
    }

    // Check for running processes
    if (!force && tabToClose.hasRunningProcess && config.confirmCloseRunningProcess) {
      const confirmed = window.confirm(
        `Tab "${tabToClose.title}" has a running process. Are you sure you want to close it?`
      );
      if (!confirmed) {
        return false;
      }
    }

    setState(prevState => {
      const updatedTabs = prevState.tabs.filter(tab => tab.id !== tabId);
      const updatedTabOrder = prevState.tabOrder.filter(id => id !== tabId);
      
      // Add to recently closed tabs
      const tabHistory: TerminalTabHistory = {
        id: tabToClose.id,
        title: tabToClose.title,
        workingDirectory: tabToClose.workingDirectory,
        commands: [], // TODO: Get command history from terminal
        environment: {}, // TODO: Get environment variables
        closedAt: new Date()
      };
      
      const updatedRecentlyClosedTabs = [
        tabHistory,
        ...prevState.recentlyClosedTabs.slice(0, 9) // Keep last 10
      ];

      // Handle active tab
      let newActiveTabId = prevState.activeTabId;
      if (prevState.activeTabId === tabId) {
        // Find next tab to activate
        const currentIndex = prevState.tabOrder.indexOf(tabId);
        if (updatedTabOrder.length > 0) {
          if (currentIndex > 0) {
            newActiveTabId = updatedTabOrder[currentIndex - 1];
          } else {
            newActiveTabId = updatedTabOrder[0];
          }
        } else {
          newActiveTabId = '';
        }
      }

      return {
        ...prevState,
        tabs: updatedTabs,
        tabOrder: updatedTabOrder,
        activeTabId: newActiveTabId,
        recentlyClosedTabs: updatedRecentlyClosedTabs
      };
    });

    // Cleanup PTY process if exists using centralized kill function
    if (tabToClose.ptyId) {
      const globalKillRegistry = window.__pty_kill_registry = window.__pty_kill_registry || new Set();
      
      if (globalKillRegistry.has(tabToClose.ptyId)) {
        console.log(`[TerminalTabs] PTY ${tabToClose.ptyId} already scheduled for killing - skipping duplicate from closeTab`);
      } else {
        globalKillRegistry.add(tabToClose.ptyId);
        console.log(`[TerminalTabs] Killing PTY ${tabToClose.ptyId} from source: closeTab`);
        
        try {
          if (window.electronAPI && window.electronAPI.terminal) {
            window.electronAPI.terminal.kill(tabToClose.ptyId!);
          } else {
            console.warn('[TerminalTabs] Electron API not available - cannot kill PTY');
          }
          setTimeout(() => globalKillRegistry.delete(tabToClose.ptyId!), 2000);
        } catch (error) {
          console.error(`[TerminalTabs] Error killing PTY ${tabToClose.ptyId} from closeTab:`, error);
          globalKillRegistry.delete(tabToClose.ptyId!);
        }
      }
    }

    // Remove from mounted tabs
    mountedTabsRef.current.delete(tabId);

    // Fire event
    events.onTabClose?.(tabToClose);

    console.log(`[TerminalTabs] Closed tab: ${tabId} (${tabToClose.title}), remaining tabs: ${state.tabs.length - 1}`);
    return true;
  }, [state.tabs, config.confirmCloseRunningProcess, events]);

  const switchToTab = useCallback((tabId: string): void => {
    const targetTab = state.tabs.find(tab => tab.id === tabId);
    if (!targetTab) {
      console.warn(`[TerminalTabs] Tab not found: ${tabId}`);
      return;
    }

    const currentTab = getActiveTab();

    setState(prevState => ({
      ...prevState,
      activeTabId: tabId
    }));

    // Fire event
    events.onTabSwitch?.(currentTab, targetTab);

    console.log(`[TerminalTabs] Switched to tab: ${tabId} (${targetTab.title})`);
  }, [state.tabs, getActiveTab, events]);

  const renameTab = useCallback((tabId: string, newTitle: string): void => {
    const tab = state.tabs.find(tab => tab.id === tabId);
    if (!tab) {
      console.warn(`[TerminalTabs] Tab not found: ${tabId}`);
      return;
    }

    const oldTitle = tab.title;

    setState(prevState => ({
      ...prevState,
      tabs: prevState.tabs.map(tab =>
        tab.id === tabId
          ? { ...tab, title: newTitle, customTitle: newTitle }
          : tab
      )
    }));

    // Fire event
    events.onTabRename?.(tab, oldTitle, newTitle);

    console.log(`[TerminalTabs] Renamed tab: ${tabId} from "${oldTitle}" to "${newTitle}"`);
  }, [state.tabs, events]);

  const updateTabState = useCallback((tabId: string, updates: Partial<TerminalTab>): void => {
    setState(prevState => ({
      ...prevState,
      tabs: prevState.tabs.map(tab =>
        tab.id === tabId
          ? { ...tab, ...updates, lastActivity: new Date() }
          : tab
      )
    }));
  }, []);

  const reorderTabs = useCallback((newOrder: string[]): void => {
    setState(prevState => ({
      ...prevState,
      tabOrder: newOrder
    }));

    // Fire event
    events.onTabReorder?.(newOrder);

    console.log(`[TerminalTabs] Reordered tabs:`, newOrder);
  }, [events]);

  const duplicateTab = useCallback((tabId: string): string => {
    const sourceTab = state.tabs.find(tab => tab.id === tabId);
    if (!sourceTab) {
      console.warn(`[TerminalTabs] Tab not found: ${tabId}`);
      return '';
    }

    return createTab({
      title: `${sourceTab.title} (Copy)`,
      workingDirectory: sourceTab.workingDirectory
    });
  }, [state.tabs, createTab]);

  const closeOtherTabs = useCallback((exceptTabId: string): void => {
    const tabsToClose = state.tabs.filter(tab => tab.id !== exceptTabId);
    tabsToClose.forEach(tab => closeTab(tab.id, true));
  }, [state.tabs, closeTab]);

  const closeTabsToRight = useCallback((fromTabId: string): void => {
    const fromIndex = state.tabOrder.indexOf(fromTabId);
    if (fromIndex === -1) return;

    const tabsToClose = state.tabOrder.slice(fromIndex + 1);
    tabsToClose.forEach(tabId => closeTab(tabId, true));
  }, [state.tabOrder, closeTab]);

  const reopenLastClosedTab = useCallback((): string | null => {
    if (state.recentlyClosedTabs.length === 0) return null;

    const lastClosed = state.recentlyClosedTabs[0];
    const newTabId = createTab({
      title: lastClosed.title,
      workingDirectory: lastClosed.workingDirectory,
      commands: lastClosed.commands
    });

    // Remove from recently closed
    setState(prevState => ({
      ...prevState,
      recentlyClosedTabs: prevState.recentlyClosedTabs.slice(1)
    }));

    return newTabId;
  }, [state.recentlyClosedTabs]); // createTab is stable enough

  // Create initial tab when no tabs exist
  const hasInitialized = useRef(false);
  useEffect(() => {
    // console.log('[TerminalTabContext] Effect: tabs.length =', state.tabs.length, 'hasInitialized =', hasInitialized.current);
    if (state.tabs.length === 0 && !hasInitialized.current) {
      // console.log('[TerminalTabContext] Creating initial tab');
      hasInitialized.current = true;
      createTab();
    }
  }, [state.tabs.length]); // Remove createTab from dependencies

  // Cleanup on unmount only - use ref to capture current tabs at unmount time
  const tabsRef = useRef(state.tabs);
  tabsRef.current = state.tabs;
  
  useEffect(() => {
    return () => {
      // Kill all PTY processes - use ref to get current tabs at unmount time
      const currentTabs = tabsRef.current;
      console.log(`[TerminalTabs] Context unmounting, cleaning up ${currentTabs.length} tabs`);
      const globalKillRegistry = window.__pty_kill_registry = window.__pty_kill_registry || new Set();
      
      currentTabs.forEach(tab => {
        if (tab.ptyId) {
          if (globalKillRegistry.has(tab.ptyId)) {
            console.log(`[TerminalTabs] PTY ${tab.ptyId} already scheduled for killing - skipping duplicate from contextUnmount`);
          } else {
            globalKillRegistry.add(tab.ptyId);
            console.log(`[TerminalTabs] Killing PTY ${tab.ptyId} from source: contextUnmount`);
            
            try {
              if (window.electronAPI && window.electronAPI.terminal) {
                window.electronAPI.terminal.kill(tab.ptyId!);
              } else {
                console.warn('[TerminalTabs] Electron API not available - cannot kill PTY');
              }
              setTimeout(() => globalKillRegistry.delete(tab.ptyId!), 2000);
            } catch (error) {
              console.error(`[TerminalTabs] Error killing PTY ${tab.ptyId} from contextUnmount:`, error);
              globalKillRegistry.delete(tab.ptyId!);
            }
          }
        }
      });
    };
  }, []); // Empty dependency array - only run cleanup on actual unmount

  // Actions object
  const actions: TerminalTabActions = {
    createTab,
    closeTab,
    renameTab,
    switchToTab,
    reorderTabs,
    updateTabState,
    duplicateTab,
    closeOtherTabs,
    closeTabsToRight,
    reopenLastClosedTab
  };

  const contextValue: TerminalTabContextType = {
    state,
    actions,
    config,
    events
  };

  return (
    <TerminalTabContext.Provider value={contextValue}>
      {children}
    </TerminalTabContext.Provider>
  );
};

// Hook to use terminal tab context
export const useTerminalTabs = (): TerminalTabContextType => {
  const context = useContext(TerminalTabContext);
  if (!context) {
    throw new Error('useTerminalTabs must be used within a TerminalTabProvider');
  }
  return context;
};

export default TerminalTabContext;