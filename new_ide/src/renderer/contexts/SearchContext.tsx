import React, { createContext, useContext, useState, useCallback, useEffect, useRef } from 'react';
import { keyboardManager, createShiftShortcut } from '../services/KeyboardManager';
import { useProject } from './ProjectContext';

interface SearchContextType {
  isSearchBarVisible: boolean;
  openSearchBar: () => void;
  closeSearchBar: () => void;
  toggleSearchBar: () => void;
}

const SearchContext = createContext<SearchContextType | undefined>(undefined);

export const useSearchContext = () => {
  const context = useContext(SearchContext);
  if (!context) {
    throw new Error('useSearchContext must be used within a SearchProvider');
  }
  return context;
};

interface SearchProviderProps {
  children: React.ReactNode;
}

export const SearchProvider: React.FC<SearchProviderProps> = ({ children }) => {
  const [isSearchBarVisible, setIsSearchBarVisible] = useState(true);
  const { projectState } = useProject();


  const openSearchBar = useCallback(() => {
    console.log('[SearchContext] Opening search bar');
    setIsSearchBarVisible(true);
  }, []);

  const closeSearchBar = useCallback(() => {
    console.log('[SearchContext] Closing search bar');
    setIsSearchBarVisible(false);
  }, []);

  const toggleSearchBar = useCallback(() => {
    if (isSearchBarVisible) {
      closeSearchBar();
    } else {
      openSearchBar();
    }
  }, [isSearchBarVisible, openSearchBar, closeSearchBar]);

  // Register keyboard shortcuts
  useEffect(() => {

    // Ctrl+P for search bar (quick search)
    const searchBarShortcut = {
      key: 'p',
      ctrlKey: true,
      metaKey: false,
      action: () => {
        if (projectState.path) {
          toggleSearchBar();
        } else {
          console.warn('[SearchContext] No project path available for search');
        }
      },
      description: 'Quick Search Bar'
    };

    keyboardManager.registerShortcut(searchBarShortcut);

    // Cleanup on unmount
    return () => {
      keyboardManager.unregisterShortcut('p', false, true, false, false);
    };
  }, [toggleSearchBar, projectState.path]);

  // Keep track of previous project path to detect actual changes
  const prevProjectPathRef = useRef<string | null>(projectState.path);
  
  // Close bar when project changes from having a path to not having one
  useEffect(() => {
    const prevPath = prevProjectPathRef.current;
    const currentPath = projectState.path;
    
    // Only close search bar if we HAD a project and now we don't
    if (isSearchBarVisible && prevPath && !currentPath) {
      closeSearchBar();
    }
    
    // Update the ref for next comparison
    prevProjectPathRef.current = currentPath;
  }, [projectState.path, isSearchBarVisible, closeSearchBar]);

  const value: SearchContextType = {
    isSearchBarVisible,
    openSearchBar,
    closeSearchBar,
    toggleSearchBar
  };

  return (
    <SearchContext.Provider value={value}>
      {children}
    </SearchContext.Provider>
  );
};

export default SearchContext;