import React, { useState, useEffect } from 'react';
import { Excalidraw, exportToBlob } from '@excalidraw/excalidraw';
// @ts-ignore - Excalidraw types have complex import paths
import type { ExcalidrawElement, AppState, ExcalidrawImperativeAPI, BinaryFiles } from '@excalidraw/excalidraw/types/types';
// import '../styles/sketch-canvas.css'; // Temporarily disabled to test CSS interference

interface SketchCanvasProps {
  onSketchChange?: (elements: ExcalidrawElement[]) => void;
  onExportImage?: (blob: Blob) => void;
}

const SketchCanvas: React.FC<SketchCanvasProps> = ({ onSketchChange, onExportImage }) => {
  console.log('SketchCanvas: Component rendering...');
  
  const [excalidrawAPI, setExcalidrawAPI] = useState<ExcalidrawImperativeAPI | null>(null);
  const [initialData, setInitialData] = useState<any>(null);
  const [isLoadingTemplate, setIsLoadingTemplate] = useState(false);

  // Initialize Excalidraw with basic setup
  useEffect(() => {
    console.log('SketchCanvas: Initializing Excalidraw...');
    
    const data = {
      elements: [],
      appState: {
        viewBackgroundColor: "#ffffff", // White background for testing
      },
    };
    
    console.log('SketchCanvas: Setting initial data:', data);
    setInitialData(data);
  }, []);


  const handleChange = (elements: readonly ExcalidrawElement[], appState: AppState, _files: BinaryFiles) => {
    console.log('SketchCanvas: onChange triggered', {
      elementCount: elements.length,
      elements: elements.map((el: any) => ({ type: el.type, id: el.id, x: el.x, y: el.y })),
      activeTool: appState.activeTool,
      currentItemStrokeColor: appState.currentItemStrokeColor,
      viewBackgroundColor: appState.viewBackgroundColor
    });
    onSketchChange?.(Array.from(elements));
  };

  const handleExportSketch = async () => {
    if (!excalidrawAPI) {
      console.error('Excalidraw API not available');
      return;
    }

    try {
      const elements = excalidrawAPI.getSceneElements();
      const appState = excalidrawAPI.getAppState();
      
      const blob = await exportToBlob({
        elements,
        appState,
        mimeType: "image/png",
        quality: 1,
      });
      
      onExportImage?.(blob);
      
      // Also log for debugging
      console.log('Sketch exported:', {
        elementCount: elements.length,
        blobSize: blob.size
      });
    } catch (error) {
      console.error('Error exporting sketch:', error);
    }
  };

  const handleAnalyzeWithAI = async () => {
    if (!excalidrawAPI) return;
    
    try {
      const elements = excalidrawAPI.getSceneElements();
      console.log('Analyzing sketch with AI...', {
        elementCount: elements.length,
        elements: elements.map((el: ExcalidrawElement) => ({ type: el.type, id: el.id }))
      });
      
      // Export and send to voice agent for analysis
      await handleExportSketch();
      
      // TODO: Send to Nova Sonic for multimodal analysis
      alert('Sketch analysis coming soon! This will send your drawing to Nova Sonic for interpretation.');
    } catch (error) {
      console.error('Error analyzing sketch:', error);
    }
  };

  const handleClearCanvas = () => {
    if (!excalidrawAPI) return;
    
    excalidrawAPI.updateScene({
      elements: [],
    });
  };

  const handleLoadDashboardTemplate = async () => {
    if (!excalidrawAPI) return;
    
    setIsLoadingTemplate(true);
    
    try {
      // Fetch the SVG content and convert to data URL
      const response = await fetch('/assets/dashboard.svg');
      if (!response.ok) {
        throw new Error(`Failed to fetch SVG: ${response.status}`);
      }
      
      const svgText = await response.text();
      const svgBlob = new Blob([svgText], { type: 'image/svg+xml' });
      const dataURL = await new Promise<string>((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => resolve(reader.result as string);
        reader.onerror = reject;
        reader.readAsDataURL(svgBlob);
      });

      // Create the image element for Excalidraw
      const imageElement: ExcalidrawElement = {
        id: 'dashboard-background',
        type: 'image',
        x: 50,
        y: 50,
        width: 800,  // Scaled down to fit better
        height: 533, // Maintain aspect ratio (800/1200 * 800)
        angle: 0,
        strokeColor: 'transparent',
        backgroundColor: 'transparent',
        fillStyle: 'solid',
        strokeWidth: 0,
        strokeStyle: 'solid',
        roughness: 0,
        opacity: 70, // More transparent so you can draw on top easily
        groupIds: [],
        frameId: null,
        roundness: null,
        seed: Math.floor(Math.random() * 1000000),
        versionNonce: Math.floor(Math.random() * 1000000),
        isDeleted: false,
        boundElements: null,
        updated: 1,
        link: null,
        locked: false,
        fileId: 'dashboard-svg',
        scale: [1, 1]
      } as ExcalidrawElement;

      // Create a File object for Excalidraw
      const file = {
        id: 'dashboard-svg',
        dataURL: dataURL,
        mimeType: 'image/svg+xml' as const,
        created: Date.now()
      };

      // Clear canvas and add the dashboard background
      excalidrawAPI.updateScene({
        elements: [imageElement],
      });

      // Add the file to Excalidraw's file storage
      excalidrawAPI.addFiles([file]);
      
      console.log('Dashboard SVG template loaded successfully!');
      
    } catch (error) {
      console.error('Error loading dashboard template:', error);
      // Fallback to basic shapes
      handleLoadBasicTemplate();
    } finally {
      setIsLoadingTemplate(false);
    }
  };

  const handleLoadBasicTemplate = () => {
    if (!excalidrawAPI) return;
    
    // Create basic shapes to represent the dashboard structure
    const dashboardElements: ExcalidrawElement[] = [
      // Header
      {
        id: 'header-rect',
        type: 'rectangle',
        x: 50,
        y: 50,
        width: 800,
        height: 60,
        angle: 0,
        strokeColor: '#ffffff',
        backgroundColor: '#242424',
        fillStyle: 'solid',
        strokeWidth: 2,
        strokeStyle: 'solid',
        roughness: 0,
        opacity: 100,
        groupIds: [],
        frameId: null,
        roundness: { type: 1 },
        seed: Math.floor(Math.random() * 1000000),
        versionNonce: Math.floor(Math.random() * 1000000),
        isDeleted: false,
        boundElements: null,
        updated: 1,
        link: null,
        locked: false
      } as ExcalidrawElement,
      
      // Header text
      {
        id: 'header-text',
        type: 'text',
        x: 70,
        y: 70,
        width: 300,
        height: 25,
        angle: 0,
        strokeColor: '#ffffff',
        backgroundColor: 'transparent',
        fillStyle: 'solid',
        strokeWidth: 2,
        strokeStyle: 'solid',
        roughness: 0,
        opacity: 100,
        groupIds: [],
        frameId: null,
        roundness: null,
        seed: Math.floor(Math.random() * 1000000),
        versionNonce: Math.floor(Math.random() * 1000000),
        isDeleted: false,
        boundElements: null,
        updated: 1,
        link: null,
        locked: false,
        text: 'Kapi Project Dashboard',
        fontSize: 20,
        fontFamily: 1,
        textAlign: 'left',
        verticalAlign: 'top',
        baseline: 18
      } as ExcalidrawElement,
      
      // Card 1 - Documentation
      {
        id: 'card1',
        type: 'rectangle',
        x: 80,
        y: 140,
        width: 180,
        height: 120,
        angle: 0,
        strokeColor: '#5b57c9',
        backgroundColor: '#5b57c9',
        fillStyle: 'solid',
        strokeWidth: 2,
        strokeStyle: 'solid',
        roughness: 0,
        opacity: 100,
        groupIds: [],
        frameId: null,
        roundness: { type: 1 },
        seed: Math.floor(Math.random() * 1000000),
        versionNonce: Math.floor(Math.random() * 1000000),
        isDeleted: false,
        boundElements: null,
        updated: 1,
        link: null,
        locked: false
      } as ExcalidrawElement,
      
      // Card 2 - Code Quality
      {
        id: 'card2',
        type: 'rectangle',
        x: 280,
        y: 140,
        width: 180,
        height: 120,
        angle: 0,
        strokeColor: '#4a8cd8',
        backgroundColor: '#4a8cd8',
        fillStyle: 'solid',
        strokeWidth: 2,
        strokeStyle: 'solid',
        roughness: 0,
        opacity: 100,
        groupIds: [],
        frameId: null,
        roundness: { type: 1 },
        seed: Math.floor(Math.random() * 1000000),
        versionNonce: Math.floor(Math.random() * 1000000),
        isDeleted: false,
        boundElements: null,
        updated: 1,
        link: null,
        locked: false
      } as ExcalidrawElement,
      
      // Card 3 - Test Coverage
      {
        id: 'card3',
        type: 'rectangle',
        x: 480,
        y: 140,
        width: 180,
        height: 120,
        angle: 0,
        strokeColor: '#e6b035',
        backgroundColor: '#e6b035',
        fillStyle: 'solid',
        strokeWidth: 2,
        strokeStyle: 'solid',
        roughness: 0,
        opacity: 100,
        groupIds: [],
        frameId: null,
        roundness: { type: 1 },
        seed: Math.floor(Math.random() * 1000000),
        versionNonce: Math.floor(Math.random() * 1000000),
        isDeleted: false,
        boundElements: null,
        updated: 1,
        link: null,
        locked: false
      } as ExcalidrawElement,
    ];
    
    excalidrawAPI.updateScene({
      elements: dashboardElements,
    });
  };

  if (!initialData) {
    console.log('SketchCanvas: Showing loading screen, initialData is:', initialData);
    return (
      <div className="sketch-canvas-loading">
        <div className="sketch-loading-spinner">🎨</div>
        <p>Loading sketch canvas...</p>
        <p style={{ fontSize: '0.75rem', color: '#666' }}>
          Debug: initialData is {initialData ? 'set' : 'null'}
        </p>
      </div>
    );
  }

  return (
    <div className="sketch-canvas-container sketch-mode-active">
      <div className="sketch-canvas-toolbar">
        <button 
          className="sketch-btn sketch-btn-primary" 
          onClick={handleLoadDashboardTemplate}
          disabled={!excalidrawAPI || isLoadingTemplate}
        >
          {isLoadingTemplate ? '⏳ Loading...' : '📋 Load Dashboard Template'}
        </button>
        <button 
          className="sketch-btn sketch-btn-secondary" 
          onClick={handleExportSketch}
          disabled={!excalidrawAPI}
        >
          📤 Export Sketch
        </button>
        <button 
          className="sketch-btn sketch-btn-secondary" 
          onClick={handleAnalyzeWithAI}
          disabled={!excalidrawAPI}
        >
          🤖 Analyze with AI
        </button>
        <button 
          className="sketch-btn sketch-btn-danger" 
          onClick={handleClearCanvas}
          disabled={!excalidrawAPI}
        >
          🗑️ Clear
        </button>
      </div>
      
      <div style={{ 
        height: '600px', 
        width: '100%', 
        border: '1px solid #ccc',
        position: 'relative',
        backgroundColor: '#ffffff',
        overflow: 'hidden'
      }}>
        <Excalidraw
          onChange={handleChange}
          initialData={{
            elements: [],
            appState: {
              viewBackgroundColor: "#ffffff",
              currentItemStrokeColor: "#000000",
              currentItemBackgroundColor: "transparent",
              currentItemFillStyle: "solid"
            }
          }}
          excalidrawAPI={(api) => {
            console.log('SketchCanvas: Excalidraw API initialized', !!api);
            setExcalidrawAPI(api);
            
            // Ensure black stroke color for visibility on white background
            if (api) {
              setTimeout(() => {
                console.log('SketchCanvas: Setting stroke color to black');
                api.updateScene({
                  appState: {
                    currentItemStrokeColor: "#000000",
                    currentItemStrokeWidth: 2,
                    currentItemBackgroundColor: "transparent"
                  }
                });
              }, 100);
            }
          }}
          theme="light"
        />
      </div>
      
      <div className="sketch-canvas-instructions">
        <p>💡 <strong>Tip:</strong> Load the dashboard template, then draw your modifications on top!</p>
        <p>🎯 Circle areas you want to change, add arrows, write notes, or sketch new UI elements.</p>
      </div>
    </div>
  );
};

export default SketchCanvas;