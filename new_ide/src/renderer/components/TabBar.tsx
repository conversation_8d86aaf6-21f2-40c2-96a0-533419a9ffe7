import React, { useState, useRef, useEffect } from 'react';
import styled from 'styled-components';
import { fontFamilies } from '../utils/fontConfig';
import { getFileIconByType } from '../utils/vsCodeIcons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { 
  faTimes, 
  faTimesCircle, 
  faCopy, 
  faLink,
  faArrowLeft,
  faArrowRight
} from '@fortawesome/free-solid-svg-icons';

interface TabItem {
  id: string;
  filePath: string;
  fileName: string;
  isModified: boolean;
  isActive: boolean;
}

interface TabBarProps {
  tabs: TabItem[];
  onTabClick: (tabId: string) => void;
  onTabClose: (tabId: string) => void;
  onTabMove?: (fromIndex: number, toIndex: number) => void;
  onCloseOthers?: (tabId: string) => void;
  onCloseAll?: () => void;
  onCloseToRight?: (tabId: string) => void;
  onCopyPath?: (filePath: string) => void;
  className?: string;
}

interface ContextMenuState {
  isVisible: boolean;
  x: number;
  y: number;
  tabId: string;
}

const TabBarContainer = styled.div`
  display: flex;
  align-items: stretch;
  background-color: #2d2d30;
  border-bottom: 1px solid #3e3e42;
  height: 35px;
  min-height: 35px;
  overflow: hidden;
  position: relative;
  font-family: ${fontFamilies.ui};
  user-select: none;
  
  /* Scrollable tabs */
  overflow-x: auto;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE/Edge */
  
  &::-webkit-scrollbar {
    display: none; /* Chrome/Safari */
  }
`;

const TabsWrapper = styled.div`
  display: flex;
  align-items: stretch;
  min-width: 100%;
  height: 100%;
`;

const Tab = styled.div<{ $isActive: boolean; $isModified: boolean }>`
  display: flex;
  align-items: center;
  background-color: ${props => props.$isActive ? '#1e1e1e' : 'transparent'};
  color: ${props => props.$isActive ? '#ffffff' : '#cccccc'};
  border-right: 1px solid #3e3e42;
  padding: 0 12px;
  min-width: 120px;
  max-width: 240px;
  height: 100%;
  cursor: pointer;
  position: relative;
  transition: background-color 0.1s ease;
  white-space: nowrap;
  overflow: hidden;
  
  &:hover {
    background-color: ${props => props.$isActive ? '#1e1e1e' : '#2a2d2e'};
  }
  
  &:first-child {
    border-left: none;
  }
  
  /* Active tab indicator */
  ${props => props.$isActive && `
    border-top: 2px solid #007acc;
    background-color: #1e1e1e;
  `}
  
  /* Modified indicator */
  ${props => props.$isModified && !props.$isActive && `
    &::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 8px;
      transform: translateY(-50%);
      width: 6px;
      height: 6px;
      border-radius: 50%;
      background-color: #ffffff;
    }
  `}
`;

const TabContent = styled.div<{ $isModified: boolean }>`
  display: flex;
  align-items: center;
  width: 100%;
  overflow: hidden;
  padding-left: ${props => props.$isModified ? '12px' : '0'};
`;

const TabIcon = styled.div`
  width: 16px;
  height: 16px;
  margin-right: 6px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #cccccc; /* Default icon color */
  
  svg {
    width: 16px;
    height: 16px;
  }
`;

const TabLabel = styled.div`
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 13px;
  line-height: 1;
`;

const TabCloseButton = styled.div<{ $isModified: boolean }>`
  width: 16px;
  height: 16px;
  margin-left: 6px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  opacity: 0.6;
  border-radius: 2px;
  transition: all 0.1s ease;
  
  &:hover {
    opacity: 1;
    background-color: rgba(255, 255, 255, 0.1);
  }
  
  /* Show modified indicator instead of close button when not hovered */
  ${props => props.$isModified && `
    &:not(:hover) {
      &::before {
        content: '●';
        font-size: 12px;
        color: #ffffff;
      }
      
      &::after {
        display: none;
      }
    }
  `}
  
  &::after {
    content: '×';
    font-weight: normal;
    line-height: 1;
  }
`;

const TabContextMenu = styled.div<{ $x: number; $y: number; $visible: boolean }>`
  position: fixed;
  top: ${props => props.$y}px;
  left: ${props => props.$x}px;
  background-color: #252526;
  border: 1px solid #3e3e42;
  border-radius: 4px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  min-width: 180px;
  z-index: 10000;
  font-family: ${fontFamilies.ui};
  font-size: 13px;
  display: ${props => props.$visible ? 'block' : 'none'};
  user-select: none;
`;

const TabContextMenuItem = styled.div<{ $disabled?: boolean; $danger?: boolean }>`
  display: flex;
  align-items: center;
  padding: 8px 12px;
  color: ${props => props.$disabled ? '#656565' : props.$danger ? '#f48771' : '#cccccc'};
  cursor: ${props => props.$disabled ? 'default' : 'pointer'};
  transition: background-color 0.1s ease;
  
  &:hover {
    background-color: ${props => props.$disabled ? 'transparent' : props.$danger ? 'rgba(244, 135, 113, 0.1)' : '#2a2d2e'};
  }
  
  &:first-child {
    border-radius: 4px 4px 0 0;
  }
  
  &:last-child {
    border-radius: 0 0 4px 4px;
  }
`;

const TabContextMenuIcon = styled.span`
  width: 16px;
  height: 16px;
  margin-right: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  
  svg {
    width: 12px;
    height: 12px;
  }
`;

const TabContextMenuSeparator = styled.div`
  height: 1px;
  background-color: #3e3e42;
  margin: 4px 0;
`;

/**
 * Get file icon component based on file extension (consistent with FileExplorer)
 */
const getFileIconComponent = (fileName: string): React.ReactNode => {
  const extension = fileName.split('.').pop()?.toLowerCase() || '';
  return getFileIconByType(extension);
};

/**
 * TabBar Component - VS Code style file tabs
 * 
 * Features:
 * - File icons and names
 * - Active tab highlighting  
 * - Modified file indicators
 * - Close buttons with hover states
 * - Horizontal scrolling for many tabs
 * - Drag and drop reordering (optional)
 */
const TabBar: React.FC<TabBarProps> = ({
  tabs,
  onTabClick,
  onTabClose,
  onTabMove,
  onCloseOthers,
  onCloseAll,
  onCloseToRight,
  onCopyPath,
  className = ''
}) => {
  const [draggedTab, setDraggedTab] = useState<string | null>(null);
  const [dragOverTab, setDragOverTab] = useState<string | null>(null);
  const [contextMenu, setContextMenu] = useState<ContextMenuState>({
    isVisible: false,
    x: 0,
    y: 0,
    tabId: ''
  });
  const tabsRef = useRef<HTMLDivElement>(null);
  const contextMenuRef = useRef<HTMLDivElement>(null);

  // Handle tab click
  const handleTabClick = (e: React.MouseEvent, tabId: string) => {
    e.preventDefault();
    e.stopPropagation();
    onTabClick(tabId);
  };

  // Handle tab close
  const handleTabClose = (e: React.MouseEvent, tabId: string) => {
    e.preventDefault();
    e.stopPropagation();
    onTabClose(tabId);
  };

  // Handle middle click to close tab
  const handleTabMiddleClick = (e: React.MouseEvent, tabId: string) => {
    if (e.button === 1) { // Middle mouse button
      e.preventDefault();
      e.stopPropagation();
      onTabClose(tabId);
    }
  };

  // Handle right click to show context menu
  const handleTabRightClick = (e: React.MouseEvent, tabId: string) => {
    e.preventDefault();
    e.stopPropagation();
    
    // Adjust position if menu would go off screen
    const menuWidth = 180;
    const menuHeight = 250;
    const adjustedX = Math.min(e.clientX, window.innerWidth - menuWidth);
    const adjustedY = Math.min(e.clientY, window.innerHeight - menuHeight);
    
    setContextMenu({
      isVisible: true,
      x: adjustedX,
      y: adjustedY,
      tabId
    });
  };

  // Close context menu
  const closeContextMenu = () => {
    setContextMenu(prev => ({ ...prev, isVisible: false }));
  };

  // Handle context menu item clicks
  const handleContextMenuAction = (action: string) => {
    const tab = tabs.find(t => t.id === contextMenu.tabId);
    if (!tab) return;

    switch (action) {
      case 'close':
        onTabClose(contextMenu.tabId);
        break;
      case 'closeOthers':
        if (onCloseOthers) onCloseOthers(contextMenu.tabId);
        break;
      case 'closeToRight':
        if (onCloseToRight) onCloseToRight(contextMenu.tabId);
        break;
      case 'closeAll':
        if (onCloseAll) onCloseAll();
        break;
      case 'copyPath':
        if (onCopyPath) onCopyPath(tab.filePath);
        break;
    }
    
    closeContextMenu();
  };

  // Drag and drop handlers (if onTabMove is provided)
  const handleDragStart = (e: React.DragEvent, tabId: string) => {
    if (!onTabMove) return;
    
    setDraggedTab(tabId);
    e.dataTransfer.effectAllowed = 'move';
    e.dataTransfer.setData('text/plain', tabId);
  };

  const handleDragOver = (e: React.DragEvent, tabId: string) => {
    if (!onTabMove || !draggedTab) return;
    
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
    setDragOverTab(tabId);
  };

  const handleDragLeave = () => {
    setDragOverTab(null);
  };

  const handleDrop = (e: React.DragEvent, targetTabId: string) => {
    if (!onTabMove || !draggedTab) return;
    
    e.preventDefault();
    
    const fromIndex = tabs.findIndex(tab => tab.id === draggedTab);
    const toIndex = tabs.findIndex(tab => tab.id === targetTabId);
    
    if (fromIndex !== -1 && toIndex !== -1 && fromIndex !== toIndex) {
      onTabMove(fromIndex, toIndex);
    }
    
    setDraggedTab(null);
    setDragOverTab(null);
  };

  const handleDragEnd = () => {
    setDraggedTab(null);
    setDragOverTab(null);
  };

  // Ensure active tab is visible when tabs change
  useEffect(() => {
    const activeTab = tabs.find(tab => tab.isActive);
    if (activeTab && tabsRef.current) {
      const activeTabElement = tabsRef.current.querySelector(`[data-tab-id="${activeTab.id}"]`) as HTMLElement;
      if (activeTabElement) {
        activeTabElement.scrollIntoView({ 
          behavior: 'smooth', 
          block: 'nearest', 
          inline: 'nearest' 
        });
      }
    }
  }, [tabs]);

  // Handle clicking outside context menu
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (contextMenuRef.current && !contextMenuRef.current.contains(event.target as Node)) {
        closeContextMenu();
      }
    };

    if (contextMenu.isVisible) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
      };
    }
  }, [contextMenu.isVisible]);

  if (tabs.length === 0) {
    return null;
  }

  return (
    <TabBarContainer className={className}>
      <TabsWrapper ref={tabsRef}>
        {tabs.map((tab) => (
          <Tab
            key={tab.id}
            data-tab-id={tab.id}
            $isActive={tab.isActive}
            $isModified={tab.isModified}
            onClick={(e) => handleTabClick(e, tab.id)}
            onMouseDown={(e) => handleTabMiddleClick(e, tab.id)}
            onContextMenu={(e) => handleTabRightClick(e, tab.id)}
            draggable={!!onTabMove}
            onDragStart={(e) => handleDragStart(e, tab.id)}
            onDragOver={(e) => handleDragOver(e, tab.id)}
            onDragLeave={handleDragLeave}
            onDrop={(e) => handleDrop(e, tab.id)}
            onDragEnd={handleDragEnd}
            style={{
              opacity: draggedTab === tab.id ? 0.5 : 1,
              borderLeft: dragOverTab === tab.id ? '2px solid #007acc' : undefined,
            }}
            title={tab.filePath}
          >
            <TabContent $isModified={tab.isModified}>
              <TabIcon>
                {getFileIconComponent(tab.fileName)}
              </TabIcon>
              <TabLabel>
                {tab.fileName}
              </TabLabel>
              <TabCloseButton
                $isModified={tab.isModified}
                onClick={(e) => handleTabClose(e, tab.id)}
                title={tab.isModified ? 'File has unsaved changes' : 'Close tab'}
              />
            </TabContent>
          </Tab>
        ))}
      </TabsWrapper>
      
      {/* Tab Context Menu */}
      <TabContextMenu 
        ref={contextMenuRef}
        $x={contextMenu.x}
        $y={contextMenu.y}
        $visible={contextMenu.isVisible}
      >
        <TabContextMenuItem onClick={() => handleContextMenuAction('close')}>
          <TabContextMenuIcon>
            <FontAwesomeIcon icon={faTimes} />
          </TabContextMenuIcon>
          Close
        </TabContextMenuItem>
        
        <TabContextMenuItem 
          onClick={() => handleContextMenuAction('closeOthers')}
          $disabled={tabs.length <= 1}
        >
          <TabContextMenuIcon>
            <FontAwesomeIcon icon={faTimesCircle} />
          </TabContextMenuIcon>
          Close Others
        </TabContextMenuItem>
        
        <TabContextMenuItem 
          onClick={() => handleContextMenuAction('closeToRight')}
          $disabled={(() => {
            const currentIndex = tabs.findIndex(t => t.id === contextMenu.tabId);
            return currentIndex === -1 || currentIndex >= tabs.length - 1;
          })()}
        >
          <TabContextMenuIcon>
            <FontAwesomeIcon icon={faArrowRight} />
          </TabContextMenuIcon>
          Close to the Right
        </TabContextMenuItem>
        
        <TabContextMenuSeparator />
        
        <TabContextMenuItem 
          onClick={() => handleContextMenuAction('closeAll')}
          $danger={true}
        >
          <TabContextMenuIcon>
            <FontAwesomeIcon icon={faTimesCircle} />
          </TabContextMenuIcon>
          Close All
        </TabContextMenuItem>
        
        <TabContextMenuSeparator />
        
        <TabContextMenuItem onClick={() => handleContextMenuAction('copyPath')}>
          <TabContextMenuIcon>
            <FontAwesomeIcon icon={faLink} />
          </TabContextMenuIcon>
          Copy Path
        </TabContextMenuItem>
      </TabContextMenu>
    </TabBarContainer>
  );
};

export default TabBar;