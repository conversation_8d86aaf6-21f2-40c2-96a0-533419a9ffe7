import React, { useRef, useState, useEffect, useCallback } from 'react';
import '../styles/simple-sketch-canvas.css';

type Tool = 'pencil' | 'rectangle' | 'arrow' | 'circle' | 'eraser' | 'text';
type Color = '#000000' | '#E53E3E' | '#3182CE'; // Black, red, and blue

interface Annotation {
  id: string;
  type: 'circle' | 'arrow' | 'rectangle' | 'pencil' | 'text';
  color: Color;
  startX: number;
  startY: number;
  endX?: number;
  endY?: number;
  radius?: number;
  points?: { x: number; y: number }[]; // For pencil tool
  text?: string; // For text tool
  fontSize?: number; // For text tool
}

interface SimpleSketchCanvasProps {
  backgroundSvg?: string;
  onExport?: (blob: Blob, instructions?: string) => void;
  onAnnotationsChange?: (annotations: Annotation[]) => void;
  clearAnnotationsOnNewBackground?: boolean;
  isProcessing?: boolean;
}

const SimpleSketchCanvas: React.FC<SimpleSketchCanvasProps> = ({ 
  backgroundSvg, 
  onExport, 
  onAnnotationsChange,
  clearAnnotationsOnNewBackground = true,
  isProcessing = false
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [tool, setTool] = useState<Tool>('pencil');
  const [color, setColor] = useState<Color>('#000000');
  const [annotations, setAnnotations] = useState<Annotation[]>([]);
  const [isDrawing, setIsDrawing] = useState(false);
  const [currentAnnotation, setCurrentAnnotation] = useState<Annotation | null>(null);
  const [backgroundImage, setBackgroundImage] = useState<HTMLImageElement | null>(null);
  const [canvasSize, setCanvasSize] = useState({ width: 800, height: 600 });
  const [isEditingText, setIsEditingText] = useState(false);
  const [textInput, setTextInput] = useState('');
  const [textPosition, setTextPosition] = useState({ x: 0, y: 0 });
  const [aiInstructions, setAiInstructions] = useState('');
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  // Throttled redraw to prevent excessive rendering
  const redrawCanvas = useCallback(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    if (!ctx) return;
    
    // Use requestAnimationFrame to ensure smooth rendering
    requestAnimationFrame(() => {
      // Clear canvas and fill with theme background
      ctx.fillStyle = '#05122D'; // var(--bg-editor)
      ctx.fillRect(0, 0, canvas.width, canvas.height);
      
      // Draw background if exists
      if (backgroundImage) {
        // Debug: Drawing background image
        ctx.globalAlpha = 1.0;
        ctx.drawImage(backgroundImage, 0, 0, canvas.width, canvas.height);
        ctx.globalAlpha = 1.0;
      } else {
        // Debug: No background image to draw
      }
      
      // Draw all annotations
      annotations.forEach(annotation => {
        ctx.strokeStyle = annotation.color;
        ctx.fillStyle = annotation.color;
        ctx.lineWidth = annotation.type === 'pencil' ? 2 : 3;
        
        switch (annotation.type) {
          case 'pencil':
            if (annotation.points && annotation.points.length > 1) {
              ctx.beginPath();
              ctx.moveTo(annotation.points[0].x, annotation.points[0].y);
              annotation.points.forEach(point => {
                ctx.lineTo(point.x, point.y);
              });
              ctx.stroke();
            }
            break;
            
          case 'rectangle':
            if (annotation.endX && annotation.endY) {
              const width = annotation.endX - annotation.startX;
              const height = annotation.endY - annotation.startY;
              ctx.strokeRect(annotation.startX, annotation.startY, width, height);
            }
            break;
            
          case 'circle':
            ctx.beginPath();
            ctx.arc(
              annotation.startX, 
              annotation.startY, 
              annotation.radius || 30, 
              0, 
              2 * Math.PI
            );
            ctx.stroke();
            break;
            
          case 'arrow':
            if (annotation.endX && annotation.endY) {
              // Draw line
              ctx.beginPath();
              ctx.moveTo(annotation.startX, annotation.startY);
              ctx.lineTo(annotation.endX, annotation.endY);
              ctx.stroke();
              
              // Draw arrowhead
              const angle = Math.atan2(
                annotation.endY - annotation.startY,
                annotation.endX - annotation.startX
              );
              const headLength = 15;
              
              ctx.beginPath();
              ctx.moveTo(annotation.endX, annotation.endY);
              ctx.lineTo(
                annotation.endX - headLength * Math.cos(angle - Math.PI / 6),
                annotation.endY - headLength * Math.sin(angle - Math.PI / 6)
              );
              ctx.moveTo(annotation.endX, annotation.endY);
              ctx.lineTo(
                annotation.endX - headLength * Math.cos(angle + Math.PI / 6),
                annotation.endY - headLength * Math.sin(angle + Math.PI / 6)
              );
              ctx.stroke();
            }
            break;
            
          case 'text':
            if (annotation.text) {
              ctx.font = `${annotation.fontSize || 16}px Arial`;
              ctx.fillStyle = annotation.color;
              ctx.fillText(annotation.text, annotation.startX, annotation.startY);
            }
            break;
        }
      });
      
      // Draw current annotation being created
      if (currentAnnotation && isDrawing && currentAnnotation.type !== 'text') {
        ctx.strokeStyle = currentAnnotation.color;
        ctx.lineWidth = currentAnnotation.type === 'pencil' ? 2 : 3;
        
        if (currentAnnotation.type === 'pencil' && currentAnnotation.points && currentAnnotation.points.length > 1) {
          ctx.beginPath();
          ctx.moveTo(currentAnnotation.points[0].x, currentAnnotation.points[0].y);
          currentAnnotation.points.forEach(point => {
            ctx.lineTo(point.x, point.y);
          });
          ctx.stroke();
        } else if (currentAnnotation.type === 'rectangle' && currentAnnotation.endX && currentAnnotation.endY) {
          const width = currentAnnotation.endX - currentAnnotation.startX;
          const height = currentAnnotation.endY - currentAnnotation.startY;
          ctx.strokeRect(currentAnnotation.startX, currentAnnotation.startY, width, height);
        } else if (currentAnnotation.type === 'circle' && currentAnnotation.radius) {
          ctx.beginPath();
          ctx.arc(
            currentAnnotation.startX,
            currentAnnotation.startY,
            currentAnnotation.radius,
            0,
            2 * Math.PI
          );
          ctx.stroke();
        } else if (currentAnnotation.type === 'arrow' && currentAnnotation.endX && currentAnnotation.endY) {
          ctx.beginPath();
          ctx.moveTo(currentAnnotation.startX, currentAnnotation.startY);
          ctx.lineTo(currentAnnotation.endX, currentAnnotation.endY);
          ctx.stroke();
        }
      }
    });
  }, [annotations, currentAnnotation, isDrawing, backgroundImage]);

  // Load background SVG with single redraw
  useEffect(() => {
    // Debug: Background effect triggered (logging suppressed)
    
    if (backgroundSvg && backgroundSvg.trim() !== '') {
      const img = new Image();
      img.crossOrigin = 'anonymous';
      
      img.onload = () => {
        // Debug: Background SVG loaded successfully
        
        // Set background image immediately for seamless transition
        setBackgroundImage(img);
        
        // Clear annotations when new background loads (if enabled)
        if (clearAnnotationsOnNewBackground && annotations.length > 0) {
          // Debug: Clearing annotations due to new background
          setAnnotations([]);
          onAnnotationsChange?.([]);
        }
        
        // Single redraw when image loads
        requestAnimationFrame(() => redrawCanvas());
      };
      
      img.onerror = (error) => {
        console.error('[SimpleSketchCanvas] Failed to load background SVG:', error);
      };
      
      // Start loading immediately
      img.src = backgroundSvg;
    } else if (backgroundSvg === '' || backgroundSvg === null || backgroundSvg === undefined) {
      // Only clear if explicitly set to empty, not if it was just uninitialized
      // Debug: BackgroundSvg explicitly cleared
      setBackgroundImage(null);
      requestAnimationFrame(() => redrawCanvas());
    }
    // If backgroundSvg is undefined initially, don't clear existing background
  }, [backgroundSvg, clearAnnotationsOnNewBackground, onAnnotationsChange]);
  
  // Single effect for redrawing on state changes
  useEffect(() => {
    redrawCanvas();
  }, [annotations, currentAnnotation, isDrawing, backgroundImage]);
  
  // Resize canvas to fit container with debouncing
  useEffect(() => {
    let resizeTimeout: NodeJS.Timeout;
    
    const handleResize = () => {
      if (containerRef.current && canvasRef.current) {
        const container = containerRef.current;
        const rect = container.getBoundingClientRect();
        const width = rect.width - 2; // Account for border
        const height = Math.max(400, rect.height - 200); // Leave room for toolbar and instructions
        
        setCanvasSize({ width, height });
        
        // Resize canvas element
        const canvas = canvasRef.current;
        canvas.width = width;
        canvas.height = height;
        
        // Debounced redraw to prevent excessive redraws during resize
        clearTimeout(resizeTimeout);
        resizeTimeout = setTimeout(() => {
          requestAnimationFrame(() => redrawCanvas());
        }, 100);
      }
    };
    
    handleResize();
    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
      clearTimeout(resizeTimeout);
    };
  }, []);
  
  // Get accurate mouse coordinates
  const getMouseCoordinates = (e: React.MouseEvent<HTMLCanvasElement>) => {
    const canvas = canvasRef.current;
    if (!canvas) return { x: 0, y: 0 };
    
    const rect = canvas.getBoundingClientRect();
    const scaleX = canvas.width / rect.width;
    const scaleY = canvas.height / rect.height;
    
    return {
      x: (e.clientX - rect.left) * scaleX,
      y: (e.clientY - rect.top) * scaleY
    };
  };
  
  // Mouse event handlers
  const handleMouseDown = (e: React.MouseEvent<HTMLCanvasElement>) => {
    const { x, y } = getMouseCoordinates(e);
    
    if (tool === 'eraser') {
      // Find and remove annotation at this position
      const updatedAnnotations = annotations.filter(ann => {
        if (ann.type === 'circle') {
          const dist = Math.sqrt(
            Math.pow(x - ann.startX, 2) + Math.pow(y - ann.startY, 2)
          );
          return dist > (ann.radius || 30);
        } else if (ann.type === 'rectangle' && ann.endX && ann.endY) {
          // Check if click is inside rectangle
          const minX = Math.min(ann.startX, ann.endX);
          const maxX = Math.max(ann.startX, ann.endX);
          const minY = Math.min(ann.startY, ann.endY);
          const maxY = Math.max(ann.startY, ann.endY);
          return !(x >= minX && x <= maxX && y >= minY && y <= maxY);
        } else if (ann.type === 'pencil' && ann.points) {
          // Check if click is near any point in the pencil path
          const threshold = 10;
          return !ann.points.some(point => {
            const dist = Math.sqrt(
              Math.pow(x - point.x, 2) + Math.pow(y - point.y, 2)
            );
            return dist <= threshold;
          });
        } else if (ann.type === 'text' && ann.text) {
          // Check if click is near text position
          // Approximate text bounds (width based on text length)
          const approxWidth = ann.text.length * 8;
          const approxHeight = ann.fontSize || 16;
          return !(x >= ann.startX - 5 && x <= ann.startX + approxWidth && 
                   y >= ann.startY - approxHeight && y <= ann.startY + 5);
        }
        // Keep other annotations
        return true;
      });
      setAnnotations(updatedAnnotations);
      onAnnotationsChange?.(updatedAnnotations);
    } else if (tool === 'text') {
      // For text tool, show input at click position
      setTextPosition({ x, y });
      setIsEditingText(true);
      setTextInput('');
    } else {
      setIsDrawing(true);
      const newAnnotation: Annotation = {
        id: Date.now().toString(),
        type: tool as 'circle' | 'arrow' | 'rectangle' | 'pencil' | 'text',
        color,
        startX: x,
        startY: y
      };
      
      // Initialize points array for pencil tool
      if (tool === 'pencil') {
        newAnnotation.points = [{ x, y }];
      }
      
      setCurrentAnnotation(newAnnotation);
    }
  };
  
  const handleMouseMove = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!isDrawing || !currentAnnotation) return;
    
    const { x, y } = getMouseCoordinates(e);
    
    if (currentAnnotation.type === 'pencil') {
      // Add point to pencil path
      const updatedPoints = [...(currentAnnotation.points || []), { x, y }];
      setCurrentAnnotation({ ...currentAnnotation, points: updatedPoints });
    } else if (currentAnnotation.type === 'rectangle') {
      setCurrentAnnotation({ ...currentAnnotation, endX: x, endY: y });
    } else if (currentAnnotation.type === 'circle') {
      const radius = Math.sqrt(
        Math.pow(x - currentAnnotation.startX, 2) + 
        Math.pow(y - currentAnnotation.startY, 2)
      );
      setCurrentAnnotation({ ...currentAnnotation, radius });
    } else if (currentAnnotation.type === 'arrow') {
      setCurrentAnnotation({ ...currentAnnotation, endX: x, endY: y });
    }
  };
  
  const handleMouseUp = () => {
    if (isDrawing && currentAnnotation) {
      if (
        (currentAnnotation.type === 'circle' && currentAnnotation.radius) ||
        (currentAnnotation.type === 'arrow' && currentAnnotation.endX && currentAnnotation.endY) ||
        (currentAnnotation.type === 'rectangle' && currentAnnotation.endX && currentAnnotation.endY) ||
        (currentAnnotation.type === 'pencil' && currentAnnotation.points && currentAnnotation.points.length > 1)
      ) {
        const updatedAnnotations = [...annotations, currentAnnotation];
        setAnnotations(updatedAnnotations);
        onAnnotationsChange?.(updatedAnnotations);
      }
    }
    setIsDrawing(false);
    setCurrentAnnotation(null);
  };
  
  // Export canvas as image
  const handleExport = () => {
    const canvas = canvasRef.current;
    if (!canvas) return;
    
    canvas.toBlob((blob) => {
      if (blob) {
        onExport?.(blob);
      }
    }, 'image/png');
  };
  
  // Take screenshot for AI analysis
  const takeScreenshot = async (): Promise<Blob | null> => {
    const canvas = canvasRef.current;
    if (!canvas) return null;
    
    // Force a complete redraw to ensure all annotations are visible
    redrawCanvas();
    
    // Wait for the next frame to ensure the redraw is complete
    return new Promise((resolve) => {
      requestAnimationFrame(() => {
        canvas.toBlob((blob) => {
          // Debug: Screenshot taken successfully
          resolve(blob);
        }, 'image/png', 1.0); // Full quality for AI
      });
    });
  };
  
  // Convert canvas to base64 for AI
  const getCanvasAsBase64 = (): string => {
    const canvas = canvasRef.current;
    if (!canvas) return '';
    
    return canvas.toDataURL('image/png');
  };
  
  // Send to AI with instructions
  const handleSendToAI = async () => {
    const blob = await takeScreenshot();
    if (!blob) return;
    
    // Debug: Sending to AI for analysis
    
    // Send the blob with instructions (the parent component will handle the actual AI call)
    onExport?.(blob, aiInstructions.trim() || undefined);
    
    // Clear instructions after sending
    setAiInstructions('');
  };
  
  // Save canvas
  const handleSave = () => {
    const canvas = canvasRef.current;
    if (!canvas) return;
    
    // Create a download link
    canvas.toBlob((blob) => {
      if (blob) {
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `sketch-${Date.now()}.png`;
        a.click();
        URL.revokeObjectURL(url);
      }
    }, 'image/png');
  };
  
  // Clear all annotations
  const handleClear = () => {
    setAnnotations([]);
    onAnnotationsChange?.([]);
  };
  
  // Handle text input submission
  const handleTextSubmit = useCallback(() => {
    if (textInput.trim()) {
      const textAnnotation: Annotation = {
        id: Date.now().toString(),
        type: 'text',
        color,
        startX: textPosition.x,
        startY: textPosition.y,
        text: textInput,
        fontSize: 16
      };
      const updatedAnnotations = [...annotations, textAnnotation];
      setAnnotations(updatedAnnotations);
      onAnnotationsChange?.(updatedAnnotations);
    }
    setIsEditingText(false);
    setTextInput('');
  }, [textInput, textPosition, color, annotations, onAnnotationsChange]);

  // Handle file upload
  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;
    
    if (file.type === 'image/svg+xml' || file.name.endsWith('.svg')) {
      const reader = new FileReader();
      reader.onload = (event) => {
        const svgText = event.target?.result as string;
        
        // Clean up SVG text to ensure proper encoding
        const cleanedSvg = svgText.trim();
        const svgDataUrl = `data:image/svg+xml;base64,${btoa(unescape(encodeURIComponent(cleanedSvg)))}`;
        
        const img = new Image();
        img.crossOrigin = 'anonymous';
        
        img.onload = () => {
          setBackgroundImage(img);
          // Force immediate redraw
          setTimeout(() => redrawCanvas(), 0);
        };
        
        img.onerror = (error) => {
          console.error('Failed to load SVG:', error);
          // Try alternative loading method
          const blob = new Blob([cleanedSvg], { type: 'image/svg+xml' });
          const url = URL.createObjectURL(blob);
          const fallbackImg = new Image();
          fallbackImg.onload = () => {
            setBackgroundImage(fallbackImg);
            URL.revokeObjectURL(url);
            setTimeout(() => redrawCanvas(), 0);
          };
          fallbackImg.onerror = () => {
            alert('Failed to load SVG file. Please ensure it\'s a valid SVG.');
          };
          fallbackImg.src = url;
        };
        
        img.src = svgDataUrl;
      };
      reader.readAsText(file);
    } else {
      alert('Please select an SVG file');
    }
    
    // Reset file input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // Save as SVG
  const handleSaveAsSVG = () => {
    const canvas = canvasRef.current;
    if (!canvas) return;
    
    // Create SVG element
    const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
    svg.setAttribute('width', canvas.width.toString());
    svg.setAttribute('height', canvas.height.toString());
    svg.setAttribute('xmlns', 'http://www.w3.org/2000/svg');
    
    // Add background rect
    const bgRect = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
    bgRect.setAttribute('width', '100%');
    bgRect.setAttribute('height', '100%');
    bgRect.setAttribute('fill', '#05122D');
    svg.appendChild(bgRect);
    
    // Add annotations as SVG elements
    annotations.forEach(annotation => {
      switch (annotation.type) {
        case 'rectangle':
          if (annotation.endX && annotation.endY) {
            const rect = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
            rect.setAttribute('x', Math.min(annotation.startX, annotation.endX).toString());
            rect.setAttribute('y', Math.min(annotation.startY, annotation.endY).toString());
            rect.setAttribute('width', Math.abs(annotation.endX - annotation.startX).toString());
            rect.setAttribute('height', Math.abs(annotation.endY - annotation.startY).toString());
            rect.setAttribute('fill', 'none');
            rect.setAttribute('stroke', annotation.color);
            rect.setAttribute('stroke-width', '3');
            svg.appendChild(rect);
          }
          break;
          
        case 'circle':
          const circle = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
          circle.setAttribute('cx', annotation.startX.toString());
          circle.setAttribute('cy', annotation.startY.toString());
          circle.setAttribute('r', (annotation.radius || 30).toString());
          circle.setAttribute('fill', 'none');
          circle.setAttribute('stroke', annotation.color);
          circle.setAttribute('stroke-width', '3');
          svg.appendChild(circle);
          break;
          
        case 'arrow':
          if (annotation.endX && annotation.endY) {
            const line = document.createElementNS('http://www.w3.org/2000/svg', 'line');
            line.setAttribute('x1', annotation.startX.toString());
            line.setAttribute('y1', annotation.startY.toString());
            line.setAttribute('x2', annotation.endX.toString());
            line.setAttribute('y2', annotation.endY.toString());
            line.setAttribute('stroke', annotation.color);
            line.setAttribute('stroke-width', '3');
            line.setAttribute('marker-end', 'url(#arrowhead)');
            svg.appendChild(line);
          }
          break;
          
        case 'pencil':
          if (annotation.points && annotation.points.length > 1) {
            const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
            const d = annotation.points.reduce((acc, point, index) => {
              return acc + (index === 0 ? `M ${point.x} ${point.y}` : ` L ${point.x} ${point.y}`);
            }, '');
            path.setAttribute('d', d);
            path.setAttribute('fill', 'none');
            path.setAttribute('stroke', annotation.color);
            path.setAttribute('stroke-width', '2');
            svg.appendChild(path);
          }
          break;
          
        case 'text':
          if (annotation.text) {
            const text = document.createElementNS('http://www.w3.org/2000/svg', 'text');
            text.setAttribute('x', annotation.startX.toString());
            text.setAttribute('y', annotation.startY.toString());
            text.setAttribute('fill', annotation.color);
            text.setAttribute('font-size', (annotation.fontSize || 16).toString());
            text.setAttribute('font-family', 'Arial');
            text.textContent = annotation.text;
            svg.appendChild(text);
          }
          break;
      }
    });
    
    // Add arrow marker definition
    const defs = document.createElementNS('http://www.w3.org/2000/svg', 'defs');
    const marker = document.createElementNS('http://www.w3.org/2000/svg', 'marker');
    marker.setAttribute('id', 'arrowhead');
    marker.setAttribute('markerWidth', '10');
    marker.setAttribute('markerHeight', '10');
    marker.setAttribute('refX', '9');
    marker.setAttribute('refY', '3');
    marker.setAttribute('orient', 'auto');
    const polygon = document.createElementNS('http://www.w3.org/2000/svg', 'polygon');
    polygon.setAttribute('points', '0 0, 10 3, 0 6');
    polygon.setAttribute('fill', '#000000');
    marker.appendChild(polygon);
    defs.appendChild(marker);
    svg.appendChild(defs);
    
    // Convert to string and download
    const svgString = new XMLSerializer().serializeToString(svg);
    const blob = new Blob([svgString], { type: 'image/svg+xml' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `sketch-${Date.now()}.svg`;
    a.click();
    URL.revokeObjectURL(url);
  };

  // Load dashboard template with proper SVG handling
  const handleLoadTemplate = async () => {
    try {
      const response = await fetch('/assets/dashboard.svg');
      const svgText = await response.text();
      
      // Method 1: Using data URL (more reliable for SVG)
      const svgDataUrl = `data:image/svg+xml;base64,${btoa(unescape(encodeURIComponent(svgText)))}`;
      
      const img = new Image();
      img.crossOrigin = 'anonymous';
      
      img.onload = () => {
        setBackgroundImage(img);
        // Force redraw after image loads
        redrawCanvas();
      };
      
      img.onerror = () => {
        // Fallback: Try blob URL method
        const svgBlob = new Blob([svgText], { type: 'image/svg+xml' });
        const url = URL.createObjectURL(svgBlob);
        
        const fallbackImg = new Image();
        fallbackImg.onload = () => {
          setBackgroundImage(fallbackImg);
          URL.revokeObjectURL(url);
          redrawCanvas();
        };
        fallbackImg.src = url;
      };
      
      img.src = svgDataUrl;
    } catch (error) {
      console.error('Failed to load template:', error);
      alert('Failed to load dashboard template');
    }
  };
  
  return (
    <div className="simple-sketch-container" ref={containerRef}>
      <div className="simple-sketch-toolbar">
        {/* Primary Actions - Left Side */}
        <div className="primary-actions">
          <span className="tool-group-label">Actions</span>
          <div className="button-row">
            <button 
              className={`sketch-tool-btn primary ${isProcessing ? 'processing' : ''}`}
              onClick={handleSendToAI}
              title="Send sketch to AI for analysis and improvement"
              data-action="ai"
              disabled={isProcessing}
            >
              {isProcessing ? (
                <div className="loading-spinner" />
              ) : (
                <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2.5">
                  <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                </svg>
              )}
            </button>
            
            <input
              ref={fileInputRef}
              type="file"
              accept=".svg,image/svg+xml"
              onChange={handleFileUpload}
              style={{ display: 'none' }}
            />
            <button 
              className="sketch-tool-btn" 
              onClick={() => fileInputRef.current?.click()}
              title="Load SVG file from computer"
              data-action="load"
            >
              <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2.5">
                <path d="M12 15V3m0 12l-4-4m4 4l4-4"/>
                <path d="M2 17l.621 2.485A2 2 0 004.561 21h14.878a2 2 0 001.94-1.515L22 17"/>
              </svg>
            </button>
            
            <button 
              className="sketch-tool-btn danger" 
              onClick={handleClear}
              title="Clear all annotations"
              data-action="clear"
            >
              <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2.5">
                <path d="M3 6h18"/>
                <path d="M8 6V4a2 2 0 012-2h4a2 2 0 012 2v2m3 0v14a2 2 0 01-2 2H7a2 2 0 01-2-2V6h14zM10 11v6M14 11v6"/>
              </svg>
            </button>
          </div>
        </div>
        
        <div className="toolbar-divider" />
        
        {/* Drawing Tools */}
        <div className="simple-sketch-tool-group" data-group="drawing">
          <span className="tool-group-label">Draw</span>
          <div className="tool-buttons-row">
            <button
              className={`sketch-tool-btn ${tool === 'pencil' ? 'active' : ''}`}
              onClick={() => setTool('pencil')}
              title="Pencil Tool - Freehand drawing"
              data-tool="pencil"
            >
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <path d="M12 19l7-7 3 3-7 7-3-3z"/>
                <path d="M18 13l-1.5-7.5L2 2l3.5 14.5L13 18l5-5z"/>
                <path d="M2 2l7.586 7.586"/>
                <circle cx="11" cy="11" r="2"/>
              </svg>
            </button>
            <button
              className={`sketch-tool-btn ${tool === 'rectangle' ? 'active' : ''}`}
              onClick={() => setTool('rectangle')}
              title="Rectangle Tool - Draw UI containers"
              data-tool="rectangle"
            >
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <rect x="3" y="3" width="18" height="18" rx="2"/>
              </svg>
            </button>
            <button
              className={`sketch-tool-btn ${tool === 'circle' ? 'active' : ''}`}
              onClick={() => setTool('circle')}
              title="Circle Tool - Highlights and emphasis"
              data-tool="circle"
            >
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <circle cx="12" cy="12" r="9"/>
              </svg>
            </button>
            <button
              className={`sketch-tool-btn ${tool === 'arrow' ? 'active' : ''}`}
              onClick={() => setTool('arrow')}
              title="Arrow Tool - Flow indicators"
              data-tool="arrow"
            >
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <path d="M5 12h14"/>
                <path d="M12 5l7 7-7 7"/>
              </svg>
            </button>
            <button
              className={`sketch-tool-btn ${tool === 'text' ? 'active' : ''}`}
              onClick={() => setTool('text')}
              title="Text Tool - Add labels and notes"
              data-tool="text"
            >
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <polyline points="4,7 4,4 20,4 20,7"/>
                <line x1="9" y1="20" x2="15" y2="20"/>
                <line x1="12" y1="4" x2="12" y2="20"/>
              </svg>
            </button>
            <button
              className={`sketch-tool-btn ${tool === 'eraser' ? 'active' : ''}`}
              onClick={() => setTool('eraser')}
              title="Eraser Tool - Remove annotations"
              data-tool="eraser"
            >
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <path d="M20 20H7l-2-2L9 14l7 7z"/>
                <path d="M9 14L4.5 9.5c-.7-.7-.7-1.8 0-2.5L12 2l7 7-4.5 4.5L9 14z"/>
              </svg>
            </button>
          </div>
        </div>
        
        <div className="toolbar-divider" />
        
        {/* Color Palette */}
        <div className="simple-sketch-tool-group" data-group="colors">
          <span className="tool-group-label">Color</span>
          <div className="color-palette">
            <button
              className={`sketch-color-btn ${color === '#000000' ? 'active' : ''}`}
              style={{ backgroundColor: '#000000' }}
              onClick={() => setColor('#000000')}
              title="Black - Primary annotations"
              data-color="black"
            />
            <button
              className={`sketch-color-btn ${color === '#E53E3E' ? 'active' : ''}`}
              style={{ backgroundColor: '#E53E3E' }}
              onClick={() => setColor('#E53E3E')}
              title="Red - Emphasis and corrections"
              data-color="red"
            />
            <button
              className={`sketch-color-btn ${color === '#3182CE' ? 'active' : ''}`}
              style={{ backgroundColor: '#3182CE' }}
              onClick={() => setColor('#3182CE')}
              title="Blue - Information highlights"
              data-color="blue"
            />
          </div>
        </div>
        
        {/* Spacer to push remaining actions to the right */}
        <div className="toolbar-spacer"></div>
      </div>
      
      {/* AI Instructions Input */}
      <div className="ai-instructions-container">
        <input
          type="text"
          value={aiInstructions}
          onChange={(e) => setAiInstructions(e.target.value)}
          placeholder="Add instructions for AI analysis (e.g., 'Focus on the navigation layout' or 'Suggest mobile responsive improvements')"
          className="ai-instructions-input"
          maxLength={200}
        />
      </div>
      
      <div className="canvas-container" style={{ position: 'relative' }}>
        <canvas
          ref={canvasRef}
          width={canvasSize.width}
          height={canvasSize.height}
          className={`simple-sketch-canvas ${tool}-tool`}
          onMouseDown={handleMouseDown}
          onMouseMove={handleMouseMove}
          onMouseUp={handleMouseUp}
          onMouseLeave={handleMouseUp}
        />
        
        {/* AI Processing Overlay */}
        {isProcessing && (
          <div className="ai-processing-overlay">
            <div className="ai-processing-content">
              <div className="ai-processing-spinner"></div>
              <div className="ai-processing-text">Analyzing sketch with AI...</div>
            </div>
          </div>
        )}
      </div>
      
      {/* Text input modal */}
      {isEditingText && (
        <div 
          className="text-input-overlay"
          onClick={() => setIsEditingText(false)}
        >
          <div 
            className="text-input-container"
            style={{ 
              left: textPosition.x + 'px', 
              top: textPosition.y + 'px' 
            }}
            onClick={(e) => e.stopPropagation()}
          >
            <input
              type="text"
              value={textInput}
              onChange={(e) => setTextInput(e.target.value)}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  handleTextSubmit();
                } else if (e.key === 'Escape') {
                  setIsEditingText(false);
                  setTextInput('');
                }
              }}
              placeholder="Enter text..."
              autoFocus
              className="text-input-field"
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default SimpleSketchCanvas;
