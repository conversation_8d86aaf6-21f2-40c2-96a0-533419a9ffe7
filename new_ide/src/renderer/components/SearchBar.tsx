import React, { useState, useEffect, useRef, useCallback } from 'react';
import styled from 'styled-components';
import { getFileIconByType, FolderIcon } from '../utils/vsCodeIcons';
import { useFocusContext, FocusableComponent, FocusPriority } from '../contexts/FocusContext';
import SemanticSearchService from '../services/SemanticSearchService';
import { SemanticSearchResult } from '../services/ChromaDbService';
import { ApiClient } from '../services/ApiClient';
//test

export interface SearchResult {
  id: string;
  file: string;
  line?: number;
  column?: number;
  match?: string;
  preview?: string;
  matchStart?: number;
  matchEnd?: number;
  type: 'content' | 'filename' | 'symbol' | 'semantic';
  name?: string;
  directory?: string;
  itemType?: 'file' | 'directory';
  // Symbol-specific fields
  kind?: 'function' | 'class' | 'interface' | 'type' | 'variable' | 'import' | 'export';
  signature?: string;
  returnType?: string;
  parameters?: string[];
  modifiers?: string[];
  // Semantic search fields
  confidence?: number;
  documentType?: string;
  metadata?: Record<string, any>;
}

interface SearchBarProps {
  projectPath?: string;
  onNavigateToFile: (file: string, line?: number, column?: number) => void;
  isVisible: boolean;
}

// Helper function to get file icon based on extension
const getFileIcon = (filename: string): React.ReactNode => {
  const ext = filename.split('.').pop()?.toLowerCase() || '';
  return getFileIconByType(ext);
};

// Helper function to get symbol icons
const getSymbolIcon = (kind?: string): string => {
  switch (kind) {
    case 'function': return '𝑓';
    case 'class': return '𝒞';
    case 'interface': return '𝒾';
    case 'type': return '𝒯';
    case 'variable': return '𝒗';
    case 'import': return '⬅';
    case 'export': return '➡';
    default: return '•';
  }
};

const SearchBarContainer = styled.div<{ $isVisible: boolean }>`
  display: ${props => props.$isVisible ? 'flex' : 'none'};
  align-items: center;
  justify-content: center;
  flex: 1;
  width: 100%;
  position: relative;
  padding: 0 16px;
`;

const SearchInputWrapper = styled.div`
  position: relative;
  width: 100%;
  max-width: 600px;
  margin: 0 auto;
`;

const SearchContent = styled.div<{ $expanded: boolean }>`
  position: ${props => props.$expanded ? 'absolute' : 'relative'};
  top: ${props => props.$expanded ? '100%' : 'auto'};
  left: 0;
  right: 0;
  background-color: ${props => props.$expanded ? 'var(--bg-modal)' : 'transparent'};
  border-radius: ${props => props.$expanded ? '0 0 8px 8px' : '0'};
  box-shadow: ${props => props.$expanded ? '0 4px 12px rgba(0, 0, 0, 0.3)' : 'none'};
  z-index: ${props => props.$expanded ? '1000' : 'auto'};
  margin-top: ${props => props.$expanded ? '4px' : '0'};
  width: 100%;
`;

const SearchInputRow = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  position: relative;
  width: 100%;
`;

const TabsContainer = styled.div<{ $hasQuery: boolean }>`
  display: ${props => props.$hasQuery ? 'flex' : 'none'};
  background-color: var(--bg-modal);
  padding: 8px 16px;
  gap: 8px;
  border-bottom: 1px solid var(--border-separator);
  width: 100%;
  box-sizing: border-box;
`;

const Tab = styled.button<{ $active: boolean }>`
  background-color: ${props => props.$active ? 'var(--primary-accent)' : 'transparent'};
  border: 1px solid ${props => props.$active ? 'var(--primary-accent)' : 'transparent'};
  color: ${props => props.$active ? 'var(--text-header)' : 'var(--text-dim)'};
  padding: 6px 16px;
  font-family: var(--font-sans);
  font-size: var(--font-size-md);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border-radius: 20px;

  &:hover {
    background-color: ${props => props.$active ? 'var(--primary-accent-hover)' : 'var(--bg-hover)'};
    color: var(--text-bright);
  }
`;

const IndexButton = styled.button<{ $isIndexing: boolean }>`
  background-color: transparent;
  border: 1px solid var(--border-button);
  color: var(--text-dim);
  padding: 4px 12px;
  font-family: var(--font-sans);
  font-size: var(--font-size-sm);
  font-weight: 500;
  cursor: ${props => props.$isIndexing ? 'not-allowed' : 'pointer'};
  transition: all 0.2s ease;
  border-radius: 16px;
  display: flex;
  align-items: center;
  gap: 4px;
  margin-left: auto;
  opacity: ${props => props.$isIndexing ? 0.6 : 1};

  &:hover {
    background-color: ${props => props.$isIndexing ? 'transparent' : 'var(--bg-hover)'};
    color: ${props => props.$isIndexing ? 'var(--text-dim)' : 'var(--text-bright)'};
  }
`;

const SearchInput = styled.input<{ $mode?: string }>`
  flex: 1;
  background-color: var(--bg-input);
  border: 1px solid var(--border-input);
  border-radius: 20px;
  padding: 6px 16px;
  padding-right: ${props => props.$mode === 'content' ? '110px' : '60px'};
  color: var(--text-bright);
  font-family: var(--font-sans);
  font-size: var(--font-size-md);
  outline: none;
  height: 32px;
  transition: all 0.2s ease;
  width: 100%;
  box-sizing: border-box;

  &:focus {
    border-color: var(--accent-purple);
    box-shadow: 0 0 0 2px rgba(108, 92, 231, 0.2);
    background-color: var(--bg-modal);
  }

  &::placeholder {
    color: var(--text-dim);
  }
`;

const SearchOptions = styled.div`
  display: flex;
  align-items: center;
  gap: 2px;
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 10;
  background-color: transparent;
`;

const OptionButton = styled.button<{ $active?: boolean }>`
  background-color: ${props => props.$active ? 'var(--accent-purple)' : 'transparent'};
  border: 1px solid ${props => props.$active ? 'var(--accent-purple)' : 'transparent'};
  border-radius: 4px;
  color: ${props => props.$active ? 'var(--text-header)' : 'var(--text-dim)'};
  padding: 2px 6px;
  font-size: var(--font-size-xs);
  cursor: pointer;
  font-family: var(--font-sans);
  transition: all 0.15s ease;
  min-width: 24px;
  height: 24px;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    background-color: ${props => props.$active ? 'var(--primary-accent-hover)' : 'var(--bg-hover)'};
    border-color: var(--accent-purple);
    color: var(--text-bright);
  }
`;

const ResultsContainer = styled.div`
  max-height: 400px;
  overflow-y: auto;
  background-color: var(--bg-dropdown);
  width: 100%;
  box-sizing: border-box;
`;

const ResultItem = styled.div<{ $selected?: boolean }>`
  padding: 10px 16px;
  cursor: pointer;
  transition: all 0.15s ease;
  display: flex;
  flex-direction: column;
  gap: 4px;
  background-color: ${props => props.$selected ? 'var(--bg-hover)' : 'transparent'};
  border-left: 3px solid ${props => props.$selected ? 'var(--accent-purple)' : 'transparent'};

  &:hover {
    background-color: var(--bg-hover);
  }

  &:not(:last-child) {
    border-bottom: 1px solid var(--border-separator);
  }
`;

const ResultHeader = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  font-family: var(--font-sans);
  font-size: var(--font-size-md);
`;

const FileIcon = styled.span`
  display: inline-flex;
  align-items: center;
  margin-right: 4px;
  color: inherit;
  
  svg {
    width: 16px;
    height: 16px;
  }
`;

const TabIcon = styled.span`
  display: inline-flex;
  align-items: center;
  margin-right: 6px;
  
  svg {
    width: 14px;
    height: 14px;
  }
`;

const ResultFile = styled.span`
  color: var(--text-dim);
  font-size: var(--font-size-sm);
  font-family: var(--font-sans);
`;

const ResultPreview = styled.div`
  font-family: var(--font-mono);
  font-size: var(--font-size-sm);
  color: var(--text-default);
  line-height: 1.5;
  white-space: pre-wrap;
  word-break: break-word;
  padding: 4px 0;
`;

const ResultMatch = styled.span`
  background-color: var(--overlay-accent);
  color: var(--accent-purple);
  font-weight: 600;
  padding: 0 2px;
  border-radius: 2px;
`;

const NoResults = styled.div`
  padding: 32px;
  text-align: center;
  color: var(--text-dim);
  font-family: var(--font-sans);
  font-size: var(--font-size-md);
`;

const StatusInfo = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-family: var(--font-sans);
  font-size: var(--font-size-sm);
  color: var(--text-dim);
  padding: 8px 16px;
  background-color: var(--bg-input);
  border-top: 1px solid var(--border-separator);
`;

// Add global styles for animations
const GlobalStylesDiv = styled.div`
  @keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
  }
`;

type SearchMode = 'files' | 'content' | 'structure' | 'semantic';

const SearchBar: React.FC<SearchBarProps> = ({
  projectPath,
  onNavigateToFile,
  isVisible
}) => {
  const [query, setQuery] = useState('');
  const [mode, setMode] = useState<SearchMode>('files');
  const [results, setResults] = useState<SearchResult[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [caseSensitive, setCaseSensitive] = useState(false);
  const [useRegex, setUseRegex] = useState(false);
  const [wholeWord, setWholeWord] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [isFocused, setIsFocused] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);
  
  const searchInputRef = useRef<HTMLInputElement>(null);
  const searchTimeoutRef = useRef<NodeJS.Timeout>();
  const searchContentRef = useRef<HTMLDivElement>(null);
  const semanticSearchRef = useRef<SemanticSearchService | null>(null);
  
  // Semantic search state
  const [semanticSearchReady, setSemanticSearchReady] = useState(false);
  const [semanticSearchIndexing, setSemanticSearchIndexing] = useState(false);
  
  // Focus context integration
  const { requestFocus, hasFocus } = useFocusContext();

  // Don't automatically request focus when becoming visible - let user interactions drive focus
  // This prevents focus loops with the Editor

  // Clear results when not visible
  useEffect(() => {
    if (!isVisible) {
      setQuery('');
      setResults([]);
      setIsSearching(false);
      setSelectedIndex(0);
      setIsExpanded(false);
      setIsFocused(false);
    }
  }, [isVisible]);

  // Listen to FocusContext changes via DOM events instead of useEffect to avoid loops
  useEffect(() => {
    const handleFocusChange = (event: CustomEvent) => {
      if (event.detail.component === FocusableComponent.SEARCH_BAR && 
          isVisible && 
          searchInputRef.current) {
        // Small delay to ensure proper focus transfer
        setTimeout(() => {
          searchInputRef.current?.focus();
        }, 10);
      }
    };

    document.addEventListener('kapi:focus-changed', handleFocusChange as EventListener);
    return () => {
      document.removeEventListener('kapi:focus-changed', handleFocusChange as EventListener);
    };
  }, [isVisible]);

  // Handle expansion state based on focus and query
  useEffect(() => {
    const shouldExpand = (isFocused || isExpanded) && query.trim() !== '';
    setIsExpanded(shouldExpand);
  }, [isFocused, query]);

  // Initialize semantic search when project path changes
  useEffect(() => {
    if (projectPath && !semanticSearchRef.current) {
      const initSemanticSearch = async () => {
        try {
          console.log('[SearchBar] Initializing semantic search for project:', projectPath);
          const apiClient = new ApiClient();
          const projectId = projectPath.split('/').pop() || 'default';
          const semanticSearch = new SemanticSearchService(projectId, apiClient);
          
          await semanticSearch.initialize();
          semanticSearchRef.current = semanticSearch;
          
          // Check if index exists
          const status = await semanticSearch.getStatus();
          setSemanticSearchReady(status.isReady && status.documentCount > 0);
          
          console.log('[SearchBar] Semantic search initialized:', status);
        } catch (error) {
          console.error('[SearchBar] Failed to initialize semantic search:', error);
          setSemanticSearchReady(false);
        }
      };
      
      initSemanticSearch();
    }
    
    // Cleanup on path change
    return () => {
      if (semanticSearchRef.current && !projectPath) {
        semanticSearchRef.current.cleanup();
        semanticSearchRef.current = null;
        setSemanticSearchReady(false);
      }
    };
  }, [projectPath]);

  // Perform search
  const performSearch = useCallback(async (searchQuery: string, searchMode: SearchMode) => {
    if (!searchQuery.trim()) {
      setResults([]);
      setIsSearching(false);
      return;
    }

    // For structure search, projectPath is less critical as we can try to init anyway
    if (searchMode !== 'structure' && !projectPath) {
      setResults([]);
      setIsSearching(false);
      return;
    }

    setIsSearching(true);
    
    console.log(`[SearchBar] Starting ${searchMode} search for "${searchQuery}" in project: ${projectPath || 'unknown'}`);

    try {
      if (searchMode === 'files') {
        // Search file names
        if (!projectPath) {
          setResults([]);
          return;
        }

        const searchResults = await window.electronAPI?.fileExplorer?.searchFileNames?.({
          query: searchQuery,
          path: projectPath,
          caseSensitive
        });

        if (searchResults?.success) {
          const formattedResults: SearchResult[] = (searchResults.results || []).map((result: any) => ({
            id: result.id,
            file: result.file,
            name: result.name,
            directory: result.directory,
            type: 'filename' as const,
            itemType: result.type
          }));
          setResults(formattedResults);
        } else {
          console.error('File name search failed:', searchResults?.error);
          setResults([]);
        }
      } else if (searchMode === 'structure') {
        // Search symbols using AST
        try {
          // First try to initialize AST service if not already done
          if (projectPath && window.electronAPI?.ast?.initializeProject) {
            try {
              await window.electronAPI.ast.initializeProject({ path: projectPath });
              console.log('[SearchBar] AST service initialized for structure search');
            } catch (initError) {
              console.warn('[SearchBar] AST initialization failed, but continuing with search:', initError);
            }
          }

          const searchResults = await window.electronAPI?.ast?.searchSymbols?.({
            query: searchQuery,
            caseSensitive,
            maxResults: 500
          });

          if (searchResults?.success) {
            const formattedResults: SearchResult[] = (searchResults.results || []).map((result: any) => ({
              id: result.id,
              file: result.file,
              line: result.line,
              column: result.column,
              name: result.name,
              type: 'symbol' as const,
              kind: result.kind,
              signature: result.signature,
              returnType: result.returnType,
              parameters: result.parameters,
              modifiers: result.modifiers,
              preview: result.preview
            }));
            setResults(formattedResults);
          } else {
            console.error('Symbol search failed:', searchResults?.error);
            // Show helpful error message if AST service isn't available
            if (searchResults?.error?.includes('not initialized')) {
              console.info('[SearchBar] Try opening a TypeScript/JavaScript project first');
            }
            setResults([]);
          }
        } catch (error) {
          console.error('Structure search error:', error);
          setResults([]);
        }
      } else if (searchMode === 'semantic') {
        // Semantic search using local ChromaDB
        if (!semanticSearchRef.current) {
          console.log('[SearchBar] Semantic search not initialized');
          setResults([]);
          return;
        }

        // Check if the service is ready for searching
        try {
          const status = await semanticSearchRef.current.getStatus();
          console.log('[SearchBar] Semantic search status:', status);
          
          if (!status.isReady) {
            console.log('[SearchBar] Semantic search service not ready');
            setResults([]);
            return;
          }

          if (status.documentCount === 0) {
            console.log('[SearchBar] No documents indexed yet');
            setResults([]);
            return;
          }
        } catch (error) {
          console.error('[SearchBar] Error checking semantic search status:', error);
          setResults([]);
          return;
        }

        try {
          const semanticResults = await semanticSearchRef.current.search(searchQuery, {
            limit: 20,
            threshold: 0.6
          });

          const formattedResults: SearchResult[] = semanticResults.map((result: SemanticSearchResult) => ({
            id: result.id,
            file: result.file,
            type: 'semantic' as const,
            line: result.line,
            column: result.column,
            preview: result.matchedContent,
            confidence: result.confidence,
            documentType: result.documentType,
            metadata: result.metadata
          }));

          setResults(formattedResults);
        } catch (error) {
          console.error('Semantic search failed:', error);
          setResults([]);
        }
      } else {
        // Search file content
        if (!projectPath) {
          setResults([]);
          return;
        }

        const searchResults = await window.electronAPI?.fileExplorer?.searchInFiles?.({
          query: searchQuery,
          path: projectPath,
          caseSensitive,
          useRegex,
          wholeWord
        });

        if (searchResults?.success) {
          const formattedResults: SearchResult[] = (searchResults.results || []).map((result: any) => ({
            ...result,
            type: 'content' as const
          }));
          setResults(formattedResults);
        } else {
          console.error('Content search failed:', searchResults?.error);
          setResults([]);
        }
      }
    } catch (error) {
      console.error('Search error:', error);
      setResults([]);
    } finally {
      setIsSearching(false);
    }
  }, [projectPath, caseSensitive, useRegex, wholeWord, semanticSearchReady]);

  // Handle query changes with debouncing
  useEffect(() => {
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    searchTimeoutRef.current = setTimeout(() => {
      performSearch(query, mode);
    }, 300);

    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, [query, mode, performSearch]);

  // Reset selected index when results change
  useEffect(() => {
    setSelectedIndex(0);
  }, [results]);

  // Handle keyboard shortcuts
  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      searchInputRef.current?.blur();
      setQuery('');
      setResults([]);
    } else if (e.key === 'Enter') {
      e.preventDefault();
      if (results.length > 0) {
        // Navigate to selected result
        const selected = results[selectedIndex];
        // Ensure we have an absolute path
        const absolutePath = selected.file.startsWith('/') ? selected.file : `${projectPath}/${selected.file}`;
        
        if (selected.type === 'content') {
          onNavigateToFile(absolutePath, selected.line, selected.column);
        } else {
          onNavigateToFile(absolutePath);
        }
      }
    } else if (e.key === 'ArrowDown') {
      e.preventDefault();
      setSelectedIndex(prev => Math.min(prev + 1, results.length - 1));
    } else if (e.key === 'ArrowUp') {
      e.preventDefault();
      setSelectedIndex(prev => Math.max(prev - 1, 0));
    }
  }, [onNavigateToFile, results, selectedIndex]);

  // Handle result click
  const handleResultClick = useCallback((result: SearchResult) => {
    // Ensure we have an absolute path
    const absolutePath = result.file.startsWith('/') ? result.file : `${projectPath}/${result.file}`;
    
    if (result.type === 'content') {
      onNavigateToFile(absolutePath, result.line, result.column);
    } else {
      onNavigateToFile(absolutePath);
    }
  }, [onNavigateToFile, projectPath]);

  // Handle manual reindexing
  const handleReindex = useCallback(async () => {
    if (!semanticSearchRef.current || !projectPath || semanticSearchIndexing) {
      return;
    }

    console.log('[SearchBar] Starting manual reindexing');
    setSemanticSearchIndexing(true);
    setSemanticSearchReady(false);
    
    try {
      // Clear existing documents first
      await semanticSearchRef.current.clearIndex();
      
      // Reindex project
      await semanticSearchRef.current.indexProject(projectPath, {
        onProgress: (progress) => {
          console.log('[SearchBar] Reindexing progress:', progress);
        }
      });
      setSemanticSearchReady(true);
      console.log('[SearchBar] Manual reindexing completed');
    } catch (error) {
      console.error('[SearchBar] Manual reindexing failed:', error);
    } finally {
      setSemanticSearchIndexing(false);
    }
  }, [projectPath, semanticSearchIndexing]);

  // Handle tab clicks (prevent blur)
  const handleTabClick = useCallback(async (newMode: SearchMode) => {
    // If switching to semantic search and it's not ready, trigger indexing
    if (newMode === 'semantic' && !semanticSearchReady && semanticSearchRef.current && projectPath) {
      console.log('[SearchBar] Starting project indexing for semantic search');
      setSemanticSearchIndexing(true);
      
      try {
        await semanticSearchRef.current.indexProject(projectPath, {
          onProgress: (progress) => {
            console.log('[SearchBar] Indexing progress:', progress);
            // You could show progress in UI here
          }
        });
        setSemanticSearchReady(true);
        console.log('[SearchBar] Project indexing completed');
      } catch (error) {
        console.error('[SearchBar] Project indexing failed:', error);
      } finally {
        setSemanticSearchIndexing(false);
      }
    }

    setMode(newMode);
    // Keep search expanded when switching tabs
    if (query.trim() !== '') {
      setIsExpanded(true);
    }
    // Request focus with USER priority for tab interaction, then focus input
    requestFocus(FocusableComponent.SEARCH_BAR, 'search-tab-click', FocusPriority.USER);
    setTimeout(() => {
      searchInputRef.current?.focus();
    }, 0);
  }, [query, semanticSearchReady, projectPath]); // Added dependencies

  // Handle input blur with delay to allow tab clicks
  const handleInputBlur = useCallback((e: React.FocusEvent) => {
    // Check if blur is due to clicking within search content
    const searchContent = searchContentRef.current;
    if (searchContent && e.relatedTarget && searchContent.contains(e.relatedTarget as Node)) {
      // Don't blur if clicking within search content (tabs, results, etc.)
      return;
    }
    
    // Delay blur to allow for potential tab clicks
    setTimeout(() => {
      setIsFocused(false);
    }, 150);
  }, []);

  // Render preview with highlighted matches
  const renderPreview = useCallback((result: SearchResult) => {
    if (result.type === 'filename') {
      return null; // Don't show preview for filename results
    }

    if (result.type === 'symbol') {
      // Render symbol signature
      return (
        <ResultPreview style={{ color: 'var(--text-dim)' }}>
          {result.signature || result.preview || ''}
          {result.modifiers && result.modifiers.length > 0 && (
            <span style={{ color: 'var(--accent-blue)', marginLeft: '8px' }}>
              {result.modifiers.join(' ')}
            </span>
          )}
        </ResultPreview>
      );
    }

    if (result.preview && result.matchStart !== undefined && result.matchEnd !== undefined) {
      const { preview, matchStart, matchEnd } = result;
      const before = preview.substring(0, matchStart);
      const match = preview.substring(matchStart, matchEnd);
      const after = preview.substring(matchEnd);

      return (
        <ResultPreview>
          {before}
          <ResultMatch>{match}</ResultMatch>
          {after}
        </ResultPreview>
      );
    }

    return <ResultPreview>{result.preview || result.match || ''}</ResultPreview>;
  }, []);

  // Always render the search bar, visibility is controlled by CSS

  return (
    <SearchBarContainer $isVisible={isVisible}>
      <SearchInputWrapper>
        <SearchInputRow>
          <SearchInput
            ref={searchInputRef}
            type="text"
            placeholder={
              mode === 'structure' 
                ? "Search functions, classes, types..." 
                : mode === 'semantic'
                ? "Ask questions about your code..."
                : "Search files and content..."
            }
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            onKeyDown={handleKeyDown}
            onFocus={() => {
              setIsFocused(true);
              // Request focus with USER priority when user directly clicks/focuses the input
              requestFocus(FocusableComponent.SEARCH_BAR, 'search-input-focus', FocusPriority.USER);
            }}
            onBlur={handleInputBlur}
            $mode={mode}
          />
          
          <SearchOptions>
            <OptionButton
              $active={caseSensitive}
              onClick={() => setCaseSensitive(!caseSensitive)}
              title="Match Case"
            >
              Aa
            </OptionButton>
            
            {mode === 'content' && (
              <>
                <OptionButton
                  $active={wholeWord}
                  onClick={() => setWholeWord(!wholeWord)}
                  title="Match Whole Word"
                >
                  Ab
                </OptionButton>
                <OptionButton
                  $active={useRegex}
                  onClick={() => setUseRegex(!useRegex)}
                  title="Use Regular Expression"
                >
                  .*
                </OptionButton>
              </>
            )}
          </SearchOptions>
        </SearchInputRow>

        <SearchContent ref={searchContentRef} $expanded={isExpanded}>
          <TabsContainer $hasQuery={query.trim() !== ''}>
            <Tab
              $active={mode === 'files'}
              onClick={() => handleTabClick('files')}
            >
              <TabIcon><FolderIcon /></TabIcon>
              Files
            </Tab>
            <Tab
              $active={mode === 'content'}
              onClick={() => handleTabClick('content')}
            >
              <TabIcon>{getFileIconByType('file')}</TabIcon>
              Content
            </Tab>
            <Tab
              $active={mode === 'structure'}
              onClick={() => handleTabClick('structure')}
            >
              <TabIcon>⚡</TabIcon>
              Structure
            </Tab>
            <Tab
              $active={mode === 'semantic'}
              onClick={() => handleTabClick('semantic')}
              title={semanticSearchReady ? 'AI-powered semantic search' : 'Semantic search (needs indexing)'}
              style={{ 
                opacity: semanticSearchReady ? 1 : 0.6,
                position: 'relative'
              }}
            >
              <TabIcon>🧠</TabIcon>
              Semantic
              {semanticSearchIndexing && (
                <span style={{ 
                  position: 'absolute', 
                  top: '-2px', 
                  right: '-2px', 
                  fontSize: '8px', 
                  animation: 'spin 1s linear infinite' 
                }}>⟳</span>
              )}
            </Tab>
            
            {/* Show reindex button when semantic search is active or ready */}
            {(mode === 'semantic' || semanticSearchReady) && (
              <IndexButton
                $isIndexing={semanticSearchIndexing}
                onClick={handleReindex}
                title={semanticSearchIndexing ? 'Indexing in progress...' : 'Reindex project documentation'}
              >
                {semanticSearchIndexing ? (
                  <>
                    <span style={{ animation: 'spin 1s linear infinite' }}>⟳</span>
                    Indexing...
                  </>
                ) : (
                  <>
                    🔄
                    Reindex
                  </>
                )}
              </IndexButton>
            )}
          </TabsContainer>

        {query.trim() && (
          <ResultsContainer>
            {isSearching ? (
              <NoResults>Searching...</NoResults>
            ) : results.length === 0 ? (
              <NoResults>No results found for "{query}"</NoResults>
            ) : (
              <>
                {results.map((result, index) => (
                  <ResultItem
                    key={result.id}
                    $selected={index === selectedIndex}
                    onClick={() => handleResultClick(result)}
                    onMouseEnter={() => setSelectedIndex(index)}
                  >
                    <ResultHeader>
                      <FileIcon>{getFileIcon(result.file)}</FileIcon>
                      <ResultFile>
                        {result.type === 'filename' ? (
                          result.name || result.file
                        ) : result.type === 'symbol' ? (
                          <>
                            <span style={{ color: 'var(--accent-purple)', fontWeight: 'bold' }}>
                              {getSymbolIcon(result.kind)} {result.name}
                            </span>
                            {' in '}
                            {result.file}
                            {result.line && `:${result.line}`}
                          </>
                        ) : result.type === 'semantic' ? (
                          <>
                            <span style={{ color: 'var(--accent-blue)', fontWeight: 'bold' }}>
                              🧠 {result.confidence}% match
                            </span>
                            {' in '}
                            {result.file}
                            {result.line && `:${result.line}`}
                            {result.documentType && (
                              <span style={{ color: 'var(--text-dim)', fontSize: '0.9em', marginLeft: '8px' }}>
                                ({result.documentType})
                              </span>
                            )}
                          </>
                        ) : (
                          <>
                            {result.file}
                            {result.line && `:${result.line}`}
                            {result.column && `:${result.column}`}
                          </>
                        )}
                      </ResultFile>
                    </ResultHeader>
                    {renderPreview(result)}
                  </ResultItem>
                ))}
                
                <StatusInfo>
                  <span>{results.length} result{results.length !== 1 ? 's' : ''}</span>
                  <span>↵ open • ↑↓ navigate • ESC close</span>
                </StatusInfo>
              </>
            )}
          </ResultsContainer>
          )}
          </SearchContent>
          </SearchInputWrapper>
          </SearchBarContainer>
  );
};

export default SearchBar;