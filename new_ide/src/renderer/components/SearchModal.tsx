import React, { useState, useEffect, useRef, useCallback } from 'react';
import styled from 'styled-components';
import { fontFamilies } from '../utils/fontConfig';
import { useFocusContext, FocusableComponent, FocusPriority } from '../contexts/FocusContext';
import '../styles/modal.css';

export interface SearchResult {
  id: string;
  file: string;
  line: number;
  column: number;
  match: string;
  preview: string;
  matchStart: number;
  matchEnd: number;
}

interface SearchModalProps {
  isOpen: boolean;
  onClose: () => void;
  onNavigateToFile: (file: string, line?: number, column?: number) => void;
  projectPath?: string;
}

const SearchModalContainer = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: flex-start;
  justify-content: center;
  padding-top: 100px;
  z-index: 1000;
`;

const SearchModalContent = styled.div`
  background-color: #252526;
  border: 1px solid #464647;
  border-radius: 6px;
  width: 800px;
  max-width: 90vw;
  max-height: 70vh;
  display: flex;
  flex-direction: column;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
`;

const SearchHeader = styled.div`
  padding: 16px 20px;
  border-bottom: 1px solid #464647;
  display: flex;
  align-items: center;
  gap: 12px;
`;

const SearchInput = styled.input`
  flex: 1;
  background-color: #3c3c3c;
  border: 1px solid #464647;
  border-radius: 4px;
  padding: 8px 12px;
  color: #cccccc;
  font-family: ${fontFamilies.ui};
  font-size: 14px;
  outline: none;

  &:focus {
    border-color: #007acc;
    box-shadow: 0 0 0 1px #007acc;
  }

  &::placeholder {
    color: #8a8a8a;
  }
`;

const SearchOptions = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
`;

const OptionButton = styled.button<{ active?: boolean }>`
  background-color: ${props => props.active ? '#007acc' : '#404040'};
  border: 1px solid ${props => props.active ? '#007acc' : '#5a5a5a'};
  border-radius: 3px;
  color: ${props => props.active ? '#ffffff' : '#cccccc'};
  padding: 4px 8px;
  font-size: 11px;
  cursor: pointer;
  font-family: ${fontFamilies.ui};
  transition: all 0.2s ease;

  &:hover {
    background-color: ${props => props.active ? '#106ba3' : '#4a4a4a'};
  }
`;

const ResultsContainer = styled.div`
  flex: 1;
  overflow-y: auto;
  padding: 8px 0;
`;

const LoadingMessage = styled.div`
  padding: 20px;
  text-align: center;
  color: #8a8a8a;
  font-family: ${fontFamilies.ui};
  font-size: 14px;
`;

const NoResultsMessage = styled.div`
  padding: 20px;
  text-align: center;
  color: #8a8a8a;
  font-family: ${fontFamilies.ui};
  font-size: 14px;
`;

const ResultItem = styled.div`
  padding: 8px 20px;
  cursor: pointer;
  border-bottom: 1px solid #333;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: #2a2a2a;
  }

  &:last-child {
    border-bottom: none;
  }
`;

const ResultFile = styled.div`
  font-family: ${fontFamilies.ui};
  font-size: 12px;
  color: #8a8a8a;
  margin-bottom: 4px;
`;

const ResultPreview = styled.div`
  font-family: ${fontFamilies.code};
  font-size: 13px;
  color: #cccccc;
  line-height: 1.4;
  white-space: pre-wrap;
  word-break: break-all;
`;

const ResultMatch = styled.span`
  background-color: #4a4a00;
  color: #ffff88;
  padding: 0 2px;
  border-radius: 2px;
`;

const StatusBar = styled.div`
  padding: 8px 20px;
  border-top: 1px solid #464647;
  background-color: #1e1e1e;
  font-family: ${fontFamilies.ui};
  font-size: 11px;
  color: #8a8a8a;
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

const SearchModal: React.FC<SearchModalProps> = ({
  isOpen,
  onClose,
  onNavigateToFile,
  projectPath
}) => {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState<SearchResult[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [caseSensitive, setCaseSensitive] = useState(false);
  const [useRegex, setUseRegex] = useState(false);
  const [wholeWord, setWholeWord] = useState(false);
  const searchInputRef = useRef<HTMLInputElement>(null);
  const searchTimeoutRef = useRef<NodeJS.Timeout>();
  
  // Get focus management
  const { requestFocus, lockFocus, releaseFocusLock } = useFocusContext();
  const focusLockIdRef = useRef<string | null>(null);

  // Focus input when modal opens and lock focus to search
  useEffect(() => {
    if (isOpen && searchInputRef.current) {
      // Lock focus to search bar with USER priority (since Cmd+F is user-initiated)
      const lockId = lockFocus(FocusableComponent.SEARCH_BAR, 'search-modal-open', 'SearchModal', 5000);
      focusLockIdRef.current = lockId;
      
      // Request focus for search bar
      requestFocus(FocusableComponent.SEARCH_BAR, 'search-modal-focus', FocusPriority.USER);
      
      setTimeout(() => {
        searchInputRef.current?.focus();
      }, 100);
    } else if (!isOpen && focusLockIdRef.current) {
      // Release focus lock when modal closes
      releaseFocusLock(focusLockIdRef.current, 'SearchModal');
      focusLockIdRef.current = null;
    }
  }, [isOpen, requestFocus, lockFocus, releaseFocusLock]);

  // Cleanup focus lock on unmount
  useEffect(() => {
    return () => {
      if (focusLockIdRef.current) {
        releaseFocusLock(focusLockIdRef.current, 'SearchModal');
      }
    };
  }, [releaseFocusLock]);

  // Clear results when modal closes
  useEffect(() => {
    if (!isOpen) {
      setQuery('');
      setResults([]);
      setIsSearching(false);
    }
  }, [isOpen]);

  // Debounced search function
  const performSearch = useCallback(async (searchQuery: string) => {
    if (!searchQuery.trim() || !projectPath) {
      setResults([]);
      setIsSearching(false);
      return;
    }

    setIsSearching(true);

    try {
      // Use the file explorer API to search
      const searchResults = await window.electronAPI?.fileExplorer?.searchInFiles?.({
        query: searchQuery,
        path: projectPath,
        caseSensitive,
        useRegex,
        wholeWord
      });

      if (searchResults?.success) {
        setResults(searchResults.results || []);
      } else {
        console.error('Search failed:', searchResults?.error);
        setResults([]);
      }
    } catch (error) {
      console.error('Search error:', error);
      setResults([]);
    } finally {
      setIsSearching(false);
    }
  }, [projectPath, caseSensitive, useRegex, wholeWord]);

  // Handle query changes with debouncing
  useEffect(() => {
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    searchTimeoutRef.current = setTimeout(() => {
      performSearch(query);
    }, 300);

    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, [query, performSearch]);

  // Handle keyboard shortcuts
  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      onClose();
    } else if (e.key === 'Enter' && results.length > 0) {
      // Navigate to first result
      const first = results[0];
      onNavigateToFile(first.file, first.line, first.column);
      onClose();
    }
  }, [onClose, onNavigateToFile, results]);

  // Handle result click
  const handleResultClick = useCallback((result: SearchResult) => {
    onNavigateToFile(result.file, result.line, result.column);
    onClose();
  }, [onNavigateToFile, onClose]);

  // Render preview with highlighted matches
  const renderPreview = useCallback((result: SearchResult) => {
    const { preview, matchStart, matchEnd } = result;
    const before = preview.substring(0, matchStart);
    const match = preview.substring(matchStart, matchEnd);
    const after = preview.substring(matchEnd);

    return (
      <ResultPreview>
        {before}
        <ResultMatch>{match}</ResultMatch>
        {after}
      </ResultPreview>
    );
  }, []);

  if (!isOpen) return null;

  return (
    <SearchModalContainer onClick={onClose}>
      <SearchModalContent onClick={(e) => e.stopPropagation()}>
        <SearchHeader>
          <SearchInput
            ref={searchInputRef}
            type="text"
            placeholder="Search across files..."
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            onKeyDown={handleKeyDown}
          />
          <SearchOptions>
            <OptionButton
              active={caseSensitive}
              onClick={() => setCaseSensitive(!caseSensitive)}
              title="Match Case"
            >
              Aa
            </OptionButton>
            <OptionButton
              active={wholeWord}
              onClick={() => setWholeWord(!wholeWord)}
              title="Match Whole Word"
            >
              Ab
            </OptionButton>
            <OptionButton
              active={useRegex}
              onClick={() => setUseRegex(!useRegex)}
              title="Use Regular Expression"
            >
              .*
            </OptionButton>
          </SearchOptions>
        </SearchHeader>

        <ResultsContainer>
          {isSearching ? (
            <LoadingMessage>Searching...</LoadingMessage>
          ) : query.trim() && results.length === 0 ? (
            <NoResultsMessage>No results found for "{query}"</NoResultsMessage>
          ) : (
            results.map((result) => (
              <ResultItem
                key={result.id}
                onClick={() => handleResultClick(result)}
              >
                <ResultFile>
                  {result.file}:{result.line}:{result.column}
                </ResultFile>
                {renderPreview(result)}
              </ResultItem>
            ))
          )}
        </ResultsContainer>

        <StatusBar>
          <span>
            {query.trim() ? `${results.length} results` : 'Enter search term'}
          </span>
          <span>
            Press Enter to navigate to first result • ESC to close
          </span>
        </StatusBar>
      </SearchModalContent>
    </SearchModalContainer>
  );
};

export default SearchModal;