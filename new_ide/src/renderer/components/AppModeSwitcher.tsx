import React from 'react';
import '../styles/app-mode-switcher.css';

interface AppModeSwitcherProps {
  currentMode: 'ide' | 'voice-agent';
  onModeChange: (mode: 'ide' | 'voice-agent') => void;
}

const AppModeSwitcher: React.FC<AppModeSwitcherProps> = ({ currentMode, onModeChange }) => {
  return (
    <div className="app-mode-switcher">
      <div className="app-mode-switcher-content">
        <button 
          className={`app-mode-btn ${currentMode === 'ide' ? 'active' : ''}`}
          onClick={() => onModeChange('ide')}
          title="Switch to IDE (Cmd+1)"
        >
          💻 IDE
        </button>
        <button 
          className={`app-mode-btn ${currentMode === 'voice-agent' ? 'active' : ''}`}
          onClick={() => onModeChange('voice-agent')}
          title="Switch to Voice Agent (Cmd+2)"
        >
          🎤 Voice
        </button>
      </div>
      <div className="app-mode-shortcut-hint">
        Cmd+1 IDE • Cmd+2 Voice
      </div>
    </div>
  );
};

export default AppModeSwitcher;