import React, { ReactNode } from 'react';
import { useClipboard } from '../hooks/useClipboard';

interface ClipboardContainerProps {
  children: ReactNode;
  className?: string;
  style?: React.CSSProperties;
  [key: string]: any; // Allow other props to pass through
}

/**
 * Higher-order component that adds clipboard functionality to any container
 * Automatically handles Ctrl+C/Cmd+C for selected text
 * Usage: <ClipboardContainer><YourContent /></ClipboardContainer>
 */
const ClipboardContainer: React.FC<ClipboardContainerProps> = ({ 
  children, 
  className = '', 
  style = {},
  ...otherProps 
}) => {
  const { createUniversalKeyboardHandler } = useClipboard();
  const handleKeyDown = createUniversalKeyboardHandler();

  return (
    <div
      className={className}
      style={{ outline: 'none', ...style }}
      onKeyDown={handleKeyDown}
      tabIndex={0}
      {...otherProps}
    >
      {children}
    </div>
  );
};

export default ClipboardContainer;