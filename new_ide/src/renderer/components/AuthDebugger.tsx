/**
 * AuthDebugger.tsx
 * 
 * Debug component to display current authentication state
 */

import React, { useEffect, useState } from 'react';
import { useAuth } from '../contexts/AuthContext';

export const AuthDebugger: React.FC = () => {
  const { isAuthenticated, isLoading, user } = useAuth();
  const [token, setToken] = useState<string | null>(null);

  useEffect(() => {
    // Monitor localStorage token changes
    const checkToken = () => {
      const currentToken = localStorage.getItem('token');
      setToken(currentToken);
    };

    checkToken();
    
    // Check token every 500ms for debugging
    const interval = setInterval(checkToken, 500);
    
    return () => clearInterval(interval);
  }, []);

  return (
    <div style={{
      position: 'fixed',
      top: 10,
      right: 10,
      background: 'rgba(0,0,0,0.8)',
      color: 'white',
      padding: '10px',
      borderRadius: '5px',
      fontSize: '12px',
      zIndex: 9999,
      fontFamily: 'monospace'
    }}>
      {/* Auth Debug
      <div><strong>Auth Debug</strong></div>
      <div>Loading: {isLoading ? 'Yes' : 'No'}</div>
      <div>Authenticated: {isAuthenticated ? 'Yes' : 'No'}</div>
      <div>User ID: {user?.id || 'None'}</div>
      <div>Token: {token ? `${token.substring(0, 10)}...` : 'None'}</div>
      <div>Token Length: {token?.length || 0}</div>
       */}
    </div>
    
  );
};

export default AuthDebugger;