/* Sketch Canvas Styles */

.sketch-canvas-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  background-color: var(--bg-editor, #1a1a1a);
  position: relative;
  min-height: 0;
}

.sketch-canvas-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--text-default);
}

.sketch-loading-spinner {
  font-size: 3rem;
  margin-bottom: 1rem;
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 0.7; }
  50% { opacity: 1; }
}

.sketch-canvas-toolbar {
  display: flex;
  gap: 0.5rem;
  padding: 1rem;
  background-color: rgba(0, 0, 0, 0.5);
  border-bottom: 1px solid var(--border-default);
  flex-wrap: wrap;
  align-items: center;
}

.sketch-btn {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  border-radius: 6px;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.375rem;
  font-family: var(--font-sans);
  white-space: nowrap;
}

.sketch-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.sketch-btn-primary {
  background-color: var(--accent-purple);
  color: var(--text-header);
}

.sketch-btn-primary:hover:not(:disabled) {
  background-color: var(--primary-accent-hover, #7B2CBF);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px var(--overlay-accent);
}

.sketch-btn-secondary {
  background-color: transparent;
  color: var(--text-default);
  border: 1px solid var(--border-default);
}

.sketch-btn-secondary:hover:not(:disabled) {
  border-color: var(--accent-purple);
  color: var(--accent-purple);
  background-color: rgba(139, 69, 19, 0.1);
}

.sketch-btn-danger {
  background-color: transparent;
  color: var(--text-error);
  border: 1px solid var(--text-error);
}

.sketch-btn-danger:hover:not(:disabled) {
  background-color: var(--text-error);
  color: var(--text-header);
}

.sketch-canvas-wrapper {
  flex: 1;
  height: 600px; /* Give explicit height */
  min-height: 500px;
  position: relative;
  width: 100%;
  overflow: visible; /* Allow Excalidraw content to show */
  pointer-events: auto;
  background: #1a1a1a; /* Ensure background is visible */
}

/* Override Excalidraw styles to match our theme */
.sketch-canvas-wrapper .excalidraw {
  --color-primary: var(--accent-purple);
  --color-primary-darker: var(--primary-accent-hover, #7B2CBF);
  --color-primary-darkest: #5A1A8B;
  --color-surface-low: var(--bg-sidebar);
  --color-surface-lowest: var(--bg-editor);
  height: 100% !important;
  width: 100% !important;
  position: relative !important;
  background: #1a1a1a !important;
}

/* Ensure the Excalidraw canvas is visible and interactive */
.sketch-canvas-wrapper .excalidraw canvas,
.sketch-canvas-wrapper .excalidraw .excalidraw__canvas {
  background: #1a1a1a !important;
  position: relative !important;
  z-index: 1 !important;
  pointer-events: auto !important;
  display: block !important;
}

/* Fix Excalidraw's tool panel to be horizontal at the top */
.sketch-canvas-wrapper .excalidraw .App-toolbar {
  /* This targets the main toolbar container */
  display: flex !important;
  flex-direction: row !important;
  justify-content: flex-start !important;
  align-items: center !important;
  flex-wrap: wrap !important;
  padding: 8px !important;
  background: rgba(30, 30, 30, 0.95) !important;
  backdrop-filter: blur(10px) !important;
  border-bottom: 1px solid var(--border-default) !important;
  height: auto !important;
  width: 100% !important;
}

/* Fix the left panel/shapes toolbar specifically */
.sketch-canvas-wrapper .excalidraw .App-menu_top .shapes-panel,
.sketch-canvas-wrapper .excalidraw .shapes-title,
.sketch-canvas-wrapper .excalidraw .Stack_tools {
  /* Force horizontal layout */
  display: flex !important;
  flex-direction: row !important;
  flex-wrap: wrap !important;
  gap: 4px !important;
  width: auto !important;
  max-width: none !important;
}

/* Hide the "Shapes" title if it exists */
.sketch-canvas-wrapper .excalidraw .shapes-title,
.sketch-canvas-wrapper .excalidraw h2:has-text("Shapes") {
  display: none !important;
}

/* Fix the panel that contains shape tools */
.sketch-canvas-wrapper .excalidraw .Island,
.sketch-canvas-wrapper .excalidraw .App-menu_top {
  position: relative !important;
  display: inline-flex !important;
  flex-direction: row !important;
  flex-wrap: wrap !important;
  width: auto !important;
  max-width: none !important;
  height: auto !important;
  margin: 0 !important;
  padding: 4px !important;
  background: transparent !important;
}

/* Tool icons/buttons styling */
.sketch-canvas-wrapper .excalidraw .ToolIcon {
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 36px !important;
  height: 36px !important;
  min-width: 36px !important;
  min-height: 36px !important;
  margin: 2px !important;
  padding: 0 !important;
  flex-shrink: 0 !important;
  background: rgba(255, 255, 255, 0.05) !important;
  border-radius: 6px !important;
  border: 1px solid transparent !important;
  transition: all 0.2s ease !important;
}

.sketch-canvas-wrapper .excalidraw .ToolIcon:hover {
  background: rgba(255, 255, 255, 0.1) !important;
  border-color: var(--accent-purple) !important;
}

.sketch-canvas-wrapper .excalidraw .ToolIcon--selected,
.sketch-canvas-wrapper .excalidraw .ToolIcon.active {
  background: var(--accent-purple) !important;
  border-color: var(--accent-purple) !important;
}

/* Tool icon content */
.sketch-canvas-wrapper .excalidraw .ToolIcon__icon {
  width: 20px !important;
  height: 20px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* Debug: ensure canvas is clickable */
.sketch-canvas-wrapper .excalidraw .excalidraw-container {
  position: relative;
  height: 100%;
  width: 100%;
}

/* Ensure the canvas element itself is not blocked */
.sketch-canvas-wrapper canvas {
  cursor: crosshair !important;
}

/* Remove any blocking overlays */
.sketch-canvas-wrapper .excalidraw .excalidraw-overlay,
.sketch-canvas-wrapper .excalidraw .excalidraw-loading {
  pointer-events: none !important;
}

/* Fix any mobile/responsive toolbar issues */
.sketch-canvas-wrapper .excalidraw .mobile-misc-tools-container {
  display: none !important;
}

/* Force desktop layout for Excalidraw */
.sketch-canvas-wrapper .excalidraw .App-mobile,
.sketch-canvas-wrapper .excalidraw.excalidraw--mobile {
  /* Override mobile styles */
}

/* Remove global pointer-events rule that might interfere */

/* Target the vertical shapes panel specifically and make it horizontal */
.sketch-canvas-wrapper .excalidraw [aria-label="Shapes"],
.sketch-canvas-wrapper .excalidraw [role="radiogroup"],
.sketch-canvas-wrapper .excalidraw .Stack__horizontal {
  display: flex !important;
  flex-direction: row !important;
  flex-wrap: wrap !important;
  width: auto !important;
  height: auto !important;
  max-width: 100% !important;
  gap: 4px !important;
}

/* Target the shapes panel that appears on the left side in your screenshot */
.sketch-canvas-wrapper .excalidraw .App-menu__left {
  position: relative !important;
  display: flex !important;
  flex-direction: row !important;
  flex-wrap: wrap !important;
  width: auto !important;
  max-width: none !important;
  height: auto !important;
  background: rgba(30, 30, 30, 0.95) !important;
  border-radius: 8px !important;
  padding: 8px !important;
  margin: 8px !important;
}

/* Fix the shapes panel Stack component */
.sketch-canvas-wrapper .excalidraw .Stack.Stack_vertical {
  flex-direction: row !important;
  flex-wrap: wrap !important;
  width: 100% !important;
  gap: 4px !important;
}

/* Ensure the main menu is horizontal */
.sketch-canvas-wrapper .excalidraw .App-menu {
  display: flex !important;
  flex-direction: row !important;
  background: rgba(30, 30, 30, 0.95) !important;
  padding: 8px !important;
  border-bottom: 1px solid var(--border-default) !important;
}

.sketch-canvas-instructions {
  padding: 0.75rem 1rem;
  background-color: rgba(0, 0, 0, 0.3);
  border-top: 1px solid var(--border-default);
  font-size: 0.75rem;
  color: var(--text-dim);
  /* Ensure it doesn't get covered by content */
  position: relative;
  z-index: 10;
}

.sketch-canvas-instructions p {
  margin: 0.25rem 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.sketch-canvas-instructions strong {
  color: var(--text-default);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .sketch-canvas-toolbar {
    padding: 0.75rem;
    gap: 0.375rem;
  }

  .sketch-btn {
    padding: 0.375rem 0.75rem;
    font-size: 0.75rem;
  }

  .sketch-canvas-wrapper {
    height: calc(100% - 120px);
  }

  .sketch-canvas-instructions {
    padding: 0.5rem;
    font-size: 0.625rem;
  }
}

@media (max-width: 480px) {
  .sketch-canvas-toolbar {
    flex-direction: column;
    align-items: stretch;
  }

  .sketch-btn {
    justify-content: center;
  }

  .sketch-canvas-instructions p {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }
}