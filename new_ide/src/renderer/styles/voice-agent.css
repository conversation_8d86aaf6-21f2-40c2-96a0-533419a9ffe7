/* Voice Agent Styles - Using KAPI Brand Colors */

.voice-agent-container {
  height: 100vh;
  width: 100vw;
  background-color: var(--bg-editor);
  color: var(--text-default);
  overflow: hidden;
  font-family: var(--font-sans);
  outline: none; /* Remove focus outline */
}

/* Panel Styling */
.voice-agent-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: var(--bg-assistance);
}

.voice-agent-file-panel {
  background-color: var(--bg-sidebar);
  border-right: 1px solid var(--border-default);
}

.voice-agent-panel-header {
  padding: 1rem 1.25rem;
  border-bottom: 1px solid var(--border-separator);
  background-color: rgba(0, 0, 0, 0.2);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.voice-agent-panel-header h3 {
  margin: 0;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-bright);
  letter-spacing: 0.5px;
  text-transform: uppercase;
}

.voice-agent-file-explorer {
  flex: 1;
  overflow-y: auto;
  padding: 0.5rem;
}

/* Main Agent Panel */
.voice-agent-agent-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: var(--bg-editor);
}

/* Header Section */
.voice-agent-header {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 1.5rem 2rem;
  border-bottom: 1px solid var(--border-separator);
  background-color: rgba(0, 0, 0, 0.3);
  text-align: center;
}

.voice-agent-header-left {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  align-items: center;
}

.voice-agent-title {
  margin: 0;
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--text-header);
  font-family: var(--font-sans);
  letter-spacing: -0.75px;
  text-align: center;
}

.voice-agent-subtitle {
  font-size: 1rem;
  color: var(--text-dim);
  font-weight: 400;
  font-family: var(--font-sans);
  text-align: center;
  opacity: 0.8;
  max-width: 600px;
  line-height: 1.4;
}

/* Canvas Task Input Section */
.canvas-task-input-section {
  padding: 1rem 2rem 0.5rem 2rem;
  border-bottom: 1px solid var(--border-separator);
  background-color: rgba(0, 0, 0, 0.1);
}

.canvas-task-input-container {
  display: flex;
  gap: 0.75rem;
  align-items: center;
  margin-bottom: 0.75rem;
}

.canvas-task-input {
  flex: 1;
  padding: 0.75rem 1rem;
  background-color: var(--bg-sidebar);
  border: 1px solid var(--border-default);
  border-radius: 8px;
  color: var(--text-default);
  font-size: 0.875rem;
  font-family: var(--font-sans);
  transition: all 0.2s ease;
  outline: none;
  min-height: 20px;
}

.canvas-task-input:focus {
  border-color: var(--border-focus);
  background-color: var(--bg-input-focus);
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.canvas-task-input:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background-color: var(--bg-disabled);
}

.canvas-task-input::placeholder {
  color: var(--text-dim);
  opacity: 0.7;
}

.canvas-task-send-btn {
  padding: 0.75rem;
  background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
  border: none;
  border-radius: 8px;
  color: white;
  font-size: 1.1rem;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.canvas-task-send-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
  background: linear-gradient(135deg, #7c3aed, #3b82f6);
}

.canvas-task-send-btn:active:not(:disabled) {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.canvas-task-send-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.canvas-task-send-btn.processing {
  background: linear-gradient(135deg, #6b7280, #9ca3af);
}

.canvas-task-examples {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  align-items: center;
  font-size: 0.8125rem;
}

.task-examples-label {
  color: var(--text-dim);
  font-weight: 500;
  margin-right: 0.5rem;
  white-space: nowrap;
}

.task-example-btn {
  padding: 0.375rem 0.75rem;
  background-color: var(--bg-sidebar);
  border: 1px solid var(--border-subtle);
  border-radius: 12px;
  color: var(--text-default);
  font-size: 0.75rem;
  font-family: var(--font-sans);
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
}

.task-example-btn:hover:not(:disabled) {
  background-color: var(--bg-input-focus);
  border-color: var(--border-focus);
  color: var(--text-bright);
  transform: translateY(-1px);
}

.task-example-btn:active:not(:disabled) {
  transform: translateY(0);
  background-color: var(--accent-primary);
  color: white;
}

.task-example-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Loading spinner for small buttons */
.loading-spinner.small {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Status Indicator */
.voice-agent-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 20px;
}

.voice-agent-status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  transition: background-color 0.3s ease;
}

.voice-agent-status-text {
  font-size: 0.875rem;
  color: var(--text-default);
  text-transform: capitalize;
}

/* Central Interaction Area */
.voice-agent-interaction-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 2rem;
  padding: 2rem;
  position: relative;
  /* Allow full height for sketch mode */
  min-height: 0;
}

/* Sketch mode specific layout - REMOVED, see below */

/* Purple Orb */
.voice-agent-orb-container {
  position: relative;
  width: 180px;
  height: 180px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.voice-agent-orb {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.voice-agent-orb-inner {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background: radial-gradient(circle at 30% 30%, var(--primary-accent-hover), var(--accent-purple));
  box-shadow: 0 0 60px var(--overlay-accent),
              inset 0 0 20px rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.voice-agent-orb-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 160px;
  height: 160px;
  border-radius: 50%;
  background: radial-gradient(circle, var(--overlay-accent), transparent 70%);
  filter: blur(20px);
  opacity: 0.6;
}

/* Pulse Rings for Listening State */
.voice-agent-pulse-ring {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border-radius: 50%;
  border: 2px solid var(--accent-purple);
  opacity: 0;
  pointer-events: none;
}

.voice-agent-pulse-1 {
  animation: pulse-ring 2s ease-out infinite;
}

.voice-agent-pulse-2 {
  animation: pulse-ring 2s ease-out infinite 0.5s;
}

.voice-agent-pulse-3 {
  animation: pulse-ring 2s ease-out infinite 1s;
}

@keyframes pulse-ring {
  0% {
    width: 120px;
    height: 120px;
    opacity: 0.6;
  }
  100% {
    width: 200px;
    height: 200px;
    opacity: 0;
  }
}

/* Orb States */
.voice-agent-orb.listening .voice-agent-orb-inner {
  animation: gentle-pulse 2s ease-in-out infinite;
  box-shadow: 0 0 80px var(--overlay-accent-hover),
              inset 0 0 30px rgba(255, 255, 255, 0.2);
}

.voice-agent-orb.connecting .voice-agent-orb-inner {
  animation: spin 1.5s linear infinite;
}

.voice-agent-orb.error .voice-agent-orb-inner {
  background: radial-gradient(circle at 30% 30%, #ff6b6b, var(--text-error));
  animation: shake 0.5s ease-in-out;
}

@keyframes gentle-pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

/* Greeting Text */
.voice-agent-greeting {
  margin: 0;
  font-size: 2rem;
  font-weight: 600;
  color: var(--text-header);
  text-align: center;
  letter-spacing: -0.5px;
}

.voice-agent-subtext {
  margin: -1rem 0 0 0;
  font-size: 1rem;
  color: var(--text-dim);
  text-align: center;
}

/* Control Buttons */
.voice-agent-controls {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  align-items: center;
}

.voice-agent-main-controls {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  justify-content: center;
}

.voice-agent-button {
  padding: 0.75rem 2rem;
  font-size: 1rem;
  font-weight: 500;
  border-radius: 25px;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-family: var(--font-sans);
}

.voice-agent-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.voice-agent-button-primary {
  background-color: var(--accent-purple);
  color: var(--text-header);
}

.voice-agent-button-primary:hover:not(:disabled) {
  background-color: var(--primary-accent-hover);
  transform: translateY(-2px);
  box-shadow: 0 4px 20px var(--overlay-accent-hover);
}

.voice-agent-button-secondary {
  background-color: transparent;
  color: var(--text-default);
  border: 2px solid var(--border-default);
}

.voice-agent-button-secondary:hover:not(:disabled) {
  border-color: var(--accent-purple);
  color: var(--accent-purple);
}

.voice-agent-button-stop {
  background-color: var(--text-error);
  color: var(--text-header);
}

.voice-agent-button-stop:hover:not(:disabled) {
  background-color: #d32f2f;
  transform: translateY(-2px);
}

/* TTS Controls */
.voice-agent-tts-controls {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  align-items: center;
  padding: 1rem;
  background-color: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  border: 1px solid var(--border-default);
}

.voice-agent-tts-toggle {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  font-size: 0.9rem;
  color: var(--text-default);
}

.voice-agent-tts-toggle input[type="checkbox"] {
  width: 16px;
  height: 16px;
  accent-color: var(--accent-purple);
}

.voice-agent-voice-select {
  padding: 0.5rem 1rem;
  border-radius: 8px;
  border: 1px solid var(--border-default);
  background-color: var(--bg-input);
  color: var(--text-default);
  font-size: 0.85rem;
  min-width: 150px;
  cursor: pointer;
}

.voice-agent-voice-select:focus {
  outline: none;
  border-color: var(--accent-purple);
  box-shadow: 0 0 0 2px rgba(138, 43, 226, 0.2);
}

.voice-agent-voice-select:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.voice-agent-button-test {
  background-color: var(--accent-purple);
  color: var(--text-header);
  padding: 0.5rem 1rem;
  font-size: 0.85rem;
  border-radius: 20px;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.4rem;
  font-family: var(--font-sans);
  font-weight: 500;
}

.voice-agent-button-test:hover:not(:disabled) {
  background-color: var(--primary-accent-hover);
  transform: translateY(-1px);
  box-shadow: 0 2px 10px rgba(138, 43, 226, 0.3);
}

.voice-agent-button-test:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/* TTS Toolbar - Inline in header */
.voice-agent-tts-toolbar {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-top: 0.5rem;
  padding: 0.75rem 0;
}

.voice-agent-tts-toggle-inline {
  display: flex;
  align-items: center;
  gap: 0.4rem;
  cursor: pointer;
  font-size: 0.85rem;
  color: var(--text-dim);
  font-weight: 500;
}

.voice-agent-tts-toggle-inline input[type="checkbox"] {
  width: 14px;
  height: 14px;
  accent-color: var(--accent-purple);
}

.voice-agent-voice-select-inline {
  padding: 0.4rem 0.8rem;
  border-radius: 6px;
  border: 1px solid var(--border-default);
  background-color: var(--bg-input);
  color: var(--text-default);
  font-size: 0.8rem;
  min-width: 130px;
  cursor: pointer;
}

.voice-agent-voice-select-inline:focus {
  outline: none;
  border-color: var(--accent-purple);
  box-shadow: 0 0 0 2px rgba(138, 43, 226, 0.15);
}

.voice-agent-voice-select-inline:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.voice-agent-button-test-inline {
  background-color: var(--accent-purple);
  color: var(--text-header);
  padding: 0.4rem 0.8rem;
  font-size: 0.8rem;
  border-radius: 6px;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.3rem;
  font-family: var(--font-sans);
  font-weight: 500;
}

.voice-agent-button-test-inline:hover:not(:disabled) {
  background-color: var(--primary-accent-hover);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(138, 43, 226, 0.25);
}

.voice-agent-button-test-inline:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/* System Prompt Area - Removed for cleaner UI */

/* Bottom Area - Contains Connect button or Status Icons */
.voice-agent-bottom-area {
  position: absolute;
  bottom: 2rem;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* Status Icons */
.voice-agent-status-icons {
  display: flex;
  gap: 1.5rem;
}

.voice-agent-icon {
  font-size: 1.25rem;
  opacity: 0.3;
  transition: opacity 0.3s ease;
}

.voice-agent-icon.active {
  opacity: 1;
}

/* Status Panel */
.voice-agent-status-panel {
  background-color: var(--bg-sidebar);
  border-left: 1px solid var(--border-default);
}

.voice-agent-clear-button {
  padding: 0.25rem 0.75rem;
  font-size: 0.75rem;
  background-color: rgba(255, 255, 255, 0.1);
  border: 1px solid var(--border-default);
  border-radius: 4px;
  color: var(--text-dim);
  cursor: pointer;
  transition: all 0.2s ease;
}

.voice-agent-clear-button:hover {
  background-color: rgba(255, 255, 255, 0.15);
  color: var(--text-default);
}

/* Messages List */
.voice-agent-messages-list {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
  font-family: var(--font-mono);
  font-size: 0.875rem;
}

.voice-agent-empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--text-dim);
  font-style: italic;
}

.voice-agent-message {
  padding: 0.5rem 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  line-height: 1.5;
  word-wrap: break-word;
  position: relative;
  display: flex;
  align-items: flex-start;
  gap: 0.5rem;
}

.voice-agent-message-content {
  flex: 1;
  display: flex;
  align-items: flex-start;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.voice-agent-message-copy-btn {
  opacity: 0;
  transition: opacity 0.2s ease;
  background: var(--accent-purple);
  border: none;
  border-radius: 4px;
  color: var(--text-header);
  padding: 2px 6px;
  font-size: 0.75rem;
  cursor: pointer;
  flex-shrink: 0;
}

.voice-agent-message:hover .voice-agent-message-copy-btn {
  opacity: 1;
}

.voice-agent-message-copy-btn:hover {
  background: var(--accent-purple-hover, #7B2CBF);
}

.voice-agent-message:last-child {
  border-bottom: none;
}

.voice-agent-message-time {
  color: var(--accent-orange);
  font-size: 0.75rem;
  margin-right: 0.5rem;
}

.voice-agent-message-icon {
  margin-right: 0.5rem;
}

.voice-agent-message-text {
  color: var(--text-default);
  user-select: text;
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  cursor: text;
  white-space: pre-wrap;
}

.voice-agent-message-info .voice-agent-message-text {
  color: var(--text-default);
}

.voice-agent-message-success .voice-agent-message-text {
  color: #4caf50;
}

.voice-agent-message-error .voice-agent-message-text {
  color: var(--text-error);
}

.voice-agent-message-transcription .voice-agent-message-text {
  color: var(--accent-purple);
  font-style: italic;
}

.voice-agent-message-response .voice-agent-message-text {
  color: var(--text-bright);
}

/* Resize Handle */
.voice-agent-resize-handle {
  background-color: var(--border-separator);
  transition: background-color 0.2s ease;
}

.voice-agent-resize-handle:hover {
  background-color: var(--accent-purple);
  opacity: 0.5;
}

/* Scrollbar Styling */
.voice-agent-messages-list::-webkit-scrollbar,
.voice-agent-file-explorer::-webkit-scrollbar {
  width: 8px;
}

.voice-agent-messages-list::-webkit-scrollbar-track,
.voice-agent-file-explorer::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.2);
}

.voice-agent-messages-list::-webkit-scrollbar-thumb,
.voice-agent-file-explorer::-webkit-scrollbar-thumb {
  background: var(--border-default);
  border-radius: 4px;
}

.voice-agent-messages-list::-webkit-scrollbar-thumb:hover,
.voice-agent-file-explorer::-webkit-scrollbar-thumb:hover {
  background: var(--accent-purple);
  opacity: 0.5;
}

/* Mode Switcher */
.voice-agent-mode-switcher {
  position: absolute;
  bottom: 0.5rem;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 0.5rem;
  background-color: rgba(0, 0, 0, 0.3);
  border-radius: 25px;
  padding: 0.5rem;
  backdrop-filter: blur(10px);
  border: 1px solid var(--border-default);
  z-index: 100;
  pointer-events: auto;
}

.voice-agent-mode-btn {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  border-radius: 20px;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.375rem;
  font-family: var(--font-sans);
  background-color: transparent;
  color: var(--text-dim);
  min-width: 100px;
  justify-content: center;
}

.voice-agent-mode-btn:disabled {
  opacity: 0.4;
  cursor: not-allowed;
}

.voice-agent-mode-btn.active {
  background-color: var(--accent-purple);
  color: var(--text-header);
  box-shadow: 0 2px 10px var(--overlay-accent);
}

.voice-agent-mode-btn:not(.active):hover:not(:disabled) {
  background-color: rgba(255, 255, 255, 0.1);
  color: var(--text-default);
}

/* Sketch Mode Styles */
.sketch-mode-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
  gap: 1rem;
  padding: 1rem;
}

.voice-agent-sketch-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 2rem;
  gap: 2rem;
}

/* When SketchCanvas is active, ensure full height */
.voice-agent-interaction-area > .sketch-canvas-container {
  width: 100%;
  height: 100%;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.voice-agent-sketch-header {
  text-align: center;
}

.voice-agent-sketch-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  border: 2px dashed var(--border-default);
  border-radius: 12px;
  background-color: rgba(255, 255, 255, 0.02);
  min-height: 300px;
  min-width: 400px;
  text-align: center;
}

.voice-agent-sketch-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.7;
}

.voice-agent-sketch-placeholder p {
  margin: 0.5rem 0;
  color: var(--text-default);
}

.voice-agent-sketch-subtext {
  color: var(--text-dim);
  font-size: 0.875rem;
  font-style: italic;
}

.sketch-controls {
  display: flex;
  gap: 1rem;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  border: 1px solid var(--border-default);
  backdrop-filter: blur(10px);
  flex-wrap: wrap;
}

.sketch-help-text {
  color: var(--text-dim);
  font-size: 0.875rem;
  text-align: center;
  margin-left: 1rem;
  font-style: italic;
}

.voice-agent-sketch-controls {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  justify-content: center;
}

/* Responsive Adjustments */
@media (max-width: 1024px) {
  .voice-agent-orb-container {
    width: 150px;
    height: 150px;
  }
  
  .voice-agent-orb-inner {
    width: 100px;
    height: 100px;
  }
  
  .voice-agent-greeting {
    font-size: 1.5rem;
  }
  
  .voice-agent-button {
    padding: 0.5rem 1.5rem;
    font-size: 0.875rem;
  }

  .voice-agent-mode-switcher {
    bottom: 0.25rem;
    padding: 0.375rem;
  }

  .voice-agent-mode-btn {
    padding: 0.375rem 0.75rem;
    font-size: 0.75rem;
    min-width: 80px;
  }

  .voice-agent-sketch-container {
    padding: 1rem;
    gap: 1.5rem;
  }

  .voice-agent-sketch-placeholder {
    padding: 2rem;
    min-height: 200px;
    min-width: 300px;
  }

  .voice-agent-sketch-icon {
    font-size: 3rem;
  }
}

/* Loading Animation Styles */
.loading-spinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: var(--accent-purple);
  animation: spin 1s ease-in-out infinite;
  margin-left: 0.5rem;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Processing Animation for Button */
.voice-agent-button.processing {
  position: relative;
  overflow: hidden;
}

.voice-agent-button.processing::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* Pulsing dots animation for processing states */
.processing-dots::after {
  content: '';
  animation: dots 1.5s infinite;
}

@keyframes dots {
  0%, 20% {
    content: '';
  }
  40% {
    content: '.';
  }
  60% {
    content: '..';
  }
  80%, 100% {
    content: '...';
  }
}
