/* Simple Sketch Canvas Styles */

.simple-sketch-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  background-color: var(--bg-editor, #1a1a1a);
  position: relative;
}

.simple-sketch-toolbar {
  display: flex;
  gap: 1.5rem;
  padding: 1rem 1.5rem;
  background: linear-gradient(135deg, var(--bg-sidebar) 0%, var(--bg-assistance) 100%);
  border-bottom: 1px solid var(--border-separator);
  align-items: center;
  justify-content: flex-start;
  flex-wrap: nowrap;
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.3);
  width: 100%;
}

.simple-sketch-tool-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  align-items: flex-start;
  flex-shrink: 0;
  position: relative;
}

.tool-buttons-row {
  display: flex;
  gap: 0.375rem;
  align-items: center;
}

.simple-sketch-tool-group[data-group="colors"] .color-palette {
  display: flex;
  gap: 0.375rem;
  align-items: center;
}

.tool-group-label {
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--text-dim, #888);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 0.25rem;
  user-select: none;
}

.color-palette {
  display: flex;
  gap: 0.375rem;
  align-items: center;
}

.toolbar-divider {
  width: 1px;
  height: 50px;
  background: linear-gradient(to bottom, transparent 0%, rgba(255, 255, 255, 0.2) 50%, transparent 100%);
  margin: 0 0.5rem;
  flex-shrink: 0;
}

.toolbar-spacer {
  flex: 1;
}

.primary-actions {
  display: flex;
  gap: 0.375rem;
  align-items: flex-start;
  flex-direction: column;
}

.primary-actions .button-row {
  display: flex;
  gap: 0.375rem;
  align-items: center;
}

.primary-actions .sketch-tool-btn {
  width: 40px;
  height: 40px;
  padding: 0.625rem;
  font-size: inherit;
  display: flex;
  align-items: center;
  justify-content: center;
}

.sketch-tool-btn {
  padding: 0.625rem;
  background: rgba(255, 255, 255, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  color: var(--text-default);
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: var(--font-sans);
  flex-shrink: 0;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
}

.sketch-tool-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.sketch-tool-btn:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(99, 102, 241, 0.8);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4), 0 0 0 1px rgba(99, 102, 241, 0.3);
}

.sketch-tool-btn:hover::before {
  opacity: 1;
}

.sketch-tool-btn.active {
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  border-color: #6366f1;
  color: #ffffff;
  transform: translateY(-1px);
  box-shadow: 0 4px 20px rgba(99, 102, 241, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.sketch-tool-btn svg {
  width: 18px;
  height: 18px;
  stroke-width: 2.5;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

.sketch-icon-btn.processing {
  position: relative;
  overflow: hidden;
}

.sketch-icon-btn.processing::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  animation: shimmer 2s infinite;
}

.sketch-icon-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

.sketch-icon-btn:disabled:hover {
  transform: none !important;
  box-shadow: none !important;
}

.sketch-action-btn {
  padding: 0.625rem 1rem;
  font-size: 0.875rem;
  background: rgba(255, 255, 255, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  color: var(--text-default);
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  font-family: var(--font-sans);
  font-weight: 500;
  flex-shrink: 0;
  backdrop-filter: blur(10px);
  white-space: nowrap;
}

.sketch-action-btn:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(99, 102, 241, 0.8);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
}

.sketch-action-btn svg {
  width: 16px;
  height: 16px;
  stroke-width: 2;
  flex-shrink: 0;
}

.sketch-color-btn {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  border: 2px solid rgba(255, 255, 255, 0.2);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  flex-shrink: 0;
  position: relative;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.sketch-color-btn:hover {
  transform: scale(1.15);
  border-color: rgba(255, 255, 255, 0.4);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.sketch-color-btn.active {
  border-color: #6366f1 !important;
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.4), 0 4px 16px rgba(0, 0, 0, 0.4);
  transform: scale(1.15);
}

.sketch-tool-btn.primary {
  background: linear-gradient(135deg, var(--accent-purple) 0%, #5b4ade 100%);
  border-color: var(--accent-purple);
  color: #ffffff;
  box-shadow: 0 4px 16px rgba(108, 92, 231, 0.3);
}

.sketch-tool-btn.primary:hover:not(:disabled) {
  background: linear-gradient(135deg, #5b4ade 0%, #4f46e5 100%);
  border-color: #5b4ade;
  box-shadow: 0 8px 25px rgba(108, 92, 231, 0.4);
  transform: translateY(-2px);
}

.sketch-tool-btn.danger {
  background: rgba(239, 68, 68, 0.1);
  border-color: rgba(239, 68, 68, 0.3);
  color: #ef4444;
}

.sketch-tool-btn.danger:hover:not(:disabled) {
  background: rgba(239, 68, 68, 0.2);
  border-color: #ef4444;
  color: #ffffff;
  box-shadow: 0 8px 25px rgba(239, 68, 68, 0.3);
}

.sketch-action-btn.primary {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  border-color: #10b981;
  color: #ffffff;
  font-weight: 600;
  box-shadow: 0 4px 16px rgba(16, 185, 129, 0.3);
}

.sketch-action-btn.primary:hover {
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
  border-color: #059669;
  box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
  transform: translateY(-2px);
}

.sketch-action-btn.danger {
  background: rgba(239, 68, 68, 0.1);
  border-color: rgba(239, 68, 68, 0.3);
  color: #ef4444;
}

.sketch-action-btn.danger:hover {
  background: rgba(239, 68, 68, 0.2);
  border-color: #ef4444;
  color: #ffffff;
  box-shadow: 0 8px 25px rgba(239, 68, 68, 0.3);
}

.simple-sketch-canvas {
  flex: 1;
  width: 100%;
  max-width: 100%;
  background: linear-gradient(45deg, var(--bg-input) 25%, transparent 25%, transparent 75%, var(--bg-input) 75%), 
              linear-gradient(45deg, var(--bg-input) 25%, transparent 25%, transparent 75%, var(--bg-input) 75%),
              var(--bg-editor);
  background-size: 20px 20px;
  background-position: 0 0, 10px 10px;
  cursor: crosshair;
  border: 2px solid var(--border-default);
  border-radius: 12px;
  position: relative;
  z-index: 1;
  margin: 1rem;
  box-shadow: 
    0 10px 40px rgba(0, 0, 0, 0.3),
    inset 0 0 0 1px var(--border-default);
}

/* Change cursor based on active tool */
.simple-sketch-canvas.pencil-tool {
  cursor: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAA7AAAAOwBeShxvQAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAADMSURBVCiRrdIxSgNBFAbgb3YkgoVgIXgCC7Gw9QDeQOy8gJfwAIIXsBXPYCFY2FhYWVhYCBaCjYhgERCxEZbdmX0W2U02yQaT+WGYYd73/u/NGxKRqJiIJhYQV1WzgBlsVQE2cJD53dKBATgN7T8EVfXZIzlSNq6qj6r6EnyPQvBIRBaLQBFZzsE4B8fDwDcR6ReBInIZgjPZvgkOhALO8+fqFoH9IvAiJ7U9qKp3qrqWb6cNrKfVrJTWs4jtgdFk6sJRQCqHvvT/3U7CxBvRYyO/sWRBBAAAAABJRU5ErkJggg==') 7 7, crosshair;
}

.simple-sketch-instructions {
  padding: 0.5rem 1rem;
  background-color: rgba(0, 0, 0, 0.3);
  border-top: 1px solid var(--border-default);
  font-size: 0.75rem;
  color: var(--text-dim);
}

.simple-sketch-instructions p {
  margin: 0.25rem 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* When embedded in voice agent */
.voice-agent-interaction-area .simple-sketch-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

/* Sketch mode container wrapper */
.sketch-mode-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  position: relative;
}

.sketch-mode-container .simple-sketch-container {
  flex: 1;
  min-height: 0;
}

/* Text input overlay */
.text-input-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  z-index: 100;
  pointer-events: all;
}

.text-input-container {
  position: absolute;
  min-width: 200px;
  background: var(--bg-editor, #1a1a1a);
  border: 2px solid var(--accent-purple);
  border-radius: 8px;
  padding: 0.5rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
  transform: translate(-50%, -50%);
}

.text-input-field {
  width: 100%;
  padding: 0.5rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid var(--border-default);
  border-radius: 4px;
  color: var(--text-default);
  font-size: 14px;
  font-family: var(--font-sans);
  outline: none;
}

/* AI Instructions Input */
.ai-instructions-container {
  padding: 1rem 1.5rem;
  background: linear-gradient(135deg, var(--bg-assistance) 0%, var(--bg-terminal) 100%);
  border-bottom: 1px solid var(--border-separator);
  backdrop-filter: blur(10px);
}

.ai-instructions-input {
  width: 100%;
  padding: 0.75rem 1rem;
  font-size: 0.875rem;
  background: rgba(255, 255, 255, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 10px;
  color: var(--text-default);
  outline: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-family: var(--font-sans);
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.ai-instructions-input::placeholder {
  color: rgba(255, 255, 255, 0.5);
  font-style: italic;
}

.ai-instructions-input:focus {
  border-color: #6366f1;
  background: rgba(255, 255, 255, 0.12);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.3), 0 4px 16px rgba(0, 0, 0, 0.3);
  transform: translateY(-1px);
}

.text-input-field:focus {
  border-color: var(--accent-purple);
  background: rgba(255, 255, 255, 0.08);
}

.text-input-field::placeholder {
  color: var(--text-dim);
  opacity: 0.7;
}

/* Text tool button styling */
.sketch-tool-btn[title="Text"] {
  font-weight: bold;
  font-size: 1.25rem;
}

/* Responsive */
@media (max-width: 768px) {
  .simple-sketch-toolbar {
    padding: 0.5rem;
    gap: 0.5rem;
  }
  
  .sketch-tool-btn,
  .sketch-action-btn {
    min-width: 35px;
    height: 35px;
    font-size: 1rem;
  }
  
  .sketch-color-btn {
    width: 25px;
    height: 25px;
  }
}

/* Animation keyframes */
@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* AI Processing Overlay */
.ai-processing-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  border-radius: 12px;
}

.ai-processing-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  color: var(--text-header);
  text-align: center;
}

.ai-processing-spinner {
  width: 48px;
  height: 48px;
  border: 4px solid rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  border-top-color: var(--accent-purple);
  animation: spin 1s linear infinite;
}

.ai-processing-text {
  font-size: 1rem;
  font-weight: 500;
  font-family: var(--font-sans);
  color: var(--text-bright);
  opacity: 0.9;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
