/* Modal styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(2px);
}

.modal-content {
  background-color: var(--bg-default, #1e1e1e);
  border: 1px solid var(--border-default, #3e3e3e);
  border-radius: 6px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
  padding: 20px;
  min-width: 300px;
  max-width: 500px;
  color: var(--text-default, #cccccc);
  font-family: var(--font-sans, 'Segoe UI', system-ui, sans-serif);
}

.input-dialog {
  min-width: 400px;
}

.modal-title {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-bright, #ffffff);
}

.modal-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid var(--border-default, #3e3e3e);
  border-radius: 4px;
  background-color: var(--bg-input, #2d2d2d);
  color: var(--text-default, #cccccc);
  font-size: 14px;
  font-family: var(--font-sans, 'Segoe UI', system-ui, sans-serif);
  outline: none;
  transition: border-color 0.15s ease;
  margin-bottom: 16px;
  box-sizing: border-box;
}

.modal-input:focus {
  border-color: var(--accent-primary, #0078d4);
  box-shadow: 0 0 0 1px var(--accent-primary, #0078d4);
}

.modal-input::placeholder {
  color: var(--text-dim, #888888);
}

.modal-buttons {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

.modal-button {
  padding: 6px 16px;
  border: 1px solid transparent;
  border-radius: 4px;
  font-size: 13px;
  font-family: var(--font-sans, 'Segoe UI', system-ui, sans-serif);
  cursor: pointer;
  transition: all 0.15s ease;
  min-width: 70px;
}

.modal-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.modal-button-primary {
  background-color: var(--accent-primary, #0078d4);
  color: white;
  border-color: var(--accent-primary, #0078d4);
}

.modal-button-primary:hover:not(:disabled) {
  background-color: var(--accent-primary-hover, #106ebe);
  border-color: var(--accent-primary-hover, #106ebe);
}

.modal-button-secondary {
  background-color: transparent;
  color: var(--text-default, #cccccc);
  border-color: var(--border-default, #3e3e3e);
}

.modal-button-secondary:hover {
  background-color: var(--bg-hover, #2a2a2a);
  border-color: var(--border-hover, #4e4e4e);
}

.modal-button:focus {
  outline: 1px solid var(--accent-primary, #0078d4);
  outline-offset: 1px;
}

/* Animation */
.modal-overlay {
  animation: fadeIn 0.15s ease-out;
}

.modal-content {
  animation: slideIn 0.15s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    transform: translateY(-10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}