/* App Mode Switcher - Floating UI element to switch between IDE and Voice Agent */

.app-mode-switcher {
  position: fixed;
  top: 1rem;
  right: 1rem;
  z-index: 9999;
  background-color: rgba(0, 0, 0, 0.8);
  border: 1px solid var(--border-default);
  border-radius: 12px;
  padding: 0.5rem;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  font-family: var(--font-sans);
  user-select: none;
  transition: all 0.2s ease;
}

.app-mode-switcher:hover {
  background-color: rgba(0, 0, 0, 0.9);
  box-shadow: 0 6px 25px rgba(0, 0, 0, 0.4);
}

.app-mode-switcher-content {
  display: flex;
  gap: 0.375rem;
  margin-bottom: 0.25rem;
}

.app-mode-btn {
  padding: 0.375rem 0.75rem;
  font-size: 0.75rem;
  font-weight: 500;
  border-radius: 8px;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-family: var(--font-sans);
  background-color: transparent;
  color: var(--text-dim);
  min-width: 60px;
  justify-content: center;
}

.app-mode-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: var(--text-default);
  transform: translateY(-1px);
}

.app-mode-btn.active {
  background-color: var(--accent-purple);
  color: var(--text-header);
  box-shadow: 0 2px 8px var(--overlay-accent);
}

.app-mode-btn.active:hover {
  background-color: var(--primary-accent-hover, #7B2CBF);
  transform: translateY(-1px);
}

.app-mode-shortcut-hint {
  font-size: 0.625rem;
  color: var(--text-dim);
  text-align: center;
  opacity: 0.7;
  font-weight: 400;
  letter-spacing: 0.3px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .app-mode-switcher {
    top: 0.5rem;
    right: 0.5rem;
    padding: 0.375rem;
  }

  .app-mode-btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.625rem;
    min-width: 50px;
  }

  .app-mode-shortcut-hint {
    font-size: 0.5rem;
  }
}

/* Hide on very small screens */
@media (max-width: 480px) {
  .app-mode-shortcut-hint {
    display: none;
  }
}