/**
 * Terminal Tab Types and Interfaces
 * Comprehensive type definitions for terminal tab management
 */

import { TerminalRef } from '../features/terminal/Terminal';

// Core terminal tab interface
export interface TerminalTab {
  id: string;                      // Unique identifier (UUID)
  title: string;                   // User-editable tab name
  ptyId: string | null;            // PTY process ID
  workingDirectory: string;        // Current working directory
  isActive: boolean;               // Active tab state
  createdAt: Date;                // Creation timestamp
  lastActivity: Date;             // Last command execution
  hasRunningProcess: boolean;     // Process running indicator
  exitCode: number | null;        // Last command exit code
  ref: React.RefObject<TerminalRef>; // Terminal component reference
  customTitle?: string;           // User-set custom title (prevents auto-update)
  isModified?: boolean;           // Indicates unsaved changes
  processName?: string;           // Currently running process name
}

// Tab creation options
export interface CreateTabOptions {
  title?: string;
  workingDirectory?: string;
  environment?: Record<string, string>;
  commands?: string[];            // Commands to run after creation
}

// Tab container state
export interface TerminalTabState {
  tabs: TerminalTab[];
  activeTabId: string;
  nextTabNumber: number;
  tabOrder: string[];              // For custom ordering
  maxTabs: number;                 // Prevent memory issues
  recentlyClosedTabs: TerminalTabHistory[]; // For "reopen tab" functionality
}

// Tab actions interface
export interface TerminalTabActions {
  createTab: (options?: CreateTabOptions) => string;
  closeTab: (tabId: string, force?: boolean) => boolean;
  renameTab: (tabId: string, newTitle: string) => void;
  switchToTab: (tabId: string) => void;
  reorderTabs: (newOrder: string[]) => void;
  updateTabState: (tabId: string, updates: Partial<TerminalTab>) => void;
  duplicateTab: (tabId: string) => string;
  closeOtherTabs: (tabId: string) => void;
  closeTabsToRight: (tabId: string) => void;
  reopenLastClosedTab: () => string | null;
}

// Tab history for restoration
export interface TerminalTabHistory {
  id: string;
  title: string;
  workingDirectory: string;
  commands: string[];
  environment: Record<string, string>;
  closedAt: Date;
}

// Tab configuration for persistence
export interface TerminalTabPersistentConfig {
  title: string;
  workingDirectory: string;
  commands: string[];
  environment: Record<string, string>;
  isActive?: boolean;
}

// Project terminal state for persistence
export interface ProjectTerminalState {
  projectPath: string;
  defaultTabs: TerminalTabPersistentConfig[];
  tabHistory: TerminalTabHistory[];
  pinnedTabs: string[];
  activeTabId?: string;
}

// Tab context menu options
export interface TabContextMenuOption {
  label: string;
  action: () => void;
  disabled?: boolean;
  separator?: boolean;
  icon?: string;
}

// Tab events
export interface TabEvents {
  onTabCreate?: (tab: TerminalTab) => void;
  onTabClose?: (tab: TerminalTab) => void;
  onTabSwitch?: (fromTab: TerminalTab | null, toTab: TerminalTab) => void;
  onTabRename?: (tab: TerminalTab, oldTitle: string, newTitle: string) => void;
  onTabReorder?: (newOrder: string[]) => void;
  onProcessStart?: (tab: TerminalTab, processName: string) => void;
  onProcessEnd?: (tab: TerminalTab, exitCode: number) => void;
}

// Tab keyboard shortcuts
export interface TabKeyboardShortcuts {
  newTab: string;           // Default: 'Ctrl+Shift+`'
  closeTab: string;         // Default: 'Ctrl+Shift+W'
  nextTab: string;          // Default: 'Ctrl+PageDown'
  previousTab: string;      // Default: 'Ctrl+PageUp'
  renameTab: string;        // Default: 'F2'
  reopenTab: string;        // Default: 'Ctrl+Shift+T'
  switchToTab1: string;     // Default: 'Ctrl+1'
  switchToTab2: string;     // Default: 'Ctrl+2'
  // ... up to switchToTab9
}

// Tab configuration options
export interface TerminalTabConfig {
  maxTabs: number;                    // Maximum number of tabs (default: 10)
  showTabBar: boolean;                // Show/hide tab bar
  showTabBarOnSingleTab: boolean;     // Show tab bar even with single tab
  enableTabReordering: boolean;       // Enable drag-and-drop reordering
  enableTabContextMenu: boolean;      // Enable right-click context menu
  autoCloseEmptyTabs: boolean;        // Auto-close tabs with no activity
  persistTabsWithProject: boolean;    // Save/restore tabs with project
  smartTabTitles: boolean;            // Auto-update titles based on directory/process
  confirmCloseRunningProcess: boolean; // Confirm before closing tabs with running processes
  shortcuts: TabKeyboardShortcuts;    // Customizable keyboard shortcuts
}

// Default tab configuration
export const DEFAULT_TAB_CONFIG: TerminalTabConfig = {
  maxTabs: 10,
  showTabBar: true,
  showTabBarOnSingleTab: true,
  enableTabReordering: true,
  enableTabContextMenu: true,
  autoCloseEmptyTabs: false,
  persistTabsWithProject: true,
  smartTabTitles: true,
  confirmCloseRunningProcess: true,
  shortcuts: {
    newTab: 'Ctrl+Shift+`',
    closeTab: 'Ctrl+Shift+W',
    nextTab: 'Ctrl+PageDown',
    previousTab: 'Ctrl+PageUp',
    renameTab: 'F2',
    reopenTab: 'Ctrl+Shift+T',
    switchToTab1: 'Ctrl+1',
    switchToTab2: 'Ctrl+2'
  }
};

// Terminal tab provider context type
export interface TerminalTabContextType {
  state: TerminalTabState;
  actions: TerminalTabActions;
  config: TerminalTabConfig;
  events: TabEvents;
}

// Utility functions type definitions
export interface TerminalTabUtils {
  generateTabId: () => string;
  generateTabTitle: (number: number, directory?: string) => string;
  getTabByPtyId: (ptyId: string) => TerminalTab | null;
  getActiveTab: () => TerminalTab | null;
  isValidTabId: (tabId: string) => boolean;
  formatTabTitle: (title: string, maxLength?: number) => string;
  getTabShortcutNumber: (tabId: string) => number | null;
}