import React, { useState, useEffect } from 'react';
import '../../../styles/dashboard.css';
import { useAuth } from '../../../contexts/AuthContext';
import { useProject } from '../../../contexts/ProjectContext';
import { processVoiceCommand } from '../../../services/voiceCommandApi';
import { ENABLE_CODE_ANALYSIS } from '../../../../config';

interface CodeHealthProps {
  className?: string;
}

interface CodeIssue {
  id: number;
  patternType: string;
  severity: 'info' | 'warning' | 'error' | 'critical';
  filePath: string;
  lineNumber?: number;
  description: string;
  suggestion?: string;
}

interface CodeHealthReport {
  id: number;
  projectId: number;
  overallScore: number;
  complexityScore: number;
  maintainability: number;
  technicalDebt: number;
  fileCount: number;
  analysisTimeMs: number;
  createdAt: Date;
  aiAnalysisModel?: string;
  metrics: {
    avgComplexity: number;
    highComplexityFiles: number;
    missingErrorHandling: number;
    totalFunctions: number;
    totalLines: number;
  };
  issues: CodeIssue[];
  aiInsights?: {
    explanation: string;
    recommendations: string[];
    riskAssessment: string;
    refactoringSuggestions: string[];
    modelUsed: string;
    conversationId?: number;
    fallbackApplied?: boolean;
  };
}

const CodeHealthWidget: React.FC<CodeHealthProps> = ({ className }) => {
  const { user } = useAuth();
  const { projectState, isProjectOpen } = useProject();
  const [healthReport, setHealthReport] = useState<CodeHealthReport | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isListening, setIsListening] = useState(false);
  const [mediaRecorder, setMediaRecorder] = useState<MediaRecorder | null>(null);

  // Load latest health report on component mount
  useEffect(() => {
    if (isProjectOpen && projectState?.name && ENABLE_CODE_ANALYSIS) {
      loadLatestHealthReport();
    }
  }, [isProjectOpen, projectState?.name]);

  const loadLatestHealthReport = async () => {
    if (!isProjectOpen || !projectState?.name || !user) return;
    
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await window.electronAPI.codeAnalysis.getLatestHealth({
        projectPath: projectState.path
      });
      
      if (response.success && response.data) {
        setHealthReport(response.data);
      } else {
        // No previous analysis found, that's OK
        setHealthReport(null);
      }
    } catch (err) {
      console.error('Error loading health report:', err);
      setError('Failed to load code health data');
    } finally {
      setIsLoading(false);
    }
  };

  const analyzeCode = async () => {
    if (!ENABLE_CODE_ANALYSIS) {
      setError('Code analysis feature is disabled');
      return;
    }

    if (!isProjectOpen || !projectState?.name || !user) {
      setError('No project selected');
      return;
    }

    setIsAnalyzing(true);
    setError(null);

    try {
      const response = await window.electronAPI.codeAnalysis.analyzeProject({
        projectPath: projectState.path,
        includeAI: true,
        analysisDepth: 'detailed'
      });

      if (response.success && response.data) {
        setHealthReport(response.data);
      } else {
        throw new Error(response.error || 'Analysis failed');
      }
    } catch (err) {
      console.error('Error analyzing code:', err);
      setError('Failed to analyze code. Please try again.');
    } finally {
      setIsAnalyzing(false);
    }
  };

  const getScoreColor = (score: number): string => {
    if (score >= 80) return '#4caf50'; // Green
    if (score >= 60) return '#ff9800'; // Orange
    return '#f44336'; // Red
  };

  const getSeverityColor = (severity: string): string => {
    switch (severity) {
      case 'critical': return '#d32f2f';
      case 'error': return '#f44336';
      case 'warning': return '#ff9800';
      case 'info': return '#2196f3';
      default: return '#757575';
    }
  };

  const getSeverityIcon = (severity: string): string => {
    switch (severity) {
      case 'critical': return '🔥';
      case 'error': return '❌';
      case 'warning': return '⚠️';
      case 'info': return 'ℹ️';
      default: return '●';
    }
  };

  const formatFilePath = (filePath: string): string => {
    // Show only the filename for brevity
    const parts = filePath.split('/');
    return parts[parts.length - 1];
  };

  const handleVoiceCommand = async () => {
    if (isListening) {
      stopListening();
    } else {
      startListening();
    }
  };

  const startListening = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      const recorder = new MediaRecorder(stream);
      const audioChunks: Blob[] = [];

      recorder.ondataavailable = (event) => {
        audioChunks.push(event.data);
      };

      recorder.onstop = async () => {
        const audioBlob = new Blob(audioChunks, { type: 'audio/wav' });
        stream.getTracks().forEach(track => track.stop());
        
        try {
          setIsAnalyzing(true);
          const response = await processVoiceCommand(audioBlob, {
            projectInfo: isProjectOpen ? `Current project: ${projectState.name}` : undefined,
            editorContext: 'Code Health Dashboard'
          });

          // Check if the command is related to code analysis
          const transcription = response.transcription.toLowerCase();
          if (transcription.includes('analyze') || 
              transcription.includes('code health') || 
              transcription.includes('quality') ||
              transcription.includes('complexity')) {
            
            // Auto-trigger code analysis
            await analyzeCode();
          }
        } catch (err) {
          console.error('Voice command error:', err);
          setError('Voice command failed');
        } finally {
          setIsAnalyzing(false);
        }
      };

      recorder.start();
      setMediaRecorder(recorder);
      setIsListening(true);
      setError(null);

      // Auto-stop after 5 seconds
      setTimeout(() => {
        if (recorder.state === 'recording') {
          recorder.stop();
          setIsListening(false);
        }
      }, 5000);

    } catch (err) {
      console.error('Error starting voice recording:', err);
      setError('Microphone access denied');
    }
  };

  const stopListening = () => {
    if (mediaRecorder && mediaRecorder.state === 'recording') {
      mediaRecorder.stop();
    }
    setIsListening(false);
  };

  if (isLoading) {
    return (
      <div className={`dashboardCard ${className || ''}`}>
        <h3 className="cardTitle">Code Health</h3>
        <div className="cardContent" style={{ 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'center',
          minHeight: '120px'
        }}>
          <div className="loading-spinner">Loading...</div>
        </div>
      </div>
    );
  }

  return (
    <div className={`dashboardCard ${className || ''}`}>
      <h3 className="cardTitle">Code Health</h3>
      
      {error && (
        <div className="error-message" style={{ 
          color: '#f44336', 
          padding: '8px', 
          fontSize: '0.9rem',
          textAlign: 'center'
        }}>
          {error}
        </div>
      )}

      <div className="cardContent">
        {healthReport ? (
          <>
            {/* Health Score Display */}
            <div style={{ 
              display: 'flex', 
              alignItems: 'center', 
              marginBottom: '16px',
              justifyContent: 'space-between'
            }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                <div style={{
                  width: '48px',
                  height: '48px',
                  borderRadius: '50%',
                  backgroundColor: getScoreColor(healthReport.overallScore),
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  color: 'white',
                  fontWeight: 'bold',
                  fontSize: '16px'
                }}>
                  {healthReport.overallScore}
                </div>
                <div>
                  <div style={{ fontWeight: 'bold', fontSize: '14px' }}>
                    Overall Health
                  </div>
                  <div style={{ fontSize: '12px', color: '#666' }}>
                    {healthReport.fileCount} files analyzed
                  </div>
                </div>
              </div>
              
              <div style={{ fontSize: '11px', color: '#888' }}>
                {new Date(healthReport.createdAt).toLocaleDateString()}
              </div>
            </div>

            {/* Metrics Summary */}
            <div style={{ 
              display: 'grid', 
              gridTemplateColumns: 'repeat(3, 1fr)', 
              gap: '8px',
              marginBottom: '16px',
              fontSize: '11px'
            }}>
              <div style={{ textAlign: 'center', padding: '4px' }}>
                <div style={{ fontWeight: 'bold', color: getScoreColor(healthReport.complexityScore) }}>
                  {healthReport.complexityScore}
                </div>
                <div style={{ color: '#666' }}>Complexity</div>
              </div>
              <div style={{ textAlign: 'center', padding: '4px' }}>
                <div style={{ fontWeight: 'bold', color: getScoreColor(healthReport.maintainability) }}>
                  {healthReport.maintainability}
                </div>
                <div style={{ color: '#666' }}>Maintainability</div>
              </div>
              <div style={{ textAlign: 'center', padding: '4px' }}>
                <div style={{ fontWeight: 'bold', color: getScoreColor(100 - healthReport.technicalDebt) }}>
                  {100 - healthReport.technicalDebt}
                </div>
                <div style={{ color: '#666' }}>Quality</div>
              </div>
            </div>

            {/* Top Issues */}
            {healthReport.issues.length > 0 && (
              <div>
                <div style={{ 
                  fontSize: '12px', 
                  fontWeight: 'bold', 
                  marginBottom: '8px',
                  color: '#333'
                }}>
                  Top Issues ({healthReport.issues.slice(0, 3).length})
                </div>
                {healthReport.issues.slice(0, 3).map((issue, index) => (
                  <div key={index} style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '8px',
                    padding: '6px 8px',
                    marginBottom: '4px',
                    backgroundColor: '#f8f9fa',
                    borderRadius: '4px',
                    fontSize: '11px'
                  }}>
                    <span style={{ 
                      color: getSeverityColor(issue.severity),
                      fontSize: '12px'
                    }}>
                      {getSeverityIcon(issue.severity)}
                    </span>
                    <div style={{ flex: 1, minWidth: 0 }}>
                      <div style={{ 
                        fontWeight: 'bold',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        whiteSpace: 'nowrap'
                      }}>
                        {formatFilePath(issue.filePath)}
                        {issue.lineNumber && `:${issue.lineNumber}`}
                      </div>
                      <div style={{ 
                        color: '#666',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        whiteSpace: 'nowrap'
                      }}>
                        {issue.description}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {/* AI Insights Preview */}
            {healthReport.aiInsights && (
              <div style={{ 
                marginTop: '12px',
                padding: '8px',
                backgroundColor: '#e3f2fd',
                borderRadius: '4px',
                fontSize: '11px'
              }}>
                <div style={{ fontWeight: 'bold', marginBottom: '4px', color: '#1976d2' }}>
                  🤖 AI Insights
                </div>
                <div style={{ 
                  color: '#333',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  display: '-webkit-box',
                  WebkitLineClamp: 2,
                  WebkitBoxOrient: 'vertical'
                }}>
                  {healthReport.aiInsights.explanation.substring(0, 100)}...
                </div>
              </div>
            )}
          </>
        ) : (
          <div style={{ 
            textAlign: 'center', 
            color: '#666',
            padding: '20px 0'
          }}>
            {!ENABLE_CODE_ANALYSIS ? (
              <>
                <div style={{ fontSize: '24px', marginBottom: '8px' }}>🚫</div>
                <div style={{ fontSize: '14px', marginBottom: '4px' }}>
                  Code analysis disabled
                </div>
                <div style={{ fontSize: '12px' }}>
                  Feature temporarily unavailable
                </div>
              </>
            ) : (
              <>
                <div style={{ fontSize: '24px', marginBottom: '8px' }}>🔍</div>
                <div style={{ fontSize: '14px', marginBottom: '4px' }}>
                  No code analysis yet
                </div>
                <div style={{ fontSize: '12px' }}>
                  Run analysis to see health metrics
                </div>
              </>
            )}
          </div>
        )}
      </div>

      {/* Action Buttons */}
      <div className="taskActions">
        <button 
          onClick={analyzeCode}
          disabled={isAnalyzing || !isProjectOpen || !ENABLE_CODE_ANALYSIS}
          style={{
            background: 'none',
            border: 'none',
            color: !ENABLE_CODE_ANALYSIS ? '#999' : '#007acc',
            cursor: (isAnalyzing || !ENABLE_CODE_ANALYSIS) ? 'not-allowed' : 'pointer',
            fontSize: '12px',
            textDecoration: 'none',
            opacity: (isAnalyzing || !ENABLE_CODE_ANALYSIS) ? 0.6 : 1,
            marginRight: '8px'
          }}
        >
          {!ENABLE_CODE_ANALYSIS ? '🚫 Disabled' : (isAnalyzing ? '🔄 Analyzing...' : '🔍 Analyze Code')}
        </button>

        <button 
          onClick={handleVoiceCommand}
          disabled={isAnalyzing || !isProjectOpen}
          style={{
            background: 'none',
            border: 'none',
            color: isListening ? '#ff4444' : '#007acc',
            cursor: isAnalyzing ? 'not-allowed' : 'pointer',
            fontSize: '12px',
            textDecoration: 'none',
            opacity: isAnalyzing ? 0.6 : 1,
            marginRight: '8px'
          }}
        >
          {isListening ? '🔴 Listening...' : '🎤 Voice Command'}
        </button>
        
        {healthReport && (
          <a href="#" className="cardLink" style={{ fontSize: '12px' }}>
            View Details →
          </a>
        )}
      </div>
    </div>
  );
};

export default CodeHealthWidget;