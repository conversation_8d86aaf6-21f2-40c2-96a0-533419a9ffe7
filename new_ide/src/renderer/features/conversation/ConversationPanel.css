/* Conversation Panel Styles */
.conversation-panel {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #1e1e1e;
  color: #d4d4d4;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  border-left: 1px solid #3e3e42;
}

/* Error Banner */
.error-banner {
  background-color: #d73a49;
  color: white;
  padding: 8px 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.9em;
}

.error-close {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  font-size: 1.2em;
  padding: 0 4px;
}

/* Header */
.conversation-header {
  padding: 16px;
  border-bottom: 1px solid #3e3e42;
  background-color: #252526;
}

.header-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.header-top h3 {
  margin: 0;
  font-size: 1.1em;
  font-weight: 600;
}

.btn-new-conversation {
  background-color: #007acc;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9em;
  transition: background-color 0.2s;
}

.btn-new-conversation:hover:not(:disabled) {
  background-color: #106ebe;
}

.btn-new-conversation:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Strategy and Model Selectors */
.strategy-selector,
.model-selector {
  margin-bottom: 8px;
}

.strategy-selector label,
.model-selector label {
  display: block;
  font-size: 0.85em;
  margin-bottom: 4px;
  color: #969696;
}

.strategy-select,
.model-select {
  width: 100%;
  background-color: #3c3c3c;
  color: #d4d4d4;
  border: 1px solid #3e3e42;
  padding: 6px 8px;
  border-radius: 4px;
  font-size: 0.9em;
}

.strategy-select:focus,
.model-select:focus {
  outline: none;
  border-color: #007acc;
}

/* Conversations List */
.conversations-list {
  padding: 12px 16px;
  border-bottom: 1px solid #3e3e42;
  max-height: 200px;
  overflow: hidden;
}

.conversations-list label {
  display: block;
  font-size: 0.85em;
  margin-bottom: 8px;
  color: #969696;
}

.conversations-scroll {
  max-height: 150px;
  overflow-y: auto;
}

.conversation-item {
  padding: 8px;
  cursor: pointer;
  border-radius: 4px;
  margin-bottom: 4px;
  transition: background-color 0.2s;
}

.conversation-item:hover {
  background-color: #2d2d30;
}

.conversation-item.active {
  background-color: #094771;
  border: 1px solid #007acc;
}

.conversation-title {
  font-size: 0.9em;
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.conversation-time {
  font-size: 0.75em;
  color: #969696;
}

/* Messages Container */
.messages-container {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.empty-state {
  padding: 32px 16px;
  text-align: center;
  color: #969696;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.empty-state p {
  margin-bottom: 16px;
}

.strategy-hint {
  background-color: #252526;
  padding: 12px;
  border-radius: 4px;
  border-left: 3px solid #007acc;
}

.strategy-hint strong {
  color: #d4d4d4;
}

.strategy-hint small {
  color: #969696;
}

/* Messages */
.messages {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  scroll-behavior: smooth;
}

.message {
  margin-bottom: 16px;
  max-width: 100%;
}

.message.user {
  margin-left: 20px;
}

.message.assistant {
  margin-right: 20px;
}

.message-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 6px;
  font-size: 0.85em;
}

.message-role {
  font-weight: 600;
  color: #007acc;
}

.message-time {
  color: #969696;
  font-size: 0.8em;
}

.message-model {
  background-color: #3c3c3c;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 0.75em;
  color: #d4d4d4;
}

.streaming-indicator {
  color: #10b981;
  animation: pulse 1s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.message-content {
  background-color: #2d2d30;
  padding: 12px;
  border-radius: 8px;
  line-height: 1.5;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.message.user .message-content {
  background-color: #094771;
  margin-left: auto;
}

.message.streaming .message-content {
  border-left: 3px solid #10b981;
}

.cursor {
  animation: blink 1s infinite;
  font-weight: bold;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

.message-metrics {
  margin-top: 6px;
  display: flex;
  gap: 12px;
  font-size: 0.75em;
  color: #969696;
}

.message-metrics span {
  display: flex;
  align-items: center;
  gap: 2px;
}

/* Input Container */
.message-input-container {
  padding: 16px;
  border-top: 1px solid #3e3e42;
  background-color: #252526;
}

.input-wrapper {
  display: flex;
  gap: 8px;
  align-items: flex-end;
}

.message-input {
  flex: 1;
  background-color: #3c3c3c;
  color: #d4d4d4;
  border: 1px solid #3e3e42;
  padding: 8px 12px;
  border-radius: 6px;
  resize: vertical;
  min-height: 60px;
  max-height: 120px;
  font-family: inherit;
  font-size: 0.9em;
  line-height: 1.4;
}

.message-input:focus {
  outline: none;
  border-color: #007acc;
}

.message-input:disabled {
  opacity: 0.5;
}

.send-button {
  background-color: #007acc;
  color: white;
  border: none;
  padding: 10px 12px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1.1em;
  transition: background-color 0.2s;
  min-width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.send-button:hover:not(:disabled) {
  background-color: #106ebe;
}

.send-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Loading Overlay */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(30, 30, 30, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.spinner {
  font-size: 2em;
  animation: spin 2s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Scrollbar Styling */
.conversations-scroll::-webkit-scrollbar,
.messages::-webkit-scrollbar {
  width: 8px;
}

.conversations-scroll::-webkit-scrollbar-track,
.messages::-webkit-scrollbar-track {
  background: #252526;
}

.conversations-scroll::-webkit-scrollbar-thumb,
.messages::-webkit-scrollbar-thumb {
  background: #3e3e42;
  border-radius: 4px;
}

.conversations-scroll::-webkit-scrollbar-thumb:hover,
.messages::-webkit-scrollbar-thumb:hover {
  background: #4e4e52;
}

/* Responsive */
@media (max-width: 768px) {
  .conversation-header {
    padding: 12px;
  }
  
  .messages {
    padding: 12px;
  }
  
  .message-input-container {
    padding: 12px;
  }
  
  .input-wrapper {
    flex-direction: column;
    gap: 8px;
  }
  
  .send-button {
    align-self: flex-end;
    min-width: 60px;
  }
}