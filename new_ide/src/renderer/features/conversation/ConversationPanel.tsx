import React, { useState, useEffect, useRef } from 'react';
import { useConversation } from '../../contexts/ConversationContext';
import { TaskStrategy } from '../../services/ConversationService';
import './ConversationPanel.css';

interface ConversationPanelProps {
  className?: string;
}

const ConversationPanel: React.FC<ConversationPanelProps> = ({ className = '' }) => {
  const {
    state,
    loadConversations,
    createConversation,
    selectConversation,
    sendMessage,
    streamingContent,
    streamingMetadata,
    clearError,
    currentStrategy,
    setCurrentStrategy,
  } = useConversation();

  const [messageInput, setMessageInput] = useState('');
  const [selectedModel, setSelectedModel] = useState('gemini-2.0-flash-lite');
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Load conversations on mount
  useEffect(() => {
    loadConversations();
  }, [loadConversations]);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [state.messages, streamingContent]);

  const handleSendMessage = async () => {
    if (!messageInput.trim() || state.isStreaming) return;

    try {
      await sendMessage(messageInput, {
        modelId: selectedModel,
      });
      setMessageInput('');
    } catch (error) {
      console.error('Failed to send message:', error);
    }
  };

  const handleCreateConversation = async () => {
    try {
      await createConversation(`New ${currentStrategy} conversation`, currentStrategy);
    } catch (error) {
      console.error('Failed to create conversation:', error);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const taskStrategyOptions: { value: TaskStrategy; label: string; description: string }[] = [
    { value: 'chat', label: '💬 Chat', description: 'General conversation' },
    { value: 'code_generation', label: '🔧 Code Gen', description: 'Generate code from specs' },
    { value: 'code_planning', label: '📋 Planning', description: 'Architecture & design' },
    { value: 'slide_generation', label: '🎨 Slides', description: 'Create presentations' },
    { value: 'svg_mockup', label: '🖼️ Mockups', description: 'Visual wireframes' },
    { value: 'test_cases', label: '🧪 Tests', description: 'Generate test cases' },
  ];

  const modelOptions = [
    { value: 'gemini-2.0-flash-lite', label: 'Gemini Flash Lite (Low Cost)', provider: 'Google' },
    { value: 'claude-3.5-haiku', label: 'Claude 3.5 Haiku (Balanced)', provider: 'Anthropic' },
    { value: 'nova-micro', label: 'Nova Micro (Lowest Cost)', provider: 'Amazon' },
    { value: 'gpt-4.1-nano', label: 'GPT-4.1 Nano (Balanced)', provider: 'Azure' },
  ];

  return (
    <div className={`conversation-panel ${className}`}>
      {/* Error Display */}
      {state.error && (
        <div className="error-banner">
          <span>{state.error}</span>
          <button onClick={clearError} className="error-close">×</button>
        </div>
      )}

      {/* Header */}
      <div className="conversation-header">
        <div className="header-top">
          <h3>Conversation</h3>
          <button 
            onClick={handleCreateConversation}
            disabled={state.isLoading}
            className="btn-new-conversation"
          >
            + New
          </button>
        </div>

        {/* Strategy Selection */}
        <div className="strategy-selector">
          <label>Strategy:</label>
          <select 
            value={currentStrategy} 
            onChange={(e) => setCurrentStrategy(e.target.value as TaskStrategy)}
            className="strategy-select"
          >
            {taskStrategyOptions.map(option => (
              <option key={option.value} value={option.value} title={option.description}>
                {option.label}
              </option>
            ))}
          </select>
        </div>

        {/* Model Selection */}
        <div className="model-selector">
          <label>Model:</label>
          <select 
            value={selectedModel} 
            onChange={(e) => setSelectedModel(e.target.value)}
            className="model-select"
          >
            {modelOptions.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Conversation List */}
      {state.conversations.length > 0 && (
        <div className="conversations-list">
          <label>Recent Conversations:</label>
          <div className="conversations-scroll">
            {state.conversations.slice(0, 5).map(conv => (
              <div 
                key={conv.id}
                className={`conversation-item ${state.currentConversation?.id === conv.id ? 'active' : ''}`}
                onClick={() => selectConversation(conv.id)}
              >
                <div className="conversation-title">{conv.title}</div>
                <div className="conversation-time">{formatTime(conv.updatedAt)}</div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Messages */}
      <div className="messages-container">
        {state.messages.length === 0 && !state.currentConversation ? (
          <div className="empty-state">
            <p>Start a new conversation to chat with AI about your code.</p>
            <p className="strategy-hint">
              Current strategy: <strong>{taskStrategyOptions.find(s => s.value === currentStrategy)?.label}</strong>
              <br />
              <small>{taskStrategyOptions.find(s => s.value === currentStrategy)?.description}</small>
            </p>
          </div>
        ) : (
          <div className="messages">
            {state.messages.map((message, index) => (
              <div key={index} className={`message ${message.role}`}>
                <div className="message-header">
                  <span className="message-role">
                    {message.role === 'user' ? '👤' : '🤖'} {message.role}
                  </span>
                  <span className="message-time">{formatTime(message.createdAt)}</span>
                  {message.model && (
                    <span className="message-model">{message.model}</span>
                  )}
                </div>
                <div className="message-content">
                  {message.content}
                </div>
                {message.metrics && (
                  <div className="message-metrics">
                    {message.metrics.promptTokens && (
                      <span>📊 {message.metrics.promptTokens}+{message.metrics.completionTokens} tokens</span>
                    )}
                    {message.metrics.cost && (
                      <span>💰 ${message.metrics.cost.toFixed(4)}</span>
                    )}
                    {message.metrics.duration && (
                      <span>⏱️ {message.metrics.duration.toFixed(1)}s</span>
                    )}
                  </div>
                )}
              </div>
            ))}

            {/* Streaming Message */}
            {state.isStreaming && streamingContent && (
              <div className="message assistant streaming">
                <div className="message-header">
                  <span className="message-role">🤖 assistant</span>
                  <span className="streaming-indicator">●</span>
                </div>
                <div className="message-content">
                  {streamingContent}
                  <span className="cursor">|</span>
                </div>
                {streamingMetadata && (
                  <div className="message-metrics">
                    {streamingMetadata.promptTokens && (
                      <span>📊 {streamingMetadata.promptTokens}+{streamingMetadata.completionTokens} tokens</span>
                    )}
                    {streamingMetadata.cost && (
                      <span>💰 ${streamingMetadata.cost.toFixed(4)}</span>
                    )}
                  </div>
                )}
              </div>
            )}

            <div ref={messagesEndRef} />
          </div>
        )}
      </div>

      {/* Input */}
      <div className="message-input-container">
        <div className="input-wrapper">
          <textarea
            value={messageInput}
            onChange={(e) => setMessageInput(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder={`Ask about your code (${currentStrategy} mode)...`}
            className="message-input"
            rows={3}
            disabled={state.isStreaming}
          />
          <button
            onClick={handleSendMessage}
            disabled={!messageInput.trim() || state.isStreaming}
            className="send-button"
          >
            {state.isStreaming ? '⏸️' : '📤'}
          </button>
        </div>
      </div>

      {/* Loading Indicator */}
      {state.isLoading && (
        <div className="loading-overlay">
          <div className="spinner">⏳</div>
        </div>
      )}
    </div>
  );
};

export default ConversationPanel;