import React, { useEffect, useRef, useState, memo } from 'react';
import mermaidService from '../../../../services/MermaidService';

interface MermaidDiagramProps {
  code: string;
  id: string;
  zoom: number;
}

/**
 * Memoized Mermaid diagram component that only re-renders when its content changes
 */
const MermaidDiagram: React.FC<MermaidDiagramProps> = memo(({ code, id, zoom }) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const svgRef = useRef<string | null>(null);
  const lastRenderedCode = useRef<string>('');

  // Only re-render the diagram if the code actually changed
  useEffect(() => {
    if (code === lastRenderedCode.current) {
      // Code hasn't changed, just apply zoom if we have an SVG
      if (containerRef.current && svgRef.current) {
        const svgElement = containerRef.current.querySelector('svg');
        if (svgElement) {
          svgElement.style.transform = `scale(${zoom})`;
        }
      }
      return;
    }

    let isMounted = true;
    const renderDiagram = async () => {
      try {
        setIsLoading(true);
        setError(null);

        const result = await mermaidService.renderDiagram(code, id, {
          applyDarkTheme: true,
          maxRetries: 2,
          timeout: 10000
        });

        if (!isMounted) return;

        if (result.success && result.svg) {
          svgRef.current = result.svg;
          lastRenderedCode.current = code;
          
          if (containerRef.current) {
            containerRef.current.innerHTML = result.svg;
            
            // Apply zoom after rendering
            const svgElement = containerRef.current.querySelector('svg');
            if (svgElement) {
              svgElement.style.transform = `scale(${zoom})`;
            }
          }
        } else {
          throw new Error(result.error || 'Unknown rendering error');
        }
      } catch (err) {
        if (!isMounted) return;
        
        console.error(`[MermaidDiagram] Error rendering diagram ${id}:`, err);
        setError(err instanceof Error ? err.message : 'Unknown error');
        svgRef.current = null;
      } finally {
        if (isMounted) {
          setIsLoading(false);
        }
      }
    };

    renderDiagram();

    return () => {
      isMounted = false;
    };
  }, [code, id]); // Only depend on code and id, not zoom

  // Separate effect for zoom changes
  useEffect(() => {
    if (!containerRef.current || !svgRef.current || isLoading) return;

    const svgElement = containerRef.current.querySelector('svg');
    if (svgElement) {
      svgElement.style.transform = `scale(${zoom})`;
    }
  }, [zoom, isLoading]);

  if (isLoading) {
    return (
      <div className="mermaid-loading">Rendering diagram...</div>
    );
  }

  if (error) {
    return (
      <div className="mermaid-error">
        <strong>⚠️ Failed to render Mermaid diagram</strong><br/>
        <small>Error: {error}</small>
        <details>
          <summary>View diagram source</summary>
          <pre style={{ fontSize: '11px', background: '#1e1e1e', padding: '8px', borderRadius: '4px', overflowX: 'auto' }}>
            {code}
          </pre>
        </details>
      </div>
    );
  }

  return <div ref={containerRef} />;
}, (prevProps, nextProps) => {
  // Custom comparison - only re-render if code or id changes
  // Zoom changes are handled by the zoom effect
  return prevProps.code === nextProps.code && 
         prevProps.id === nextProps.id;
});

MermaidDiagram.displayName = 'MermaidDiagram';

export default MermaidDiagram;
