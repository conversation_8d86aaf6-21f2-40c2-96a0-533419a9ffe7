import React, { useState, useEffect, useCallback, useRef, useMemo } from 'react';
import styled from 'styled-components';
import '../../../styles/markdown.css';
import { fontFamilies } from '../../../utils/fontConfig';
import DOMPurify from 'dompurify';
import { marked } from 'marked';
import mermaidService from '../../../services/MermaidService';
import { useEditorContext } from '../../../contexts/EditorContext';

// Type definitions
type DOMPurifyType = {
  sanitize(source: string, config?: { USE_PROFILES?: { html: boolean } }): string;
};

const purify = DOMPurify as unknown as DOMPurifyType;

export interface MarkdownViewerProps {
  content: string;
  onChange?: (value: string) => void;
  readOnly?: boolean;
  fileName?: string;
  filePath?: string;
  onEditClick?: () => void;
  className?: string;
}

// Styled components (keeping existing styles)
const StyledMarkdownContainer = styled.div`
  font-family: ${fontFamilies.markdown};
  line-height: 1.6;
  color: #d4d4d4;
  padding: 16px;
  overflow-y: auto;
  height: calc(100% - 32px);
  width: calc(100% - 32px);
  background-color: var(--bg-editor);

  /* ... keeping all existing styles ... */
  
  h1, h2, h3, h4, h5, h6 {
    font-family: ${fontFamilies.markdown};
    line-height: 1.3;
    margin-top: 1.5em;
    margin-bottom: 0.5em;
    color: #ffffff;
    scroll-margin-top: 20px;
    position: relative;
    
    &:hover {
      opacity: 0.8;
    }
  }

  h1 {
    font-size: 2em;
    font-weight: 600;
    border-bottom: 1px solid #333;
    padding-bottom: 0.3em;
  }

  h2 {
    font-size: 1.5em;
    font-weight: 600;
    border-bottom: 1px solid #333;
    padding-bottom: 0.3em;
  }

  h3 {
    font-size: 1.25em;
    font-weight: 600;
  }

  p {
    margin-bottom: 1em;
  }

  a {
    color: #007acc;
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
    
    &[href^="#"] {
      color: #6C5CE7;
      cursor: pointer;
      transition: color 0.2s ease;
      
      &:hover {
        color: #8b7cf7;
        text-decoration: underline;
      }
      
      &:before {
        content: "🔗";
        margin-right: 4px;
        font-size: 0.8em;
        opacity: 0.7;
      }
    }
  }

  ul, ol {
    margin-bottom: 1em;
    margin-left: 1.5em;
  }

  li {
    margin-bottom: 0.5em;
  }

  code {
    font-family: ${fontFamilies.code};
    padding: 0.1em 0.4em;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
    font-size: 0.9em;
  }

  pre {
    margin-bottom: 1em;

    code {
      font-family: ${fontFamilies.code};
      padding: 1em;
      line-height: 1.5;
      background-color: #1e1e1e;
      display: block;
      overflow-x: auto;
      border-radius: 5px;
    }
  }

  blockquote {
    padding: 0 1em;
    color: #cccccc;
    border-left: 0.25em solid #444;
    margin: 0 0 1em 0;
  }

  table {
    border-collapse: collapse;
    width: 100%;
    margin-bottom: 1em;
  }

  th, td {
    border: 1px solid #444;
    padding: 8px;
  }

  th {
    padding-top: 12px;
    padding-bottom: 12px;
    text-align: left;
    background-color: #333;
    color: white;
    font-weight: 600;
  }

  img {
    max-width: 100%;
    height: auto;
  }

  hr {
    height: 0.25em;
    padding: 0;
    margin: 24px 0;
    background-color: #444;
    border: 0;
  }

  .mermaid-wrapper {
    position: relative;
    background-color: #1e1e1e;
    border-radius: 0.375rem;
    margin: 1.25rem 0;
    padding: 0;
    overflow: hidden;
    border: 1px solid #333;
    box-shadow: 0 0.125rem 0.5rem rgba(0, 0, 0, 0.15);
    transition: box-shadow 0.2s ease;
  }

  .mermaid-wrapper:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  }

  .mermaid-header {
    background-color: #252526;
    padding: 0.5rem 1rem;
    font-size: 0.6875rem;
    color: #8a8a8a;
    font-family: ${fontFamilies.code};
    border-bottom: 1px solid #333;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: 500;
  }

  .mermaid-label {
    text-transform: uppercase;
    letter-spacing: 0.5px;
    color: #6b7280;
  }

  .mermaid-container {
    background-color: #1e1e1e;
    padding: 2rem 1.5rem;
    overflow: visible;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 11.25rem;
    position: relative;
    
    svg {
      max-width: 100%;
      height: auto;
      background-color: transparent;
      border-radius: 4px;
      transform-origin: center;
      transition: transform 0.2s ease;
    }

    /* Dark theme SVG styling */
    svg text {
      fill: #e6d9ff !important;
      font-family: ${fontFamilies.ui} !important;
    }

    svg rect, svg polygon {
      stroke: #6C5CE7 !important;
    }

    svg path {
      stroke: #6b7280 !important;
    }

    svg .node rect {
      fill: #1a0933 !important;
      stroke: #6C5CE7 !important;
    }

    svg .actor {
      fill: #1a0933 !important;
      stroke: #6C5CE7 !important;
    }

    svg .classBox {
      fill: #1a0933 !important;
      stroke: #6C5CE7 !important;
    }

    svg .nodeLabel, svg .edgeLabel {
      fill: #e6d9ff !important;
    }

    svg .flowchart-link {
      stroke: #6C5CE7 !important;
    }

    /* Force dark theme overrides */
    svg rect, svg polygon, svg ellipse, svg circle, svg path[fill] {
      &[fill^="#f"], &[fill^="#e"], &[fill^="#d"], &[fill^="#c"], &[fill^="#b"], &[fill^="#a"] {
        fill: #1a0933 !important;
      }
      
      &[fill="#ffffff"], &[fill="white"], &[fill="#fff"] {
        fill: #1a0933 !important;
      }
    }

    svg text, svg tspan {
      fill: #e6d9ff !important;
      
      &[fill^="#0"], &[fill^="#1"], &[fill^="#2"], &[fill^="#3"], &[fill^="#4"] {
        fill: #e6d9ff !important;
      }
      
      &[fill="#000000"], &[fill="black"], &[fill="#000"] {
        fill: #e6d9ff !important;
      }
    }

    svg g[class*="subgraph"] rect,
    svg .cluster rect,
    svg .node > rect,
    svg .node > polygon,
    svg .node > circle,
    svg .node > ellipse {
      fill: #1a0933 !important;
      stroke: #6C5CE7 !important;
      stroke-width: 2px !important;
    }
  }

  .mermaid-error {
    color: #ff8a80;
    background-color: #2d1b1b;
    border: 1px solid #5d3a3a;
    border-radius: 6px;
    padding: 16px 20px;
    margin: 16px 0;
    font-family: ${fontFamilies.ui};
    font-size: 0.9em;
    line-height: 1.5;

    strong {
      display: block;
      margin-bottom: 8px;
      font-size: 0.95em;
    }

    small {
      color: #ff8a8080;
      font-size: 0.85em;
    }

    details {
      margin-top: 12px;
      
      summary {
        cursor: pointer;
        color: #ff8a80;
        font-size: 0.85em;
        margin-bottom: 8px;
        
        &:hover {
          color: #ffab91;
        }
      }
    }
  }

  .mermaid-loading {
    color: #8a8a8a;
    padding: 48px 24px;
    text-align: center;
    font-style: italic;
    font-size: 0.9em;
    
    &::before {
      content: "⏳";
      margin-right: 8px;
      animation: spin 2s linear infinite;
    }
  }

  @keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
  }
`;

const ControlsContainer = styled.div`
  position: absolute;
  top: 10px;
  right: 10px;
  display: flex;
  gap: 8px;
  background-color: rgba(30, 30, 30, 0.9);
  padding: 5px;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  z-index: 10;
`;

const Button = styled.button`
  background-color: #333;
  color: #e6e6e6;
  border: none;
  border-radius: 3px;
  padding: 5px 10px;
  font-size: 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 4px;
  font-family: ${fontFamilies.ui};
  transition: background-color 0.2s ease;

  &:hover {
    background-color: #444;
  }

  span {
    font-size: 14px;
    font-weight: bold;
  }
`;

const ErrorDisplay = styled.div`
  padding: 16px;
  background-color: rgba(255, 0, 0, 0.1);
  border: 1px solid rgba(255, 0, 0, 0.3);
  color: #ff9999;
  border-radius: 4px;
  margin: 16px;
`;

// Helper functions
const isMarkdownFile = (fileName?: string): boolean => {
  return !!fileName && fileName.toLowerCase().endsWith('.md');
};

const generateHeadingId = (text: string): string => {
  return text
    .toLowerCase()
    .replace(/[^\w\- ]/g, '')
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-')
    .replace(/^-|-$/g, '');
};

// Create simple content hash for memoization (without crypto dependency)
const createContentHash = (content: string): string => {
  let hash = 0;
  for (let i = 0; i < content.length; i++) {
    const char = content.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  return hash.toString(36);
};

/**
 * MarkdownViewer Component - Optimized version
 */
const MarkdownViewer: React.FC<MarkdownViewerProps> = ({ 
  content, 
  fileName, 
  filePath, 
  onEditClick, 
  className: _className 
}) => {
  const { navigateToFile } = useEditorContext();
  const [isPreviewing, setIsPreviewing] = useState(true);
  const [renderedHTML, setRenderedHTML] = useState<string>('');
  const [renderError, setRenderError] = useState<string | null>(null);
  const [currentZoom, setCurrentZoom] = useState<number>(1);
  const [contentHash, setContentHash] = useState<string>('');
  const currentZoomRef = useRef<number>(1);
  const mermaidCacheRef = useRef<Map<string, { code: string; svg: string | null }>>(new Map());
  const mermaidsRenderedRef = useRef<boolean>(false);
  
  const containerRef = useRef<HTMLDivElement>(null);
  const zoomCheckIntervalRef = useRef<NodeJS.Timeout>();

  // Memoize markdown renderer
  const customRenderer = useMemo(() => {
    const renderer = new marked.Renderer();
    
    renderer.heading = function({ text, depth }: { text: string; depth: number }) {
      const id = generateHeadingId(text);
      return `<h${depth} id="${id}">${text}</h${depth}>`;
    };
    
    renderer.code = function({ text, lang }: { text: string; lang?: string; escaped?: boolean }) {
      const codeText = text || '';
      const language = lang;
      
      if (language === 'mermaid') {
        const id = mermaidService.generateDiagramId();
        const encodedCode = btoa(encodeURIComponent(codeText));
        return `<div class="mermaid-wrapper">
          <div class="mermaid-header">
            <span class="mermaid-label">Mermaid Diagram</span>
          </div>
          <div class="mermaid-container" data-mermaid-id="${id}" data-mermaid-code="${encodedCode}">
            <div class="mermaid-loading">Rendering diagram...</div>
          </div>
        </div>`;
      }
      
      const escapedCode = codeText
        .replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
        .replace(/"/g, '&quot;')
        .replace(/'/g, '&#039;');
      
      return `<pre><code class="language-${language || ''}">${escapedCode}</code></pre>`;
    };

    return renderer;
  }, []); // No dependencies - renderer never changes

  // Initialize MermaidService once
  useEffect(() => {
    mermaidService.initialize({
      securityLevel: 'loose'
    }).catch(err => {
      console.error('[MarkdownViewer] Failed to initialize mermaid service:', err);
      setRenderError('Failed to load Mermaid diagram library');
    });
  }, []); // Only run once on mount

  // Debounced zoom check function
  const checkZoomLevel = useCallback(async () => {
    try {
      const zoomFactor = await (window as any).electronAPI?.zoom?.getZoomFactor();
      if (zoomFactor && zoomFactor !== currentZoom) {
        setCurrentZoom(zoomFactor);
        currentZoomRef.current = zoomFactor;
      }
    } catch (error) {
      console.error('[MarkdownViewer] Error getting zoom factor:', error);
    }
  }, [currentZoom]);

  // Monitor zoom level changes efficiently
  useEffect(() => {
    // Initial zoom check
    checkZoomLevel();

    // Debounce the check function
    let timeoutId: NodeJS.Timeout;
    const debouncedCheck = () => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(checkZoomLevel, 200);
    };

    // Listen for window resize events
    window.addEventListener('resize', debouncedCheck);

    // Check periodically but less frequently
    zoomCheckIntervalRef.current = setInterval(checkZoomLevel, 2000);

    return () => {
      window.removeEventListener('resize', debouncedCheck);
      clearTimeout(timeoutId);
      if (zoomCheckIntervalRef.current) {
        clearInterval(zoomCheckIntervalRef.current);
      }
    };
  }, [checkZoomLevel]);

  // Resolve markdown links
  const resolveMarkdownLink = useCallback((href: string): string | null => {
    if (!filePath || !href) return null;
    if (href.startsWith('#')) return null;
    if (href.startsWith('http://') || href.startsWith('https://')) return null;
    if (!href.endsWith('.md')) return null;
    
    try {
      const currentDir = filePath.substring(0, filePath.lastIndexOf('/'));
      const segments = href.split('/');
      const dirSegments = currentDir.split('/');
      
      for (const segment of segments) {
        if (segment === '..') {
          dirSegments.pop();
        } else if (segment === '.') {
          continue;
        } else if (segment) {
          dirSegments.push(segment);
        }
      }
      
      return dirSegments.join('/');
    } catch (error) {
      console.error('[MarkdownViewer] Error resolving link:', href, error);
      return null;
    }
  }, [filePath]);

  // Parse and render markdown
  const renderMarkdown = useCallback(async () => {
    try {
      if (!content || typeof content !== 'string') {
        setRenderedHTML('<div class="no-content">No content to display</div>');
        return;
      }

      // Check if content has actually changed
      const newHash = createContentHash(content);
      if (newHash === contentHash) {
        return; // Content hasn't changed, skip re-rendering
      }
      setContentHash(newHash);

      // Parse markdown
      const markdownResult = await marked(content, {
        breaks: true,
        gfm: true,
        renderer: customRenderer
      });

      // Clear only stale entries from mermaid cache
      const currentIds = new Set<string>();
      const mermaidContainers = markdownResult.match(/data-mermaid-id="([^"]+)"/g);
      if (mermaidContainers) {
        mermaidContainers.forEach(match => {
          const id = match.match(/data-mermaid-id="([^"]+)"/)?.[1];
          if (id) currentIds.add(id);
        });
      }
      
      // Remove stale cache entries
      for (const [id] of mermaidCacheRef.current) {
        if (!currentIds.has(id)) {
          mermaidCacheRef.current.delete(id);
        }
      }

      // Sanitize HTML
      const html = purify.sanitize(markdownResult, { 
        USE_PROFILES: { html: true },
        ADD_TAGS: ['div', 'details', 'summary'] as any,
        ADD_ATTR: ['data-mermaid-id', 'data-mermaid-code', 'id', 'class'] as any,
      } as any);

      setRenderedHTML(html);
      setRenderError(null);

    } catch (error) {
      console.error('[MarkdownViewer] Error rendering markdown:', error);
      setRenderError(error instanceof Error ? error.message : String(error));
      setRenderedHTML('');
    }
  }, [content, customRenderer]); // Don't depend on contentHash to avoid circular deps

  // Render markdown when content changes
  useEffect(() => {
    renderMarkdown();
  }, [content]); // Only re-run when content actually changes

  // Handle link clicks
  useEffect(() => {
    if (!containerRef.current) return;

    const handleLinkClick = (event: MouseEvent) => {
      const target = event.target as HTMLElement;
      if (target.tagName === 'A') {
        const href = target.getAttribute('href');
        if (!href) return;

        if (href.startsWith('#')) {
          event.preventDefault();
          const targetId = href.substring(1);
          const targetElement = containerRef.current?.querySelector(`#${targetId}`);
          if (targetElement) {
            targetElement.scrollIntoView({ 
              behavior: 'smooth', 
              block: 'start',
              inline: 'nearest'
            });
          }
          return;
        }

        const resolvedPath = resolveMarkdownLink(href);
        if (resolvedPath) {
          event.preventDefault();
          navigateToFile(resolvedPath);
        }
      }
    };

    const container = containerRef.current;
    container.addEventListener('click', handleLinkClick);

    return () => {
      container.removeEventListener('click', handleLinkClick);
    };
  }, [renderedHTML, resolveMarkdownLink, navigateToFile]);

  // Don't render if not a markdown file or not previewing
  if (!isMarkdownFile(fileName) || !isPreviewing) {
    return null;
  }

  const handleToggle = () => {
    if (onEditClick) {
      onEditClick();
    }
  };

  // Render mermaid diagrams with caching - no dependencies on zoom!
  const renderMermaidDiagrams = useCallback(async () => {
    if (!containerRef.current) return;
    
    // Skip if already rendered for this content
    if (mermaidsRenderedRef.current) {
      console.log('[MarkdownViewer] Skipping mermaid render - already rendered for this content');
      return;
    }

    const mermaidContainers = containerRef.current.querySelectorAll('.mermaid-container[data-mermaid-code]');
    
    if (mermaidContainers.length === 0) {
      mermaidsRenderedRef.current = true;
      return;
    }
    
    console.log(`[MarkdownViewer] Rendering ${mermaidContainers.length} mermaid diagrams`);
    
    for (const container of Array.from(mermaidContainers)) {
      const id = container.getAttribute('data-mermaid-id');
      const encodedCode = container.getAttribute('data-mermaid-code');
      
      if (!id || !encodedCode) continue;

      try {
        const code = decodeURIComponent(atob(encodedCode));
        
        // Check cache first
        const cached = mermaidCacheRef.current.get(id);
        if (cached && cached.code === code && cached.svg) {
          console.log(`[MarkdownViewer] Using cached diagram: ${id}`);
          // Use cached SVG
          container.innerHTML = cached.svg;
          
          // Apply current zoom from ref
          const svgElement = container.querySelector('svg');
          if (svgElement) {
            svgElement.style.transform = `scale(${currentZoomRef.current})`;
          }
          continue;
        }

        console.log(`[MarkdownViewer] Rendering new diagram: ${id}`);
        
        // Render new diagram
        const result = await mermaidService.renderDiagram(code, id, {
          applyDarkTheme: true,
          maxRetries: 2,
          timeout: 10000
        });
        
        if (result.success && result.svg) {
          // Cache the result
          mermaidCacheRef.current.set(id, { code, svg: result.svg });
          
          // Update DOM
          container.innerHTML = result.svg;
          
          // Apply current zoom from ref
          const svgElement = container.querySelector('svg');
          if (svgElement) {
            svgElement.style.transform = `scale(${currentZoomRef.current})`;
          }
        } else {
          throw new Error(result.error || 'Unknown rendering error');
        }
      } catch (err) {
        console.error(`[MarkdownViewer] Error rendering diagram ${id}:`, err);
        
        let mermaidCode = '';
        try {
          mermaidCode = decodeURIComponent(atob(encodedCode));
        } catch (e) {
          mermaidCode = 'Unable to decode diagram code';
        }
        
        container.innerHTML = `
          <div class="mermaid-error">
            <strong>⚠️ Failed to render Mermaid diagram</strong><br/>
            <small>Error: ${err instanceof Error ? err.message : 'Unknown error'}</small>
            <details>
              <summary>View diagram source</summary>
              <pre style="font-size: 11px; background: #1e1e1e; padding: 8px; border-radius: 4px; overflow-x: auto;">${mermaidCode}</pre>
            </details>
          </div>
        `;
      }
    }
    
    // Mark as rendered for this content
    mermaidsRenderedRef.current = true;
  }, []); // NO DEPENDENCIES - especially not currentZoom!

  // Render Mermaid diagrams ONLY when content actually changes
  useEffect(() => {
    // Reset the rendered flag when content changes
    mermaidsRenderedRef.current = false;
    
    if (renderedHTML && contentHash) {
      // Small delay to ensure DOM is ready
      const timer = setTimeout(() => {
        renderMermaidDiagrams();
      }, 100);

      return () => clearTimeout(timer);
    }
  }, [contentHash, renderMermaidDiagrams]); // Depend on content hash

  // Separate effect for zoom changes - just update transform
  useEffect(() => {
    if (!containerRef.current || !mermaidsRenderedRef.current) return;

    console.log(`[MarkdownViewer] Applying zoom ${currentZoom} to existing diagrams`);
    const svgElements = containerRef.current.querySelectorAll('.mermaid-container svg');
    svgElements.forEach((svg) => {
      const element = svg as HTMLElement;
      element.style.transform = `scale(${currentZoom})`;
    });
  }, [currentZoom]);

  return (
    <div style={{ position: 'relative', height: '100%', width: '100%', overflow: 'auto', backgroundColor: 'var(--bg-editor)' }}>
      <ControlsContainer>
        <Button
          title="Edit Markdown"
          onClick={handleToggle}
          data-testid="edit-markdown-button"
        >
          <span>✎</span>Edit
        </Button>
      </ControlsContainer>

      <StyledMarkdownContainer 
        ref={containerRef}
        className="markdown-viewer markdown-preview"
      >
        {renderError ? (
          <ErrorDisplay>
            <h3>Error Rendering Markdown</h3>
            <p>{renderError}</p>
            <pre>{typeof content === 'string' ? content.substring(0, 300) : 'No content'}</pre>
          </ErrorDisplay>
        ) : (
          <div
            className="markdown-content"
            dangerouslySetInnerHTML={{ __html: renderedHTML }}
          />
        )}
      </StyledMarkdownContainer>
    </div>
  );
};

/**
 * Error boundary wrapper
 */
export class MarkdownViewerWithErrorBoundary extends React.Component<
  MarkdownViewerProps,
  { hasError: boolean; errorMessage: string }
> {
  constructor(props: MarkdownViewerProps) {
    super(props);
    this.state = {
      hasError: false,
      errorMessage: ''
    };
  }

  static getDerivedStateFromError(error: Error) {
    return {
      hasError: true,
      errorMessage: error.message || 'An error occurred while rendering markdown'
    };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('[MarkdownViewerWithErrorBoundary] Error caught:', error);
    console.error('[MarkdownViewerWithErrorBoundary] Error Info:', errorInfo);
  }

  render() {
    const { fileName } = this.props;

    if (!isMarkdownFile(fileName)) {
      return null;
    }

    if (this.state.hasError) {
      const { content = '' } = this.props;

      return (
        <div style={{ height: '100%', overflow: 'auto', padding: '20px', backgroundColor: 'var(--bg-editor)' }}>
          <ErrorDisplay>
            <h3>Error Rendering Markdown</h3>
            <p>{this.state.errorMessage}</p>
            {this.props.onEditClick && (
              <Button onClick={this.props.onEditClick} data-testid="edit-content-button">
                Edit Content
              </Button>
            )}
          </ErrorDisplay>
          <pre style={{
            padding: '16px',
            backgroundColor: '#1e1e1e',
            color: '#d4d4d4',
            fontFamily: fontFamilies.code,
            fontSize: '12px',
            overflow: 'auto',
            margin: '16px'
          }}>
            {typeof content === 'string' ? content.substring(0, 500) : 'No content'}
            {typeof content === 'string' && content.length > 500 ? '...' : ''}
          </pre>
        </div>
      );
    }

    return <MemoizedMarkdownViewer {...this.props} />;
  }
}

// Memoize the MarkdownViewer component
const MemoizedMarkdownViewer = React.memo(MarkdownViewer, (prevProps, nextProps) => {
  // Custom comparison - only re-render if content or filename actually changes
  return (
    prevProps.content === nextProps.content &&
    prevProps.fileName === nextProps.fileName &&
    prevProps.filePath === nextProps.filePath &&
    // For functions, we assume they're stable or check referential equality
    prevProps.onEditClick === nextProps.onEditClick
  );
});

MemoizedMarkdownViewer.displayName = 'MemoizedMarkdownViewer';

export default MarkdownViewerWithErrorBoundary;
