import React, { useState, useEffect, useCallback, useRef, useMemo } from 'react';
import styled from 'styled-components';
import '../../../styles/markdown.css';
import { fontFamilies } from '../../../utils/fontConfig';
import DOMPurify from 'dompurify';
import { marked } from 'marked';
import mermaidService, { MermaidError, MermaidErrorType } from '../../../services/MermaidService';
import { useEditorContext } from '../../../contexts/EditorContext';

// Extend DOMPurify types
type DOMPurifyType = {
  sanitize(source: string, config?: { USE_PROFILES?: { html: boolean } }): string;
};

// Cast DOMPurify to the correct type
const purify = DOMPurify as unknown as DOMPurifyType;

export interface MarkdownViewerProps {
  content: string;
  onChange?: (value: string) => void;
  readOnly?: boolean;
  fileName?: string;
  filePath?: string; // Add filePath for resolving relative links
  onEditClick?: () => void;
  className?: string;
}

// Styled components for markdown rendering
const StyledMarkdownContainer = styled.div`
  font-family: ${fontFamilies.markdown};
  line-height: 1.6;
  color: #d4d4d4;
  padding: 16px;
  overflow-y: auto;
  height: calc(100% - 32px);
  width: calc(100% - 32px);
  background-color: var(--bg-editor);

  h1, h2, h3, h4, h5, h6 {
    font-family: ${fontFamilies.markdown};
    line-height: 1.3;
    margin-top: 1.5em;
    margin-bottom: 0.5em;
    color: #ffffff;
    scroll-margin-top: 20px; /* Add some padding when scrolling to headings */
    position: relative;
    
    /* Add hover effect to show that headings are linkable */
    &:hover {
      opacity: 0.8;
    }
  }

  h1 {
    font-size: 2em;
    font-weight: 600;
    border-bottom: 1px solid #333;
    padding-bottom: 0.3em;
  }

  h2 {
    font-size: 1.5em;
    font-weight: 600;
    border-bottom: 1px solid #333;
    padding-bottom: 0.3em;
  }

  h3 {
    font-size: 1.25em;
    font-weight: 600;
  }

  p {
    margin-bottom: 1em;
  }

  a {
    color: #007acc;
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
    
    /* Style anchor links (internal links starting with #) */
    &[href^="#"] {
      color: #6C5CE7;
      cursor: pointer;
      transition: color 0.2s ease;
      
      &:hover {
        color: #8b7cf7;
        text-decoration: underline;
      }
      
      &:before {
        content: "🔗";
        margin-right: 4px;
        font-size: 0.8em;
        opacity: 0.7;
      }
    }
  }

  ul, ol {
    margin-bottom: 1em;
    margin-left: 1.5em;
  }

  li {
    margin-bottom: 0.5em;
  }

  code {
    font-family: ${fontFamilies.code};
    padding: 0.1em 0.4em;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
    font-size: 0.9em;
  }

  pre {
    margin-bottom: 1em;

    code {
      font-family: ${fontFamilies.code};
      padding: 1em;
      line-height: 1.5;
      background-color: #1e1e1e;
      display: block;
      overflow-x: auto;
      border-radius: 5px;
    }
  }

  blockquote {
    padding: 0 1em;
    color: #cccccc;
    border-left: 0.25em solid #444;
    margin: 0 0 1em 0;
  }

  table {
    border-collapse: collapse;
    width: 100%;
    margin-bottom: 1em;
  }

  th, td {
    border: 1px solid #444;
    padding: 8px;
  }

  th {
    padding-top: 12px;
    padding-bottom: 12px;
    text-align: left;
    background-color: #333;
    color: white;
    font-weight: 600;
  }

  img {
    max-width: 100%;
    height: auto;
  }

  hr {
    height: 0.25em;
    padding: 0;
    margin: 24px 0;
    background-color: #444;
    border: 0;
  }

  /* Mermaid diagram container styles - Obsidian-inspired */
  .mermaid-wrapper {
    position: relative;
    background-color: #1e1e1e;
    border-radius: 0.375rem;
    margin: 1.25rem 0;
    padding: 0;
    overflow: hidden;
    border: 1px solid #333;
    box-shadow: 0 0.125rem 0.5rem rgba(0, 0, 0, 0.15);
    transition: box-shadow 0.2s ease;
  }

  .mermaid-wrapper:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  }

  .mermaid-header {
    background-color: #252526;
    padding: 0.5rem 1rem;
    font-size: 0.6875rem;
    color: #8a8a8a;
    font-family: ${fontFamilies.code};
    border-bottom: 1px solid #333;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: 500;
  }

  .mermaid-label {
    text-transform: uppercase;
    letter-spacing: 0.5px;
    color: #6b7280;
  }

  .mermaid-container {
    background-color: #1e1e1e;
    padding: 2rem 1.5rem;
    overflow: visible;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 11.25rem;
    position: relative;
    
    svg {
      max-width: 100%;
      height: auto;
      background-color: transparent;
      border-radius: 4px;
      transform-origin: center;
      transition: transform 0.2s ease;
    }

    /* Ensure text in SVG is readable */
    svg text {
      fill: #e6d9ff !important;
      font-family: ${fontFamilies.ui} !important;
    }

    /* Style SVG shapes for dark theme */
    svg rect, svg polygon {
      stroke: #6C5CE7 !important;
    }

    svg path {
      stroke: #6b7280 !important;
    }

    /* Style flowchart nodes */
    svg .node rect {
      fill: #1a0933 !important;
      stroke: #6C5CE7 !important;
    }

    /* Style sequence diagram */
    svg .actor {
      fill: #1a0933 !important;
      stroke: #6C5CE7 !important;
    }

    /* Style class diagram */
    svg .classBox {
      fill: #1a0933 !important;
      stroke: #6C5CE7 !important;
    }

    /* Additional node styling for better contrast */
    svg .nodeLabel, svg .edgeLabel {
      fill: #e6d9ff !important;
    }

    /* Flowchart specific styling */
    svg .flowchart-link {
      stroke: #6C5CE7 !important;
    }

    /* Force dark theme - override ALL light colors in fills */
    svg rect, svg polygon, svg ellipse, svg circle, svg path[fill] {
      /* Override any light background colors with dark theme */
      &[fill^="#f"], &[fill^="#e"], &[fill^="#d"], &[fill^="#c"], &[fill^="#b"], &[fill^="#a"] {
        fill: #1a0933 !important;
      }
      
      /* Specific light color overrides */
      &[fill="#ffffff"], &[fill="white"], &[fill="#fff"] {
        fill: #1a0933 !important;
      }
      
      /* Override light greens, yellows, blues used in the diagram */
      &[fill*="#fff"], &[fill*="#f0f"], &[fill*="#e0e"], &[fill*="#d0d"], 
      &[fill*="#c0c"], &[fill*="#b0b"], &[fill*="#a0a"] {
        fill: #1a0933 !important;
      }
    }

    /* Force dark text colors - override light text */
    svg text, svg tspan {
      fill: #e6d9ff !important;
      
      /* Override any light text colors */
      &[fill^="#0"], &[fill^="#1"], &[fill^="#2"], &[fill^="#3"], &[fill^="#4"] {
        fill: #e6d9ff !important;
      }
      
      &[fill="#000000"], &[fill="black"], &[fill="#000"] {
        fill: #e6d9ff !important;
      }
    }

    /* More aggressive overrides for subgraph boxes and flowchart elements */
    svg g[class*="subgraph"] rect,
    svg .cluster rect,
    svg .node > rect,
    svg .node > polygon,
    svg .node > circle,
    svg .node > ellipse {
      fill: #1a0933 !important;
      stroke: #6C5CE7 !important;
      stroke-width: 2px !important;
    }

    /* Override any inline styles that might set light colors */
    svg [style*="fill:#f"], svg [style*="fill:#e"], svg [style*="fill:#d"], 
    svg [style*="fill:#c"], svg [style*="fill:#b"], svg [style*="fill:#a"],
    svg [style*="fill:white"], svg [style*="fill:#fff"] {
      fill: #1a0933 !important;
    }

    /* Special handling for flowchart subgraph styling */
    svg .cluster .label text,
    svg .subgraph-lvl-1 .cluster .label text {
      fill: #e6d9ff !important;
    }
  }

  .mermaid-error {
    color: #ff8a80;
    background-color: #2d1b1b;
    border: 1px solid #5d3a3a;
    border-radius: 6px;
    padding: 16px 20px;
    margin: 16px 0;
    font-family: ${fontFamilies.ui};
    font-size: 0.9em;
    line-height: 1.5;

    strong {
      display: block;
      margin-bottom: 8px;
      font-size: 0.95em;
    }

    small {
      color: #ff8a8080;
      font-size: 0.85em;
    }

    details {
      margin-top: 12px;
      
      summary {
        cursor: pointer;
        color: #ff8a80;
        font-size: 0.85em;
        margin-bottom: 8px;
        
        &:hover {
          color: #ffab91;
        }
      }
    }
  }

  .mermaid-loading {
    color: #8a8a8a;
    padding: 48px 24px;
    text-align: center;
    font-style: italic;
    font-size: 0.9em;
    
    &::before {
      content: "\u23f3";
      margin-right: 8px;
      animation: spin 2s linear infinite;
    }
  }

  @keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
  }
`;

const ControlsContainer = styled.div`
  position: absolute;
  top: 10px;
  right: 10px;
  display: flex;
  gap: 8px;
  background-color: rgba(30, 30, 30, 0.9);
  padding: 5px;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  z-index: 10;
`;

const Button = styled.button`
  background-color: #333;
  color: #e6e6e6;
  border: none;
  border-radius: 3px;
  padding: 5px 10px;
  font-size: 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 4px;
  font-family: ${fontFamilies.ui};
  transition: background-color 0.2s ease;

  &:hover {
    background-color: #444;
  }

  span {
    font-size: 14px;
    font-weight: bold;
  }
`;

const ErrorDisplay = styled.div`
  padding: 16px;
  background-color: rgba(255, 0, 0, 0.1);
  border: 1px solid rgba(255, 0, 0, 0.3);
  color: #ff9999;
  border-radius: 4px;
  margin: 16px;
`;

/**
 * Helper function to check if a file is a markdown file based on its extension
 */
const isMarkdownFile = (fileName?: string): boolean => {
  return !!fileName && fileName.toLowerCase().endsWith('.md');
};

// No longer needed - using MermaidService

/**
 * MarkdownViewer Component
 * Renders markdown content as HTML with proper styling
 */
const MarkdownViewer: React.FC<MarkdownViewerProps> = ({ content, fileName, filePath, onEditClick, className: _className }) => {
  const { navigateToFile } = useEditorContext();
  const [isPreviewing, setIsPreviewing] = useState(true);
  const [renderedHTML, setRenderedHTML] = useState<string>('');
  const [renderError, setRenderError] = useState<string | null>(null);
  const [currentZoom, setCurrentZoom] = useState<number>(1);
  const containerRef = useRef<HTMLDivElement>(null);
  const renderingRef = useRef<boolean>(false);

  // Helper function to resolve relative markdown links
  const resolveMarkdownLink = useCallback((href: string): string | null => {
    if (!filePath || !href) return null;
    
    // Skip anchor links (already handled)
    if (href.startsWith('#')) return null;
    
    // Skip absolute URLs
    if (href.startsWith('http://') || href.startsWith('https://')) return null;
    
    // Skip non-markdown files for now (could be extended to handle other file types)
    if (!href.endsWith('.md')) return null;
    
    try {
      // Custom path resolution for cross-platform compatibility
      // Get the directory of the current file
      const currentDir = filePath.substring(0, filePath.lastIndexOf('/'));
      
      // Handle relative paths
      const segments = href.split('/');
      const dirSegments = currentDir.split('/');
      
      // Process each segment in the relative path
      for (const segment of segments) {
        if (segment === '..') {
          // Go up one directory
          dirSegments.pop();
        } else if (segment === '.') {
          // Current directory, do nothing
          continue;
        } else if (segment) {
          // Add the segment to the path
          dirSegments.push(segment);
        }
      }
      
      // Join back into a path
      const resolvedPath = dirSegments.join('/');
      
      console.log('[MarkdownViewer] Resolved link:', href, '->', resolvedPath);
      return resolvedPath;
    } catch (error) {
      console.error('[MarkdownViewer] Error resolving link:', href, error);
      return null;
    }
  }, [filePath]);

  // Initialize MermaidService on component mount
  useEffect(() => {
    mermaidService.initialize({
      securityLevel: 'loose' // Using loose for better compatibility in markdown context
    }).catch(err => {
      console.error('[MarkdownViewer] Failed to initialize mermaid service:', err);
      if (err instanceof MermaidError && err.type === MermaidErrorType.LIBRARY_NOT_AVAILABLE) {
        setRenderError('Mermaid library not available. Please install it with "npm install mermaid"');
      } else {
        setRenderError('Failed to load Mermaid diagram library');
      }
    });
    
    return () => {
      // Clear cache when component unmounts
      mermaidService.clearCache();
    };
  }, []);

  // Monitor zoom level changes
  useEffect(() => {
    const updateZoomLevel = async () => {
      try {
        const zoomFactor = await (window as any).electronAPI?.zoom?.getZoomFactor();
        if (zoomFactor && zoomFactor !== currentZoom) {
          setCurrentZoom(zoomFactor);
          console.log('[MarkdownViewer] Zoom level changed to:', zoomFactor);
        }
      } catch (error) {
        console.error('[MarkdownViewer] Error getting zoom factor:', error);
      }
    };

    // Initial zoom level
    updateZoomLevel();

    // Listen for window resize events (triggered by zoom changes)
    const handleResize = () => {
      // Debounce the zoom check
      setTimeout(updateZoomLevel, 100);
    };

    window.addEventListener('resize', handleResize);
    
    // Also check periodically in case zoom changes aren't triggering resize
    const intervalId = setInterval(updateZoomLevel, 1000);

    return () => {
      window.removeEventListener('resize', handleResize);
      clearInterval(intervalId);
    };
  }, [currentZoom]);

  // Helper function to generate heading IDs (like GitHub)
  const generateHeadingId = useCallback((text: string): string => {
    return text
      .toLowerCase()
      .replace(/[^\w\- ]/g, '') // Remove special characters except spaces and hyphens
      .replace(/\s+/g, '-') // Replace spaces with hyphens
      .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
      .replace(/^-|-$/g, ''); // Remove leading/trailing hyphens
  }, []);

  // Create stable custom renderer (memoized)
  const customRenderer = useMemo(() => {
    const renderer = new marked.Renderer();
    
    // Override heading rendering to add IDs
    renderer.heading = function({ text, depth }: { text: string; depth: number }) {
      const id = generateHeadingId(text);
      return `<h${depth} id="${id}">${text}</h${depth}>`;
    };
    
    // Override code block rendering
    renderer.code = function({ text, lang, escaped }: { text: string; lang?: string; escaped?: boolean }) {
      const codeText = text || '';
      const language = lang;
      
      if (language === 'mermaid') {
        // Generate a unique ID for this diagram using the service
        const id = mermaidService.generateDiagramId();
        
        // Store the mermaid code in a data attribute (base64 encoded to avoid parsing issues)
        const encodedCode = btoa(encodeURIComponent(codeText));
        
        return `
          <div class="mermaid-wrapper">
            <div class="mermaid-header">
              <span class="mermaid-label">Mermaid Diagram</span>
            </div>
            <div class="mermaid-container" data-mermaid-id="${id}" data-mermaid-code="${encodedCode}">
              <div class="mermaid-loading">Rendering diagram...</div>
            </div>
          </div>
        `;
      }
      
      // Default code block rendering
      const escapedCode = codeText
        .replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
        .replace(/"/g, '&quot;')
        .replace(/'/g, '&#039;');
      
      return `<pre><code class="language-${language || ''}">${escapedCode}</code></pre>`;
    };

    return renderer;
  }, [generateHeadingId]);  // Include generateHeadingId dependency

  // Render Mermaid diagrams using the service
  const renderMermaidDiagrams = useCallback(async (applyZoom: boolean = true) => {
    if (!containerRef.current || renderingRef.current) {
      return;
    }

    renderingRef.current = true;

    try {
      const mermaidContainers = containerRef.current.querySelectorAll('.mermaid-container[data-mermaid-code]');
      
      if (mermaidContainers.length === 0) {
        return;
      }

      // Render diagrams sequentially to avoid overwhelming the renderer
      for (let i = 0; i < mermaidContainers.length; i++) {
        const container = mermaidContainers[i];
        const id = container.getAttribute('data-mermaid-id');
        const encodedCode = container.getAttribute('data-mermaid-code');
        
        if (!id || !encodedCode) {
          continue;
        }

        try {
          // Decode the mermaid code
          const mermaidCode = decodeURIComponent(atob(encodedCode));

          // Show loading state with diagram number for better feedback
          container.innerHTML = `<div class="mermaid-loading">Rendering diagram ${i + 1} of ${mermaidContainers.length}...</div>`;

          console.log(`[MarkdownViewer] Rendering diagram ${i + 1}/${mermaidContainers.length}: ${id}`);

          // Estimate diagram complexity for timeout adjustment
          const codeLength = mermaidCode.length;
          const complexityScore = Math.min(codeLength / 100, 10); // Scale 0-10
          const timeoutMs = Math.max(5000, complexityScore * 2000); // 5-20 seconds based on complexity

          console.log(`[MarkdownViewer] Rendering diagram with ${codeLength} chars, timeout: ${timeoutMs}ms`);

          // Use the MermaidService to render the diagram with adaptive timeout for large diagrams
          const result = await mermaidService.renderDiagram(mermaidCode, id, {
            applyDarkTheme: true,
            maxRetries: 3,
            timeout: timeoutMs
          });
          
          if (result.success && result.svg) {
            // Clear loading and insert the SVG
            container.innerHTML = result.svg;
            
            // Apply current zoom level to the newly rendered SVG
            if (applyZoom) {
              const svgElement = container.querySelector('svg') as SVGSVGElement;
              if (svgElement && currentZoom !== 1) {
                svgElement.style.transform = `scale(${currentZoom})`;
                console.log(`[MarkdownViewer] Applied zoom ${currentZoom} to newly rendered SVG`);
              }
            }
            
            console.log(`[MarkdownViewer] Successfully rendered diagram ${i + 1}/${mermaidContainers.length}`);
          } else {
            // Handle rendering error
            throw new Error(result.error || 'Unknown rendering error');
          }
          
          // Small delay between renderings to prevent overwhelming the renderer
          if (i < mermaidContainers.length - 1) {
            await new Promise(resolve => setTimeout(resolve, 50));
          }
          
        } catch (err) {
          console.error(`[MarkdownViewer] Error rendering diagram ${id}:`, err);
          
          // Decode the code for error display
          let mermaidCode = '';
          try {
            mermaidCode = decodeURIComponent(atob(encodedCode));
          } catch (e) {
            mermaidCode = 'Unable to decode diagram code';
          }
          
          container.innerHTML = `
            <div class="mermaid-error">
              <strong>⚠️ Failed to render Mermaid diagram</strong><br/>
              <small>Error: ${err instanceof Error ? err.message : 'Unknown error'}</small>
              <br/><br/>
              <details>
                <summary>View diagram source</summary>
                <pre style="font-size: 11px; background: #1e1e1e; padding: 8px; border-radius: 4px; overflow-x: auto;">${mermaidCode}</pre>
              </details>
              <br/>
              <small style="color: #888;">Try refreshing or check the diagram syntax.</small>
            </div>
          `;
        }
      }
    } catch (error) {
      console.error('[MarkdownViewer] Critical error in renderMermaidDiagrams:', error);
      // Update any loading containers with error state
      if (containerRef.current) {
        const loadingContainers = containerRef.current.querySelectorAll('.mermaid-loading');
        loadingContainers.forEach(container => {
          container.innerHTML = `
            <div class="mermaid-error">
              <strong>⚠️ Mermaid initialization failed</strong><br/>
              <small>Check browser console for details</small>
            </div>
          `;
        });
      }
    } finally {
      renderingRef.current = false;
    }
  }, [currentZoom]);

  // Function to render markdown content (stable dependencies)
  const renderMarkdown = useCallback(async () => {
    try {
      if (!content || typeof content !== 'string') {
        setRenderedHTML('<div class="no-content">No content to display</div>');
        return;
      }

      // Clear service cache when content changes
      mermaidService.clearCache();

      // Use the marked function to parse markdown
      let markdownResult: string;
      try {
        markdownResult = await marked(content, {
          breaks: true,
          gfm: true,
          renderer: customRenderer
        });
      } catch (e) {
        console.error('[MarkdownViewer] Error using marked:', e);
        markdownResult = `<pre>${content}</pre>`;
      }

      // Sanitize HTML but preserve our data attributes
      const html = purify.sanitize(markdownResult, { 
        USE_PROFILES: { html: true },
        ADD_TAGS: ['div', 'details', 'summary'] as any,
        ADD_ATTR: ['data-mermaid-id', 'data-mermaid-code', 'id', 'class'] as any,
      } as any);

      setRenderedHTML(html);
      setRenderError(null);

    } catch (error) {
      console.error('[MarkdownViewer] Error rendering markdown:', error);
      setRenderError(error instanceof Error ? error.message : String(error));
      setRenderedHTML('');
    }
  }, [content, customRenderer]);  // Only depend on content and stable renderer

  // Effect to call renderMarkdown when content changes
  useEffect(() => {
    renderMarkdown();
  }, [renderMarkdown]);  // Only depend on renderMarkdown function

  // Effect to render Mermaid diagrams after HTML is updated
  useEffect(() => {
    if (renderedHTML) {
      // Add a small delay to ensure DOM is ready
      const timer = setTimeout(() => {
        renderMermaidDiagrams();
      }, 100);

      return () => clearTimeout(timer);
    }
  }, [renderedHTML, renderMermaidDiagrams]);

  // Effect to apply zoom scaling to existing SVG elements when zoom changes
  useEffect(() => {
    if (!containerRef.current) return;

    const svgElements = containerRef.current.querySelectorAll('.mermaid-container svg');
    console.log(`[MarkdownViewer] Applying zoom ${currentZoom} to ${svgElements.length} existing SVG elements`);
    
    svgElements.forEach((svg, index) => {
      const element = svg as HTMLElement;
      // Apply zoom scaling directly to the SVG
      element.style.transform = `scale(${currentZoom})`;
      console.log(`[MarkdownViewer] Applied scale(${currentZoom}) to existing SVG ${index + 1}`);
    });
  }, [currentZoom]); // Only re-apply when zoom changes (not when HTML re-renders)

  // Handle link clicks for both anchor links and cross-document navigation
  useEffect(() => {
    if (!containerRef.current) return;

    const handleLinkClick = (event: MouseEvent) => {
      const target = event.target as HTMLElement;
      if (target.tagName === 'A') {
        const href = target.getAttribute('href');
        if (!href) return;

        // Handle anchor links (intra-document navigation)
        if (href.startsWith('#')) {
          event.preventDefault();
          
          // Remove the # from the href to get the target ID
          const targetId = href.substring(1);
          
          // Find the target element
          const targetElement = containerRef.current?.querySelector(`#${targetId}`);
          if (targetElement) {
            // Scroll to the target element smoothly
            targetElement.scrollIntoView({ 
              behavior: 'smooth', 
              block: 'start',
              inline: 'nearest'
            });
            
            console.log('[MarkdownViewer] Scrolled to:', targetId);
          } else {
            console.warn('[MarkdownViewer] Target element not found:', targetId);
          }
          return;
        }

        // Handle cross-document markdown links
        const resolvedPath = resolveMarkdownLink(href);
        if (resolvedPath) {
          event.preventDefault();
          
          // Check if there's an anchor in the link (e.g., ./file.md#section)
          const [filePath, anchor] = href.split('#');
          const fullResolvedPath = resolveMarkdownLink(filePath);
          
          if (fullResolvedPath) {
            console.log('[MarkdownViewer] Navigating to:', fullResolvedPath, anchor ? `#${anchor}` : '');
            
            // Navigate to the file using the EditorContext
            navigateToFile(fullResolvedPath);
            
            // If there's an anchor, we could potentially scroll to it after the file loads
            // This would require additional coordination with the editor
            if (anchor) {
              console.log('[MarkdownViewer] TODO: Scroll to anchor after file loads:', anchor);
            }
          }
          return;
        }

        // For external links or non-markdown files, let the default behavior happen
        // (this will open them in the default browser)
      }
    };

    const container = containerRef.current;
    container.addEventListener('click', handleLinkClick);

    return () => {
      container.removeEventListener('click', handleLinkClick);
    };
  }, [renderedHTML, resolveMarkdownLink, navigateToFile]); // Re-attach when HTML changes

  // Only render if this is a markdown file
  if (!isMarkdownFile(fileName)) {
    return null;
  }

  const handleToggle = () => {
    if (isPreviewing && onEditClick) {
      onEditClick();
    } else {
      setIsPreviewing(true);
    }
  };

  // In edit mode, parent should handle rendering
  if (!isPreviewing) {
    return null;
  }

  return (
    <div style={{ position: 'relative', height: '100%', width: '100%', overflow: 'auto', backgroundColor: 'var(--bg-editor)' }}>
      <ControlsContainer>
        <Button
          title="Edit Markdown"
          onClick={handleToggle}
          data-testid="edit-markdown-button"
        >
          <span>✎</span>Edit
        </Button>
      </ControlsContainer>

      <StyledMarkdownContainer 
        ref={containerRef}
        className="markdown-viewer markdown-preview"
      >
        {renderError ? (
          <ErrorDisplay>
            <h3>Error Rendering Markdown</h3>
            <p>{renderError}</p>
            <pre>{typeof content === 'string' ? content.substring(0, 300) : 'No content'}</pre>
          </ErrorDisplay>
        ) : (
          <div
            className="markdown-content"
            dangerouslySetInnerHTML={{ __html: renderedHTML }}
          />
        )}
      </StyledMarkdownContainer>
    </div>
  );
};

/**
 * Error boundary wrapper to catch rendering errors
 */
export class MarkdownViewerWithErrorBoundary extends React.Component<
  MarkdownViewerProps,
  { hasError: boolean; errorMessage: string }
> {
  constructor(props: MarkdownViewerProps) {
    super(props);
    this.state = {
      hasError: false,
      errorMessage: ''
    };
  }

  static getDerivedStateFromError(error: Error) {
    console.error('[MarkdownViewerWithErrorBoundary] Error:', error);
    return {
      hasError: true,
      errorMessage: error.message || 'An error occurred while rendering markdown'
    };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('[MarkdownViewerWithErrorBoundary] Error caught:', error);
    console.error('[MarkdownViewerWithErrorBoundary] Error Info:', errorInfo);
  }

  render() {
    const { fileName } = this.props;

    // Check if this is a markdown file
    if (!isMarkdownFile(fileName)) {
      return null;
    }

    if (this.state.hasError) {
      const { content = '' } = this.props;

      return (
        <div style={{ height: '100%', overflow: 'auto', padding: '20px', backgroundColor: 'var(--bg-editor)' }}>
          <ErrorDisplay>
            <h3>Error Rendering Markdown</h3>
            <p>{this.state.errorMessage}</p>
            {this.props.onEditClick && (
              <Button onClick={this.props.onEditClick} data-testid="edit-content-button">
                Edit Content
              </Button>
            )}
          </ErrorDisplay>
          <pre style={{
            padding: '16px',
            backgroundColor: '#1e1e1e',
            color: '#d4d4d4',
            fontFamily: fontFamilies.code,
            fontSize: '12px',
            overflow: 'auto',
            margin: '16px'
          }}>
            {typeof content === 'string' ? content.substring(0, 500) : 'No content'}
            {typeof content === 'string' && content.length > 500 ? '...' : ''}
          </pre>
        </div>
      );
    }

    return <MarkdownViewer {...this.props} />;
  }
}

// Export the component
export default MarkdownViewerWithErrorBoundary;
