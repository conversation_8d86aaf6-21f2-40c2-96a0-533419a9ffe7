import React, { useState, useRef, useCallback, useEffect } from 'react';
import { Panel, PanelGroup, PanelResizeHandle } from 'react-resizable-panels';
import FileExplorer from '../explorer/FileExplorer';
import { useNovaSonic } from '../../hooks/useNovaSonic';
import { useClipboard } from '../../hooks/useClipboard';
import { useConversation } from '../../contexts/ConversationContext';
import ClipboardContainer from '../../components/ClipboardContainer';
import SimpleSketchCanvas from '../../components/SimpleSketchCanvas';
import '../../styles/voice-agent.css';

// Status message type
interface StatusMessage {
  id: string;
  text: string;
  type: 'info' | 'success' | 'error' | 'transcription' | 'response';
  timestamp: Date;
  icon?: string;
}

// Canvas mode states
type CanvasState = 'idle' | 'connecting' | 'connected' | 'listening' | 'processing' | 'error';

// Mode type for voice/sketch switching
type CanvasMode = 'voice' | 'sketch';

const CanvasMode: React.FC = () => {
  const [canvasState, setCanvasState] = useState<CanvasState>('idle');
  const [currentMode, setCurrentMode] = useState<CanvasMode>('sketch');
  const [statusMessages, setStatusMessages] = useState<StatusMessage[]>([]);
  const [systemPrompt] = useState('You are a helpful AI assistant for building applications with KAPI IDE.');
  const [isAnalyzingSketch, setIsAnalyzingSketch] = useState(false);
  const [lastAnalysisResult, setLastAnalysisResult] = useState<any>(null);
  const [improvedSvgUrl, setImprovedSvgUrl] = useState<string>('');
  
  // Loading states
  const [isProcessingInstructions, setIsProcessingInstructions] = useState(false);
  
  // Text input for direct task commands
  const [taskInput, setTaskInput] = useState('');
  const [isProcessingTask, setIsProcessingTask] = useState(false);
  
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Use clipboard utility for copy button
  const { copyToClipboard } = useClipboard();

  // Use conversation context for multimodal analysis and chat
  const { 
    currentConversationId, 
    createConversation,
    sendMultimodalMessage,
    sendMessage,
    setCurrentStrategy,
    streamingContent,
    state: conversationState
  } = useConversation();

  // Use the working Nova Sonic service instead of AudioApi
  const {
    isConnected,
    isSessionInitialized,
    isStreaming,
    error,
    connect,
    disconnect,
    initializeSession,
    startStreaming,
    stopStreaming,
    textOutput,
    clearOutput
  } = useNovaSonic({
    systemPrompt,
    autoConnect: false,
    autoInitialize: false
  });

  // Auto-scroll to latest status message
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [statusMessages]);

  // Log SVG URL changes
  useEffect(() => {
    if (improvedSvgUrl) {
      console.log('[CanvasMode] Improved SVG URL set:', improvedSvgUrl);
      console.log('[CanvasMode] This will be passed to SimpleSketchCanvas as backgroundSvg prop');
    } else {
      console.log('[CanvasMode] Improved SVG URL cleared');
    }
  }, [improvedSvgUrl]);

  // Update canvas state based on Nova Sonic state
  useEffect(() => {
    if (error) {
      setCanvasState('error');
    } else if (isStreaming) {
      setCanvasState('listening');
    } else if (isSessionInitialized) {
      setCanvasState('connected');
    } else if (isConnected) {
      setCanvasState('connecting');
    } else {
      setCanvasState('idle');
    }
  }, [isConnected, isSessionInitialized, isStreaming, error]);

  // Add status message helper
  const addStatusMessage = useCallback((text: string, type: StatusMessage['type'] = 'info', icon?: string) => {
    const message: StatusMessage = {
      id: `${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
      text,
      type,
      timestamp: new Date(),
      icon
    };
    
    setStatusMessages(prev => [...prev, message]);
  }, []);


  // Add status messages based on text output (Nova Sonic)
  useEffect(() => {
    if (textOutput.length > 0) {
      const latestOutput = textOutput[textOutput.length - 1];
      addStatusMessage(latestOutput, 'response', '🤖');
    }
  }, [textOutput, addStatusMessage]);

  // Monitor new messages for logging and file saving
  useEffect(() => {
    if (conversationState.messages.length > 0) {
      const latestMessage = conversationState.messages[conversationState.messages.length - 1];
      
      // Check if it's a new assistant message
      if (latestMessage && latestMessage.role === 'assistant' && latestMessage.content) {
        console.log('[CanvasMode] New assistant message received:', latestMessage.content.substring(0, 200) + '...');
        
        // Check if the content is HTML (for slides)
        if (isHTMLContent(latestMessage.content)) {
          console.log('[CanvasMode] Detected HTML content, attempting to save to slides folder');
          saveHTMLToSlidesFolder(latestMessage.content);
        }
      }
    }
  }, [conversationState.messages]);

  // Connect to Nova Sonic
  const handleConnect = async () => {
    try {
      setCanvasState('connecting');
      addStatusMessage('Connecting to Nova Sonic...', 'info', '🔄');
      
      await connect();
      
      addStatusMessage('Connected to Nova Sonic!', 'success', '✅');
      
      // Auto-initialize session with default prompt
      await initializeSession(systemPrompt);
      addStatusMessage('Session initialized successfully', 'success', '🎯');
      
    } catch (error) {
      setCanvasState('error');
      addStatusMessage(`Connection failed: ${error instanceof Error ? error.message : 'Unknown error'}`, 'error', '❌');
    }
  };

  // Disconnect from Nova Sonic
  const handleDisconnect = () => {
    disconnect();
    addStatusMessage('Disconnected from Nova Sonic', 'info', '🔌');
  };

  // Start streaming
  const handleStartStreaming = async () => {
    if (!isSessionInitialized) {
      addStatusMessage('Please connect and initialize first', 'error', '⚠️');
      return;
    }

    try {
      await startStreaming();
      addStatusMessage('Streaming started. Speak now...', 'info', '🎙️');
    } catch (error) {
      addStatusMessage('Failed to start streaming. Please check microphone permissions.', 'error', '❌');
      setCanvasState('error');
    }
  };

  // Stop streaming
  const handleStopStreaming = () => {
    stopStreaming();
    addStatusMessage('Streaming stopped', 'info', '⏹️');
  };

  // Clear messages
  const handleClearMessages = () => {
    setStatusMessages([]);
    clearOutput();
  };


  // Copy message text to clipboard using reusable utility
  const handleCopyMessage = useCallback((text: string) => {
    copyToClipboard(text);
  }, [copyToClipboard]);

  // Extract the last SVG from content that may contain multiple SVGs
  const extractLastSvg = useCallback((content: string): string | null => {
    // Find all SVG blocks in the content
    const svgRegex = /<svg[^>]*>[\s\S]*?<\/svg>/gi;
    const svgMatches = content.match(svgRegex);
    
    if (svgMatches && svgMatches.length > 0) {
      // Return the last SVG found
      const lastSvg = svgMatches[svgMatches.length - 1];
      console.log(`[CanvasMode] Found ${svgMatches.length} SVG(s), using the last one`);
      console.log('[CanvasMode] Last SVG content:', lastSvg.substring(0, 200) + '...');
      return lastSvg;
    }
    
    console.log('[CanvasMode] No SVG found in content');
    return null;
  }, []);

  // Save SVG to kapi folder and clear canvas annotations
  const saveSvgAndRenderInCanvas = useCallback(async (svgContent: string) => {
    try {
      // Extract only the last SVG if multiple exist
      const filteredSvg = extractLastSvg(svgContent);
      
      if (!filteredSvg) {
        addStatusMessage('⚠️ No valid SVG found in response', 'error', '⚠️');
        return;
      }
      
      // Generate filename with timestamp
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-').split('T')[0] + '_' + 
                       new Date().toISOString().split('T')[1].substring(0, 8).replace(/:/g, '-');
      const filename = `kapi-design-${timestamp}.svg`;
      
      // Create kapi folder and save SVG using Electron file system
      if (window.electronAPI?.fileExplorer?.createFile) {
        try {
          // First, ensure the kapi directory exists
          if (window.electronAPI?.fileExplorer?.createDirectory) {
            await window.electronAPI.fileExplorer.createDirectory('kapi');
          }
          
          await window.electronAPI.fileExplorer.createFile(`kapi/${filename}`, filteredSvg);
          addStatusMessage(`💾 SVG saved: kapi/${filename}`, 'success', '💾');
        } catch (error) {
          console.error('Error saving SVG:', error);
          addStatusMessage(`⚠️ Failed to save SVG: ${error}`, 'error', '⚠️');
        }
      } else {
        // Fallback: Save to downloads
        const svgBlob = new Blob([filteredSvg], { type: 'image/svg+xml;charset=utf-8' });
        const url = URL.createObjectURL(svgBlob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        a.click();
        URL.revokeObjectURL(url);
        addStatusMessage(`💾 SVG downloaded: ${filename}`, 'success', '💾');
      }
      
      // Create data URL for canvas rendering (CSP compliant)
      const svgDataUrl = `data:image/svg+xml;charset=utf-8,${encodeURIComponent(filteredSvg)}`;
      console.log('[CanvasMode] Filtered SVG data URL created (length:', svgDataUrl.length, ')');
      
      // Set the new URL immediately to avoid flicker
      setImprovedSvgUrl(svgDataUrl);
      
      addStatusMessage('🎨 Design updated in canvas! Previous annotations cleared.', 'success', '🎯');
      addStatusMessage('✏️ You can now draw annotations on the updated design.', 'info', '✨');
      
    } catch (error) {
      console.error('Error saving and rendering SVG:', error);
      addStatusMessage(`❌ Error processing SVG: ${error instanceof Error ? error.message : 'Unknown error'}`, 'error', '❌');
    }
  }, [extractLastSvg, addStatusMessage]);

  // Process text instructions with chat model and TTS
  const handleProcessInstructions = useCallback(async (instructions: string) => {
    if (!instructions?.trim()) return;

    setIsProcessingInstructions(true);
    addStatusMessage('🤖 Processing instructions with AI...', 'info', '🔄');

    try {
      // Ensure we have a conversation for chat task
      let conversationId = currentConversationId;
      if (!conversationId) {
        const conversation = await createConversation(
          'Canvas Instructions',
          'chat'
        );
        conversationId = conversation.id;
        addStatusMessage('Created new conversation for instructions', 'success', '💬');
      }

      // Set strategy to chat
      setCurrentStrategy('chat');
      
      // Send instructions to chat model
      await sendMessage(instructions);
      
      addStatusMessage('✅ Instructions sent to chat model!', 'success', '✅');
      
    } catch (error) {
      console.error('Error processing instructions:', error);
      addStatusMessage(`❌ Failed to process instructions: ${error instanceof Error ? error.message : 'Unknown error'}`, 'error', '❌');
    } finally {
      setIsProcessingInstructions(false);
    }
  }, [currentConversationId, createConversation, sendMessage, setCurrentStrategy, addStatusMessage]);

  // Handle direct task input (slides, mockups, etc.)
  const handleProcessTask = useCallback(async () => {
    if (!taskInput?.trim()) {
      addStatusMessage('⚠️ Please enter a task first', 'error', '⚠️');
      return;
    }

    setIsProcessingTask(true);
    addStatusMessage(`🚀 Processing task: "${taskInput}"`, 'info', '🚀');

    try {
      // Always create a new conversation for task processing
      console.log('🔧 [Canvas] Current conversation ID:', currentConversationId);
      console.log('🔧 [Canvas] Creating new conversation for intelligent task routing');
      
      const conversation = await createConversation(
        'Canvas Task',
        'chat' // Start with chat, let backend classify the task type
      );
      const conversationId = conversation.id;
      addStatusMessage('Created new conversation for intelligent task routing', 'success', '💬');

      console.log('🔧 [Canvas] Using intelligent task classification...');
      
      // Send task to conversation stream - let backend classify task type
      console.log('🔧 [Canvas] Sending to conversation stream for intelligent routing...');
      
      const response = await fetch(`${import.meta.env.VITE_API_URL || 'http://localhost:3000'}/api/conversations/${conversationId}/stream`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token') || 'dev-token'}`
        },
        body: JSON.stringify({
          prompt: taskInput,
          // No taskType specified - let backend classify intelligently
          model: 'claude-3.7-sonnet',
          maxTokens: 8192,
          temperature: 0.7
        })
      });

      if (!response.ok) {
        throw new Error(`Task router failed: ${response.status}`);
      }

      // Handle streaming response
      const reader = response.body?.getReader();
      const decoder = new TextDecoder();
      let fullContent = '';

      if (reader) {
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;

          const chunk = decoder.decode(value, { stream: true });
          const lines = chunk.split('\n');

          for (const line of lines) {
            if (line.startsWith('data: ')) {
              try {
                const data = JSON.parse(line.slice(6));
                if (data.type === 'content') {
                  fullContent += data.content;
                  //addStatusMessage(`📝 Generating: ${fullContent.length} characters...`, 'info', '📝');
                } else if (data.type === 'done') {
                  addStatusMessage(`✅ Task completed! Routed by: ${data.routedBy}`, 'success', '✅');
                  addStatusMessage(`🎯 Task type: ${data.taskType}`, 'info', '🎯');
                  addStatusMessage(`🤖 Model: ${data.model}`, 'info', '🤖');
                  
                  // Check if it's HTML content and save it
                  if (fullContent.includes('<!DOCTYPE html>') || fullContent.includes('<html')) {
                    console.log('[CanvasMode] Detected HTML content, attempting to save to slides folder');
                    saveHTMLToSlidesFolder(fullContent);
                  }
                }
              } catch (e) {
                // Skip malformed JSON
                continue;
              }
            }
          }
        }
      }
      
      // Clear the input after successful processing
      setTaskInput('');
      addStatusMessage('🎯 Task sent for intelligent classification!', 'success', '🎯');
      addStatusMessage('📁 Generated content will be saved to appropriate folders', 'info', '💾');
      
    } catch (error) {
      console.error('Error processing task:', error);
      addStatusMessage(`❌ Failed to process task: ${error instanceof Error ? error.message : 'Unknown error'}`, 'error', '❌');
    } finally {
      setIsProcessingTask(false);
    }
  }, [taskInput, currentConversationId, createConversation, sendMessage, setCurrentStrategy, addStatusMessage]);

  // Handle Enter key in task input
  const handleTaskInputKeyDown = useCallback((e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleProcessTask();
    }
  }, [handleProcessTask]);

  // Check if content is HTML
  const isHTMLContent = useCallback((content: string): boolean => {
    const htmlPattern = /^\s*<!doctype\s+html|^\s*<html[^>]*>|^\s*<\?xml[^>]*\?>\s*<html/i;
    const hasHTMLTags = /<(html|head|body|div|section|script|style|link)\b[^>]*>/i.test(content);
    return htmlPattern.test(content) || (hasHTMLTags && content.length > 500);
  }, []);

  // Clean up HTML content (frontend safety cleanup)
  const cleanupHTMLContent = useCallback((content: string): string => {
    console.log('[CanvasMode] Cleaning up HTML content, original length:', content.length);
    
    // Remove markdown code blocks
    let cleaned = content.replace(/```html\s*/gi, '');
    cleaned = cleaned.replace(/```\s*$/gm, '');
    cleaned = cleaned.replace(/```/g, '');
    
    // Find HTML start and end
    const htmlStartMatch = cleaned.search(/<!DOCTYPE\s+html|<html[^>]*>/i);
    const htmlEndMatch = cleaned.search(/<\/html>\s*$/i);
    
    if (htmlStartMatch !== -1) {
      cleaned = cleaned.substring(htmlStartMatch);
    }
    
    if (htmlEndMatch !== -1) {
      const endPosition = htmlEndMatch + cleaned.match(/<\/html>\s*$/i)![0].length;
      cleaned = cleaned.substring(0, endPosition);
    }
    
    cleaned = cleaned.trim();
    
    console.log('[CanvasMode] HTML cleanup completed, final length:', cleaned.length);
    return cleaned;
  }, []);

  // Save HTML content to slides folder
  const saveHTMLToSlidesFolder = useCallback(async (htmlContent: string) => {
    try {
      // Apply frontend cleanup as additional safety measure
      const cleanedHTML = cleanupHTMLContent(htmlContent);
      
      // Generate filename with timestamp
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-').split('T')[0] + '_' + 
                       new Date().toISOString().split('T')[1].substring(0, 8).replace(/:/g, '-');
      const filename = `slides-${timestamp}.html`;
      
      // Ensure slides directory exists and save HTML file using Electron file system
      if (window.electronAPI?.fileExplorer?.createFile) {
        try {
          // First, ensure the slides directory exists
          if (window.electronAPI?.fileExplorer?.createDirectory) {
            await window.electronAPI.fileExplorer.createDirectory('slides');
          }
          
          await window.electronAPI.fileExplorer.createFile(`slides/${filename}`, cleanedHTML);
          addStatusMessage(`💾 Slides saved: slides/${filename}`, 'success', '💾');
          addStatusMessage(`🌐 Open the file in your browser to view the presentation`, 'info', '🌐');
          addStatusMessage(`📊 Generated with Claude 3.7 Sonnet (${cleanedHTML.length} chars)`, 'info', '📊');
        } catch (error) {
          console.error('Error saving HTML to slides folder:', error);
          addStatusMessage(`⚠️ Failed to save slides: ${error}`, 'error', '⚠️');
        }
      } else {
        // Fallback: Save to downloads
        const htmlBlob = new Blob([cleanedHTML], { type: 'text/html;charset=utf-8' });
        const url = URL.createObjectURL(htmlBlob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        a.click();
        URL.revokeObjectURL(url);
        addStatusMessage(`💾 Slides downloaded: ${filename}`, 'success', '💾');
      }
      
    } catch (error) {
      console.error('Error saving HTML content:', error);
      addStatusMessage(`❌ Error saving slides: ${error instanceof Error ? error.message : 'Unknown error'}`, 'error', '❌');
    }
  }, [addStatusMessage, cleanupHTMLContent]);

  // Handle sketch analysis with conversation context
  const handleAnalyzeSketch = useCallback(async (imageBlob: Blob, instructions?: string) => {
    if (!imageBlob) return;

    setIsAnalyzingSketch(true);
    addStatusMessage('Analyzing sketch with AI...', 'info', '🔍');

    try {
      // Check blob size and compress if needed
      const maxSizeBytes = 500 * 1024; // 500KB limit
      let processedBlob = imageBlob;
      
      if (imageBlob.size > maxSizeBytes) {
        addStatusMessage(`Image too large (${Math.round(imageBlob.size / 1024)}KB), compressing...`, 'info', '🔄');
        
        // Create compressed image
        processedBlob = await new Promise<Blob>((resolve, reject) => {
          const canvas = document.createElement('canvas');
          const ctx = canvas.getContext('2d');
          const img = new Image();
          
          img.onload = () => {
            // Calculate new dimensions to keep reasonable size
            const maxDimension = 800;
            const scale = Math.min(maxDimension / img.width, maxDimension / img.height);
            
            canvas.width = img.width * scale;
            canvas.height = img.height * scale;
            
            ctx?.drawImage(img, 0, 0, canvas.width, canvas.height);
            
            // Convert to blob with reduced quality
            canvas.toBlob((blob) => {
              if (blob) {
                resolve(blob);
              } else {
                reject(new Error('Failed to compress image'));
              }
            }, 'image/jpeg', 0.7); // 70% quality
          };
          
          img.onerror = reject;
          img.src = URL.createObjectURL(imageBlob);
        });
        
        addStatusMessage(`Compressed to ${Math.round(processedBlob.size / 1024)}KB`, 'success', '✅');
      }

      // If instructions are provided, process them with chat model and TTS in parallel
      if (instructions?.trim()) {
        // Process instructions with chat model (async, non-blocking)
        handleProcessInstructions(instructions);
        // Continue with image analysis for SVG generation
      }

      // Convert blob to data URL for analysis
      const dataUrl = await new Promise<string>((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => resolve(reader.result as string);
        reader.onerror = reject;
        reader.readAsDataURL(processedBlob);
      });

      // Ensure we have a conversation
      let conversationId = currentConversationId;
      if (!conversationId) {
        const conversation = await createConversation(
          'Sketch Analysis',
          'multimodal'
        );
        conversationId = conversation.id;
        addStatusMessage('Created new conversation for sketch analysis', 'success', '💬');
      }

      // Send multimodal analysis request
      const analysisInstructions = instructions || 'Please analyze this UI mockup sketch and provide design recommendations. Focus on usability, layout, and modern design principles.';
      
      addStatusMessage('Sending sketch to multimodal AI...', 'info', '📤');

      const response = await sendMultimodalMessage({
        conversationId,
        prompt: 'Please analyze my UI sketch and provide improvement suggestions.',
        images: [{
          url: dataUrl,
          description: 'UI mockup sketch created in the KAPI IDE sketch canvas'
        }],
        instructions: analysisInstructions,
        outputFormat: 'both',
        designStyle: 'modern',
        targetPlatform: 'web'
      });

      if (response.success && response.message) {
        addStatusMessage('Sketch analysis completed!', 'success', '✅');
        
        // Try to parse structured response for better display
        let content = response.message.content;
        console.log('[CanvasMode] Raw AI response content:', content);
        console.log('[CanvasMode] Content type:', typeof content);
        console.log('[CanvasMode] Content length:', content ? content.length : 0);
        
        // Handle empty or invalid content
        if (!content || typeof content !== 'string' || content.trim().length === 0) {
          console.log('[CanvasMode] Empty or invalid content received');
          console.log('[CanvasMode] Full response object:', response);
          addStatusMessage('⚠️ Received empty or invalid response from AI', 'error', '⚠️');
          return;
        }
        
        // Clean up content and detect SVG more robustly
        const cleanContent = content.trim();
        
        // Enhanced SVG detection pattern
        const svgPattern = /^\s*<\?xml[^>]*\?>?\s*<svg[^>]*>|^\s*<svg[^>]*>/i;
        const containsSvgPattern = /<svg[^>]*>/i;
        const isSvgContent = svgPattern.test(cleanContent) || 
                            (containsSvgPattern.test(cleanContent) && cleanContent.includes('</svg>'));
        
        console.log('[CanvasMode] SVG detection result:', isSvgContent);
        console.log('[CanvasMode] First 200 characters:', cleanContent.substring(0, 200));
        
        if (isSvgContent) {
          console.log('[CanvasMode] Detected raw SVG response');
          console.log('[CanvasMode] SVG content received (raw):');
          console.log(cleanContent);
          
          addStatusMessage('🎨 Improved SVG mockup generated!', 'success', '🎨');
          
          // Save SVG to kapi folder and render in canvas (filtering will happen inside)
          await saveSvgAndRenderInCanvas(cleanContent);
          
          // Store result for later use
          setLastAnalysisResult({ 
            content: cleanContent,
            updatedMockup: { improvedSvg: cleanContent }
          });
          
        } else {
          // Try to parse as JSON
          try {
            // Ensure we have valid JSON before parsing
            if (!cleanContent.startsWith('{') && !cleanContent.startsWith('[')) {
              throw new Error('Content does not appear to be JSON');
            }
            
            const structuredResponse = JSON.parse(cleanContent);
            console.log('[CanvasMode] Parsed structured response:', structuredResponse);
            setLastAnalysisResult(structuredResponse); // Store for further actions
            
            if (structuredResponse.analysis) {
              addStatusMessage('📊 Analysis:', 'info', '📊');
              addStatusMessage(structuredResponse.analysis, 'response', '🔍');
            }
            
            if (structuredResponse.recommendations && Array.isArray(structuredResponse.recommendations)) {
              addStatusMessage(`💡 ${structuredResponse.recommendations.length} Recommendations:`, 'info', '💡');
              structuredResponse.recommendations.forEach((rec: string, index: number) => {
                addStatusMessage(`${index + 1}. ${rec}`, 'response', '•');
              });
            }
            
            if (structuredResponse.updatedMockup?.components) {
              addStatusMessage(`🧩 ${structuredResponse.updatedMockup.components.length} Components identified:`, 'info', '🧩');
              structuredResponse.updatedMockup.components.forEach((comp: any, index: number) => {
                addStatusMessage(`${index + 1}. ${comp.type} at (${comp.position?.x || 0}, ${comp.position?.y || 0})`, 'response', '📦');
              });
            }
            
            if (structuredResponse.updatedMockup?.codeSnippet) {
              addStatusMessage('💻 Code snippet generated:', 'info', '💻');
              addStatusMessage(structuredResponse.updatedMockup.codeSnippet, 'response', '📝');
            }
            
            if (structuredResponse.updatedMockup?.improvedSvg) {
              let rawSvgContent = structuredResponse.updatedMockup.improvedSvg;
              console.log('[CanvasMode] SVG content received (from JSON):');
              console.log(rawSvgContent);
              
              addStatusMessage('🎨 Improved SVG mockup generated!', 'success', '🎨');
              
              // Save SVG to kapi folder and render in canvas (filtering will happen inside)
              await saveSvgAndRenderInCanvas(rawSvgContent);
            } else {
              console.log('[CanvasMode] No SVG found in JSON response');
              console.log('[CanvasMode] updatedMockup structure:', structuredResponse.updatedMockup);
            }
            
            // Add action buttons for follow-up
            addStatusMessage('💬 Ask follow-up questions about specific recommendations, or request code implementation!', 'info', '🎯');
            
          } catch (parseError) {
            console.log('[CanvasMode] Failed to parse JSON response:', parseError);
            console.log('[CanvasMode] Raw content that failed to parse:', cleanContent);
            
            // If not JSON and not SVG, display as regular text
            addStatusMessage(cleanContent, 'response', '🤖');
            setLastAnalysisResult({ content: cleanContent }); // Store plain text response
          }
        }
      } else {
        throw new Error(response.error || 'Analysis failed');
      }

    } catch (error) {
      console.error('Error analyzing sketch:', error);
      addStatusMessage(
        `Failed to analyze sketch: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'error',
        '❌'
      );
    } finally {
      setIsAnalyzingSketch(false);
    }
  }, [currentConversationId, createConversation, sendMultimodalMessage, addStatusMessage, saveSvgAndRenderInCanvas, handleProcessInstructions]);

  // Get greeting text based on state
  const getGreetingText = () => {
    switch (canvasState) {
      case 'connecting':
        return "Connecting to Nova Sonic...";
      case 'connected':
        return "Ready to help you build!";
      case 'listening':
        return "I'm listening...";
      case 'processing':
        return "Processing your request...";
      case 'error':
        return "Something went wrong. Let's try again.";
      default:
        return "Hey! Let's build something amazing.";
    }
  };

  const getSubtext = () => {
    if (canvasState === 'idle') {
      return "Connect to Nova Sonic to start building with voice";
    } else if (canvasState === 'connected' && !isStreaming) {
      return "Click 'Start Streaming' and tell me what you want to build";
    }
    return "";
  };


  return (
    <ClipboardContainer className="voice-agent-container">
      <PanelGroup direction="horizontal" autoSaveId="canvas-mode-layout">
        {/* File Explorer Panel */}
        <Panel id="file-explorer" defaultSize={20} minSize={15} maxSize={30}>
          <div className="voice-agent-panel voice-agent-file-panel">
            <div className="voice-agent-panel-header">
              <h3>📁 Project Files</h3>
            </div>
            <div className="voice-agent-file-explorer">
              <FileExplorer />
            </div>
          </div>
        </Panel>

        <PanelResizeHandle className="voice-agent-resize-handle" />

        {/* Main Canvas Panel */}
        <Panel id="canvas-view" defaultSize={50}>
          <div className="voice-agent-agent-panel">
            {/* Header with Status */}
            <div className="voice-agent-header">
              <div className="voice-agent-header-left">
                <h1 className="voice-agent-title">KAPI Canvas Mode</h1>
                
                <p className="voice-agent-subtitle">AI-powered sketch analysis and design generation</p>
              </div>
            </div>

            {/* Direct Task Input */}
            <div className="canvas-task-input-section">
              <div className="canvas-task-input-container">
                <input
                  type="text"
                  value={taskInput}
                  onChange={(e) => setTaskInput(e.target.value)}
                  onKeyDown={handleTaskInputKeyDown}
                  placeholder="Type a task like 'Build slides for my e-commerce app' or 'Create a dashboard mockup'"
                  className="canvas-task-input"
                  disabled={isProcessingTask}
                  maxLength={200}
                />
                <button
                  className={`canvas-task-send-btn ${isProcessingTask ? 'processing' : ''}`}
                  onClick={handleProcessTask}
                  disabled={isProcessingTask || !taskInput.trim()}
                  title="Send task for AI processing"
                >
                  {isProcessingTask ? (
                    <div className="loading-spinner small" />
                  ) : (
                    '🚀'
                  )}
                </button>
              </div>
              <div className="canvas-task-examples">
                <span className="task-examples-label">💡 Try:</span>
                <button 
                  className="task-example-btn"
                  onClick={() => setTaskInput('Build slides for my e-commerce mobile app')}
                  disabled={isProcessingTask}
                >
                  Build slides for my e-commerce mobile app
                </button>
                <button 
                  className="task-example-btn"
                  onClick={() => setTaskInput('Create a dashboard mockup with charts and tables')}
                  disabled={isProcessingTask}
                >
                  Create a dashboard mockup
                </button>
                <button 
                  className="task-example-btn"
                  onClick={() => setTaskInput('Generate a landing page for a SaaS product')}
                  disabled={isProcessingTask}
                >
                  Generate a landing page
                </button>
              </div>
            </div>

            {/* Central Interaction Area */}
            <div className="voice-agent-interaction-area">
              {currentMode === 'voice' ? (
                /* Voice Mode UI */
                <>
                  {/* Purple Pulsating Orb */}
                  <div className="voice-agent-orb-container">
                    <div className={`voice-agent-orb ${canvasState}`}>
                      <div className="voice-agent-orb-inner" />
                      <div className="voice-agent-orb-glow" />
                      {isStreaming && (
                        <>
                          <div className="voice-agent-pulse-ring voice-agent-pulse-1" />
                          <div className="voice-agent-pulse-ring voice-agent-pulse-2" />
                          <div className="voice-agent-pulse-ring voice-agent-pulse-3" />
                        </>
                      )}
                    </div>
                  </div>

                  {/* Greeting Text */}
                  <h2 className="voice-agent-greeting">{getGreetingText()}</h2>
                  {getSubtext() && <p className="voice-agent-subtext">{getSubtext()}</p>}

                  {/* Control Buttons - Only show when connected */}
                  {isConnected && (
                    <div className="voice-agent-controls">
                      <div className="voice-agent-main-controls">
                        {!isStreaming ? (
                          <button
                            className="voice-agent-button voice-agent-button-primary"
                            onClick={handleStartStreaming}
                            disabled={!isSessionInitialized}
                          >
                            🎤 Start Streaming
                          </button>
                        ) : (
                          <button
                            className="voice-agent-button voice-agent-button-stop"
                            onClick={handleStopStreaming}
                          >
                            ⏹️ Stop Streaming
                          </button>
                        )}
                        <button
                          className="voice-agent-button voice-agent-button-secondary"
                          onClick={handleDisconnect}
                        >
                          🔌 Disconnect
                        </button>
                      </div>
                    </div>
                  )}
                </>
              ) : (
                /* Sketch Mode UI */
                <div className="sketch-mode-container">
                  <SimpleSketchCanvas 
                    backgroundSvg={improvedSvgUrl}
                    clearAnnotationsOnNewBackground={true}
                    isProcessing={isAnalyzingSketch || isProcessingInstructions}
                    onAnnotationsChange={(annotations) => {
                      console.log('Sketch updated:', annotations.length, 'annotations');
                    }}
                    onExport={(blob, instructions) => {
                      console.log('Sketch exported from canvas:', {
                        size: blob.size,
                        type: blob.type,
                        instructions: instructions
                      });
                      addStatusMessage(`Sketch exported (${Math.round(blob.size / 1024)}KB)`, 'success', '📤');
                      
                      if (instructions) {
                        addStatusMessage(`Instructions: "${instructions}"`, 'info', '💭');
                      }
                      
                      // Check if blob is empty (indicates drawing issue)
                      if (blob.size < 1000) {
                        addStatusMessage(`⚠️ Warning: Exported image is very small (${blob.size} bytes) - may be empty`, 'error', '⚠️');
                      }
                      
                      // Always analyze the sketch for SVG generation
                      // Instructions will be processed in parallel if provided
                      handleAnalyzeSketch(blob, instructions);
                    }}
                  />
                  
                  {/* Sketch Analysis Controls */}
                  <div className="sketch-controls">
                    <button
                      className={`voice-agent-button voice-agent-button-secondary ${
                        (isAnalyzingSketch || isProcessingInstructions) ? 'processing' : ''
                      }`}
                      onClick={() => {
                        // Trigger export which will automatically analyze
                        const canvas = document.querySelector('.sketch-canvas canvas') as HTMLCanvasElement;
                        if (canvas) {
                          canvas.toBlob((blob) => {
                            if (blob) handleAnalyzeSketch(blob);
                          });
                        }
                      }}
                      disabled={isAnalyzingSketch || isProcessingInstructions || !isConnected}
                    >
                      {isAnalyzingSketch ? (
                        <>🔄 Analyzing<span className="processing-dots"></span></>
                      ) : isProcessingInstructions ? (
                        <>🤖 Processing<span className="processing-dots"></span></>
                      ) : (
                        '🔍 Analyze Sketch'
                      )}
                    </button>
                    
                    {lastAnalysisResult && (
                      <>
                        <button
                          className="voice-agent-button voice-agent-button-secondary"
                          onClick={() => {
                            if (lastAnalysisResult.updatedMockup?.codeSnippet) {
                              copyToClipboard(lastAnalysisResult.updatedMockup.codeSnippet);
                              addStatusMessage('Code snippet copied to clipboard!', 'success', '📋');
                            }
                          }}
                          disabled={!lastAnalysisResult.updatedMockup?.codeSnippet}
                        >
                          📋 Copy Code
                        </button>
                        
                        <button
                          className="voice-agent-button voice-agent-button-secondary"
                          onClick={() => {
                            // Switch to voice mode for follow-up questions
                            setCurrentMode('voice');
                            addStatusMessage('Switched to voice mode. Ask me about the analysis!', 'info', '🎤');
                          }}
                        >
                          🎤 Ask Questions
                        </button>
                        
                        {improvedSvgUrl && (
                          <>
                            <button
                              className="voice-agent-button voice-agent-button-secondary"
                              onClick={() => {
                                addStatusMessage('Improved design is loaded as background!', 'success', '✅');
                                addStatusMessage('Draw annotations or export for further analysis.', 'info', '✏️');
                              }}
                            >
                              🎨 Design Loaded
                            </button>
                            
                            <button
                              className="voice-agent-button voice-agent-button-secondary"
                              onClick={() => {
                                setImprovedSvgUrl('');
                                setLastAnalysisResult(null);
                                addStatusMessage('Canvas cleared. Ready for new sketch!', 'success', '🗑️');
                              }}
                            >
                              🗑️ Clear Canvas
                            </button>
                          </>
                        )}
                      </>
                    )}
                    
                    <span className="sketch-help-text">
                      💡 Draw your UI mockup and click "Analyze Sketch" for AI feedback
                    </span>
                  </div>
                </div>
              )}


              {/* Bottom Area - Connect Button and Status Icons */}
              <div className="voice-agent-bottom-area">
                {!isConnected ? (
                  <button
                    className="voice-agent-button voice-agent-button-primary"
                    onClick={handleConnect}
                    disabled={canvasState === 'connecting'}
                  >
                    🔌 Connect
                  </button>
                ) : (
                  <div className="voice-agent-status-icons">
                    <div className={`voice-agent-icon ${isConnected ? 'active' : ''}`} title="Connection Status">
                      🔗
                    </div>
                    <div className={`voice-agent-icon ${isStreaming ? 'active' : ''}`} title="Streaming Status">
                      📡
                    </div>
                    <div className={`voice-agent-icon ${canvasState === 'listening' ? 'active' : ''}`} title="Microphone Status">
                      🎙️
                    </div>
                  </div>
                )}
              </div>

              {/* Mode Switcher */}
              <div className="voice-agent-mode-switcher">
                <button 
                  className={`voice-agent-mode-btn ${currentMode === 'voice' ? 'active' : ''}`}
                  onClick={() => setCurrentMode('voice')}
                  disabled={isStreaming}
                >
                  🎤 Voice Mode
                </button>
                <button 
                  className={`voice-agent-mode-btn ${currentMode === 'sketch' ? 'active' : ''}`}
                  onClick={() => setCurrentMode('sketch')}
                  disabled={isStreaming}
                >
                  ✏️ Sketch Mode
                </button>
              </div>
            </div>
          </div>
        </Panel>

        <PanelResizeHandle className="voice-agent-resize-handle" />

        {/* Status/Output Panel */}
        <Panel id="status-panel" defaultSize={30} minSize={20} maxSize={40}>
          <div className="voice-agent-panel voice-agent-status-panel">
            <div className="voice-agent-panel-header">
              <h3>💬 Canvas Output</h3>
              {statusMessages.length > 0 && (
                <button 
                  className="voice-agent-clear-button"
                  onClick={handleClearMessages}
                >
                  Clear
                </button>
              )}
            </div>
            <div className="voice-agent-messages-list">
              {statusMessages.length === 0 ? (
                <div className="voice-agent-empty-state">
                  <p>Messages will appear here...</p>
                </div>
              ) : (
                statusMessages.map((message) => (
                  <div
                    key={message.id}
                    className={`voice-agent-message voice-agent-message-${message.type}`}
                  >
                    <div className="voice-agent-message-content">
                      <span className="voice-agent-message-time">
                        [{message.timestamp.toLocaleTimeString()}]
                      </span>
                      {message.icon && <span className="voice-agent-message-icon">{message.icon}</span>}
                      <span className="voice-agent-message-text">{message.text}</span>
                    </div>
                    <button
                      className="voice-agent-message-copy-btn"
                      onClick={() => handleCopyMessage(message.text)}
                      title="Copy message"
                    >
                      📋
                    </button>
                  </div>
                ))
              )}
              <div ref={messagesEndRef} />
            </div>
          </div>
        </Panel>
      </PanelGroup>
    </ClipboardContainer>
  );
};

export default CanvasMode;