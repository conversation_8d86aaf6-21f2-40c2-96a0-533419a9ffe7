/**
 * Terminal Tab Container
 * Main component that orchestrates tabbed terminal interface
 */

import React, { useEffect, useRef } from 'react';
import TerminalTabBar from './TerminalTabBar';
import TerminalWithClipboard from './TerminalWithClipboard';
import { useTerminalTabs } from '../../contexts/TerminalTabContext';
import { TerminalRef } from './Terminal';

interface TerminalTabContainerProps {
  className?: string;
  style?: React.CSSProperties;
}

const TerminalTabContainer: React.FC<TerminalTabContainerProps> = ({
  className = '',
  style = {}
}) => {
  const { state, actions } = useTerminalTabs();
  
  // Debug mounting
  useEffect(() => {
    console.log('[TerminalTabContainer] Mounted with tabs:', state.tabs.length);
    return () => {
      console.log('[TerminalTabContainer] Unmounting');
    };
  }, []);
  const terminalRefs = useRef<Map<string, React.RefObject<TerminalRef>>>(new Map());

  // Get active tab
  const activeTab = state.tabs.find(tab => tab.id === state.activeTabId);

  // Clean up refs for closed tabs
  useEffect(() => {
    const tabIds = new Set(state.tabs.map(tab => tab.id));
    terminalRefs.current.forEach((ref, tabId) => {
      if (!tabIds.has(tabId)) {
        terminalRefs.current.delete(tabId);
        console.log(`[TerminalTabContainer] Cleaned up ref for closed tab: ${tabId}`);
      }
    });
  }, [state.tabs.map(tab => tab.id).join(',')]); // More stable dependency

  // Handle PTY connection for active terminal
  useEffect(() => {
    if (!activeTab) return;

    const terminalRef = terminalRefs.current.get(activeTab.id);
    if (!terminalRef?.current) return;

    // Monitor PTY ID changes
    const checkPtyId = () => {
      const ptyId = terminalRef.current?.getPtyId();
      if (ptyId && ptyId !== activeTab.ptyId) {
        actions.updateTabState(activeTab.id, { ptyId });
      }
    };

    // Check immediately and set up interval
    checkPtyId();
    const interval = setInterval(checkPtyId, 1000);

    return () => clearInterval(interval);
  }, [activeTab, actions]);

  // Update tab state based on terminal activity
  useEffect(() => {
    state.tabs.forEach(tab => {
      const terminalRef = terminalRefs.current.get(tab.id);
      if (!terminalRef?.current) return;

      // TODO: Monitor terminal for:
      // - Running process detection
      // - Working directory changes
      // - Command completion with exit codes
      // This would require extending the Terminal component or adding listeners
    });
  }, [state.tabs]);

  // Debug current state (reduced logging)
  // console.log('[TerminalTabContainer] Render - tabs:', state.tabs.length, 'activeTabId:', state.activeTabId);

  // Handle empty state
  if (state.tabs.length === 0) {
    console.log('[TerminalTabContainer] Rendering empty state');
    return (
      <div
        className={`terminal-tab-container empty ${className}`}
        style={{
          display: 'flex',
          flexDirection: 'column',
          height: '100%',
          minHeight: '200px', // Ensure minimum height
          backgroundColor: '#1e1e1e',
          border: '1px solid #007acc', // Debug border
          ...style
        }}
      >
        <div
          style={{
            flex: 1,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            color: '#cccccc',
            fontSize: '14px',
            fontFamily: 'var(--font-ui)'
          }}
        >
          <div style={{ textAlign: 'center' }}>
            <p>No terminal tabs open</p>
            <button
              onClick={() => {
                console.log('[TerminalTabContainer] Creating tab from empty state');
                actions.createTab();
              }}
              style={{
                marginTop: '8px',
                padding: '8px 16px',
                backgroundColor: '#007acc',
                color: 'white',
                border: 'none',
                borderRadius: '3px',
                cursor: 'pointer',
                fontSize: '12px'
              }}
            >
              Create Terminal
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Debug logging removed to prevent spam

  return (
    <div
      className={`terminal-tab-container ${className}`}
      style={{
        display: 'flex',
        flexDirection: 'column',
        height: '100%',
        minHeight: '200px', // Ensure minimum height
        backgroundColor: '#1e1e1e',
        border: '1px solid #ff6b35', // Debug border - orange
        ...style
      }}
    >
      {/* Tab bar */}
      <TerminalTabBar />

      {/* Terminal content area */}
      <div
        className="terminal-content-area"
        style={{
          flex: 1,
          position: 'relative',
          overflow: 'hidden'
        }}
      >
        {/* Render all terminals but hide inactive ones to preserve state */}
        {state.tabs.map(tab => {
          const isActive = tab.id === state.activeTabId;
          let terminalRef = terminalRefs.current.get(tab.id);

          // Create ref immediately if it doesn't exist (instead of waiting for useEffect)
          if (!terminalRef) {
            console.log(`[TerminalTabContainer] Creating ref immediately for tab ${tab.id}`);
            terminalRef = React.createRef<TerminalRef>();
            terminalRefs.current.set(tab.id, terminalRef);
          }

          // Debug logging removed to prevent spam

          return (
            <div
              key={`terminal-${tab.id}`} // Stable key to prevent remounting
              className={`terminal-content ${isActive ? 'active' : 'inactive'}`}
              style={{
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                visibility: isActive ? 'visible' : 'hidden',
                opacity: isActive ? 1 : 0,
                zIndex: isActive ? 10 : 1,
                backgroundColor: '#1e1e1e',
                pointerEvents: isActive ? 'auto' : 'none' // Prevent interactions with hidden terminals
              }}
            >
              <TerminalWithClipboard
                key={`terminal-component-${tab.id}`} // Stable key to prevent remounting
                ref={terminalRef}
                style={{
                  width: '100%',
                  height: '100%'
                }}
                // Pass static props to prevent re-renders
                options={undefined}
                addons={undefined}
              />
            </div>
          );
        })}
      </div>

      {/* Debug info (development only) */}
      {process.env.NODE_ENV === 'development' && (
        <div
          style={{
            position: 'absolute',
            top: '5px',
            right: '5px',
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            color: 'white',
            padding: '4px 8px',
            fontSize: '10px',
            borderRadius: '3px',
            pointerEvents: 'none',
            zIndex: 1000
          }}
        >
          Tabs: {state.tabs.length} | Active: {activeTab?.title || 'None'}
        </div>
      )}
    </div>
  );
};

export default TerminalTabContainer;