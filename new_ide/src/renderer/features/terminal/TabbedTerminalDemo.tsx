/**
 * Tabbed Terminal Demo Component
 * Shows how to integrate the new tabbed terminal system
 * This can replace the single terminal in IDE.tsx
 */

import React from 'react';
import { TerminalTabProvider } from '../../contexts/TerminalTabContext';
import TerminalTabContainer from './TerminalTabContainer';

interface TabbedTerminalDemoProps {
  className?: string;
  style?: React.CSSProperties;
}

const TabbedTerminalDemo: React.FC<TabbedTerminalDemoProps> = ({
  className = '',
  style = {}
}) => {
  return (
    <TerminalTabProvider>
      <div
        className={`tabbed-terminal-demo ${className}`}
        style={{
          height: '100%',
          width: '100%',
          display: 'flex',
          flexDirection: 'column',
          backgroundColor: '#1e1e1e',
          ...style
        }}
      >
        <TerminalTabContainer />
      </div>
    </TerminalTabProvider>
  );
};

export default TabbedTerminalDemo;