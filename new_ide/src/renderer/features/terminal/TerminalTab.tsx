/**
 * Terminal Tab Component
 * Individual tab in the terminal tab bar
 */

import React, { useState, useRef, useEffect } from 'react';
import { TerminalTab as TerminalTabType } from '../../types/TerminalTypes';
import { useTerminalTabs } from '../../contexts/TerminalTabContext';

interface TerminalTabProps {
  tab: TerminalTabType;
  index: number;
  isActive: boolean;
  onContextMenu: (e: React.MouseEvent, tab: TerminalTabType) => void;
}

const TerminalTab: React.FC<TerminalTabProps> = ({
  tab,
  index,
  isActive,
  onContextMenu
}) => {
  const { actions } = useTerminalTabs();
  const [isEditing, setIsEditing] = useState(false);
  const [editTitle, setEditTitle] = useState(tab.title);
  const [isHovered, setIsHovered] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);

  // Handle tab click
  const handleClick = () => {
    if (!isEditing) {
      actions.switchToTab(tab.id);
    }
  };

  // Handle double click to rename
  const handleDoubleClick = () => {
    startEditing();
  };

  // Start editing mode
  const startEditing = () => {
    setIsEditing(true);
    setEditTitle(tab.title);
  };

  // Save edited title
  const saveTitle = () => {
    const trimmedTitle = editTitle.trim();
    if (trimmedTitle && trimmedTitle !== tab.title) {
      actions.renameTab(tab.id, trimmedTitle);
    }
    setIsEditing(false);
  };

  // Cancel editing
  const cancelEditing = () => {
    setEditTitle(tab.title);
    setIsEditing(false);
  };

  // Handle input events
  const handleInputKeyDown = (e: React.KeyboardEvent) => {
    e.stopPropagation(); // Prevent global shortcuts
    
    if (e.key === 'Enter') {
      saveTitle();
    } else if (e.key === 'Escape') {
      cancelEditing();
    }
  };

  const handleInputBlur = () => {
    saveTitle();
  };

  // Handle close button click
  const handleCloseClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    actions.closeTab(tab.id);
  };

  // Handle context menu
  const handleContextMenu = (e: React.MouseEvent) => {
    e.preventDefault();
    onContextMenu(e, tab);
  };

  // Auto-focus input when editing starts
  useEffect(() => {
    if (isEditing && inputRef.current) {
      inputRef.current.focus();
      inputRef.current.select();
    }
  }, [isEditing]);

  // Format title for display
  const formatTitle = (title: string, maxLength = 20) => {
    if (title.length <= maxLength) return title;
    return title.substring(0, maxLength - 3) + '...';
  };

  // Get tab number for display (1-based)
  const tabNumber = index + 1;
  const showTabNumber = tabNumber <= 9;

  return (
    <div
      className={`terminal-tab ${isActive ? 'active' : ''} ${isHovered ? 'hovered' : ''}`}
      onClick={handleClick}
      onDoubleClick={handleDoubleClick}
      onContextMenu={handleContextMenu}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      data-tab-id={tab.id}
      style={{
        display: 'flex',
        alignItems: 'center',
        padding: '8px 12px',
        minWidth: '100px',
        maxWidth: '200px',
        backgroundColor: isActive ? '#007acc' : '#2d2d30',
        color: isActive ? '#ffffff' : '#cccccc',
        borderRight: '1px solid #3e3e42',
        cursor: 'pointer',
        userSelect: 'none',
        position: 'relative',
        fontSize: '12px',
        fontFamily: 'var(--font-ui)',
        transition: 'background-color 0.15s ease'
      }}
    >
      {/* Tab number indicator */}
      {showTabNumber && (
        <span
          style={{
            fontSize: '10px',
            opacity: 0.7,
            marginRight: '6px',
            minWidth: '12px'
          }}
        >
          {tabNumber}:
        </span>
      )}

      {/* Process status indicator */}
      {tab.hasRunningProcess && (
        <span
          className="process-indicator"
          style={{
            width: '6px',
            height: '6px',
            borderRadius: '50%',
            backgroundColor: '#4CAF50',
            marginRight: '6px',
            animation: 'pulse 1.5s infinite'
          }}
          title="Process running"
        />
      )}

      {/* Error indicator */}
      {tab.exitCode !== null && tab.exitCode !== 0 && (
        <span
          className="error-indicator"
          style={{
            width: '6px',
            height: '6px',
            borderRadius: '50%',
            backgroundColor: '#f44336',
            marginRight: '6px'
          }}
          title={`Last command failed (exit code: ${tab.exitCode})`}
        />
      )}

      {/* Tab title */}
      <div
        style={{
          flex: 1,
          overflow: 'hidden',
          whiteSpace: 'nowrap',
          textOverflow: 'ellipsis'
        }}
      >
        {isEditing ? (
          <input
            ref={inputRef}
            type="text"
            value={editTitle}
            onChange={(e) => setEditTitle(e.target.value)}
            onKeyDown={handleInputKeyDown}
            onBlur={handleInputBlur}
            style={{
              background: 'transparent',
              border: '1px solid #007acc',
              color: 'inherit',
              font: 'inherit',
              width: '100%',
              outline: 'none',
              borderRadius: '2px',
              padding: '1px 3px'
            }}
          />
        ) : (
          <span title={tab.title}>
            {formatTitle(tab.title)}
          </span>
        )}
      </div>

      {/* Modified indicator */}
      {tab.isModified && (
        <span
          style={{
            marginLeft: '4px',
            opacity: 0.8,
            fontSize: '10px'
          }}
        >
          ●
        </span>
      )}

      {/* Close button */}
      {(isActive || isHovered) && !isEditing && (
        <button
          className="tab-close-button"
          onClick={handleCloseClick}
          style={{
            marginLeft: '6px',
            padding: '2px',
            background: 'transparent',
            border: 'none',
            color: 'inherit',
            cursor: 'pointer',
            borderRadius: '2px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            opacity: 0.7,
            fontSize: '12px',
            width: '16px',
            height: '16px',
            lineHeight: '1'
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.backgroundColor = 'rgba(255, 255, 255, 0.1)';
            e.currentTarget.style.opacity = '1';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.backgroundColor = 'transparent';
            e.currentTarget.style.opacity = '0.7';
          }}
          title="Close tab"
        >
          ×
        </button>
      )}

      {/* CSS for animations */}
      <style>{`
        @keyframes pulse {
          0% { opacity: 1; }
          50% { opacity: 0.5; }
          100% { opacity: 1; }
        }
        
        .terminal-tab:hover {
          background-color: ${isActive ? '#0e639c' : '#37373d'} !important;
        }
        
        .terminal-tab.active {
          border-bottom: 2px solid #007acc;
        }
      `}</style>
    </div>
  );
};

export default TerminalTab;