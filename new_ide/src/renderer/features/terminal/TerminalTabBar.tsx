/**
 * Terminal Tab Bar Component
 * Manages the tab bar UI with tabs, new tab button, and overflow handling
 */

import React, { useState, useRef, useEffect } from 'react';
import TerminalTab from './TerminalTab';
import { TerminalTab as TerminalTabType, TabContextMenuOption } from '../../types/TerminalTypes';
import { useTerminalTabs } from '../../contexts/TerminalTabContext';

interface TerminalTabBarProps {
  className?: string;
  style?: React.CSSProperties;
}

const TerminalTabBar: React.FC<TerminalTabBarProps> = ({
  className = '',
  style = {}
}) => {
  const { state, actions, config } = useTerminalTabs();
  const [contextMenu, setContextMenu] = useState<{
    show: boolean;
    x: number;
    y: number;
    tab: TerminalTabType | null;
  }>({
    show: false,
    x: 0,
    y: 0,
    tab: null
  });

  const contextMenuRef = useRef<HTMLDivElement>(null);

  // Don't show tab bar if configured not to show on single tab
  const shouldShowTabBar = config.showTabBar && 
    (config.showTabBarOnSingleTab || state.tabs.length > 1);

  // console.log('[TerminalTabBar] Rendering decision:', {
  //   showTabBar: config.showTabBar,
  //   showTabBarOnSingleTab: config.showTabBarOnSingleTab,
  //   tabCount: state.tabs.length,
  //   shouldShowTabBar
  // });

  if (!shouldShowTabBar) {
    console.log('[TerminalTabBar] Not showing tab bar');
    return null;
  }

  // Handle new tab button click - TEMPORARILY DISABLED
  const handleNewTab = () => {
    // actions.createTab(); // Disabled due to PTY management issues
    console.log('[TerminalTabBar] New tab creation temporarily disabled');
  };

  // Handle context menu
  const handleContextMenu = (e: React.MouseEvent, tab: TerminalTabType) => {
    if (!config.enableTabContextMenu) return;

    e.preventDefault();
    setContextMenu({
      show: true,
      x: e.clientX,
      y: e.clientY,
      tab
    });
  };

  // Close context menu
  const closeContextMenu = () => {
    setContextMenu(prev => ({ ...prev, show: false }));
  };

  // Context menu options
  const getContextMenuOptions = (tab: TerminalTabType): TabContextMenuOption[] => {
    const options: TabContextMenuOption[] = [
      {
        label: 'Rename Tab',
        action: () => {
          // Start inline editing - this will be handled by the tab component
          closeContextMenu();
          // Trigger rename mode in the tab
          setTimeout(() => {
            const tabElement = document.querySelector(`[data-tab-id="${tab.id}"]`);
            if (tabElement) {
              (tabElement as HTMLElement).dispatchEvent(new Event('dblclick'));
            }
          }, 0);
        }
      },
      {
        label: 'Duplicate Tab',
        action: () => {
          console.log('[TerminalTabBar] Duplicate tab temporarily disabled');
          // actions.duplicateTab(tab.id); // Disabled due to PTY management issues
          closeContextMenu();
        },
        disabled: true // Temporarily disabled
      },
      { separator: true } as TabContextMenuOption,
      {
        label: 'Close Tab',
        action: () => {
          actions.closeTab(tab.id);
          closeContextMenu();
        }
      },
      {
        label: 'Close Other Tabs',
        action: () => {
          actions.closeOtherTabs(tab.id);
          closeContextMenu();
        },
        disabled: state.tabs.length <= 1
      },
      {
        label: 'Close Tabs to the Right',
        action: () => {
          actions.closeTabsToRight(tab.id);
          closeContextMenu();
        },
        disabled: state.tabOrder.indexOf(tab.id) >= state.tabOrder.length - 1
      }
    ];

    return options;
  };

  // Handle clicks outside context menu
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (contextMenuRef.current && !contextMenuRef.current.contains(event.target as Node)) {
        closeContextMenu();
      }
    };

    if (contextMenu.show) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [contextMenu.show]);

  // Handle global keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Check if we're in an input field
      if ((e.target as HTMLElement)?.tagName === 'INPUT') return;

      const isMac = navigator.platform.toUpperCase().includes('MAC');
      const ctrlKey = isMac ? e.metaKey : e.ctrlKey;
      
      // Debug keyboard events (uncomment for debugging)
      // if (ctrlKey && e.shiftKey) {
      //   console.log('[TerminalTabBar] Keyboard event:', {
      //     code: e.code,
      //     key: e.key,
      //     ctrlKey,
      //     metaKey: e.metaKey,
      //     shiftKey: e.shiftKey,
      //     isMac
      //   });
      // }

      if (ctrlKey && e.shiftKey) {
        switch (e.code) {
          case 'Backquote': // Ctrl+Shift+` - TEMPORARILY DISABLED
            e.preventDefault();
            console.log('[TerminalTabBar] New tab keyboard shortcut temporarily disabled');
            // handleNewTab(); // Disabled due to PTY management issues
            break;
          case 'KeyW': // Ctrl+Shift+W
            e.preventDefault();
            if (state.activeTabId) {
              actions.closeTab(state.activeTabId);
            }
            break;
          case 'KeyT': // Ctrl+Shift+T - TEMPORARILY DISABLED
            e.preventDefault();
            console.log('[TerminalTabBar] Reopen tab keyboard shortcut temporarily disabled');
            // actions.reopenLastClosedTab(); // Disabled due to PTY management issues
            break;
        }
      } else if (ctrlKey) {
        switch (e.code) {
          case 'PageUp': // Ctrl+PageUp
            e.preventDefault();
            switchToPreviousTab();
            break;
          case 'PageDown': // Ctrl+PageDown
            e.preventDefault();
            switchToNextTab();
            break;
          case 'Digit1':
          case 'Digit2':
          case 'Digit3':
          case 'Digit4':
          case 'Digit5':
          case 'Digit6':
          case 'Digit7':
          case 'Digit8':
          case 'Digit9':
            e.preventDefault();
            const tabNumber = parseInt(e.code.replace('Digit', ''));
            switchToTabByNumber(tabNumber);
            break;
        }
      } else if (e.code === 'F2') {
        e.preventDefault();
        // Start rename on active tab
        if (state.activeTabId) {
          const tabElement = document.querySelector(`[data-tab-id="${state.activeTabId}"]`);
          if (tabElement) {
            (tabElement as HTMLElement).dispatchEvent(new Event('dblclick'));
          }
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [state.activeTabId, state.tabs.length]);

  // Navigation helpers
  const switchToPreviousTab = () => {
    const currentIndex = state.tabOrder.indexOf(state.activeTabId);
    if (currentIndex > 0) {
      actions.switchToTab(state.tabOrder[currentIndex - 1]);
    } else if (state.tabOrder.length > 0) {
      actions.switchToTab(state.tabOrder[state.tabOrder.length - 1]);
    }
  };

  const switchToNextTab = () => {
    const currentIndex = state.tabOrder.indexOf(state.activeTabId);
    if (currentIndex < state.tabOrder.length - 1) {
      actions.switchToTab(state.tabOrder[currentIndex + 1]);
    } else if (state.tabOrder.length > 0) {
      actions.switchToTab(state.tabOrder[0]);
    }
  };

  const switchToTabByNumber = (number: number) => {
    if (number >= 1 && number <= state.tabOrder.length) {
      actions.switchToTab(state.tabOrder[number - 1]);
    }
  };

  return (
    <div
      className={`terminal-tab-bar ${className}`}
      style={{
        display: 'flex',
        alignItems: 'stretch',
        backgroundColor: '#2d2d30',
        borderBottom: '1px solid #3e3e42',
        height: '35px',
        overflow: 'hidden',
        ...style
      }}
    >
      {/* Tab container with horizontal scroll */}
      <div
        style={{
          display: 'flex',
          flex: 1,
          overflowX: 'auto',
          overflowY: 'hidden',
          scrollbarWidth: 'none', // Firefox
          msOverflowStyle: 'none' // IE/Edge
        }}
        className="tab-container"
      >
        {/* Render tabs in order */}
        {state.tabOrder.map((tabId, index) => {
          const tab = state.tabs.find(t => t.id === tabId);
          if (!tab) return null;

          return (
            <TerminalTab
              key={tab.id}
              tab={tab}
              index={index}
              isActive={tab.id === state.activeTabId}
              onContextMenu={handleContextMenu}
            />
          );
        })}
      </div>

      {/* New tab button - TEMPORARILY DISABLED */}
      <button
        className="new-tab-button"
        onClick={handleNewTab}
        disabled={true} // Temporarily disabled
        style={{
          padding: '8px 12px',
          backgroundColor: 'transparent',
          border: 'none',
          color: '#666666', // Dimmed color to show disabled state
          cursor: 'not-allowed',
          fontSize: '16px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          minWidth: '35px',
          borderLeft: '1px solid #3e3e42',
          opacity: 0.5 // Show as disabled
        }}
        title="Terminal tabs temporarily disabled (PTY management issues)"
      >
        +
      </button>

      {/* Context menu */}
      {contextMenu.show && contextMenu.tab && (
        <div
          ref={contextMenuRef}
          className="terminal-tab-context-menu"
          style={{
            position: 'fixed',
            top: contextMenu.y,
            left: contextMenu.x,
            backgroundColor: '#252526',
            border: '1px solid #454545',
            borderRadius: '3px',
            padding: '4px 0',
            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.3)',
            zIndex: 10000,
            minWidth: '160px',
            fontSize: '12px',
            fontFamily: 'var(--font-ui)'
          }}
        >
          {getContextMenuOptions(contextMenu.tab).map((option, index) => {
            if (option.separator) {
              return (
                <div
                  key={index}
                  style={{
                    height: '1px',
                    backgroundColor: '#454545',
                    margin: '4px 0'
                  }}
                />
              );
            }

            return (
              <div
                key={index}
                className="context-menu-item"
                onClick={option.disabled ? undefined : option.action}
                style={{
                  padding: '6px 12px',
                  color: option.disabled ? '#666666' : '#cccccc',
                  cursor: option.disabled ? 'not-allowed' : 'pointer',
                  backgroundColor: 'transparent'
                }}
                onMouseEnter={(e) => {
                  if (!option.disabled) {
                    e.currentTarget.style.backgroundColor = '#094771';
                  }
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = 'transparent';
                }}
              >
                {option.label}
              </div>
            );
          })}
        </div>
      )}

      {/* Hide scrollbar for WebKit browsers */}
      <style>{`
        .tab-container::-webkit-scrollbar {
          display: none;
        }
      `}</style>
    </div>
  );
};

export default TerminalTabBar;