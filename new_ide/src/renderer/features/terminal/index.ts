/**
 * Terminal Feature Exports
 * Central export point for all terminal-related components
 */

// Core terminal components
export { default as Terminal, type TerminalRef } from './Terminal';
export { default as TerminalWithClipboard } from './TerminalWithClipboard';

// Tab components
export { default as TerminalTabContainer } from './TerminalTabContainer';
export { default as TerminalTabBar } from './TerminalTabBar';
export { default as TerminalTab } from './TerminalTab';

// Context and hooks
export { TerminalTabProvider, useTerminalTabs } from '../../contexts/TerminalTabContext';
export { default as TerminalTabContext } from '../../contexts/TerminalTabContext';

// Types
export * from '../../types/TerminalTypes';