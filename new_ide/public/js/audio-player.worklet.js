// Audio player worklet for Nova Sonic
// Handles audio playback with buffering

class ExpandableBuffer {
  constructor() {
    // Start with one second's worth of buffered audio capacity
    this.buffer = new Float32Array(24000);
    this.readIndex = 0;
    this.writeIndex = 0;
    this.underflowedSamples = 0;
    this.isInitialBuffering = true;
    this.initialBufferLength = 24000;  // One second
  }

  write(samples) {
    if (this.writeIndex + samples.length <= this.buffer.length) {
      // Enough space to append the new samples
    } else {
      // Not enough space
      if (samples.length <= this.readIndex) {
        // Shift samples to the beginning of the buffer
        const subarray = this.buffer.subarray(this.readIndex, this.writeIndex);
        // console.log(`Shifting audio buffer by ${this.readIndex}`);
        this.buffer.set(subarray);
      } else {
        // Need to grow the buffer capacity
        const newLength = (samples.length + this.writeIndex - this.readIndex) * 2;
        const newBuffer = new Float32Array(newLength);
        // console.log(`Expanding audio buffer from ${this.buffer.length} to ${newLength}`);
        newBuffer.set(this.buffer.subarray(this.readIndex, this.writeIndex));
        this.buffer = newBuffer;
      }
      this.writeIndex -= this.readIndex;
      this.readIndex = 0;
    }
    
    this.buffer.set(samples, this.writeIndex);
    this.writeIndex += samples.length;
    
    if (this.writeIndex - this.readIndex >= this.initialBufferLength) {
      // Filled the initial buffer length
      this.isInitialBuffering = false;
      // console.log("Initial audio buffer filled");
    }
  }

  read(destination) {
    let copyLength = 0;
    if (!this.isInitialBuffering) {
      // Only start playing after initial buffer is filled
      copyLength = Math.min(destination.length, this.writeIndex - this.readIndex);
    }
    
    destination.set(this.buffer.subarray(this.readIndex, this.readIndex + copyLength));
    this.readIndex += copyLength;
    
    if (copyLength > 0 && this.underflowedSamples > 0) {
      // console.log(`Detected audio buffer underflow of ${this.underflowedSamples} samples`);
      this.underflowedSamples = 0;
    }
    
    if (copyLength < destination.length) {
      // Not enough samples (buffer underflow). Fill with silence.
      destination.fill(0, copyLength);
      this.underflowedSamples += destination.length - copyLength;
    }
    
    if (copyLength === 0) {
      // Ran out of audio, reset to initial buffering
      this.isInitialBuffering = true;
    }
  }

  clearBuffer() {
    this.readIndex = 0;
    this.writeIndex = 0;
    this.underflowedSamples = 0;
    this.isInitialBuffering = true;
  }
}

class AudioPlayerProcessor extends AudioWorkletProcessor {
  constructor() {
    super();
    this.playbackBuffer = new ExpandableBuffer();
    
    // Enhanced playback features
    this.isPlaying = false;
    this.volume = 1.0;
    this.playbackRate = 1.0; // For future speed control
    this.totalSamplesPlayed = 0;
    this.totalSamplesReceived = 0;
    
    // Performance monitoring
    this.underflowCount = 0;
    this.lastMetricsTime = 0;
    this.framesProcessed = 0;
    
    // Audio analysis
    this.peakLevel = 0;
    this.rmsLevel = 0;
    this.levelSmoothingFactor = 0.95;
    
    this.port.onmessage = (event) => {
      if (event.data.type === "audio") {
        this.playbackBuffer.write(event.data.audioData);
        this.totalSamplesReceived += event.data.audioData.length;
        this.isPlaying = true;
      } else if (event.data.type === "barge-in") {
        this.playbackBuffer.clearBuffer();
        this.isPlaying = false;
        this.resetMetrics();
        console.log("Barge-in: Cleared audio buffer");
      } else if (event.data.type === "setVolume") {
        this.volume = Math.max(0, Math.min(1, event.data.volume));
      } else if (event.data.type === "getMetrics") {
        this.sendMetrics();
      } else if (event.data.type === "pause") {
        this.isPlaying = false;
      } else if (event.data.type === "resume") {
        this.isPlaying = true;
      }
    };
  }
  
  resetMetrics() {
    this.totalSamplesPlayed = 0;
    this.totalSamplesReceived = 0;
    this.underflowCount = 0;
    this.lastMetricsTime = currentTime;
    this.framesProcessed = 0;
    this.peakLevel = 0;
    this.rmsLevel = 0;
  }
  
  sendMetrics() {
    const now = currentTime;
    const duration = now - this.lastMetricsTime;
    
    this.port.postMessage({
      type: 'playback-metrics',
      data: {
        totalSamplesPlayed: this.totalSamplesPlayed,
        totalSamplesReceived: this.totalSamplesReceived,
        underflowCount: this.underflowCount,
        framesProcessed: this.framesProcessed,
        peakLevel: this.peakLevel,
        rmsLevel: this.rmsLevel,
        isPlaying: this.isPlaying,
        volume: this.volume,
        duration: duration,
        bufferHealth: this.playbackBuffer.writeIndex - this.playbackBuffer.readIndex
      }
    });
  }
  
  // Calculate audio levels for monitoring
  calculateAudioLevels(samples) {
    let peak = 0;
    let sum = 0;
    
    for (let i = 0; i < samples.length; i++) {
      const abs = Math.abs(samples[i]);
      if (abs > peak) peak = abs;
      sum += samples[i] * samples[i];
    }
    
    const rms = Math.sqrt(sum / samples.length);
    
    // Smooth the levels
    this.peakLevel = Math.max(peak, this.peakLevel * this.levelSmoothingFactor);
    this.rmsLevel = this.rmsLevel * this.levelSmoothingFactor + rms * (1 - this.levelSmoothingFactor);
  }

  process(inputs, outputs, parameters) {
    this.framesProcessed++;
    const output = outputs[0][0]; // Assume one output with one channel
    
    if (this.isPlaying) {
      // Read audio data into a temporary buffer for processing
      const tempBuffer = new Float32Array(output.length);
      this.playbackBuffer.read(tempBuffer);
      
      // Apply volume control
      for (let i = 0; i < tempBuffer.length; i++) {
        output[i] = tempBuffer[i] * this.volume;
      }
      
      // Calculate audio levels for monitoring
      this.calculateAudioLevels(tempBuffer);
      
      // Track samples played
      let actualSamples = 0;
      for (let i = 0; i < tempBuffer.length; i++) {
        if (tempBuffer[i] !== 0) actualSamples++;
      }
      this.totalSamplesPlayed += actualSamples;
      
      // Detect underflows
      if (actualSamples < tempBuffer.length) {
        this.underflowCount++;
      }
      
      // Periodic metrics reporting
      if (this.framesProcessed % 240 === 0) { // Every ~5ms at 48kHz
        this.sendMetrics();
      }
    } else {
      // Fill with silence when not playing
      output.fill(0);
    }
    
    return true; // Keep processor alive
  }
}

registerProcessor("audio-player-processor", AudioPlayerProcessor);