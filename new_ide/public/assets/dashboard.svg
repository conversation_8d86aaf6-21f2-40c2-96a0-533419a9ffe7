<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 800">
  <!-- Background -->
  <rect width="1200" height="800" fill="#1a1a1a"/>
  
  <!-- Header -->
  <rect x="0" y="0" width="1200" height="60" fill="#242424"/>
  <text x="20" y="38" font-family="Arial" font-size="24" fill="#ffffff"><PERSON><PERSON></text>
  <text x="80" y="38" font-family="Arial" font-size="16" fill="#777777">Project Dashboard</text>
  
  <!-- Left sidebar -->
  <rect x="0" y="60" width="60" height="740" fill="#242424"/>
  <circle cx="30" cy="100" r="15" fill="#e87f5e"/>
  <rect x="15" cy="140" width="30" height="30" fill="#444444"/>
  <circle cx="30" cy="200" r="15" fill="#444444"/>
  
  <!-- Project stats cards - first row -->
  <rect x="80" y="80" width="260" height="160" rx="8" ry="8" fill="#5b57c9"/>
  <text x="100" y="120" font-family="Arial" font-size="28" fill="#ffffff">85%</text>
  <text x="160" y="120" font-family="Arial" font-size="16" fill="#ffffff">(+12% ↑)</text>
  <text x="100" y="145" font-family="Arial" font-size="18" fill="#ffffff">Documentation</text>
  <path d="M100,160 L120,150 L140,155 L160,140 L180,145 L200,130 L220,125 L240,135" stroke="#ffffff" stroke-width="2" fill="none"/>
  <circle cx="240" cy="135" r="3" fill="#ffffff"/>
  
  <rect x="360" y="80" width="260" height="160" rx="8" ry="8" fill="#4a8cd8"/>
  <text x="380" y="120" font-family="Arial" font-size="28" fill="#ffffff">92%</text>
  <text x="440" y="120" font-family="Arial" font-size="16" fill="#ffffff">(+5% ↑)</text>
  <text x="380" y="145" font-family="Arial" font-size="18" fill="#ffffff">Code Quality</text>
  <path d="M380,160 L400,155 L420,158 L440,150 L460,148 L480,140 L500,135 L520,130" stroke="#ffffff" stroke-width="2" fill="none"/>
  <circle cx="520" cy="130" r="3" fill="#ffffff"/>
  
  <rect x="640" y="80" width="260" height="160" rx="8" ry="8" fill="#e6b035"/>
  <text x="660" y="120" font-family="Arial" font-size="28" fill="#ffffff">78%</text>
  <text x="720" y="120" font-family="Arial" font-size="16" fill="#ffffff">(+32% ↑)</text>
  <text x="660" y="145" font-family="Arial" font-size="18" fill="#ffffff">Test Coverage</text>
  <path d="M660,170 L680,165 L700,155 L720,160 L740,150 L760,145 L780,150 L800,140" stroke="#ffffff" stroke-width="2" fill="none"/>
  <circle cx="800" cy="140" r="3" fill="#ffffff"/>
  
  <rect x="920" y="80" width="260" height="160" rx="8" ry="8" fill="#e85e5e"/>
  <text x="940" y="120" font-family="Arial" font-size="28" fill="#ffffff">65%</text>
  <text x="1000" y="120" font-family="Arial" font-size="16" fill="#ffffff">(+45% ↑)</text>
  <text x="940" y="145" font-family="Arial" font-size="18" fill="#ffffff">Implementation</text>
  <rect x="940" y="160" width="220" height="10" rx="5" ry="5" fill="rgba(255,255,255,0.3)"/>
  <rect x="940" y="160" width="143" height="10" rx="5" ry="5" fill="#ffffff"/>
  
  <!-- Tabs for main content area -->
  <rect x="80" y="260" width="1100" height="40" fill="#2c2c2c"/>
  <rect x="80" y="260" width="160" height="40" fill="#3a536b"/>
  <text x="130" y="285" font-family="Arial" font-size="16" text-anchor="middle" fill="#ffffff">Overview</text>
  <text x="290" y="285" font-family="Arial" font-size="16" text-anchor="middle" fill="#aaaaaa">Documentation</text>
  <text x="450" y="285" font-family="Arial" font-size="16" text-anchor="middle" fill="#aaaaaa">Tests</text>
  <text x="610" y="285" font-family="Arial" font-size="16" text-anchor="middle" fill="#aaaaaa">Code</text>
  <text x="770" y="285" font-family="Arial" font-size="16" text-anchor="middle" fill="#aaaaaa">Slides</text>
  <text x="930" y="285" font-family="Arial" font-size="16" text-anchor="middle" fill="#aaaaaa">Issues</text>
  
  <!-- Main content area -->
  <rect x="80" y="300" width="1100" height="480" fill="#2c2c2c"/>
  
  <!-- Left column - code quality -->
  <rect x="100" y="320" width="520" height="220" rx="8" ry="8" fill="#303030"/>
  <text x="120" y="350" font-family="Arial" font-size="18" fill="#ffffff">Code Quality Issues</text>
  
  <!-- Linting issues table -->
  <rect x="120" y="370" width="480" height="30" fill="#242424"/>
  <text x="140" y="390" font-family="Arial" font-size="14" fill="#ffffff">File</text>
  <text x="300" y="390" font-family="Arial" font-size="14" fill="#ffffff">Issue</text>
  <text x="500" y="390" font-family="Arial" font-size="14" fill="#ffffff">Severity</text>
  
  <rect x="120" y="400" width="480" height="30" fill="#303030"/>
  <text x="140" y="420" font-family="Arial" font-size="14" fill="#aaaaaa">src/api/tasks.ts</text>
  <text x="300" y="420" font-family="Arial" font-size="14" fill="#aaaaaa">Missing return type</text>
  <rect x="500" y="410" width="60" height="20" rx="10" ry="10" fill="#e6b035"/>
  <text x="530" y="423" font-family="Arial" font-size="12" text-anchor="middle" fill="#ffffff">WARN</text>
  
  <rect x="120" y="430" width="480" height="30" fill="#2c2c2c"/>
  <text x="140" y="450" font-family="Arial" font-size="14" fill="#aaaaaa">src/components/TaskList.tsx</text>
  <text x="300" y="450" font-family="Arial" font-size="14" fill="#aaaaaa">Unused variable</text>
  <rect x="500" y="440" width="60" height="20" rx="10" ry="10" fill="#e6b035"/>
  <text x="530" y="453" font-family="Arial" font-size="12" text-anchor="middle" fill="#ffffff">WARN</text>
  
  <rect x="120" y="460" width="480" height="30" fill="#303030"/>
  <text x="140" y="480" font-family="Arial" font-size="14" fill="#aaaaaa">src/utils/format.ts</text>
  <text x="300" y="480" font-family="Arial" font-size="14" fill="#aaaaaa">Function >50 lines</text>
  <rect x="500" y="470" width="60" height="20" rx="10" ry="10" fill="#e85e5e"/>
  <text x="530" y="483" font-family="Arial" font-size="12" text-anchor="middle" fill="#ffffff">ERROR</text>
  
  <rect x="120" y="490" width="480" height="30" fill="#2c2c2c"/>
  <text x="140" y="510" font-family="Arial" font-size="14" fill="#aaaaaa">src/auth/login.ts</text>
  <text x="300" y="510" font-family="Arial" font-size="14" fill="#aaaaaa">Missing JSDoc</text>
  <rect x="500" y="500" width="60" height="20" rx="10" ry="10" fill="#e85e5e"/>
  <text x="530" y="513" font-family="Arial" font-size="12" text-anchor="middle" fill="#ffffff">ERROR</text>
  
  <!-- Right column - test status -->
  <rect x="640" y="320" width="520" height="220" rx="8" ry="8" fill="#303030"/>
  <text x="660" y="350" font-family="Arial" font-size="18" fill="#ffffff">Test Suite Status</text>
  
  <!-- Test results donut chart -->
  <circle cx="760" cy="460" r="70" fill="#303030" stroke="#4a8cd8" stroke-width="20"/>
  <path d="M760,390 A70,70 0 1,1 700,445" fill="none" stroke="#e6b035" stroke-width="20"/>
  <path d="M700,445 A70,70 0 0,1 710,475" fill="none" stroke="#e85e5e" stroke-width="20"/>
  
  <text x="760" y="450" font-family="Arial" font-size="24" text-anchor="middle" fill="#ffffff">148</text>
  <text x="760" y="475" font-family="Arial" font-size="14" text-anchor="middle" fill="#ffffff">Tests</text>
  
  <!-- Test status legend -->
  <rect x="860" y="400" width="15" height="15" fill="#4a8cd8"/>
  <text x="885" y="413" font-family="Arial" font-size="14" fill="#ffffff">Passing (124)</text>
  
  <rect x="860" y="430" width="15" height="15" fill="#e6b035"/>
  <text x="885" y="443" font-family="Arial" font-size="14" fill="#ffffff">Skipped (18)</text>
  
  <rect x="860" y="460" width="15" height="15" fill="#e85e5e"/>
  <text x="885" y="473" font-family="Arial" font-size="14" fill="#ffffff">Failing (6)</text>
  
  <!-- User Stories & Work Items -->
  <rect x="100" y="560" width="520" height="200" rx="8" ry="8" fill="#303030"/>
  <text x="120" y="590" font-family="Arial" font-size="18" fill="#ffffff">User Stories & Work Items</text>
  
  <!-- Work items table -->
  <rect x="120" y="610" width="480" height="30" fill="#242424"/>
  <text x="140" y="630" font-family="Arial" font-size="14" fill="#ffffff">Story</text>
  <text x="400" y="630" font-family="Arial" font-size="14" fill="#ffffff">Status</text>
  <text x="500" y="630" font-family="Arial" font-size="14" fill="#ffffff">Progress</text>
  
  <rect x="120" y="640" width="480" height="30" fill="#303030"/>
  <text x="140" y="660" font-family="Arial" font-size="14" fill="#aaaaaa">Task management API</text>
  <text x="400" y="660" font-family="Arial" font-size="14" fill="#aaaaaa">In Progress</text>
  <rect x="500" y="650" width="80" height="10" rx="5" ry="5" fill="#333333"/>
  <rect x="500" y="650" width="60" height="10" rx="5" ry="5" fill="#4a8cd8"/>
  
  <rect x="120" y="670" width="480" height="30" fill="#2c2c2c"/>
  <text x="140" y="690" font-family="Arial" font-size="14" fill="#aaaaaa">User authentication</text>
  <text x="400" y="690" font-family="Arial" font-size="14" fill="#aaaaaa">Completed</text>
  <rect x="500" y="680" width="80" height="10" rx="5" ry="5" fill="#333333"/>
  <rect x="500" y="680" width="80" height="10" rx="5" ry="5" fill="#4a8cd8"/>
  
  <rect x="120" y="700" width="480" height="30" fill="#303030"/>
  <text x="140" y="720" font-family="Arial" font-size="14" fill="#aaaaaa">Dashboard UI</text>
  <text x="400" y="720" font-family="Arial" font-size="14" fill="#aaaaaa">In Review</text>
  <rect x="500" y="710" width="80" height="10" rx="5" ry="5" fill="#333333"/>
  <rect x="500" y="710" width="70" height="10" rx="5" ry="5" fill="#e6b035"/>
  
  <!-- Documentation/Slides Preview -->
  <rect x="640" y="560" width="520" height="200" rx="8" ry="8" fill="#303030"/>
  <text x="660" y="590" font-family="Arial" font-size="18" fill="#ffffff">Documentation & Slides</text>
  
  <!-- Documentation preview tabs -->
  <rect x="660" y="610" width="480" height="30" fill="#242424"/>
  <rect x="660" y="610" width="100" height="30" fill="#3a536b"/>
  <text x="710" y="630" font-family="Arial" font-size="14" text-anchor="middle" fill="#ffffff">README</text>
  <text x="790" y="630" font-family="Arial" font-size="14" text-anchor="middle" fill="#aaaaaa">API Docs</text>
  <text x="880" y="630" font-family="Arial" font-size="14" text-anchor="middle" fill="#aaaaaa">Slides</text>
  
  <!-- Doc preview -->
  <rect x="660" y="640" width="480" height="100" fill="#242424"/>
  <text x="680" y="665" font-family="Arial" font-size="14" fill="#ffffff"># Task Management System</text>
  <text x="680" y="690" font-family="Arial" font-size="14" fill="#aaaaaa">A modern task management system for remote teams</text>
  <text x="680" y="715" font-family="Arial" font-size="14" fill="#aaaaaa">that integrates with calendar and communication tools.</text>
  
  <!-- Footer stats -->
  <rect x="80" y="780" width="1100" height="70" rx="8" ry="8" fill="#303030"/> 
  
  <rect x="120" y="800" width="200" height="30" rx="15" ry="15" fill="#242424"/>
  <circle cx="135" cy="815" r="10" fill="#4a8cd8"/>
  <text x="160" y="820" font-family="Arial" font-size="14" fill="#ffffff">Token Usage: 250K / 1M</text>
  
  <rect x="340" y="800" width="200" height="30" rx="15" ry="15" fill="#242424"/>
  <circle cx="355" cy="815" r="10" fill="#e6b035"/>
  <text x="380" y="820" font-family="Arial" font-size="14" fill="#ffffff">Last Updated: Today</text>
  
  <rect x="560" y="800" width="250" height="30" rx="15" ry="15" fill="#242424"/>
  <circle cx="575" cy="815" r="10" fill="#e87f5e"/>
  <text x="600" y="820" font-family="Arial" font-size="14" fill="#ffffff">Daily Review: Scheduled</text>
  
  <rect x="830" y="800" width="200" height="30" rx="15" ry="15" fill="#242424"/>
  <circle cx="845" cy="815" r="10" fill="#5b57c9"/>
  <text x="870" y="820" font-family="Arial" font-size="14" fill="#ffffff">Karma Points: 15</text>
</svg>