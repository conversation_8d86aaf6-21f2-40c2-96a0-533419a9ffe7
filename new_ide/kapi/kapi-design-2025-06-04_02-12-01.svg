<svg width="900" height="600" viewBox="0 0 900 600" fill="none" xmlns="http://www.w3.org/2000/svg">
  <rect width="900" height="600" fill="#141B29"/>
  <!-- User Circle Avatar -->
  <circle cx="66" cy="60" r="24" fill="#252A41"/>
  <text x="66" y="67" text-anchor="middle" alignment-baseline="middle" font-family="Inter, Arial, sans-serif" font-size="22" fill="#A1B6EC" font-weight="bold">K</text>
  <!-- Left Progress Steps -->
  <rect x="38" y="120" width="16" height="48" rx="8" fill="#273053"/>
  <rect x="38" y="180" width="16" height="48" rx="8" fill="#222A44"/>
  <rect x="38" y="240" width="16" height="48" rx="8" fill="#222A44"/>
  <rect x="38" y="300" width="16" height="48" rx="8" fill="#222A44"/>
  <!-- Main card -->
  <rect x="120" y="84" width="540" height="433" rx="16" fill="#1A223A"/>
  <text x="140" y="120" font-family="Inter, Arial, sans-serif" font-size="28" fill="#E9EEFA" font-weight="bold">New Project Setup</text>
  <text x="140" y="154" font-family="Inter, Arial, sans-serif" font-size="15" fill="#6E7CA3">Step 1 of 4: Project Goals</text>
  <!-- Project Goals Section, improved as a headline and list -->
  <text x="160" y="195" font-family="Inter, Arial, sans-serif" font-size="17" fill="#D3E1FF" font-weight="bold">Project Goals</text>
  <rect x="154" y="172" width="500" height="105" rx="9" fill="#212A46"/>
  <g font-family="Inter, Arial, sans-serif" font-size="15" fill="#B8C4EA">
      <text x="175" y="215">1. Build a backwards-first dev environment for Electron</text>
      <text x="175" y="239">2. Focus on documentation and testing before implementation</text>
      <text x="175" y="263">3. Integrate with 3rd party linting and quality tools</text>
  </g>
  <!-- Would you like... Checkboxes -->
  <text x="140" y="298" font-family="Inter, Arial, sans-serif" font-size="15" fill="#BBC7E8">Would you like any of these additional features in your project?</text>
  <g font-family="Inter, Arial, sans-serif" font-size="15" fill="#B8C4EA">
    <!-- Checkbox 1 -->
    <rect x="155" y="314" width="22" height="22" rx="4" fill="#181F32" stroke="#43518A" stroke-width="2"/>
    <text x="185" y="330">Integrated terminal with code context</text>
    <!-- Checkbox 2 -->
    <rect x="155" y="345" width="22" height="22" rx="4" fill="#181F32" stroke="#43518A" stroke-width="2"/>
    <text x="185" y="361">AI-assisted code completion</text>
    <!-- Checkbox 3 -->
    <rect x="155" y="376" width="22" height="22" rx="4" fill="#181F32" stroke="#43518A" stroke-width="2"/>
    <text x="185" y="392">Visual documentation preview</text>
    <!-- Checkbox 4 -->
    <rect x="155" y="407" width="22" height="22" rx="4" fill="#181F32" stroke="#43518A" stroke-width="2"/>
    <text x="185" y="423">Token usage tracking and optimization</text>
    <!-- Checkbox 5 -->
    <rect x="155" y="438" width="22" height="22" rx="4" fill="#181F32" stroke="#43518A" stroke-width="2"/>
    <text x="185" y="454">Developer network with karma points system</text>
  </g>
  <!-- Project Timeline -->
  <text x="140" y="484" font-family="Inter, Arial, sans-serif" font-size="15" fill="#BBC7E8">Project Timeline</text>
  <g>
    <!-- Timeline dots and lines -->
    <circle cx="160" cy="507" r="7" fill="#759DFF"/>
    <rect x="160" y="514" width="3" height="16" fill="#2D3866"/>
    <circle cx="160" cy="537" r="7" fill="#7BAFEE"/>
    <rect x="160" y="544" width="3" height="16" fill="#2D3866"/>
    <circle cx="160" cy="567" r="7" fill="#7BAFEE"/>
    <!-- Timeline text -->
    <text x="180" y="512" font-family="Inter, Arial, sans-serif" font-size="14" fill="#AAC3F5">Documentation setup (1-2 days)</text>
    <text x="180" y="542" font-family="Inter, Arial, sans-serif" font-size="14" fill="#AAC3F5">Test infrastructure (~2-3 days)</text>
    <text x="180" y="572" font-family="Inter, Arial, sans-serif" font-size="14" fill="#AAC3F5">Implementation (~1 week)</text>
  </g>
  <!-- Any changes to the project plan input -->
  <rect x="140" y="590" width="490" height="38" rx="10" fill="#212A46"/>
  <text x="155" y="615" font-family="Inter, Arial, sans-serif" font-size="15" fill="#6E7CA3">Any changes to the project plan?</text>
  <rect x="595" y="596" width="28" height="28" rx="8" fill="#2747CB"/>
  <text x="609" y="616" font-family="Inter, Arial, sans-serif" font-size="20" fill="#ffffff" text-anchor="middle" font-weight="bold">&#10003;</text>
  <!-- Project Knowledge Sidebar -->
  <rect x="700" y="84" width="180" height="433" rx="16" fill="#1A223A"/>
  <text x="715" y="115" font-family="Inter, Arial, sans-serif" font-size="18" fill="#E9EEFA" font-weight="bold">Project Knowledge</text>
  <!-- Doc, Tests, Implementation -->
  <text x="725" y="150" font-family="Inter, Arial, sans-serif" font-size="15" fill="#BCC6E8">Documentation</text>
  <rect x="725" y="160" width="110" height="12" rx="6" fill="#222B4B"/>
  <rect x="725" y="160" width="47.5" height="12" rx="6" fill="#5D81E2"/>
  <text x="845" y="170" font-family="Inter, Arial, sans-serif" font-size="12" fill="#8CA3D7" text-anchor="end">25% complete</text>
  <text x="725" y="195" font-family="Inter, Arial, sans-serif" font-size="15" fill="#BCC6E8">Tests</text>
  <rect x="725" y="205" width="110" height="12" rx="6" fill="#222B4B"/>
  <rect x="725" y="205" width="22" height="12" rx="6" fill="#7BAFEE"/>
  <text x="845" y="215" font-family="Inter, Arial, sans-serif" font-size="12" fill="#8CA3D7" text-anchor="end">10% complete</text>
  <text x="725" y="240" font-family="Inter, Arial, sans-serif" font-size="15" fill="#BCC6E8">Implementation</text>
  <rect x="725" y="250" width="110" height="12" rx="6" fill="#222B4B"/>
  <rect x="725" y="250" width="11.5" height="12" rx="6" fill="#004DDD"/>
  <text x="845" y="260" font-family="Inter, Arial, sans-serif" font-size="12" fill="#8CA3D7" text-anchor="end">5% complete</text>
  <!-- Divider -->
  <rect x="715" y="285" width="148" height="2" fill="#20253D"/>
  <!-- Recommended Resources -->
  <text x="715" y="315" font-family="Inter, Arial, sans-serif" font-size="16" fill="#A4B9F8" font-weight="bold">Recommended Resources</text>
  <rect x="720" y="335" width="154" height="39" rx="8" fill="#20253D"/>
  <text x="728" y="360" font-family="Inter, Arial, sans-serif" font-size="14" fill="#7BB7F5">Electron App Architecture Guide</text>
  <rect x="720" y="384" width="154" height="39" rx="8" fill="#20253D"/>
  <text x="728" y="409" font-family="Inter, Arial, sans-serif" font-size="14" fill="#7BB7F5">ESLint Setup for Electron</text>
</svg>