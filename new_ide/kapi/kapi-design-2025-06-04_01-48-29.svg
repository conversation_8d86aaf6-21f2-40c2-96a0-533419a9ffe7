<svg width="375" height="780" viewBox="0 0 375 780" fill="none" xmlns="http://www.w3.org/2000/svg">
  <rect x="0" y="0" width="375" height="780" fill="#fafbfc" />
  <rect x="0" y="0" width="375" height="56" rx="0" fill="#fff"/>
  <text x="24" y="36" font-size="24" font-family="SF Pro, Arial" font-weight="700" fill="#176AFF">ShopEase</text>
  <circle cx="337" cy="28" r="16" fill="#f1f3f4" />
  <rect x="16" y="64" width="343" height="44" rx="22" fill="#f1f3f4" stroke="#e3e5e8" />
  <text x="34" y="92" font-size="17" font-family="SF Pro, Arial" fill="#9ea5b3">Search products...</text>

  <!-- Product Card 1 -->
  <rect x="16" y="120" width="162" height="200" rx="16" fill="#fff" stroke="#e3e5e8"/>
  <rect x="28" y="132" width="138" height="100" rx="12" fill="#e3f0ff" />
  <text x="38" y="254" font-size="17" font-family="SF Pro, Arial" font-weight="600" fill="#111">Eco Tote Bag</text>
  <text x="38" y="274" font-size="16" font-family="SF Pro, Arial" font-weight="700" fill="#176AFF">$18</text>
  <rect x="38" y="284" width="60" height="28" rx="8" fill="#e3f0ff" />
  <text x="48" y="304" font-size="14" font-family="SF Pro, Arial" fill="#176AFF">Add</text>

  <!-- Product Card 2 -->
  <rect x="197" y="120" width="162" height="200" rx="16" fill="#fff" stroke="#e3e5e8"/>
  <rect x="209" y="132" width="138" height="100" rx="12" fill="#ffe0e3" />
  <text x="219" y="254" font-size="17" font-family="SF Pro, Arial" font-weight="600" fill="#111">Classic Sneakers</text>
  <text x="219" y="274" font-size="16" font-family="SF Pro, Arial" font-weight="700" fill="#176AFF">$55</text>
  <rect x="219" y="284" width="60" height="28" rx="8" fill="#e3f0ff" />
  <text x="229" y="304" font-size="14" font-family="SF Pro, Arial" fill="#176AFF">Add</text>

  <!-- Additional product cards would be similarly arranged -->

  <!-- Floating Action Button -->
  <circle cx="324" cy="648" r="28" fill="#176AFF" filter="url(#shadow)"/>
  <text x="316" y="656" font-size="32" font-family="SF Pro, Arial" font-weight="500" fill="#fff">+</text>

  <!-- Bottom Navigation Bar -->
  <rect x="0" y="720" width="375" height="60" rx="0" fill="#fff" />
  <g fill="#176AFF">
    <circle cx="48" cy="750" r="16"/>
    <circle cx="120" cy="750" r="16" fill="#90A6FF"/>
    <circle cx="192" cy="750" r="16" fill="#90A6FF"/>
    <circle cx="264" cy="750" r="16" fill="#90A6FF"/>
  </g>
  <g font-size="12" font-family="SF Pro, Arial" fill="#176AFF">
    <text x="36" y="772">Home</text>
    <text x="106" y="772" fill="#888">Catg</text>
    <text x="178" y="772" fill="#888">Favs</text>
    <text x="250" y="772" fill="#888">Me</text>
  </g>
  <defs>
  <filter id="shadow" x="285" y="609" width="78" height="78" filterUnits="userSpaceOnUse">
    <feDropShadow dx="0" dy="6" stdDeviation="8" flood-color="#176AFF" flood-opacity="0.15"/>
  </filter>
  </defs>
</svg>