
> kapi-ide@0.1.0 dev
> cross-env NODE_ENV=development VITE_DEV_SERVER_URL=http://localhost:5173 vite

[33mThe CJS build of Vite's Node API is deprecated. See https://vite.dev/guide/troubleshooting.html#vite-cjs-node-api-deprecated for more details.[39m
Environment variables loaded: {
  VITE_USE_MOCK_AUTH: 'false',
  VITE_ENV: undefined,
  MODE: 'development'
}
Port 5173 is in use, trying another one...

  VITE v5.4.18  ready in 164 ms

  ➜  Local:   http://localhost:5174/
  ➜  Network: use --host to expose
vite v5.4.18 building for development...

watching for file changes...
vite v5.4.18 building for development...

watching for file changes...

build started...

build started...
transforming...
transforming...
✓ 1 modules transformed.
rendering chunks...
computing gzip size...
dist/preload/preload.js  18.56 kB │ gzip: 4.23 kB
built in 66ms.
✓ 277 modules transformed.
rendering chunks...
computing gzip size...
dist/main/main.js  12,716.38 kB │ gzip: 2,095.37 kB
built in 10962ms.
Environment information:
- __dirname: /Users/<USER>/Code/kapi-fresh/new_ide/dist/main
- isPackaged: false
Development DIST path: /Users/<USER>/Code/kapi-fresh/new_ide/dist
- DIST path: /Users/<USER>/Code/kapi-fresh/new_ide/dist
Preload paths:
- devPreload: /Users/<USER>/Code/kapi-fresh/new_ide/dist/preload/preload.js
- prodPreload: /Users/<USER>/Code/kapi-fresh/new_ide/dist/preload/preload.js
- devPreload exists: true
- prodPreload exists: true
- chosen preload: /Users/<USER>/Code/kapi-fresh/new_ide/dist/preload/preload.js
Environment variables: {
  NVM_INC: '/Users/<USER>/.nvm/versions/node/v20.16.0/include/node',
  CLAUDE_CODE_ENTRYPOINT: 'cli',
  TERM_PROGRAM: 'vscode',
  NODE: '/Users/<USER>/.nvm/versions/node/v20.16.0/bin/node',
  INIT_CWD: '/Users/<USER>/Code/kapi-fresh/new_ide',
  NVM_CD_FLAGS: '-q',
  TERM: 'xterm-256color',
  SHELL: '/bin/zsh',
  HOMEBREW_REPOSITORY: '/opt/homebrew',
  TMPDIR: '/var/folders/m1/q6hdb5210rn1150gnzw33fdh0000gn/T/',
  npm_config_global_prefix: '/Users/<USER>/.nvm/versions/node/v20.16.0',
  CONDA_SHLVL: '0',
  TERM_PROGRAM_VERSION: '0.50.5',
  ZDOTDIR: '/Users/<USER>',
  CURSOR_TRACE_ID: 'e8d2f0700a074524a417e8b2a55f5313',
  ORIGINAL_XDG_CURRENT_DESKTOP: 'undefined',
  MallocNanoZone: '0',
  COLOR: '0',
  npm_config_noproxy: '',
  npm_config_local_prefix: '/Users/<USER>/Code/kapi-fresh/new_ide',
  ENABLE_IDE_INTEGRATION: 'true',
  GIT_EDITOR: 'true',
  OBJC_DISABLE_INITIALIZE_FORK_SAFETY: 'YES',
  NVM_DIR: '/Users/<USER>/.nvm',
  USER: 'balajiviswanathan',
  COMMAND_MODE: 'unix2003',
  npm_config_globalconfig: '/Users/<USER>/.nvm/versions/node/v20.16.0/etc/npmrc',
  CONDA_EXE: '/Users/<USER>/miniconda3/bin/conda',
  CLAUDE_CODE_SSE_PORT: '49630',
  SSH_AUTH_SOCK: '/private/tmp/com.apple.launchd.K4JZGhmBC1/Listeners',
  __CF_USER_TEXT_ENCODING: '0x1F5:0x0:0x0',
  npm_execpath: '/Users/<USER>/.nvm/versions/node/v20.16.0/lib/node_modules/npm/bin/npm-cli.js',
  _CE_CONDA: '',
  PATH: '/Users/<USER>/Code/kapi-fresh/new_ide/node_modules/.bin:/Users/<USER>/Code/kapi-fresh/node_modules/.bin:/Users/<USER>/Code/node_modules/.bin:/Users/<USER>/node_modules/.bin:/Users/<USER>/.bin:/node_modules/.bin:/Users/<USER>/.nvm/versions/node/v20.16.0/lib/node_modules/npm/node_modules/@npmcli/run-script/lib/node-gyp-bin:/Users/<USER>/.local/bin:/opt/homebrew/opt/postgresql@17/bin:/usr/local/opt/postgresql@17/bin:/Users/<USER>/.codeium/windsurf/bin:/Users/<USER>/.rbenv/shims:/Users/<USER>/.rbenv/bin:/Users/<USER>/.nvm/versions/node/v20.16.0/bin:/Users/<USER>/miniconda3/condabin:/opt/homebrew/bin:/opt/homebrew/sbin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin',
  npm_package_json: '/Users/<USER>/Code/kapi-fresh/new_ide/package.json',
  _: '/Users/<USER>/Code/kapi-fresh/new_ide/node_modules/.bin/cross-env',
  npm_config_userconfig: '/Users/<USER>/.npmrc',
  npm_config_init_module: '/Users/<USER>/.npm-init.js',
  USER_ZDOTDIR: '/Users/<USER>',
  __CFBundleIdentifier: 'com.todesktop.230313mzl4w4u92',
  npm_command: 'run-script',
  PWD: '/Users/<USER>/Code/kapi-fresh/new_ide',
  npm_lifecycle_event: 'dev',
  EDITOR: 'vi',
  OTEL_EXPORTER_OTLP_METRICS_TEMPORALITY_PREFERENCE: 'delta',
  npm_package_name: 'kapi-ide',
  LANG: 'en_US.UTF-8',
  npm_config_npm_version: '10.8.1',
  VSCODE_GIT_ASKPASS_EXTRA_ARGS: '',
  XPC_FLAGS: '0x0',
  npm_config_node_gyp: '/Users/<USER>/.nvm/versions/node/v20.16.0/lib/node_modules/npm/node_modules/node-gyp/bin/node-gyp.js',
  RBENV_SHELL: 'zsh',
  npm_package_version: '0.1.0',
  _CE_M: '',
  XPC_SERVICE_NAME: '0',
  VSCODE_INJECTION: '1',
  SHLVL: '3',
  HOME: '/Users/<USER>',
  VSCODE_GIT_ASKPASS_MAIN: '/Applications/Cursor.app/Contents/Resources/app/extensions/git/dist/askpass-main.js',
  HOMEBREW_PREFIX: '/opt/homebrew',
  npm_config_cache: '/Users/<USER>/.npm',
  CONDA_PYTHON_EXE: '/Users/<USER>/miniconda3/bin/python',
  LOGNAME: 'balajiviswanathan',
  npm_lifecycle_script: 'cross-env NODE_ENV=development VITE_DEV_SERVER_URL=http://localhost:5173 vite',
  VSCODE_GIT_IPC_HANDLE: '/var/folders/m1/q6hdb5210rn1150gnzw33fdh0000gn/T/vscode-git-2e3e1e4492.sock',
  COREPACK_ENABLE_AUTO_PIN: '0',
  NVM_BIN: '/Users/<USER>/.nvm/versions/node/v20.16.0/bin',
  npm_config_user_agent: 'npm/10.8.1 node/v20.16.0 darwin arm64 workspaces/false',
  INFOPATH: '/opt/homebrew/share/info:/opt/homebrew/share/info:',
  HOMEBREW_CELLAR: '/opt/homebrew/Cellar',
  VSCODE_GIT_ASKPASS_NODE: '/Applications/Cursor.app/Contents/Frameworks/Cursor Helper (Plugin).app/Contents/MacOS/Cursor Helper (Plugin)',
  GIT_ASKPASS: '/Applications/Cursor.app/Contents/Resources/app/extensions/git/dist/askpass.sh',
  CLAUDECODE: '1',
  npm_node_execpath: '/Users/<USER>/.nvm/versions/node/v20.16.0/bin/node',
  npm_config_prefix: '/Users/<USER>/.nvm/versions/node/v20.16.0',
  COLORTERM: 'truecolor',
  NODE_ENV: 'development',
  VITE_DEV_SERVER_URL: 'http://localhost:5174/',
  DIST: '/Users/<USER>/Code/kapi-fresh/new_ide/dist',
  PUBLIC: '/Users/<USER>/Code/kapi-fresh/new_ide/public'
}
Creating main window with the following paths:
- __dirname: /Users/<USER>/Code/kapi-fresh/new_ide/dist/main
- process.env.DIST: /Users/<USER>/Code/kapi-fresh/new_ide/dist
- process.env.PUBLIC: /Users/<USER>/Code/kapi-fresh/new_ide/public
- preload path: /Users/<USER>/Code/kapi-fresh/new_ide/dist/preload/preload.js
- isPackaged: false
- url: http://localhost:5174/
- isPlaywrightRunning: false
Loading dev URL: http://localhost:5174/
[FileWatcher MAIN] 🚀 Setting up file system handlers...
[Main] Created terminal d7ef46a9-163a-4031-8ef9-d40cbc47b599 (PID: 9164)
[Main] Created terminal c3304a4f-1a48-4208-a885-4dc4226d758f (PID: 9679)
2025-05-31 20:52:58.761 Electron[9135:8570722] The class 'NSOpenPanel' overrides the method identifier.  This method is implemented by class 'NSWindow'
[AST] Initializing TypeScript project: /Users/<USER>/Code/kapi-fresh
[AST] Scanning for TypeScript projects in: /Users/<USER>/Code/kapi-fresh
[AST] Detected TypeScript project: new_ide (8 files, config: true)
[AST] Detected TypeScript project: src (4 files, config: false)
[AST] Detected TypeScript project: nodejs_backend (2 files, config: true)
[AST] Detected TypeScript project: prisma (10 files, config: false)
[AST] Detected TypeScript project: src (6 files, config: false)
[AST] Detected TypeScript project: tests (2 files, config: false)
[AST] Found 6 TypeScript projects
[AST] Auto-detected TypeScript project at: /Users/<USER>/Code/kapi-fresh/new_ide
[AST] Found 6 TypeScript projects: [
  {
    path: '/Users/<USER>/Code/kapi-fresh/new_ide',
    hasConfig: true,
    fileCount: 8
  },
  {
    path: '/Users/<USER>/Code/kapi-fresh/nodejs_backend',
    hasConfig: true,
    fileCount: 2
  },
  {
    path: '/Users/<USER>/Code/kapi-fresh/nodejs_backend/prisma',
    hasConfig: false,
    fileCount: 10
  },
  {
    path: '/Users/<USER>/Code/kapi-fresh/nodejs_backend/src',
    hasConfig: false,
    fileCount: 6
  },
  {
    path: '/Users/<USER>/Code/kapi-fresh/new_ide/src',
    hasConfig: false,
    fileCount: 4
  },
  {
    path: '/Users/<USER>/Code/kapi-fresh/nodejs_backend/tests',
    hasConfig: false,
    fileCount: 2
  }
]
[AST] Found tsconfig.json at: /Users/<USER>/Code/kapi-fresh/new_ide/tsconfig.json
[AST] TypeScript project initialized with 142 files
[FileWatcher MAIN] 🚀 START WATCHING REQUEST for: /Users/<USER>/Code/kapi-fresh
[FileWatcher MAIN] 🔧 Creating watcher with optimized config...
[FileWatcher MAIN] ✅ Watcher created successfully for: /Users/<USER>/Code/kapi-fresh
[FileWatcher MAIN] ✅ READY! Now watching: /Users/<USER>/Code/kapi-fresh
8:56:02 PM [vite] hmr update /contexts/EditorContext.tsx
8:56:02 PM [vite] hmr invalidate /contexts/EditorContext.tsx Could not Fast Refresh ("default" export is incompatible). Learn more at https://github.com/vitejs/vite-plugin-react/tree/main/packages/plugin-react#consistent-components-exports
8:56:02 PM [vite] hmr update /pages/IDE.tsx, /core/AppProviders.tsx, /components/TopMenuBar.tsx, /features/editor/Editor.tsx, /features/explorer/FileExplorer.tsx, /features/editor/viewers/MarkdownViewer.tsx
8:56:03 PM [vite] hmr invalidate /features/editor/Editor.tsx Could not Fast Refresh ("getEditor" export is incompatible). Learn more at https://github.com/vitejs/vite-plugin-react/tree/main/packages/plugin-react#consistent-components-exports
8:56:03 PM [vite] hmr update /pages/IDE.tsx
[Main Debug] Received kill request. Raw args: {"id":"c3304a4f-1a48-4208-a885-4dc4226d758f"}
[Main Debug] Type of id: string, Value: c3304a4f-1a48-4208-a885-4dc4226d758f
[Main] Killing terminal c3304a4f-1a48-4208-a885-4dc4226d758f
[FileWatcher] Stopped watching
[Main] Terminal c3304a4f-1a48-4208-a885-4dc4226d758f exited (code: 1, signal: 0)
[FileWatcher MAIN] 🚀 START WATCHING REQUEST for: /Users/<USER>/Code/kapi-fresh
[FileWatcher MAIN] 🔧 Creating watcher with optimized config...
[FileWatcher MAIN] ✅ Watcher created successfully for: /Users/<USER>/Code/kapi-fresh
[Main] Created terminal b266b55d-01b1-4161-8aab-e10952aec9df (PID: 10615)
[FileWatcher MAIN] ✅ READY! Now watching: /Users/<USER>/Code/kapi-fresh
8:56:12 PM [vite] hmr update /features/explorer/FileExplorer.tsx
[FileWatcher] Stopped watching
[FileWatcher MAIN] 🚀 START WATCHING REQUEST for: /Users/<USER>/Code/kapi-fresh
[FileWatcher MAIN] 🔧 Creating watcher with optimized config...
[FileWatcher MAIN] ✅ Watcher created successfully for: /Users/<USER>/Code/kapi-fresh
[FileWatcher MAIN] ✅ READY! Now watching: /Users/<USER>/Code/kapi-fresh
8:56:21 PM [vite] hmr update /features/explorer/FileExplorer.tsx
[FileWatcher] Stopped watching
[FileWatcher MAIN] 🚀 START WATCHING REQUEST for: /Users/<USER>/Code/kapi-fresh
[FileWatcher MAIN] 🔧 Creating watcher with optimized config...
[FileWatcher MAIN] ✅ Watcher created successfully for: /Users/<USER>/Code/kapi-fresh
[FileWatcher MAIN] ✅ READY! Now watching: /Users/<USER>/Code/kapi-fresh

build started...
✓ 1 modules transformed.
dist/main/main.js  12,716.16 kB │ gzip: 2,095.32 kB
built in 16593ms.

build started...
Environment information:
- __dirname: /Users/<USER>/Code/kapi-fresh/new_ide/dist/main
- isPackaged: false
Development DIST path: /Users/<USER>/Code/kapi-fresh/new_ide/dist
- DIST path: /Users/<USER>/Code/kapi-fresh/new_ide/dist
Preload paths:
- devPreload: /Users/<USER>/Code/kapi-fresh/new_ide/dist/preload/preload.js
- prodPreload: /Users/<USER>/Code/kapi-fresh/new_ide/dist/preload/preload.js
- devPreload exists: true
- prodPreload exists: true
- chosen preload: /Users/<USER>/Code/kapi-fresh/new_ide/dist/preload/preload.js
Environment variables: {
  NVM_INC: '/Users/<USER>/.nvm/versions/node/v20.16.0/include/node',
  CLAUDE_CODE_ENTRYPOINT: 'cli',
  TERM_PROGRAM: 'vscode',
  NODE: '/Users/<USER>/.nvm/versions/node/v20.16.0/bin/node',
  INIT_CWD: '/Users/<USER>/Code/kapi-fresh/new_ide',
  NVM_CD_FLAGS: '-q',
  TERM: 'xterm-256color',
  SHELL: '/bin/zsh',
  HOMEBREW_REPOSITORY: '/opt/homebrew',
  TMPDIR: '/var/folders/m1/q6hdb5210rn1150gnzw33fdh0000gn/T/',
  npm_config_global_prefix: '/Users/<USER>/.nvm/versions/node/v20.16.0',
  CONDA_SHLVL: '0',
  TERM_PROGRAM_VERSION: '0.50.5',
  ZDOTDIR: '/Users/<USER>',
  CURSOR_TRACE_ID: 'e8d2f0700a074524a417e8b2a55f5313',
  ORIGINAL_XDG_CURRENT_DESKTOP: 'undefined',
  MallocNanoZone: '0',
  COLOR: '0',
  npm_config_noproxy: '',
  npm_config_local_prefix: '/Users/<USER>/Code/kapi-fresh/new_ide',
  ENABLE_IDE_INTEGRATION: 'true',
  GIT_EDITOR: 'true',
  OBJC_DISABLE_INITIALIZE_FORK_SAFETY: 'YES',
  NVM_DIR: '/Users/<USER>/.nvm',
  USER: 'balajiviswanathan',
  COMMAND_MODE: 'unix2003',
  npm_config_globalconfig: '/Users/<USER>/.nvm/versions/node/v20.16.0/etc/npmrc',
  CONDA_EXE: '/Users/<USER>/miniconda3/bin/conda',
  CLAUDE_CODE_SSE_PORT: '49630',
  SSH_AUTH_SOCK: '/private/tmp/com.apple.launchd.K4JZGhmBC1/Listeners',
  __CF_USER_TEXT_ENCODING: '0x1F5:0x0:0x0',
  npm_execpath: '/Users/<USER>/.nvm/versions/node/v20.16.0/lib/node_modules/npm/bin/npm-cli.js',
  _CE_CONDA: '',
  PATH: '/Users/<USER>/Code/kapi-fresh/new_ide/node_modules/.bin:/Users/<USER>/Code/kapi-fresh/node_modules/.bin:/Users/<USER>/Code/node_modules/.bin:/Users/<USER>/node_modules/.bin:/Users/<USER>/.bin:/node_modules/.bin:/Users/<USER>/.nvm/versions/node/v20.16.0/lib/node_modules/npm/node_modules/@npmcli/run-script/lib/node-gyp-bin:/Users/<USER>/.local/bin:/opt/homebrew/opt/postgresql@17/bin:/usr/local/opt/postgresql@17/bin:/Users/<USER>/.codeium/windsurf/bin:/Users/<USER>/.rbenv/shims:/Users/<USER>/.rbenv/bin:/Users/<USER>/.nvm/versions/node/v20.16.0/bin:/Users/<USER>/miniconda3/condabin:/opt/homebrew/bin:/opt/homebrew/sbin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin',
  npm_package_json: '/Users/<USER>/Code/kapi-fresh/new_ide/package.json',
  _: '/Users/<USER>/Code/kapi-fresh/new_ide/node_modules/.bin/cross-env',
  npm_config_userconfig: '/Users/<USER>/.npmrc',
  npm_config_init_module: '/Users/<USER>/.npm-init.js',
  USER_ZDOTDIR: '/Users/<USER>',
  __CFBundleIdentifier: 'com.todesktop.230313mzl4w4u92',
  npm_command: 'run-script',
  PWD: '/Users/<USER>/Code/kapi-fresh/new_ide',
  npm_lifecycle_event: 'dev',
  EDITOR: 'vi',
  OTEL_EXPORTER_OTLP_METRICS_TEMPORALITY_PREFERENCE: 'delta',
  npm_package_name: 'kapi-ide',
  LANG: 'en_US.UTF-8',
  npm_config_npm_version: '10.8.1',
  VSCODE_GIT_ASKPASS_EXTRA_ARGS: '',
  XPC_FLAGS: '0x0',
  npm_config_node_gyp: '/Users/<USER>/.nvm/versions/node/v20.16.0/lib/node_modules/npm/node_modules/node-gyp/bin/node-gyp.js',
  RBENV_SHELL: 'zsh',
  npm_package_version: '0.1.0',
  _CE_M: '',
  XPC_SERVICE_NAME: '0',
  VSCODE_INJECTION: '1',
  SHLVL: '3',
  HOME: '/Users/<USER>',
  VSCODE_GIT_ASKPASS_MAIN: '/Applications/Cursor.app/Contents/Resources/app/extensions/git/dist/askpass-main.js',
  HOMEBREW_PREFIX: '/opt/homebrew',
  npm_config_cache: '/Users/<USER>/.npm',
  CONDA_PYTHON_EXE: '/Users/<USER>/miniconda3/bin/python',
  LOGNAME: 'balajiviswanathan',
  npm_lifecycle_script: 'cross-env NODE_ENV=development VITE_DEV_SERVER_URL=http://localhost:5173 vite',
  VSCODE_GIT_IPC_HANDLE: '/var/folders/m1/q6hdb5210rn1150gnzw33fdh0000gn/T/vscode-git-2e3e1e4492.sock',
  COREPACK_ENABLE_AUTO_PIN: '0',
  NVM_BIN: '/Users/<USER>/.nvm/versions/node/v20.16.0/bin',
  npm_config_user_agent: 'npm/10.8.1 node/v20.16.0 darwin arm64 workspaces/false',
  INFOPATH: '/opt/homebrew/share/info:/opt/homebrew/share/info:',
  HOMEBREW_CELLAR: '/opt/homebrew/Cellar',
  VSCODE_GIT_ASKPASS_NODE: '/Applications/Cursor.app/Contents/Frameworks/Cursor Helper (Plugin).app/Contents/MacOS/Cursor Helper (Plugin)',
  GIT_ASKPASS: '/Applications/Cursor.app/Contents/Resources/app/extensions/git/dist/askpass.sh',
  CLAUDECODE: '1',
  npm_node_execpath: '/Users/<USER>/.nvm/versions/node/v20.16.0/bin/node',
  npm_config_prefix: '/Users/<USER>/.nvm/versions/node/v20.16.0',
  COLORTERM: 'truecolor',
  NODE_ENV: 'development',
  VITE_DEV_SERVER_URL: 'http://localhost:5174/',
  DIST: '/Users/<USER>/Code/kapi-fresh/new_ide/dist',
  PUBLIC: '/Users/<USER>/Code/kapi-fresh/new_ide/public'
}
Creating main window with the following paths:
- __dirname: /Users/<USER>/Code/kapi-fresh/new_ide/dist/main
- process.env.DIST: /Users/<USER>/Code/kapi-fresh/new_ide/dist
- process.env.PUBLIC: /Users/<USER>/Code/kapi-fresh/new_ide/public
- preload path: /Users/<USER>/Code/kapi-fresh/new_ide/dist/preload/preload.js
- isPackaged: false
- url: http://localhost:5174/
- isPlaywrightRunning: false
Loading dev URL: http://localhost:5174/
[FileWatcher MAIN] 🚀 Setting up file system handlers...
✓ 1 modules transformed.
[Main] Created terminal 5273cccb-77c5-44e0-8469-5deaa28a16e5 (PID: 11620)
dist/main/main.js  12,716.04 kB │ gzip: 2,095.30 kB
built in 22919ms.

build started...
Environment information:
- __dirname: /Users/<USER>/Code/kapi-fresh/new_ide/dist/main
- isPackaged: false
Development DIST path: /Users/<USER>/Code/kapi-fresh/new_ide/dist
- DIST path: /Users/<USER>/Code/kapi-fresh/new_ide/dist
Preload paths:
- devPreload: /Users/<USER>/Code/kapi-fresh/new_ide/dist/preload/preload.js
- prodPreload: /Users/<USER>/Code/kapi-fresh/new_ide/dist/preload/preload.js
- devPreload exists: true
- prodPreload exists: true
- chosen preload: /Users/<USER>/Code/kapi-fresh/new_ide/dist/preload/preload.js
Environment variables: {
  NVM_INC: '/Users/<USER>/.nvm/versions/node/v20.16.0/include/node',
  CLAUDE_CODE_ENTRYPOINT: 'cli',
  TERM_PROGRAM: 'vscode',
  NODE: '/Users/<USER>/.nvm/versions/node/v20.16.0/bin/node',
  INIT_CWD: '/Users/<USER>/Code/kapi-fresh/new_ide',
  NVM_CD_FLAGS: '-q',
  TERM: 'xterm-256color',
  SHELL: '/bin/zsh',
  HOMEBREW_REPOSITORY: '/opt/homebrew',
  TMPDIR: '/var/folders/m1/q6hdb5210rn1150gnzw33fdh0000gn/T/',
  npm_config_global_prefix: '/Users/<USER>/.nvm/versions/node/v20.16.0',
  CONDA_SHLVL: '0',
  TERM_PROGRAM_VERSION: '0.50.5',
  ZDOTDIR: '/Users/<USER>',
  CURSOR_TRACE_ID: 'e8d2f0700a074524a417e8b2a55f5313',
  ORIGINAL_XDG_CURRENT_DESKTOP: 'undefined',
  MallocNanoZone: '0',
  COLOR: '0',
  npm_config_noproxy: '',
  npm_config_local_prefix: '/Users/<USER>/Code/kapi-fresh/new_ide',
  ENABLE_IDE_INTEGRATION: 'true',
  GIT_EDITOR: 'true',
  OBJC_DISABLE_INITIALIZE_FORK_SAFETY: 'YES',
  NVM_DIR: '/Users/<USER>/.nvm',
  USER: 'balajiviswanathan',
  COMMAND_MODE: 'unix2003',
  npm_config_globalconfig: '/Users/<USER>/.nvm/versions/node/v20.16.0/etc/npmrc',
  CONDA_EXE: '/Users/<USER>/miniconda3/bin/conda',
  CLAUDE_CODE_SSE_PORT: '49630',
  SSH_AUTH_SOCK: '/private/tmp/com.apple.launchd.K4JZGhmBC1/Listeners',
  __CF_USER_TEXT_ENCODING: '0x1F5:0x0:0x0',
  npm_execpath: '/Users/<USER>/.nvm/versions/node/v20.16.0/lib/node_modules/npm/bin/npm-cli.js',
  _CE_CONDA: '',
  PATH: '/Users/<USER>/Code/kapi-fresh/new_ide/node_modules/.bin:/Users/<USER>/Code/kapi-fresh/node_modules/.bin:/Users/<USER>/Code/node_modules/.bin:/Users/<USER>/node_modules/.bin:/Users/<USER>/.bin:/node_modules/.bin:/Users/<USER>/.nvm/versions/node/v20.16.0/lib/node_modules/npm/node_modules/@npmcli/run-script/lib/node-gyp-bin:/Users/<USER>/.local/bin:/opt/homebrew/opt/postgresql@17/bin:/usr/local/opt/postgresql@17/bin:/Users/<USER>/.codeium/windsurf/bin:/Users/<USER>/.rbenv/shims:/Users/<USER>/.rbenv/bin:/Users/<USER>/.nvm/versions/node/v20.16.0/bin:/Users/<USER>/miniconda3/condabin:/opt/homebrew/bin:/opt/homebrew/sbin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin',
  npm_package_json: '/Users/<USER>/Code/kapi-fresh/new_ide/package.json',
  _: '/Users/<USER>/Code/kapi-fresh/new_ide/node_modules/.bin/cross-env',
  npm_config_userconfig: '/Users/<USER>/.npmrc',
  npm_config_init_module: '/Users/<USER>/.npm-init.js',
  USER_ZDOTDIR: '/Users/<USER>',
  __CFBundleIdentifier: 'com.todesktop.230313mzl4w4u92',
  npm_command: 'run-script',
  PWD: '/Users/<USER>/Code/kapi-fresh/new_ide',
  npm_lifecycle_event: 'dev',
  EDITOR: 'vi',
  OTEL_EXPORTER_OTLP_METRICS_TEMPORALITY_PREFERENCE: 'delta',
  npm_package_name: 'kapi-ide',
  LANG: 'en_US.UTF-8',
  npm_config_npm_version: '10.8.1',
  VSCODE_GIT_ASKPASS_EXTRA_ARGS: '',
  XPC_FLAGS: '0x0',
  npm_config_node_gyp: '/Users/<USER>/.nvm/versions/node/v20.16.0/lib/node_modules/npm/node_modules/node-gyp/bin/node-gyp.js',
  RBENV_SHELL: 'zsh',
  npm_package_version: '0.1.0',
  _CE_M: '',
  XPC_SERVICE_NAME: '0',
  VSCODE_INJECTION: '1',
  SHLVL: '3',
  HOME: '/Users/<USER>',
  VSCODE_GIT_ASKPASS_MAIN: '/Applications/Cursor.app/Contents/Resources/app/extensions/git/dist/askpass-main.js',
  HOMEBREW_PREFIX: '/opt/homebrew',
  npm_config_cache: '/Users/<USER>/.npm',
  CONDA_PYTHON_EXE: '/Users/<USER>/miniconda3/bin/python',
  LOGNAME: 'balajiviswanathan',
  npm_lifecycle_script: 'cross-env NODE_ENV=development VITE_DEV_SERVER_URL=http://localhost:5173 vite',
  VSCODE_GIT_IPC_HANDLE: '/var/folders/m1/q6hdb5210rn1150gnzw33fdh0000gn/T/vscode-git-2e3e1e4492.sock',
  COREPACK_ENABLE_AUTO_PIN: '0',
  NVM_BIN: '/Users/<USER>/.nvm/versions/node/v20.16.0/bin',
  npm_config_user_agent: 'npm/10.8.1 node/v20.16.0 darwin arm64 workspaces/false',
  INFOPATH: '/opt/homebrew/share/info:/opt/homebrew/share/info:',
  HOMEBREW_CELLAR: '/opt/homebrew/Cellar',
  VSCODE_GIT_ASKPASS_NODE: '/Applications/Cursor.app/Contents/Frameworks/Cursor Helper (Plugin).app/Contents/MacOS/Cursor Helper (Plugin)',
  GIT_ASKPASS: '/Applications/Cursor.app/Contents/Resources/app/extensions/git/dist/askpass.sh',
  CLAUDECODE: '1',
  npm_node_execpath: '/Users/<USER>/.nvm/versions/node/v20.16.0/bin/node',
  npm_config_prefix: '/Users/<USER>/.nvm/versions/node/v20.16.0',
  COLORTERM: 'truecolor',
  NODE_ENV: 'development',
  VITE_DEV_SERVER_URL: 'http://localhost:5174/',
  DIST: '/Users/<USER>/Code/kapi-fresh/new_ide/dist',
  PUBLIC: '/Users/<USER>/Code/kapi-fresh/new_ide/public'
}
Creating main window with the following paths:
- __dirname: /Users/<USER>/Code/kapi-fresh/new_ide/dist/main
- process.env.DIST: /Users/<USER>/Code/kapi-fresh/new_ide/dist
- process.env.PUBLIC: /Users/<USER>/Code/kapi-fresh/new_ide/public
- preload path: /Users/<USER>/Code/kapi-fresh/new_ide/dist/preload/preload.js
- isPackaged: false
- url: http://localhost:5174/
- isPlaywrightRunning: false
Loading dev URL: http://localhost:5174/
[FileWatcher MAIN] 🚀 Setting up file system handlers...
✓ 1 modules transformed.
[Main] Created terminal 4603af2a-5a4c-4c3f-8e2f-81c7a082c922 (PID: 11958)
dist/main/main.js  12,715.97 kB │ gzip: 2,095.29 kB
built in 20574ms.

build started...
Environment information:
- __dirname: /Users/<USER>/Code/kapi-fresh/new_ide/dist/main
- isPackaged: false
Development DIST path: /Users/<USER>/Code/kapi-fresh/new_ide/dist
- DIST path: /Users/<USER>/Code/kapi-fresh/new_ide/dist
Preload paths:
- devPreload: /Users/<USER>/Code/kapi-fresh/new_ide/dist/preload/preload.js
- prodPreload: /Users/<USER>/Code/kapi-fresh/new_ide/dist/preload/preload.js
- devPreload exists: true
- prodPreload exists: true
- chosen preload: /Users/<USER>/Code/kapi-fresh/new_ide/dist/preload/preload.js
Environment variables: {
  NVM_INC: '/Users/<USER>/.nvm/versions/node/v20.16.0/include/node',
  CLAUDE_CODE_ENTRYPOINT: 'cli',
  TERM_PROGRAM: 'vscode',
  NODE: '/Users/<USER>/.nvm/versions/node/v20.16.0/bin/node',
  INIT_CWD: '/Users/<USER>/Code/kapi-fresh/new_ide',
  NVM_CD_FLAGS: '-q',
  TERM: 'xterm-256color',
  SHELL: '/bin/zsh',
  HOMEBREW_REPOSITORY: '/opt/homebrew',
  TMPDIR: '/var/folders/m1/q6hdb5210rn1150gnzw33fdh0000gn/T/',
  npm_config_global_prefix: '/Users/<USER>/.nvm/versions/node/v20.16.0',
  CONDA_SHLVL: '0',
  TERM_PROGRAM_VERSION: '0.50.5',
  ZDOTDIR: '/Users/<USER>',
  CURSOR_TRACE_ID: 'e8d2f0700a074524a417e8b2a55f5313',
  ORIGINAL_XDG_CURRENT_DESKTOP: 'undefined',
  MallocNanoZone: '0',
  COLOR: '0',
  npm_config_noproxy: '',
  npm_config_local_prefix: '/Users/<USER>/Code/kapi-fresh/new_ide',
  ENABLE_IDE_INTEGRATION: 'true',
  GIT_EDITOR: 'true',
  OBJC_DISABLE_INITIALIZE_FORK_SAFETY: 'YES',
  NVM_DIR: '/Users/<USER>/.nvm',
  USER: 'balajiviswanathan',
  COMMAND_MODE: 'unix2003',
  npm_config_globalconfig: '/Users/<USER>/.nvm/versions/node/v20.16.0/etc/npmrc',
  CONDA_EXE: '/Users/<USER>/miniconda3/bin/conda',
  CLAUDE_CODE_SSE_PORT: '49630',
  SSH_AUTH_SOCK: '/private/tmp/com.apple.launchd.K4JZGhmBC1/Listeners',
  __CF_USER_TEXT_ENCODING: '0x1F5:0x0:0x0',
  npm_execpath: '/Users/<USER>/.nvm/versions/node/v20.16.0/lib/node_modules/npm/bin/npm-cli.js',
  _CE_CONDA: '',
  PATH: '/Users/<USER>/Code/kapi-fresh/new_ide/node_modules/.bin:/Users/<USER>/Code/kapi-fresh/node_modules/.bin:/Users/<USER>/Code/node_modules/.bin:/Users/<USER>/node_modules/.bin:/Users/<USER>/.bin:/node_modules/.bin:/Users/<USER>/.nvm/versions/node/v20.16.0/lib/node_modules/npm/node_modules/@npmcli/run-script/lib/node-gyp-bin:/Users/<USER>/.local/bin:/opt/homebrew/opt/postgresql@17/bin:/usr/local/opt/postgresql@17/bin:/Users/<USER>/.codeium/windsurf/bin:/Users/<USER>/.rbenv/shims:/Users/<USER>/.rbenv/bin:/Users/<USER>/.nvm/versions/node/v20.16.0/bin:/Users/<USER>/miniconda3/condabin:/opt/homebrew/bin:/opt/homebrew/sbin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin',
  npm_package_json: '/Users/<USER>/Code/kapi-fresh/new_ide/package.json',
  _: '/Users/<USER>/Code/kapi-fresh/new_ide/node_modules/.bin/cross-env',
  npm_config_userconfig: '/Users/<USER>/.npmrc',
  npm_config_init_module: '/Users/<USER>/.npm-init.js',
  USER_ZDOTDIR: '/Users/<USER>',
  __CFBundleIdentifier: 'com.todesktop.230313mzl4w4u92',
  npm_command: 'run-script',
  PWD: '/Users/<USER>/Code/kapi-fresh/new_ide',
  npm_lifecycle_event: 'dev',
  EDITOR: 'vi',
  OTEL_EXPORTER_OTLP_METRICS_TEMPORALITY_PREFERENCE: 'delta',
  npm_package_name: 'kapi-ide',
  LANG: 'en_US.UTF-8',
  npm_config_npm_version: '10.8.1',
  VSCODE_GIT_ASKPASS_EXTRA_ARGS: '',
  XPC_FLAGS: '0x0',
  npm_config_node_gyp: '/Users/<USER>/.nvm/versions/node/v20.16.0/lib/node_modules/npm/node_modules/node-gyp/bin/node-gyp.js',
  RBENV_SHELL: 'zsh',
  npm_package_version: '0.1.0',
  _CE_M: '',
  XPC_SERVICE_NAME: '0',
  VSCODE_INJECTION: '1',
  SHLVL: '3',
  HOME: '/Users/<USER>',
  VSCODE_GIT_ASKPASS_MAIN: '/Applications/Cursor.app/Contents/Resources/app/extensions/git/dist/askpass-main.js',
  HOMEBREW_PREFIX: '/opt/homebrew',
  npm_config_cache: '/Users/<USER>/.npm',
  CONDA_PYTHON_EXE: '/Users/<USER>/miniconda3/bin/python',
  LOGNAME: 'balajiviswanathan',
  npm_lifecycle_script: 'cross-env NODE_ENV=development VITE_DEV_SERVER_URL=http://localhost:5173 vite',
  VSCODE_GIT_IPC_HANDLE: '/var/folders/m1/q6hdb5210rn1150gnzw33fdh0000gn/T/vscode-git-2e3e1e4492.sock',
  COREPACK_ENABLE_AUTO_PIN: '0',
  NVM_BIN: '/Users/<USER>/.nvm/versions/node/v20.16.0/bin',
  npm_config_user_agent: 'npm/10.8.1 node/v20.16.0 darwin arm64 workspaces/false',
  INFOPATH: '/opt/homebrew/share/info:/opt/homebrew/share/info:',
  HOMEBREW_CELLAR: '/opt/homebrew/Cellar',
  VSCODE_GIT_ASKPASS_NODE: '/Applications/Cursor.app/Contents/Frameworks/Cursor Helper (Plugin).app/Contents/MacOS/Cursor Helper (Plugin)',
  GIT_ASKPASS: '/Applications/Cursor.app/Contents/Resources/app/extensions/git/dist/askpass.sh',
  CLAUDECODE: '1',
  npm_node_execpath: '/Users/<USER>/.nvm/versions/node/v20.16.0/bin/node',
  npm_config_prefix: '/Users/<USER>/.nvm/versions/node/v20.16.0',
  COLORTERM: 'truecolor',
  NODE_ENV: 'development',
  VITE_DEV_SERVER_URL: 'http://localhost:5174/',
  DIST: '/Users/<USER>/Code/kapi-fresh/new_ide/dist',
  PUBLIC: '/Users/<USER>/Code/kapi-fresh/new_ide/public'
}
Creating main window with the following paths:
- __dirname: /Users/<USER>/Code/kapi-fresh/new_ide/dist/main
- process.env.DIST: /Users/<USER>/Code/kapi-fresh/new_ide/dist
- process.env.PUBLIC: /Users/<USER>/Code/kapi-fresh/new_ide/public
- preload path: /Users/<USER>/Code/kapi-fresh/new_ide/dist/preload/preload.js
- isPackaged: false
- url: http://localhost:5174/
- isPlaywrightRunning: false
Loading dev URL: http://localhost:5174/
[FileWatcher MAIN] 🚀 Setting up file system handlers...
✓ 1 modules transformed.
[Main] Created terminal dbe1a525-6182-471b-aa59-269ab2ffc5e3 (PID: 12304)
dist/main/main.js  12,715.90 kB │ gzip: 2,095.27 kB
built in 15344ms.

build started...
Environment information:
- __dirname: /Users/<USER>/Code/kapi-fresh/new_ide/dist/main
- isPackaged: false
Development DIST path: /Users/<USER>/Code/kapi-fresh/new_ide/dist
- DIST path: /Users/<USER>/Code/kapi-fresh/new_ide/dist
Preload paths:
- devPreload: /Users/<USER>/Code/kapi-fresh/new_ide/dist/preload/preload.js
- prodPreload: /Users/<USER>/Code/kapi-fresh/new_ide/dist/preload/preload.js
- devPreload exists: true
- prodPreload exists: true
- chosen preload: /Users/<USER>/Code/kapi-fresh/new_ide/dist/preload/preload.js
Environment variables: {
  NVM_INC: '/Users/<USER>/.nvm/versions/node/v20.16.0/include/node',
  CLAUDE_CODE_ENTRYPOINT: 'cli',
  TERM_PROGRAM: 'vscode',
  NODE: '/Users/<USER>/.nvm/versions/node/v20.16.0/bin/node',
  INIT_CWD: '/Users/<USER>/Code/kapi-fresh/new_ide',
  NVM_CD_FLAGS: '-q',
  TERM: 'xterm-256color',
  SHELL: '/bin/zsh',
  HOMEBREW_REPOSITORY: '/opt/homebrew',
  TMPDIR: '/var/folders/m1/q6hdb5210rn1150gnzw33fdh0000gn/T/',
  npm_config_global_prefix: '/Users/<USER>/.nvm/versions/node/v20.16.0',
  CONDA_SHLVL: '0',
  TERM_PROGRAM_VERSION: '0.50.5',
  ZDOTDIR: '/Users/<USER>',
  CURSOR_TRACE_ID: 'e8d2f0700a074524a417e8b2a55f5313',
  ORIGINAL_XDG_CURRENT_DESKTOP: 'undefined',
  MallocNanoZone: '0',
  COLOR: '0',
  npm_config_noproxy: '',
  npm_config_local_prefix: '/Users/<USER>/Code/kapi-fresh/new_ide',
  ENABLE_IDE_INTEGRATION: 'true',
  GIT_EDITOR: 'true',
  OBJC_DISABLE_INITIALIZE_FORK_SAFETY: 'YES',
  NVM_DIR: '/Users/<USER>/.nvm',
  USER: 'balajiviswanathan',
  COMMAND_MODE: 'unix2003',
  npm_config_globalconfig: '/Users/<USER>/.nvm/versions/node/v20.16.0/etc/npmrc',
  CONDA_EXE: '/Users/<USER>/miniconda3/bin/conda',
  CLAUDE_CODE_SSE_PORT: '49630',
  SSH_AUTH_SOCK: '/private/tmp/com.apple.launchd.K4JZGhmBC1/Listeners',
  __CF_USER_TEXT_ENCODING: '0x1F5:0x0:0x0',
  npm_execpath: '/Users/<USER>/.nvm/versions/node/v20.16.0/lib/node_modules/npm/bin/npm-cli.js',
  _CE_CONDA: '',
  PATH: '/Users/<USER>/Code/kapi-fresh/new_ide/node_modules/.bin:/Users/<USER>/Code/kapi-fresh/node_modules/.bin:/Users/<USER>/Code/node_modules/.bin:/Users/<USER>/node_modules/.bin:/Users/<USER>/.bin:/node_modules/.bin:/Users/<USER>/.nvm/versions/node/v20.16.0/lib/node_modules/npm/node_modules/@npmcli/run-script/lib/node-gyp-bin:/Users/<USER>/.local/bin:/opt/homebrew/opt/postgresql@17/bin:/usr/local/opt/postgresql@17/bin:/Users/<USER>/.codeium/windsurf/bin:/Users/<USER>/.rbenv/shims:/Users/<USER>/.rbenv/bin:/Users/<USER>/.nvm/versions/node/v20.16.0/bin:/Users/<USER>/miniconda3/condabin:/opt/homebrew/bin:/opt/homebrew/sbin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin',
  npm_package_json: '/Users/<USER>/Code/kapi-fresh/new_ide/package.json',
  _: '/Users/<USER>/Code/kapi-fresh/new_ide/node_modules/.bin/cross-env',
  npm_config_userconfig: '/Users/<USER>/.npmrc',
  npm_config_init_module: '/Users/<USER>/.npm-init.js',
  USER_ZDOTDIR: '/Users/<USER>',
  __CFBundleIdentifier: 'com.todesktop.230313mzl4w4u92',
  npm_command: 'run-script',
  PWD: '/Users/<USER>/Code/kapi-fresh/new_ide',
  npm_lifecycle_event: 'dev',
  EDITOR: 'vi',
  OTEL_EXPORTER_OTLP_METRICS_TEMPORALITY_PREFERENCE: 'delta',
  npm_package_name: 'kapi-ide',
  LANG: 'en_US.UTF-8',
  npm_config_npm_version: '10.8.1',
  VSCODE_GIT_ASKPASS_EXTRA_ARGS: '',
  XPC_FLAGS: '0x0',
  npm_config_node_gyp: '/Users/<USER>/.nvm/versions/node/v20.16.0/lib/node_modules/npm/node_modules/node-gyp/bin/node-gyp.js',
  RBENV_SHELL: 'zsh',
  npm_package_version: '0.1.0',
  _CE_M: '',
  XPC_SERVICE_NAME: '0',
  VSCODE_INJECTION: '1',
  SHLVL: '3',
  HOME: '/Users/<USER>',
  VSCODE_GIT_ASKPASS_MAIN: '/Applications/Cursor.app/Contents/Resources/app/extensions/git/dist/askpass-main.js',
  HOMEBREW_PREFIX: '/opt/homebrew',
  npm_config_cache: '/Users/<USER>/.npm',
  CONDA_PYTHON_EXE: '/Users/<USER>/miniconda3/bin/python',
  LOGNAME: 'balajiviswanathan',
  npm_lifecycle_script: 'cross-env NODE_ENV=development VITE_DEV_SERVER_URL=http://localhost:5173 vite',
  VSCODE_GIT_IPC_HANDLE: '/var/folders/m1/q6hdb5210rn1150gnzw33fdh0000gn/T/vscode-git-2e3e1e4492.sock',
  COREPACK_ENABLE_AUTO_PIN: '0',
  NVM_BIN: '/Users/<USER>/.nvm/versions/node/v20.16.0/bin',
  npm_config_user_agent: 'npm/10.8.1 node/v20.16.0 darwin arm64 workspaces/false',
  INFOPATH: '/opt/homebrew/share/info:/opt/homebrew/share/info:',
  HOMEBREW_CELLAR: '/opt/homebrew/Cellar',
  VSCODE_GIT_ASKPASS_NODE: '/Applications/Cursor.app/Contents/Frameworks/Cursor Helper (Plugin).app/Contents/MacOS/Cursor Helper (Plugin)',
  GIT_ASKPASS: '/Applications/Cursor.app/Contents/Resources/app/extensions/git/dist/askpass.sh',
  CLAUDECODE: '1',
  npm_node_execpath: '/Users/<USER>/.nvm/versions/node/v20.16.0/bin/node',
  npm_config_prefix: '/Users/<USER>/.nvm/versions/node/v20.16.0',
  COLORTERM: 'truecolor',
  NODE_ENV: 'development',
  VITE_DEV_SERVER_URL: 'http://localhost:5174/',
  DIST: '/Users/<USER>/Code/kapi-fresh/new_ide/dist',
  PUBLIC: '/Users/<USER>/Code/kapi-fresh/new_ide/public'
}
Creating main window with the following paths:
- __dirname: /Users/<USER>/Code/kapi-fresh/new_ide/dist/main
- process.env.DIST: /Users/<USER>/Code/kapi-fresh/new_ide/dist
- process.env.PUBLIC: /Users/<USER>/Code/kapi-fresh/new_ide/public
- preload path: /Users/<USER>/Code/kapi-fresh/new_ide/dist/preload/preload.js
- isPackaged: false
- url: http://localhost:5174/
- isPlaywrightRunning: false
Loading dev URL: http://localhost:5174/
[FileWatcher MAIN] 🚀 Setting up file system handlers...
✓ 1 modules transformed.
[Main] Created terminal 6b047a0d-469a-4651-a0fb-a1e791460ef6 (PID: 12638)
dist/main/main.js  12,715.62 kB │ gzip: 2,095.20 kB
built in 13198ms.
Environment information:
- __dirname: /Users/<USER>/Code/kapi-fresh/new_ide/dist/main
- isPackaged: false
Development DIST path: /Users/<USER>/Code/kapi-fresh/new_ide/dist
- DIST path: /Users/<USER>/Code/kapi-fresh/new_ide/dist
Preload paths:
- devPreload: /Users/<USER>/Code/kapi-fresh/new_ide/dist/preload/preload.js
- prodPreload: /Users/<USER>/Code/kapi-fresh/new_ide/dist/preload/preload.js
- devPreload exists: true
- prodPreload exists: true
- chosen preload: /Users/<USER>/Code/kapi-fresh/new_ide/dist/preload/preload.js
Environment variables: {
  NVM_INC: '/Users/<USER>/.nvm/versions/node/v20.16.0/include/node',
  CLAUDE_CODE_ENTRYPOINT: 'cli',
  TERM_PROGRAM: 'vscode',
  NODE: '/Users/<USER>/.nvm/versions/node/v20.16.0/bin/node',
  INIT_CWD: '/Users/<USER>/Code/kapi-fresh/new_ide',
  NVM_CD_FLAGS: '-q',
  TERM: 'xterm-256color',
  SHELL: '/bin/zsh',
  HOMEBREW_REPOSITORY: '/opt/homebrew',
  TMPDIR: '/var/folders/m1/q6hdb5210rn1150gnzw33fdh0000gn/T/',
  npm_config_global_prefix: '/Users/<USER>/.nvm/versions/node/v20.16.0',
  CONDA_SHLVL: '0',
  TERM_PROGRAM_VERSION: '0.50.5',
  ZDOTDIR: '/Users/<USER>',
  CURSOR_TRACE_ID: 'e8d2f0700a074524a417e8b2a55f5313',
  ORIGINAL_XDG_CURRENT_DESKTOP: 'undefined',
  MallocNanoZone: '0',
  COLOR: '0',
  npm_config_noproxy: '',
  npm_config_local_prefix: '/Users/<USER>/Code/kapi-fresh/new_ide',
  ENABLE_IDE_INTEGRATION: 'true',
  GIT_EDITOR: 'true',
  OBJC_DISABLE_INITIALIZE_FORK_SAFETY: 'YES',
  NVM_DIR: '/Users/<USER>/.nvm',
  USER: 'balajiviswanathan',
  COMMAND_MODE: 'unix2003',
  npm_config_globalconfig: '/Users/<USER>/.nvm/versions/node/v20.16.0/etc/npmrc',
  CONDA_EXE: '/Users/<USER>/miniconda3/bin/conda',
  CLAUDE_CODE_SSE_PORT: '49630',
  SSH_AUTH_SOCK: '/private/tmp/com.apple.launchd.K4JZGhmBC1/Listeners',
  __CF_USER_TEXT_ENCODING: '0x1F5:0x0:0x0',
  npm_execpath: '/Users/<USER>/.nvm/versions/node/v20.16.0/lib/node_modules/npm/bin/npm-cli.js',
  _CE_CONDA: '',
  PATH: '/Users/<USER>/Code/kapi-fresh/new_ide/node_modules/.bin:/Users/<USER>/Code/kapi-fresh/node_modules/.bin:/Users/<USER>/Code/node_modules/.bin:/Users/<USER>/node_modules/.bin:/Users/<USER>/.bin:/node_modules/.bin:/Users/<USER>/.nvm/versions/node/v20.16.0/lib/node_modules/npm/node_modules/@npmcli/run-script/lib/node-gyp-bin:/Users/<USER>/.local/bin:/opt/homebrew/opt/postgresql@17/bin:/usr/local/opt/postgresql@17/bin:/Users/<USER>/.codeium/windsurf/bin:/Users/<USER>/.rbenv/shims:/Users/<USER>/.rbenv/bin:/Users/<USER>/.nvm/versions/node/v20.16.0/bin:/Users/<USER>/miniconda3/condabin:/opt/homebrew/bin:/opt/homebrew/sbin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin',
  npm_package_json: '/Users/<USER>/Code/kapi-fresh/new_ide/package.json',
  _: '/Users/<USER>/Code/kapi-fresh/new_ide/node_modules/.bin/cross-env',
  npm_config_userconfig: '/Users/<USER>/.npmrc',
  npm_config_init_module: '/Users/<USER>/.npm-init.js',
  USER_ZDOTDIR: '/Users/<USER>',
  __CFBundleIdentifier: 'com.todesktop.230313mzl4w4u92',
  npm_command: 'run-script',
  PWD: '/Users/<USER>/Code/kapi-fresh/new_ide',
  npm_lifecycle_event: 'dev',
  EDITOR: 'vi',
  OTEL_EXPORTER_OTLP_METRICS_TEMPORALITY_PREFERENCE: 'delta',
  npm_package_name: 'kapi-ide',
  LANG: 'en_US.UTF-8',
  npm_config_npm_version: '10.8.1',
  VSCODE_GIT_ASKPASS_EXTRA_ARGS: '',
  XPC_FLAGS: '0x0',
  npm_config_node_gyp: '/Users/<USER>/.nvm/versions/node/v20.16.0/lib/node_modules/npm/node_modules/node-gyp/bin/node-gyp.js',
  RBENV_SHELL: 'zsh',
  npm_package_version: '0.1.0',
  _CE_M: '',
  XPC_SERVICE_NAME: '0',
  VSCODE_INJECTION: '1',
  SHLVL: '3',
  HOME: '/Users/<USER>',
  VSCODE_GIT_ASKPASS_MAIN: '/Applications/Cursor.app/Contents/Resources/app/extensions/git/dist/askpass-main.js',
  HOMEBREW_PREFIX: '/opt/homebrew',
  npm_config_cache: '/Users/<USER>/.npm',
  CONDA_PYTHON_EXE: '/Users/<USER>/miniconda3/bin/python',
  LOGNAME: 'balajiviswanathan',
  npm_lifecycle_script: 'cross-env NODE_ENV=development VITE_DEV_SERVER_URL=http://localhost:5173 vite',
  VSCODE_GIT_IPC_HANDLE: '/var/folders/m1/q6hdb5210rn1150gnzw33fdh0000gn/T/vscode-git-2e3e1e4492.sock',
  COREPACK_ENABLE_AUTO_PIN: '0',
  NVM_BIN: '/Users/<USER>/.nvm/versions/node/v20.16.0/bin',
  npm_config_user_agent: 'npm/10.8.1 node/v20.16.0 darwin arm64 workspaces/false',
  INFOPATH: '/opt/homebrew/share/info:/opt/homebrew/share/info:',
  HOMEBREW_CELLAR: '/opt/homebrew/Cellar',
  VSCODE_GIT_ASKPASS_NODE: '/Applications/Cursor.app/Contents/Frameworks/Cursor Helper (Plugin).app/Contents/MacOS/Cursor Helper (Plugin)',
  GIT_ASKPASS: '/Applications/Cursor.app/Contents/Resources/app/extensions/git/dist/askpass.sh',
  CLAUDECODE: '1',
  npm_node_execpath: '/Users/<USER>/.nvm/versions/node/v20.16.0/bin/node',
  npm_config_prefix: '/Users/<USER>/.nvm/versions/node/v20.16.0',
  COLORTERM: 'truecolor',
  NODE_ENV: 'development',
  VITE_DEV_SERVER_URL: 'http://localhost:5174/',
  DIST: '/Users/<USER>/Code/kapi-fresh/new_ide/dist',
  PUBLIC: '/Users/<USER>/Code/kapi-fresh/new_ide/public'
}
Creating main window with the following paths:
- __dirname: /Users/<USER>/Code/kapi-fresh/new_ide/dist/main
- process.env.DIST: /Users/<USER>/Code/kapi-fresh/new_ide/dist
- process.env.PUBLIC: /Users/<USER>/Code/kapi-fresh/new_ide/public
- preload path: /Users/<USER>/Code/kapi-fresh/new_ide/dist/preload/preload.js
- isPackaged: false
- url: http://localhost:5174/
- isPlaywrightRunning: false
Loading dev URL: http://localhost:5174/
[FileWatcher MAIN] 🚀 Setting up file system handlers...
[Main] Created terminal 5c31d6a2-752b-4c07-85ab-cd45b0a17598 (PID: 13022)
[Main] App quitting. Ensuring all terminals are killed.
[Main] Killing terminal 5c31d6a2-752b-4c07-85ab-cd45b0a17598 before quit.
[Main] Window closed. Killing active terminals.
[Main] Terminal 5c31d6a2-752b-4c07-85ab-cd45b0a17598 exited (code: 1, signal: 0)
