<svg viewBox="0 0 1200 900" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <rect width="1200" height="900" fill="#1a1825"/>

  <!-- Main IDE Window -->
  <rect x="20" y="20" width="1160" height="860" rx="8" fill="#242030"/>

  <!-- Top Menu Bar -->
  <rect x="20" y="20" width="1160" height="40" rx="8" fill="#2f2b3a"/>
  <text x="40" y="45" fill="#ffffff" font-family="Inter, sans-serif" font-size="14">KAPI IDE</text>
  <text x="140" y="45" fill="#8b7fc9" font-family="Inter, sans-serif" font-size="14">File</text>
  <text x="200" y="45" fill="#8b7fc9" font-family="Inter, sans-serif" font-size="14">Edit</text>
  <text x="260" y="45" fill="#8b7fc9" font-family="Inter, sans-serif" font-size="14">View</text>
  <text x="320" y="45" fill="#8b7fc9" font-family="Inter, sans-serif" font-size="14">Run</text>
  <text x="380" y="45" fill="#8b7fc9" font-family="Inter, sans-serif" font-size="14">Help</text>

  <!-- Voice indicator -->
  <g transform="translate(1100, 25)">
    <circle cx="0" cy="0" r="8" fill="#8b7fc9">
      <animate attributeName="opacity" values="0.3;1;0.3" dur="2s" repeatCount="indefinite"/>
    </circle>
    <text x="-50" y="5" text-anchor="end" fill="#8b7fc9" font-family="Inter, sans-serif" font-size="12">Voice Active</text>
  </g>

  <!-- Left Sidebar -->
  <rect x="20" y="60" width="250" height="820" fill="#2f2b3a"/>
  <text x="40" y="90" fill="#ffffff" font-family="Inter, sans-serif" font-size="16" font-weight="bold">KAPI IDE</text>

  <!-- Sidebar Items with Modern Icons -->
  <g transform="translate(40, 120)">
    <rect x="0" y="0" width="230" height="40" rx="8" fill="#8b7fc9" fill-opacity="0.1"/>
    <!-- Dashboard Icon -->
    <circle cx="25" cy="20" r="12" fill="#8b7fc9"/>
    <path d="M19,20 L25,14 L31,20" stroke="#242030" stroke-width="2" fill="none" stroke-linecap="round"/>
    <path d="M21,20 L21,26 L29,26 L29,20" stroke="#242030" stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
    <text x="45" y="25" fill="#ffffff" font-family="Inter, sans-serif" font-size="14" font-weight="500">Dashboard</text>
  </g>

  <g transform="translate(40, 170)">
    <rect x="0" y="0" width="230" height="40" rx="8" fill="#8b7fc9" fill-opacity="0.05"/>
    <!-- Projects Icon -->
    <rect x="13" y="14" width="24" height="16" rx="2" stroke="#8b7fc9" stroke-width="2" fill="none"/>
    <path d="M13,18 L37,18" stroke="#8b7fc9" stroke-width="2"/>
    <circle cx="25" cy="16" r="1.5" fill="#8b7fc9"/>
    <text x="45" y="25" fill="#8b7fc9" font-family="Inter, sans-serif" font-size="14" font-weight="500">Projects</text>
  </g>

  <g transform="translate(40, 220)">
    <rect x="0" y="0" width="230" height="40" rx="8" fill="#8b7fc9" fill-opacity="0.05"/>
    <!-- Slides Icon -->
    <rect x="13" y="12" width="18" height="18" rx="2" stroke="#8b7fc9" stroke-width="2" fill="none"/>
    <rect x="19" y="16" width="18" height="18" rx="2" stroke="#8b7fc9" stroke-width="2" fill="none" fill-opacity="0.5"/>
    <text x="45" y="25" fill="#8b7fc9" font-family="Inter, sans-serif" font-size="14" font-weight="500">Slides</text>
  </g>

  <g transform="translate(40, 270)">
    <rect x="0" y="0" width="230" height="40" rx="8" fill="#8b7fc9" fill-opacity="0.05"/>
    <!-- Documentation Icon -->
    <path d="M15,12 L35,12 L35,28 L15,28 Z" stroke="#8b7fc9" stroke-width="2" fill="none"/>
    <path d="M19,16 L31,16" stroke="#8b7fc9" stroke-width="1.5"/>
    <path d="M19,20 L31,20" stroke="#8b7fc9" stroke-width="1.5"/>
    <path d="M19,24 L27,24" stroke="#8b7fc9" stroke-width="1.5"/>
    <text x="45" y="25" fill="#8b7fc9" font-family="Inter, sans-serif" font-size="14" font-weight="500">Documentation</text>
  </g>

  <g transform="translate(40, 320)">
    <rect x="0" y="0" width="230" height="40" rx="8" fill="#8b7fc9" fill-opacity="0.05"/>
    <!-- Test Cases Icon -->
    <circle cx="25" cy="20" r="10" stroke="#8b7fc9" stroke-width="2" fill="none"/>
    <path d="M25,14 L25,20" stroke="#8b7fc9" stroke-width="2" stroke-linecap="round"/>
    <path d="M25,20 L30,24" stroke="#8b7fc9" stroke-width="2" stroke-linecap="round"/>
    <text x="45" y="25" fill="#8b7fc9" font-family="Inter, sans-serif" font-size="14" font-weight="500">Test Cases</text>
  </g>

  <g transform="translate(40, 370)">
    <rect x="0" y="0" width="230" height="40" rx="8" fill="#8b7fc9" fill-opacity="0.05"/>
    <!-- Code Icon -->
    <path d="M18,14 L14,20 L18,26" stroke="#8b7fc9" stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
    <path d="M32,14 L36,20 L32,26" stroke="#8b7fc9" stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
    <path d="M22,28 L28,12" stroke="#8b7fc9" stroke-width="2" fill="none" stroke-linecap="round"/>
    <text x="45" y="25" fill="#8b7fc9" font-family="Inter, sans-serif" font-size="14" font-weight="500">Code</text>
  </g>

  <!-- Tools Section -->
  <text x="40" y="450" fill="#ffffff" font-family="Inter, sans-serif" font-size="16" font-weight="bold" letter-spacing="0.5">Tools</text>

  <g transform="translate(40, 470)">
    <rect x="0" y="0" width="230" height="40" rx="8" fill="#8b7fc9" fill-opacity="0.05"/>
    <!-- Version Control Icon -->
    <circle cx="25" cy="20" r="10" stroke="#8b7fc9" stroke-width="2" fill="none"/>
    <path d="M25,15 L25,25" stroke="#8b7fc9" stroke-width="2" stroke-linecap="round"/>
    <circle cx="25" cy="15" r="2" fill="#8b7fc9"/>
    <circle cx="25" cy="25" r="2" fill="#8b7fc9"/>
    <path d="M25,20 L32,20" stroke="#8b7fc9" stroke-width="2" stroke-linecap="round"/>
    <circle cx="32" cy="20" r="2" fill="#8b7fc9"/>
    <text x="45" y="25" fill="#8b7fc9" font-family="Inter, sans-serif" font-size="14" font-weight="500">Version Control</text>
  </g>

  <g transform="translate(40, 520)">
    <rect x="0" y="0" width="230" height="40" rx="8" fill="#8b7fc9" fill-opacity="0.05"/>
    <!-- Team Icon -->
    <circle cx="20" cy="20" r="5" stroke="#8b7fc9" stroke-width="2" fill="none"/>
    <path d="M15,28 C15,25 17,23 20,23 C23,23 25,25 25,28" stroke="#8b7fc9" stroke-width="2" fill="none" stroke-linecap="round"/>
    <circle cx="30" cy="20" r="5" stroke="#8b7fc9" stroke-width="2" fill="none"/>
    <path d="M25,28 C25,25 27,23 30,23 C33,23 35,25 35,28" stroke="#8b7fc9" stroke-width="2" fill="none" stroke-linecap="round"/>
    <text x="45" y="25" fill="#8b7fc9" font-family="Inter, sans-serif" font-size="14" font-weight="500">Team</text>
  </g>

  <g transform="translate(40, 570)">
    <rect x="0" y="0" width="230" height="40" rx="8" fill="#8b7fc9" fill-opacity="0.05"/>
    <!-- Settings Icon -->
    <circle cx="25" cy="20" r="5" stroke="#8b7fc9" stroke-width="2" fill="none"/>
    <path d="M25,10 L25,12" stroke="#8b7fc9" stroke-width="2" stroke-linecap="round"/>
    <path d="M25,28 L25,30" stroke="#8b7fc9" stroke-width="2" stroke-linecap="round"/>
    <path d="M15,20 L17,20" stroke="#8b7fc9" stroke-width="2" stroke-linecap="round"/>
    <path d="M33,20 L35,20" stroke="#8b7fc9" stroke-width="2" stroke-linecap="round"/>
    <path d="M18,13 L20,15" stroke="#8b7fc9" stroke-width="2" stroke-linecap="round"/>
    <path d="M30,25 L32,27" stroke="#8b7fc9" stroke-width="2" stroke-linecap="round"/>
    <path d="M18,27 L20,25" stroke="#8b7fc9" stroke-width="2" stroke-linecap="round"/>
    <path d="M30,15 L32,13" stroke="#8b7fc9" stroke-width="2" stroke-linecap="round"/>
    <text x="45" y="25" fill="#8b7fc9" font-family="Inter, sans-serif" font-size="14" font-weight="500">Settings</text>
  </g>

  <!-- Middle Section - Voice Animation & Creation -->
  <rect x="270" y="60" width="400" height="820" fill="#2f2b3a"/>
  <text x="290" y="90" fill="#ffffff" font-family="Inter, sans-serif" font-size="16" font-weight="bold">Voice-Generated Development</text>
  <text x="290" y="120" fill="#8b7fc9" font-family="Inter, sans-serif" font-size="14">Watch as your voice commands create a website</text>

  <!-- Top Half - Voice Animation -->
  <g transform="translate(270, 140)">
    <rect x="20" y="0" width="360" height="380" rx="8" fill="#242030"/>

    <!-- Voice command text -->
    <rect x="40" y="30" width="320" height="50" rx="8" fill="#8b7fc9" fill-opacity="0.1"/>
    <text x="60" y="60" fill="#8b7fc9" font-family="Inter, sans-serif" font-size="14">"Create a landing page with hero section ..."</text>

    <!-- Voice visualization -->
    <g transform="translate(200, 180)">
      <!-- Outer rings -->
      <circle cx="0" cy="0" r="70" fill="none" stroke="#8b7fc9" stroke-width="2" opacity="0.3">
        <animate attributeName="r" values="70;75;70" dur="2s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.3;0.1;0.3" dur="2s" repeatCount="indefinite"/>
      </circle>
      <circle cx="0" cy="0" r="50" fill="none" stroke="#8b7fc9" stroke-width="2" opacity="0.5">
        <animate attributeName="r" values="50;55;50" dur="1.5s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.5;0.2;0.5" dur="1.5s" repeatCount="indefinite"/>
      </circle>
      <!-- Inner core -->
      <circle cx="0" cy="0" r="30" fill="#8b7fc9">
        <animate attributeName="opacity" values="0.8;1;0.8" dur="1s" repeatCount="indefinite"/>
      </circle>
      <!-- Sound waves -->
      <path d="M -40,-40 Q -20,-20 -40,40" stroke="#8b7fc9" stroke-width="2" fill="none">
        <animate attributeName="opacity" values="1;0" dur="0.5s" repeatCount="indefinite"/>
      </path>
      <path d="M 40,-40 Q 20,-20 40,40" stroke="#8b7fc9" stroke-width="2" fill="none">
        <animate attributeName="opacity" values="1;0" dur="0.5s" repeatCount="indefinite"/>
      </path>
    </g>

    <!-- Voice command indicator -->
    <rect x="40" y="300" width="320" height="40" rx="8" fill="#8b7fc9" fill-opacity="0.1"/>
    <circle cx="60" cy="320" r="5" fill="#8b7fc9">
      <animate attributeName="opacity" values="0.3;1;0.3" dur="0.8s" repeatCount="indefinite"/>
    </circle>
    <circle cx="90" cy="320" r="5" fill="#8b7fc9">
      <animate attributeName="opacity" values="0.3;1;0.3" dur="0.8s" repeatCount="indefinite" begin="0.2s"/>
    </circle>
    <circle cx="120" cy="320" r="5" fill="#8b7fc9">
      <animate attributeName="opacity" values="0.3;1;0.3" dur="0.8s" repeatCount="indefinite" begin="0.4s"/>
    </circle>
    <text x="150" y="325" fill="#8b7fc9" font-family="Inter, sans-serif" font-size="12">Processing...</text>
  </g>

  <!-- Bottom Half - Webpage Creation -->
  <g transform="translate(270, 540)">
    <rect x="20" y="0" width="360" height="340" rx="8" fill="#242030"/>

    <!-- Browser mockup -->
    <rect x="40" y="20" width="320" height="30" rx="6" fill="#1a1825"/>
    <circle cx="60" cy="35" r="4" fill="#ff5f57"/>
    <circle cx="75" cy="35" r="4" fill="#ffbd2e"/>
    <circle cx="90" cy="35" r="4" fill="#28c840"/>
    <rect x="120" y="27" width="180" height="16" rx="8" fill="#2f2b3a"/>
    <text x="210" y="39" text-anchor="middle" fill="#8b7fc9" font-family="Inter, sans-serif" font-size="10">landingpage.dev</text>

    <!-- Website content -->
    <g transform="translate(40, 60)">
      <!-- Hero section -->
      <rect x="0" y="0" width="320" height="100" rx="4" fill="#2f2b3a">
        <animate attributeName="height" values="0;100" dur="2s" fill="freeze"/>
      </rect>
      <text x="160" y="40" text-anchor="middle" fill="#ffffff" font-family="Inter, sans-serif" font-size="14">Hero Section</text>
      <text x="160" y="65" text-anchor="middle" fill="#8b7fc9" font-family="Inter, sans-serif" font-size="11">Generated by Voice</text>
      <rect x="130" y="75" width="60" height="15" rx="4" fill="#8b7fc9">
        <animate attributeName="width" values="0;60" dur="1s" begin="1.5s" fill="freeze"/>
      </rect>

      <!-- Features section -->
      <rect x="0" y="120" width="320" height="160" rx="4" fill="#242030">
        <animate attributeName="height" values="0;160" dur="2s" begin="2s" fill="freeze"/>
      </rect>
      <text x="160" y="150" text-anchor="middle" fill="#ffffff" font-family="Inter, sans-serif" font-size="14">Features Section</text>

      <!-- Feature boxes -->
      <rect x="10" y="170" width="95" height="80" rx="4" fill="#2f2b3a">
        <animate attributeName="height" values="0;80" dur="1s" begin="3s" fill="freeze"/>
      </rect>
      <rect x="112.5" y="170" width="95" height="80" rx="4" fill="#2f2b3a">
        <animate attributeName="height" values="0;80" dur="1s" begin="3.2s" fill="freeze"/>
      </rect>
      <rect x="215" y="170" width="95" height="80" rx="4" fill="#2f2b3a">
        <animate attributeName="height" values="0;80" dur="1s" begin="3.4s" fill="freeze"/>
      </rect>
    </g>

    <!-- Creation indicator -->
    <rect x="250" y="300" width="110" height="25" rx="4" fill="#8b7fc9"/>
    <text x="305" y="317" text-anchor="middle" fill="#ffffff" font-family="Inter, sans-serif" font-size="12">Creating Page...</text>
  </g>

  <!-- Right Section - Project Area -->
  <rect x="670" y="60" width="490" height="820" fill="#2f2b3a"/>

  <!-- Project Header -->
  <g transform="translate(670, 60)">
    <rect x="20" y="20" width="450" height="80" rx="8" fill="#242030"/>
    <text x="40" y="50" fill="#ffffff" font-family="Inter, sans-serif" font-size="16">Project: ReverseEngineeringDemo</text>
    <rect x="400" y="30" width="50" height="25" rx="4" fill="#28c840"/>
    <text x="425" y="48" text-anchor="middle" fill="#ffffff" font-family="Inter, sans-serif" font-size="12">Active</text>

    <!-- Voice Command Display -->
    <g transform="translate(40, 120)">
      <rect x="0" y="0" width="410" height="50" rx="8" fill="#8b7fc9" fill-opacity="0.1"/>
      <circle cx="20" cy="25" r="8" fill="#8b7fc9">
        <animate attributeName="opacity" values="0.3;1;0.3" dur="2s" repeatCount="indefinite"/>
      </circle>
      <text x="40" y="50" fill="#8b7fc9" font-family="Inter, sans-serif" font-size="14">"Generate auth module based on slide 3"</text>
    </g>
  </g>

  <!-- Tabs -->
  <g transform="translate(670, 180)">
    <rect x="20" y="0" width="80" height="30" rx="4" fill="#8b7fc9" fill-opacity="0.7"/>
    <text x="60" y="20" text-anchor="middle" fill="#ffffff" font-family="Inter, sans-serif" font-size="12">Editor</text>
    <rect x="110" y="0" width="80" height="30" rx="4" fill="#8b7fc9" fill-opacity="0.2"/>
    <text x="150" y="20" text-anchor="middle" fill="#8b7fc9" font-family="Inter, sans-serif" font-size="12">Slides</text>
    <rect x="200" y="0" width="120" height="30" rx="4" fill="#8b7fc9" fill-opacity="0.2"/>
    <text x="260" y="20" text-anchor="middle" fill="#8b7fc9" font-family="Inter, sans-serif" font-size="12">Documentation</text>
    <rect x="330" y="0" width="80" height="30" rx="4" fill="#8b7fc9" fill-opacity="0.2"/>
    <text x="370" y="20" text-anchor="middle" fill="#8b7fc9" font-family="Inter, sans-serif" font-size="12">Tests</text>
  </g>

  <!-- Code Editor with Improved Formatting -->
  <g transform="translate(690, 220)">
    <rect x="0" y="0" width="470" height="650" rx="8" fill="#242030"/>

    <!-- Line Numbers -->
    <rect x="0" y="0" width="30" height="650" fill="#1a1825" fill-opacity="0.5"/>
    <text x="15" y="30" text-anchor="middle" fill="#8b7fc9" font-family="JetBrains Mono, monospace" font-size="12" opacity="0.7">1</text>
    <text x="15" y="60" text-anchor="middle" fill="#8b7fc9" font-family="JetBrains Mono, monospace" font-size="12" opacity="0.7">2</text>
    <text x="15" y="80" text-anchor="middle" fill="#8b7fc9" font-family="JetBrains Mono, monospace" font-size="12" opacity="0.7">3</text>
    <text x="15" y="100" text-anchor="middle" fill="#8b7fc9" font-family="JetBrains Mono, monospace" font-size="12" opacity="0.7">4</text>
    <text x="15" y="120" text-anchor="middle" fill="#8b7fc9" font-family="JetBrains Mono, monospace" font-size="12" opacity="0.7">5</text>
    <text x="15" y="140" text-anchor="middle" fill="#8b7fc9" font-family="JetBrains Mono, monospace" font-size="12" opacity="0.7">6</text>
    <text x="15" y="160" text-anchor="middle" fill="#8b7fc9" font-family="JetBrains Mono, monospace" font-size="12" opacity="0.7">7</text>
    <text x="15" y="180" text-anchor="middle" fill="#8b7fc9" font-family="JetBrains Mono, monospace" font-size="12" opacity="0.7">8</text>

    <!-- Code with Better Formatting -->
    <text x="40" y="30" fill="#8b7fc9" font-family="JetBrains Mono, monospace" font-size="13" letter-spacing="0.5">// This is where you'll write your code</text>
    <text x="40" y="50" fill="#8b7fc9" font-family="JetBrains Mono, monospace" font-size="13" letter-spacing="0.5">// after completing slides, docs, and tests</text>

    <text x="40" y="80" fill="#ffffff" font-family="JetBrains Mono, monospace" font-size="14" letter-spacing="0.5" font-weight="500">function <tspan fill="#7dcfff">calculateResult</tspan>(<tspan fill="#ff9e64">input</tspan>) {</text>

    <text x="60" y="110" fill="#ff9e64" font-family="JetBrains Mono, monospace" font-size="14" letter-spacing="0.5">// Implementation will be guided by the</text>
    <text x="60" y="130" fill="#ff9e64" font-family="JetBrains Mono, monospace" font-size="14" letter-spacing="0.5">// documentation and test</text>

    <text x="60" y="160" fill="#7dcfff" font-family="JetBrains Mono, monospace" font-size="14" letter-spacing="0.5">const <tspan fill="#bb9af7">result</tspan> = <tspan fill="#7dcfff">processData</tspan>(<tspan fill="#ff9e64">input</tspan>);</text>
    <text x="60" y="180" fill="#7dcfff" font-family="JetBrains Mono, monospace" font-size="14" letter-spacing="0.5">return <tspan fill="#7dcfff">formatResponse</tspan>(<tspan fill="#bb9af7">result</tspan>);</text>

    <text x="40" y="210" fill="#ffffff" font-family="JetBrains Mono, monospace" font-size="14" letter-spacing="0.5">}</text>

    <!-- Cursor Animation -->
    <rect x="60" y="158" width="2" height="14" fill="#ffffff">
      <animate attributeName="opacity" values="1;0;1" dur="1.2s" repeatCount="indefinite"/>
    </rect>

    <!-- AI Generation Indicator with Improved Design -->
    <rect x="20" y="250" width="430" height="40" rx="6" fill="#8b7fc9" fill-opacity="0.15"/>
    <circle cx="40" cy="270" r="8" fill="#8b7fc9" fill-opacity="0.6">
      <animate attributeName="opacity" values="0.6;1;0.6" dur="1.5s" repeatCount="indefinite"/>
    </circle>
    <text x="60" y="274" fill="#8b7fc9" font-family="Inter, sans-serif" font-size="13" font-weight="500" letter-spacing="0.5">AI is generating code based on your voice command...</text>
    <rect x="390" y="255" width="50" height="30" rx="6" fill="#8b7fc9"/>
    <text x="415" y="274" text-anchor="middle" fill="#ffffff" font-family="Inter, sans-serif" font-size="12" font-weight="500">Stop</text>

    <!-- Additional Code Suggestions -->
    <rect x="20" y="310" width="430" height="120" rx="6" fill="#1a1825" fill-opacity="0.5"/>
    <text x="40" y="330" fill="#ffffff" font-family="Inter, sans-serif" font-size="13" font-weight="500" letter-spacing="0.5">AI Suggestions:</text>

    <rect x="40" y="340" width="390" height="80" rx="4" fill="#242030"/>
    <text x="50" y="360" fill="#7dcfff" font-family="JetBrains Mono, monospace" font-size="12" letter-spacing="0.5">function <tspan fill="#7dcfff">processData</tspan>(<tspan fill="#ff9e64">input</tspan>) {</text>
    <text x="50" y="380" fill="#bb9af7" font-family="JetBrains Mono, monospace" font-size="12" letter-spacing="0.5">  // Transform input data for processing</text>
    <text x="50" y="400" fill="#7dcfff" font-family="JetBrains Mono, monospace" font-size="12" letter-spacing="0.5">  return <tspan fill="#ff9e64">input</tspan>.map(<tspan fill="#bb9af7">item</tspan> => <tspan fill="#bb9af7">item</tspan>.transform());</text>
    <text x="50" y="420" fill="#ffffff" font-family="JetBrains Mono, monospace" font-size="12" letter-spacing="0.5">}</text>
  </g>

  <!-- Right Panel Preview with Modern Design -->
  <g transform="translate(680, 210)">
    <rect x="500" y="0" width="200" height="300" rx="8" fill="#242030"/>
    <text x="520" y="30" fill="#ffffff" font-family="Inter, sans-serif" font-size="14" font-weight="600" letter-spacing="0.5">Input Methods</text>

    <!-- Modern Multi-modal Indicators -->
    <g transform="translate(520, 50)">
      <!-- Voice Input -->
      <rect x="0" y="0" width="160" height="60" rx="8" fill="#8b7fc9" fill-opacity="0.1"/>
      <circle cx="30" cy="30" r="20" fill="#8b7fc9" fill-opacity="0.2" stroke="#8b7fc9" stroke-width="2">
        <animate attributeName="r" values="20;22;20" dur="2s" repeatCount="indefinite"/>
      </circle>

      <!-- Modern Microphone Icon -->
      <path d="M30,20 L30,40" stroke="#8b7fc9" stroke-width="2" stroke-linecap="round"/>
      <path d="M25,20 C25,17 27,15 30,15 C33,15 35,17 35,20 L35,30 C35,33 33,35 30,35 C27,35 25,33 25,30 Z" stroke="#8b7fc9" stroke-width="2" fill="none"/>
      <path d="M22,30 C22,36 25,40 30,40 C35,40 38,36 38,30" stroke="#8b7fc9" stroke-width="2" fill="none"/>

      <!-- Sound Wave Animation -->
      <path d="M40,25 Q42,30 40,35" stroke="#8b7fc9" stroke-width="1.5" fill="none">
        <animate attributeName="opacity" values="0.3;1;0.3" dur="1.5s" repeatCount="indefinite"/>
      </path>
      <path d="M44,22 Q48,30 44,38" stroke="#8b7fc9" stroke-width="1.5" fill="none">
        <animate attributeName="opacity" values="0.3;1;0.3" dur="1.5s" repeatCount="indefinite" begin="0.2s"/>
      </path>
      <path d="M48,18 Q54,30 48,42" stroke="#8b7fc9" stroke-width="1.5" fill="none">
        <animate attributeName="opacity" values="0.3;1;0.3" dur="1.5s" repeatCount="indefinite" begin="0.4s"/>
      </path>

      <text x="80" y="25" fill="#ffffff" font-family="Inter, sans-serif" font-size="12" font-weight="600">Voice Input</text>
      <text x="80" y="42" fill="#8b7fc9" font-family="Inter, sans-serif" font-size="10" letter-spacing="0.5">Active - Listening...</text>

      <!-- Active Indicator -->
      <circle cx="150" cy="30" r="5" fill="#8b7fc9">
        <animate attributeName="opacity" values="0.5;1;0.5" dur="1s" repeatCount="indefinite"/>
      </circle>
    </g>

    <!-- Sketch Input -->
    <g transform="translate(520, 120)">
      <rect x="0" y="0" width="160" height="60" rx="8" fill="#ff9e64" fill-opacity="0.1"/>

      <!-- Modern Sketch Icon -->
      <path d="M20,40 L25,25 L35,35 L40,20" stroke="#ff9e64" stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
      <path d="M20,40 C25,38 35,42 40,40" stroke="#ff9e64" stroke-width="2" fill="none" stroke-linecap="round"/>
      <circle cx="25" cy="25" r="2" fill="#ff9e64"/>
      <circle cx="35" cy="35" r="2" fill="#ff9e64"/>
      <circle cx="40" cy="20" r="2" fill="#ff9e64"/>

      <text x="80" y="25" fill="#ffffff" font-family="Inter, sans-serif" font-size="12" font-weight="600">Sketch Input</text>
      <text x="80" y="42" fill="#ff9e64" font-family="Inter, sans-serif" font-size="10" letter-spacing="0.5">Available</text>
    </g>

    <!-- Code Input -->
    <g transform="translate(520, 190)">
      <rect x="0" y="0" width="160" height="60" rx="8" fill="#7dcfff" fill-opacity="0.1"/>

      <!-- Modern Code Icon -->
      <path d="M22,30 L28,20 L34,30" stroke="#7dcfff" stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
      <path d="M20,35 L25,35" stroke="#7dcfff" stroke-width="2" stroke-linecap="round"/>
      <path d="M28,35 L38,35" stroke="#7dcfff" stroke-width="2" stroke-linecap="round"/>
      <path d="M20,40 L30,40" stroke="#7dcfff" stroke-width="2" stroke-linecap="round"/>

      <text x="80" y="25" fill="#ffffff" font-family="Inter, sans-serif" font-size="12" font-weight="600">Code Input</text>
      <text x="80" y="42" fill="#7dcfff" font-family="Inter, sans-serif" font-size="10" letter-spacing="0.5">Available</text>
    </g>

    <!-- Bottom Gradient -->
    <linearGradient id="bottomGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" stop-color="#242030" stop-opacity="0"/>
      <stop offset="100%" stop-color="#8b7fc9" stop-opacity="0.1"/>
    </linearGradient>
    <rect x="500" y="260" width="200" height="40" fill="url(#bottomGradient)"/>

    <!-- Input Method Switcher -->
    <rect x="540" y="270" width="120" height="25" rx="12.5" fill="#1a1825"/>
    <circle cx="550" cy="282.5" r="7.5" fill="#8b7fc9"/>
    <text x="600" y="287" text-anchor="middle" fill="#ffffff" font-family="Inter, sans-serif" font-size="10" letter-spacing="0.5">Switch Input</text>
  </g>

  <!-- Modern Status Bar -->
  <rect x="20" y="845" width="1160" height="35" rx="8" fill="#2f2b3a"/>

  <!-- Voice Mode Status -->
  <g transform="translate(40, 862)">
    <circle cx="0" cy="0" r="5" fill="#8b7fc9">
      <animate attributeName="opacity" values="0.7;1;0.7" dur="2s" repeatCount="indefinite"/>
    </circle>
    <text x="12" y="4" fill="#8b7fc9" font-family="Inter, sans-serif" font-size="12" font-weight="500" letter-spacing="0.5">Voice Mode Active</text>
  </g>

  <!-- Git Status -->
  <g transform="translate(180, 862)">
    <path d="M-5,-5 L0,0 L-5,5" stroke="#28c840" stroke-width="1.5" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
    <path d="M0,0 L5,0" stroke="#28c840" stroke-width="1.5" fill="none" stroke-linecap="round"/>
    <text x="12" y="4" fill="#28c840" font-family="Inter, sans-serif" font-size="12" font-weight="500" letter-spacing="0.5">Git: Clean</text>
  </g>

  <!-- AI Status -->
  <g transform="translate(300, 862)">
    <rect x="-8" y="-8" width="16" height="16" rx="3" fill="#8b7fc9" fill-opacity="0.2"/>
    <text x="-1" y="4" fill="#8b7fc9" font-family="Inter, sans-serif" font-size="12" font-weight="bold">AI</text>
    <text x="12" y="4" fill="#8b7fc9" font-family="Inter, sans-serif" font-size="12" font-weight="500" letter-spacing="0.5">Ready</text>
  </g>

  <!-- Token Usage -->
  <g transform="translate(400, 862)">
    <rect x="-8" y="-6" width="100" height="12" rx="6" fill="#1a1825"/>
    <rect x="-8" y="-6" width="12" height="12" rx="6" fill="#8b7fc9"/>
    <text x="12" y="4" fill="#8b7fc9" font-family="Inter, sans-serif" font-size="12" font-weight="500" letter-spacing="0.5">Tokens: 1.2K/10K</text>
  </g>

  <!-- Time -->
  <g transform="translate(1120, 862)">
    <text x="0" y="4" text-anchor="end" fill="#ffffff" font-family="Inter, sans-serif" font-size="12" font-weight="500" letter-spacing="0.5">10:42 AM</text>
  </g>

  <!-- Markers -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#8b7fc9"/>
    </marker>
  </defs>
</svg>