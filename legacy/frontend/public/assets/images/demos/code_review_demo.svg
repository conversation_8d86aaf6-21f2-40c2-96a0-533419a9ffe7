<svg viewBox="0 0 800 600" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <rect width="800" height="600" fill="#1a1825"/>
  
  <!-- Main Container -->
  <rect x="20" y="20" width="760" height="560" rx="12" fill="#242030" stroke="#8b7fc9" stroke-width="1"/>
  
  <!-- Header -->
  <text x="400" y="50" text-anchor="middle" fill="#ffffff" font-family="Inter, sans-serif" font-size="20" font-weight="bold">KAPI Code Review</text>
  
  <!-- Project Structure Panel -->
  <g transform="translate(40, 80)">
    <rect x="0" y="0" width="260" height="450" rx="8" fill="#2f2b3a"/>
    <text x="10" y="25" fill="#8b7fc9" font-family="Inter, sans-serif" font-size="14" font-weight="bold">Project Structure</text>
    
    <!-- Folder Tree -->
    <g transform="translate(20, 50)">
      <!-- Root -->
      <g>
        <rect x="0" y="0" width="16" height="16" rx="3" fill="#ffbd2e" fill-opacity="0.2"/>
        <text x="4" y="13" fill="#ffbd2e" font-family="monospace" font-size="12">📁</text>
        <text x="24" y="13" fill="#ffffff" font-family="Inter, sans-serif" font-size="12">ai-project</text>
      </g>
      
      <!-- Src folder -->
      <g transform="translate(12, 30)">
        <rect x="0" y="0" width="16" height="16" rx="3" fill="#7dcfff" fill-opacity="0.2"/>
        <text x="4" y="13" fill="#7dcfff" font-family="monospace" font-size="12">📁</text>
        <text x="24" y="13" fill="#ffffff" font-family="Inter, sans-serif" font-size="12">src</text>
      </g>
      
      <!-- Auth package -->
      <g transform="translate(24, 60)">
        <rect x="0" y="0" width="16" height="16" rx="3" fill="#bb9af7" fill-opacity="0.2"/>
        <text x="4" y="13" fill="#bb9af7" font-family="monospace" font-size="12">📦</text>
        <text x="24" y="13" fill="#ffffff" font-family="Inter, sans-serif" font-size="12">auth/</text>
        <rect x="0" y="0" width="140" height="16" rx="3" fill="#bb9af7" fill-opacity="0.1"/>
      </g>
      
      <!-- Auth files -->
      <g transform="translate(36, 90)">
        <text x="0" y="13" fill="#8b7fc9" font-family="monospace" font-size="11">└── login.ts</text>
        <text x="0" y="33" fill="#8b7fc9" font-family="monospace" font-size="11">└── signup.ts</text>
        <text x="0" y="53" fill="#8b7fc9" font-family="monospace" font-size="11">└── utils.ts</text>
      </g>
      
      <!-- Core package -->
      <g transform="translate(24, 150)">
        <rect x="0" y="0" width="16" height="16" rx="3" fill="#bb9af7" fill-opacity="0.2"/>
        <text x="4" y="13" fill="#bb9af7" font-family="monospace" font-size="12">📦</text>
        <text x="24" y="13" fill="#ffffff" font-family="Inter, sans-serif" font-size="12">core/</text>
      </g>
      
      <!-- Core files -->
      <g transform="translate(36, 180)">
        <text x="0" y="13" fill="#8b7fc9" font-family="monospace" font-size="11">└── engine.ts</text>
        <text x="0" y="33" fill="#8b7fc9" font-family="monospace" font-size="11">└── config.ts</text>
        <text x="0" y="53" fill="#8b7fc9" font-family="monospace" font-size="11">└── index.ts</text>
      </g>
      
      <!-- API package -->
      <g transform="translate(24, 240)">
        <rect x="0" y="0" width="16" height="16" rx="3" fill="#bb9af7" fill-opacity="0.2"/>
        <text x="4" y="13" fill="#bb9af7" font-family="monospace" font-size="12">📦</text>
        <text x="24" y="13" fill="#ffffff" font-family="Inter, sans-serif" font-size="12">api/</text>
      </g>
      
      <!-- API files -->
      <g transform="translate(36, 270)">
        <text x="0" y="13" fill="#8b7fc9" font-family="monospace" font-size="11">└── routes.ts</text>
        <text x="0" y="33" fill="#8b7fc9" font-family="monospace" font-size="11">└── middleware.ts</text>
        <text x="0" y="53" fill="#8b7fc9" font-family="monospace" font-size="11">└── controllers.ts</text>
      </g>
    </g>
    
    <!-- Status indicators -->
    <g transform="translate(20, 360)">
      <rect x="0" y="0" width="220" height="80" rx="6" fill="#8b7fc9" fill-opacity="0.1"/>
      <text x="10" y="25" fill="#ffffff" font-family="Inter, sans-serif" font-size="12" font-weight="bold">Package Analysis Status</text>
      
      <!-- Status row 1 -->
      <g transform="translate(10, 40)">
        <circle cx="6" cy="6" r="5" fill="#28c840"/>
        <text x="20" y="10" fill="#ffffff" font-family="Inter, sans-serif" font-size="11">auth/ - Clean</text>
      </g>
      
      <!-- Status row 2 -->
      <g transform="translate(10, 55)">
        <circle cx="6" cy="6" r="5" fill="#ff5f57"/>
        <text x="20" y="10" fill="#ffffff" font-family="Inter, sans-serif" font-size="11">core/ - 3 Critical Issues</text>
      </g>
      
      <!-- Status row 3 -->
      <g transform="translate(10, 70)">
        <circle cx="6" cy="6" r="5" fill="#ffbd2e"/>
        <text x="20" y="10" fill="#ffffff" font-family="Inter, sans-serif" font-size="11">api/ - 2 Warnings</text>
      </g>
    </g>
  </g>
  
  <!-- Detailed Review Panel -->
  <g transform="translate(320, 80)">
    <rect x="0" y="0" width="440" height="450" rx="8" fill="#2f2b3a"/>
    <text x="10" y="25" fill="#ffffff" font-family="Inter, sans-serif" font-size="14" font-weight="bold">Package Deep Analysis: auth/</text>
    
    <!-- Tab Navigation -->
    <g transform="translate(0, 45)">
      <rect x="20" y="0" width="120" height="30" rx="6" fill="#8b7fc9" fill-opacity="0.2"/>
      <rect x="145" y="0" width="120" height="30" rx="6" fill="#8b7fc9" fill-opacity="0.1"/>
      <rect x="270" y="0" width="120" height="30" rx="6" fill="#8b7fc9" fill-opacity="0.1"/>
      
      <text x="80" y="20" text-anchor="middle" fill="#ffffff" font-family="Inter, sans-serif" font-size="12">Code Quality</text>
      <text x="205" y="20" text-anchor="middle" fill="#8b7fc9" font-family="Inter, sans-serif" font-size="12">Dependencies</text>
      <text x="330" y="20" text-anchor="middle" fill="#8b7fc9" font-family="Inter, sans-serif" font-size="12">Tests Coverage</text>
    </g>
    
    <!-- Code Quality Content -->
    <g transform="translate(20, 90)">
      <!-- File metrics -->
      <g>
        <rect x="0" y="0" width="400" height="40" rx="6" fill="#bb9af7" fill-opacity="0.1"/>
        <text x="10" y="25" fill="#ffffff" font-family="Inter, sans-serif" font-size="12" font-weight="bold">login.ts</text>
        
        <!-- Metrics -->
        <g transform="translate(350, 0)">
          <rect x="0" y="10" width="40" height="20" rx="4" fill="#28c840"/>
          <text x="20" y="25" text-anchor="middle" fill="#ffffff" font-family="Inter, sans-serif" font-size="11">92</text>
        </g>
      </g>
      
      <!-- Issues List -->
      <g transform="translate(0, 50)">
        <!-- Issue 1 -->
        <g>
          <rect x="0" y="0" width="400" height="50" rx="6" fill="#ff5f57" fill-opacity="0.1">
            <animate attributeName="height" values="0;50" dur="0.8s" fill="freeze"/>
          </rect>
          <circle cx="15" cy="25" r="6" fill="#ff5f57"/>
          <text x="30" y="25" fill="#ff5f57" font-family="Inter, sans-serif" font-size="11">Security: Direct password comparison</text>
          <text x="30" y="40" fill="#8b7fc9" font-family="Inter, sans-serif" font-size="10">Line 11: Use bcrypt.compare() instead</text>
        </g>
        
        <!-- Issue 2 -->
        <g transform="translate(0, 60)">
          <rect x="0" y="0" width="400" height="50" rx="6" fill="#ffbd2e" fill-opacity="0.1">
            <animate attributeName="height" values="0;50" dur="0.8s" begin="0.8s" fill="freeze"/>
          </rect>
          <circle cx="15" cy="25" r="6" fill="#ffbd2e"/>
          <text x="30" y="25" fill="#ffbd2e" font-family="Inter, sans-serif" font-size="11">Error Handling: Inconsistent return types</text>
          <text x="30" y="40" fill="#8b7fc9" font-family="Inter, sans-serif" font-size="10">Lines 3, 8, 15: Standardize Error | boolean | Token</text>
        </g>
        
        <!-- Issue 3 -->
        <g transform="translate(0, 120)">
          <rect x="0" y="0" width="400" height="50" rx="6" fill="#7dcfff" fill-opacity="0.1">
            <animate attributeName="height" values="0;50" dur="0.8s" begin="1.6s" fill="freeze"/>
          </rect>
          <circle cx="15" cy="25" r="6" fill="#7dcfff"/>
          <text x="30" y="25" fill="#7dcfff" font-family="Inter, sans-serif" font-size="11">Code Style: Function complexity exceeds limit</text>
          <text x="30" y="40" fill="#8b7fc9" font-family="Inter, sans-serif" font-size="10">52 lines (limit: 50). Consider refactoring.</text>
        </g>
      </g>
    </g>
    
    <!-- Package Health Overview -->
    <g transform="translate(20, 310)">
      <rect x="0" y="0" width="400" height="120" rx="6" fill="#8b7fc9" fill-opacity="0.1"/>
      <text x="10" y="25" fill="#ffffff" font-family="Inter, sans-serif" font-size="12" font-weight="bold">Package Health Score</text>
      
      <!-- Score Chart -->
      <g transform="translate(200, 40)">
        <circle cx="40" cy="40" r="35" fill="none" stroke="#2f2b3a" stroke-width="8"/>
        <circle cx="40" cy="40" r="35" fill="none" stroke="#bb9af7" stroke-width="8" stroke-dasharray="110" stroke-dashoffset="30">
          <animate attributeName="stroke-dashoffset" values="110;30" dur="1.5s" fill="freeze"/>
        </circle>
        <text x="40" y="50" text-anchor="middle" fill="#ffffff" font-family="Inter, sans-serif" font-size="20" font-weight="bold">92</text>
      </g>
      
      <!-- Metrics -->
      <g transform="translate(10, 35)">
        <text x="0" y="20" fill="#8b7fc9" font-family="Inter, sans-serif" font-size="11">Code Quality: 92/100</text>
        <text x="0" y="40" fill="#8b7fc9" font-family="Inter, sans-serif" font-size="11">Test Coverage: 85%</text>
        <text x="0" y="60" fill="#8b7fc9" font-family="Inter, sans-serif" font-size="11">Dependencies: Up to date</text>
        <text x="0" y="80" fill="#8b7fc9" font-family="Inter, sans-serif" font-size="11">Documentation: 75%</text>
      </g>
    </g>
  </g>
  
  <!-- AI Review Indicator -->
  <g transform="translate(680, 540)">
    <circle cx="30" cy="0" r="20" fill="none" stroke="#8b7fc9" stroke-width="2">
      <animate attributeName="r" values="20;25;20" dur="2s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.5;1;0.5" dur="2s" repeatCount="indefinite"/>
    </circle>
    <text x="60" y="5" fill="#8b7fc9" font-family="Inter, sans-serif" font-size="12">AI reviewing packages...</text>
  </g>
  
  <!-- Connection Lines -->
  <g transform="translate(160, 140)">
    <path d="M140,0 L160,0" stroke="#8b7fc9" stroke-width="2" stroke-dasharray="5,5">
      <animate attributeName="stroke-dashoffset" values="0;-10" dur="1s" repeatCount="indefinite"/>
    </path>
  </g>
</svg>