<svg width="1200" height="800" viewBox="0 0 1200 800" xmlns="http://www.w3.org/2000/svg">
  <style>
    /* Define CSS variables for colors */
    :root {
      --background-color: #f4f7f6; /* Light background */
      --sidebar-color: #ffffff; /* White sidebar */
      --panel-color: #ffffff; /* White panel */
      --border-color: #e0e0e0; /* Light grey borders */
      --text-color: #333333; /* Dark grey text */
      --text-color-light: #666666; /* Medium grey text */
      --accent-color: #007bff; /* Primary accent color (like a vibrant blue) */
      --accent-color-light: #e0f2ff; /* Light accent color */
      --icon-color: #666666; /* Default icon color */
      --active-icon-color: #007bff; /* Active icon color */
      --scrollbar-thumb: #cccccc; /* Scrollbar thumb color */
      --scrollbar-track: #f4f4f4; /* Scrollbar track color */
      --editor-background: #f8f8f8; /* Editor background */
      --editor-line-numbers: #bbbbbb; /* Editor line numbers */
      --editor-syntax-keyword: #007bff; /* Syntax highlighting - keyword */
      --editor-syntax-string: #28a745; /* Syntax highlighting - string */
      --editor-syntax-comment: #6a737d; /* Syntax highlighting - comment */
      --editor-syntax-function: #6f42c1; /* Syntax highlighting - function */
      --editor-selection-background: rgba(0, 123, 255, 0.2); /* Selection background */
    }

    /* Basic styles for shapes */
    rect {
      stroke: var(--border-color);
      stroke-width: 1;
    }

    /* Style for text */
    text {
      font-family: sans-serif;
      font-size: 12px;
      fill: var(--text-color);
    }

    /* Specific styles for UI elements */
    .sidebar {
      fill: var(--sidebar-color);
    }

    .top-bar {
      fill: var(--background-color);
    }

    .editor-area {
      fill: var(--editor-background);
    }

    .panel-area {
      fill: var(--panel-color);
    }

    .status-bar {
      fill: var(--background-color);
      font-size: 11px;
      fill: var(--text-color-light);
    }

    .icon {
      fill: var(--icon-color);
    }

    .icon.active {
      fill: var(--active-icon-color);
    }

    .tab {
      fill: var(--background-color);
      stroke-width: 0;
      fill: var(--text-color);
      font-size: 11px;
    }

    .tab.active {
      fill: var(--editor-background);
      stroke: var(--border-color);
      stroke-width: 1;
      stroke-bottom: none; /* Create a connected look with the editor */
    }

    .scrollbar-vertical {
      fill: var(--scrollbar-track);
    }

    .scrollbar-thumb-vertical {
      fill: var(--scrollbar-thumb);
      rx: 3; /* Rounded corners for the thumb */
      ry: 3;
    }

    .line-numbers {
      fill: var(--editor-line-numbers);
      font-size: 11px;
    }

    .syntax-keyword { fill: var(--editor-syntax-keyword); }
    .syntax-string { fill: var(--editor-syntax-string); }
    .syntax-comment { fill: var(--editor-syntax-comment); }
    .syntax-function { fill: var(--editor-syntax-function); }

    .selection-background {
        fill: var(--editor-selection-background);
        stroke-width: 0;
    }

  </style>

  <!-- Background -->
  <rect x="0" y="0" width="1200" height="800" fill="var(--background-color)" stroke-width="0"/>

  <!-- Top Bar (Menu Bar & Tabs) -->
  <rect x="0" y="0" width="1200" height="30" class="top-bar" stroke-width="0"/>
  <text x="10" y="20" font-size="13" font-weight="bold" fill="var(--accent-color)">MyIDE</text>
  <text x="90" y="20" font-size="12">File</text>
  <text x="130" y="20" font-size="12">Edit</text>
  <text x="170" y="20" font-size="12">View</text>
  <text x="210" y="20" font-size="12">Run</text>
  <text x="250" y="20" font-size="12">Terminal</text>
  <text x="310" y="20" font-size="12">Help</text>

  <!-- Tabs -->
  <rect x="350" y="30" width="150" height="25" class="tab active"/>
  <text x="360" y="46" class="tab active">index.html</text>
  <rect x="500" y="30" width="150" height="25" class="tab"/>
  <text x="510" y="46" class="tab">style.css</text>
  <rect x="650" y="30" width="150" height="25" class="tab"/>
  <text x="660" y="46" class="tab">script.js</text>

  <!-- Sidebar -->
  <rect x="0" y="30" width="50" height="740" class="sidebar"/>
  <!-- Placeholder icons in the sidebar -->
  <circle cx="25" cy="60" r="8" class="icon active"/>
  <circle cx="25" cy="100" r="8" class="icon"/>
  <circle cx="25" cy="140" r="8" class="icon"/>
  <circle cx="25" cy="180" r="8" class="icon"/>
  <circle cx="25" cy="220" r="8" class="icon"/>

  <!-- Main Editor Area -->
  <rect x="50" y="55" width="900" height="500" class="editor-area" stroke-width="0"/>

  <!-- Editor Content (Simplified Syntax Highlighting and Line Numbers) -->
  <!-- Line Numbers -->
  <text x="70" y="80" class="line-numbers">1</text>
  <text x="70" y="95" class="line-numbers">2</text>
  <text x="70" y="110" class="line-numbers">3</text>
  <text x="70" y="125" class="line-numbers">4</text>
  <text x="70" y="140" class="line-numbers">5</text>
  <text x="70" y="155" class="line-numbers">6</text>
  <text x="70" y="170" class="line-numbers">7</text>
  <text x="70" y="185" class="line-numbers">8</text>
  <text x="70" y="200" class="line-numbers">9</text>
  <text x="70" y="215" class="line-numbers">10</text>

  <!-- Code Content (Simplified - using text with color classes) -->

  <text x="115" y="125"><<tspan class="syntax-keyword">title</tspan>><tspan class="syntax-string">My Awesome App</tspan></<tspan class="syntax-keyword">title</tspan>></text>
  <text x="100" y="140"></<tspan class="syntax-keyword">head</tspan>></text>
  <text x="100" y="155"><<tspan class="syntax-keyword">body</tspan>></text>
  <text x="115" y="170"><<tspan class="syntax-keyword">h1</tspan>><tspan class="syntax-string">Welcome to MyIDE!</tspan></<tspan class="syntax-keyword">h1</tspan>></text>
  <text x="115" y="185"><tspan class="syntax-comment">// This is a comment</tspan></text>
  <text x="115" y="200"><tspan class="syntax-keyword">let</tspan> <tspan class="syntax-function">greeting</tspan> = <tspan class="syntax-string">"Hello, world!"</tspan>;</text>
  <text x="100" y="215"></<tspan class="syntax-keyword">body</tspan>></text>

  <!-- Selection (Example) -->
  <rect x="115" y="190" width="200" height="14" class="selection-background"/>

  <!-- Vertical Scrollbar (Placeholder) -->
  <rect x="950" y="55" width="10" height="500" class="scrollbar-vertical"/>
  <rect x="951" y="70" width="8" height="100" class="scrollbar-thumb-vertical"/>


  <!-- Right Sidebar (Optional - for extensions, outlines, etc.) -->
  <rect x="950" y="30" width="250" height="740" class="sidebar"/>
   <text x="965" y="55" font-size="13" font-weight="bold">Outline</text>
   <text x="965" y="80" font-size="12">├── Document</text>
   <text x="965" y="95" font-size="12">│   ├── Head</text>
   <text x="965" y="110" font-size="12">│   │   └── Title</text>
   <text x="965" y="125" font-size="12">│   └── Body</text>
    <text x="965" y="140" font-size="12">│       └── H1</text>


  <!-- Bottom Panel (Terminal, Output, Problems, Debug Console) -->
  <rect x="50" y="555" width="1150" height="215" class="panel-area"/>
   <text x="65" y="575" font-size="13" font-weight="bold">Terminal</text>
   <text x="150" y="575" font-size="13" fill="var(--text-color-light)">Output</text>
   <text x="230" y="575" font-size="13" fill="var(--text-color-light)">Problems</text>
   <text x="320" y="575" font-size="13" fill="var(--text-color-light)">Debug Console</text>

   <!-- Terminal Content (Example) -->
   <text x="65" y="600" font-size="11" fill="#000000">> npm start</text>
   <text x="65" y="615" font-size="11" fill="#000000">> Starting development server...</text>
   <text x="65" y="630" font-size="11" fill="#008000">Server listening on port 3000</text>

  <!-- Status Bar -->
  <rect x="0" y="770" width="1200" height="30" class="status-bar" stroke-width="0"/>
  <text x="10" y="788" class="status-bar">Branch: main</text>
  <text x="100" y="788" class="status-bar">Encoding: UTF-8</text>
  <text x="200" y="788" class="status-bar">Line Endings: LF</text>
  <text x="300" y="788" class="status-bar">Language: HTML</text>
  <text x="1150" y="788" class="status-bar">Ln 8, Col 25</text>

</svg>