<svg viewBox="0 0 500 500" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <rect width="500" height="500" fill="#1a1825"/>

  <!-- Main Container -->
  <rect x="20" y="20" width="460" height="460" rx="12" fill="#242030" stroke="#8b7fc9" stroke-width="1"/>

  <!-- Title Bar -->
  <rect x="20" y="20" width="460" height="35" rx="12" fill="#2f2b3a"/>
  <circle cx="35" cy="37.5" r="6" fill="#ff5f57"/>
  <circle cx="55" cy="37.5" r="6" fill="#ffbd2e"/>
  <circle cx="75" cy="37.5" r="6" fill="#28c840"/>
  <text x="250" y="42" text-anchor="middle" fill="#8b7fc9" font-family="Inter, sans-serif" font-size="14">KAPI IDE - Multimodal Coding Everywhere</text>

  <!-- Central Hub - Desktop -->
  <g transform="translate(250, 180)">
    <!-- Central Desktop -->
    <rect x="-80" y="-60" width="160" height="120" rx="8" fill="#2f2b3a" stroke="#8b7fc9" stroke-width="2"/>
    <rect x="-70" y="-50" width="140" height="20" rx="4" fill="#242030"/>
    <text x="0" y="-35" text-anchor="middle" fill="#ffffff" font-family="Inter, sans-serif" font-size="12">Desktop IDE</text>

    <!-- Code on Desktop -->
    <rect x="-70" y="-20" width="140" height="70" rx="4" fill="#242030"/>
    <text x="-60" y="0" fill="#7dcfff" font-family="monospace" font-size="8">function processData() {</text>
    <text x="-60" y="15" fill="#ff9e64" font-family="monospace" font-size="8">  // Multimodal code</text>
    <text x="-60" y="30" fill="#ff9e64" font-family="monospace" font-size="8">  return analysis;</text>
    <text x="-60" y="45" fill="#7dcfff" font-family="monospace" font-size="8">}</text>

    <!-- Pulsing Indicator -->
    <circle cx="0" cy="0" r="90" fill="none" stroke="#8b7fc9" stroke-width="1" opacity="0.3">
      <animate attributeName="r" values="90;95;90" dur="3s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.3;0.1;0.3" dur="3s" repeatCount="indefinite"/>
    </circle>
  </g>

  <!-- Mobile Device -->
  <g transform="translate(120, 320)">
    <!-- Phone Shape -->
    <rect x="-30" y="-50" width="60" height="100" rx="10" fill="#2f2b3a" stroke="#8b7fc9" stroke-width="2"/>
    <rect x="-25" y="-45" width="50" height="90" rx="5" fill="#242030"/>

    <!-- Phone Screen Content -->
    <text x="0" y="-30" text-anchor="middle" fill="#ffffff" font-family="Inter, sans-serif" font-size="8">KAPI Mobile</text>
    <rect x="-20" y="-20" width="40" height="60" rx="3" fill="#1a1825"/>
    <text x="0" y="0" text-anchor="middle" fill="#7dcfff" font-family="monospace" font-size="6">// Continue</text>
    <text x="0" y="10" text-anchor="middle" fill="#7dcfff" font-family="monospace" font-size="6">// your work</text>
    <text x="0" y="20" text-anchor="middle" fill="#7dcfff" font-family="monospace" font-size="6">// on mobile</text>

    <!-- Connection Line to Desktop -->
    <path d="M0,-50 C0,-80 100,-120 130,-60" stroke="#8b7fc9" stroke-width="1.5" fill="none" stroke-dasharray="4,4">
      <animate attributeName="stroke-dashoffset" values="0;8" dur="1s" repeatCount="indefinite"/>
    </path>

    <!-- Sync Icon -->
    <circle cx="0" cy="-60" r="10" fill="#8b7fc9" opacity="0.2"/>
    <path d="M-5,-60 L0,-65 L5,-60 M0,-65 L0,-55 M-5,-60 L0,-55 L5,-60" stroke="#8b7fc9" stroke-width="1.5" fill="none"/>
  </g>

  <!-- AR/VR Headset (Apple Vision) -->
  <g transform="translate(380, 320)">
    <!-- Headset Shape -->
    <path d="M-40,-10 C-40,-30 40,-30 40,-10 L40,10 C40,30 -40,30 -40,10 Z" fill="#2f2b3a" stroke="#8b7fc9" stroke-width="2"/>
    <path d="M-30,-5 C-30,-20 30,-20 30,-5 L30,5 C30,20 -30,20 -30,5 Z" fill="#242030"/>

    <!-- Headset Display -->
    <text x="0" y="-10" text-anchor="middle" fill="#ffffff" font-family="Inter, sans-serif" font-size="8">Apple Vision</text>
    <rect x="-20" y="0" width="40" height="15" rx="2" fill="#1a1825"/>
    <text x="0" y="10" text-anchor="middle" fill="#7dcfff" font-family="monospace" font-size="6">Immersive IDE</text>

    <!-- Connection Line to Desktop -->
    <path d="M0,-20 C0,-50 -100,-120 -130,-60" stroke="#8b7fc9" stroke-width="1.5" fill="none" stroke-dasharray="4,4">
      <animate attributeName="stroke-dashoffset" values="0;8" dur="1s" repeatCount="indefinite" begin="0.5s"/>
    </path>

    <!-- Sync Icon -->
    <circle cx="0" cy="-30" r="10" fill="#8b7fc9" opacity="0.2"/>
    <path d="M-5,-30 L0,-35 L5,-30 M0,-35 L0,-25 M-5,-30 L0,-25 L5,-30" stroke="#8b7fc9" stroke-width="1.5" fill="none"/>
  </g>

  <!-- Other Device (Tablet/Laptop) -->
  <g transform="translate(250, 380)">
    <!-- Device Shape -->
    <rect x="-50" y="-30" width="100" height="60" rx="8" fill="#2f2b3a" stroke="#8b7fc9" stroke-width="2"/>
    <rect x="-45" y="-25" width="90" height="50" rx="4" fill="#242030"/>

    <!-- Device Content -->
    <text x="0" y="-10" text-anchor="middle" fill="#ffffff" font-family="Inter, sans-serif" font-size="8">Other Devices</text>
    <rect x="-35" y="0" width="70" height="15" rx="2" fill="#1a1825"/>
    <text x="0" y="10" text-anchor="middle" fill="#7dcfff" font-family="monospace" font-size="6">Seamless Transition</text>

    <!-- Connection Line to Desktop -->
    <path d="M0,-30 L0,-60" stroke="#8b7fc9" stroke-width="1.5" fill="none" stroke-dasharray="4,4">
      <animate attributeName="stroke-dashoffset" values="0;8" dur="1s" repeatCount="indefinite" begin="0.25s"/>
    </path>

    <!-- Sync Icon -->
    <circle cx="0" cy="-40" r="10" fill="#8b7fc9" opacity="0.2"/>
    <path d="M-5,-40 L0,-45 L5,-40 M0,-45 L0,-35 M-5,-40 L0,-35 L5,-40" stroke="#8b7fc9" stroke-width="1.5" fill="none"/>
  </g>

  <!-- Cloud Sync Indicator -->
  <g transform="translate(250, 100)">
    <!-- Cloud Shape -->
    <path d="M-40,0 C-40,-20 -20,-30 0,-30 C20,-30 40,-20 40,0 C50,0 60,10 60,20 C60,30 50,40 40,40 C40,50 30,60 20,60 C10,60 0,50 0,40 C-10,40 -20,30 -20,20 C-30,20 -40,10 -40,0 Z" fill="#2f2b3a" stroke="#8b7fc9" stroke-width="2"/>

    <!-- Cloud Text -->
    <text x="0" y="15" text-anchor="middle" fill="#ffffff" font-family="Inter, sans-serif" font-size="12">Multimodal Sync</text>

    <!-- Animated Sync Icon -->
    <g>
      <path d="M-15,15 L0,0 L15,15" stroke="#8b7fc9" stroke-width="1.5" fill="none">
        <animate attributeName="opacity" values="1;0;1" dur="2s" repeatCount="indefinite"/>
      </path>
      <path d="M-15,25 L0,40 L15,25" stroke="#8b7fc9" stroke-width="1.5" fill="none">
        <animate attributeName="opacity" values="0;1;0" dur="2s" repeatCount="indefinite"/>
      </path>
    </g>
  </g>

  <!-- Bottom Info Bar -->
  <rect x="40" y="440" width="420" height="25" rx="4" fill="#2f2b3a"/>
  <text x="250" y="457" text-anchor="middle" fill="#8b7fc9" font-family="Inter, sans-serif" font-size="12">Continue your desktop work on any device</text>

  <!-- Markers -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#8b7fc9"/>
    </marker>
  </defs>
</svg>