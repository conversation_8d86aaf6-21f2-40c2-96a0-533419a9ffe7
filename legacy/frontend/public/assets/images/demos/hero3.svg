<svg viewBox="0 0 500 500" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <rect width="500" height="500" fill="#1a1825"/>
  
  <!-- IDE Window -->
  <rect x="20" y="20" width="460" height="460" rx="12" fill="#242030" stroke="#8b7fc9" stroke-width="1"/>
  
  <!-- Title Bar -->
  <rect x="20" y="20" width="460" height="35" rx="12" fill="#2f2b3a"/>
  <circle cx="35" cy="37.5" r="6" fill="#ff5f57"/>
  <circle cx="55" cy="37.5" r="6" fill="#ffbd2e"/>
  <circle cx="75" cy="37.5" r="6" fill="#28c840"/>
  <text x="250" y="42" text-anchor="middle" fill="#8b7fc9" font-family="Inter, sans-serif" font-size="14">KAPI IDE - Backwards Build in Action</text>
  
  <!-- Split Screen Layout -->
  <g transform="translate(40, 70)">
    <!-- Left Panel - Slides/Design -->
    <rect x="0" y="0" width="200" height="380" rx="8" fill="#2f2b3a" stroke="#8b7fc9" stroke-width="1"/>
    <text x="10" y="25" fill="#ffffff" font-family="Inter, sans-serif" font-size="12">Architecture Slides</text>
    <rect x="10" y="40" width="180" height="100" rx="4" fill="#242030"/>
    <text x="100" y="70" text-anchor="middle" fill="#8b7fc9" font-family="Inter, sans-serif" font-size="10">System Architecture</text>
    <text x="100" y="90" text-anchor="middle" fill="#8b7fc9" font-family="Inter, sans-serif" font-size="10">User Flow Diagram</text>
    <text x="100" y="110" text-anchor="middle" fill="#8b7fc9" font-family="Inter, sans-serif" font-size="10">Component Structure</text>
    
    <!-- Voice Command Overlay -->
    <g transform="translate(50, 160)">
      <circle cx="50" cy="50" r="45" fill="none" stroke="#8b7fc9" stroke-width="2" stroke-dasharray="5,5">
        <animateTransform attributeName="transform" attributeType="XML" type="rotate" from="0 50 50" to="360 50 50" dur="8s" repeatCount="indefinite"/>
      </circle>
      <circle cx="50" cy="50" r="30" fill="#8b7fc9" opacity="0.2"/>
      <text x="50" y="55" text-anchor="middle" fill="#ffffff" font-family="Inter, sans-serif" font-size="14">🎤</text>
    </g>
    <rect x="10" y="250" width="180" height="40" rx="4" fill="#242030"/>
    <text x="20" y="275" fill="#8b7fc9" font-family="Inter, sans-serif" font-size="10">"Generate auth module from this flow"</text>
    
    <!-- Test Preview -->
    <rect x="10" y="300" width="180" height="70" rx="4" fill="#242030"/>
    <text x="20" y="320" fill="#9ece6a" font-family="monospace" font-size="9">test('authentication flow'..{</text>
    <text x="30" y="335" fill="#9ece6a" font-family="monospace" font-size="9">expect(login).toSucceed();</text>
    <text x="30" y="350" fill="#9ece6a" font-family="monospace" font-size="9">expect(user).toBeAuthentica..</text>
    <text x="20" y="365" fill="#9ece6a" font-family="monospace" font-size="9">});</text>
  </g>
  
  <!-- Right Panel - Code Generation -->
  <g transform="translate(260, 70)">
    <rect x="0" y="0" width="200" height="380" rx="8" fill="#2f2b3a" stroke="#8b7fc9" stroke-width="1"/>
    <text x="10" y="25" fill="#ffffff" font-family="Inter, sans-serif" font-size="12">Code Implementation</text>
    <rect x="10" y="40" width="180" height="330" rx="4" fill="#242030"/>
    
    <!-- Code Generation Animation -->
    <g id="codeGeneration">
      <text x="20" y="60" fill="#8b7fc9" font-family="monospace" font-size="9">// Generated from slides and </text>
      <text x="20" y="70" fill="#8b7fc9" font-family="monospace" font-size="9">//voice commands</text>
      <text x="20" y="90" fill="#ffffff" font-family="monospace" font-size="9">class AuthModule {</text>
      <text x="40" y="100" fill="#ff9e64" font-family="monospace" font-size="9">async login(credentials) {</text>
      <text x="60" y="120" fill="#7dcfff" font-family="monospace" font-size="9">return await auth.auth..;</text>
      <text x="40" y="140" fill="#ff9e64" font-family="monospace" font-size="9">}</text>
      <text x="40" y="160" fill="#ff9e64" font-family="monospace" font-size="9">async validateUser(token) {</text>
      <text x="60" y="180" fill="#7dcfff" font-family="monospace" font-size="9">return await token.ver..</text>
      <text x="40" y="200" fill="#ff9e64" font-family="monospace" font-size="9">}</text>
      <text x="20" y="220" fill="#ffffff" font-family="monospace" font-size="9">}</text>
      
      <!-- AI Generation Indicator -->
      <rect x="140" y="340" width="50" height="25" rx="4" fill="#8b7fc9" opacity="0.2"/>
      <text x="165" y="358" text-anchor="middle" fill="#8b7fc9" font-family="Inter, sans-serif" font-size="10">AI Gen.</text>
    </g>
  </g>
  
  <!-- Flow Arrow -->
  <path d="M220 250 L260 250" stroke="#8b7fc9" stroke-width="2" stroke-dasharray="5,5" marker-end="url(#arrowhead)">
    <animate attributeName="stroke-dashoffset" values="0;10" dur="1s" repeatCount="indefinite"/>
  </path>
  
  <!-- Markers -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#8b7fc9"/>
    </marker>
  </defs>
</svg>