<?xml version="1.0" encoding="UTF-8"?>
<svg width="800" height="600" viewBox="0 0 800 600" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <defs>
    <!-- Gradients -->
    <linearGradient id="screenGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#1e1b4b;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#312e81;stop-opacity:1" />
    </linearGradient>

    <linearGradient id="slideGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" style="stop-color:#312e81;stop-opacity:1" />
        <stop offset="100%" style="stop-color:#4338ca;stop-opacity:1" />
    </linearGradient>

    <!-- New drop shadow filter for the slide -->
    <filter id="dropShadow" x="-20%" y="-20%" width="140%" height="140%">
      <feGaussianBlur in="SourceAlpha" stdDeviation="5"/>
      <feOffset dx="4" dy="4" result="offsetblur"/>
      <feFlood flood-color="#000" flood-opacity="0.6"/>
      <feComposite in2="offsetblur" operator="in"/>
      <feMerge>
        <feMergeNode/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>

    <clipPath id="monitorClip">
        <rect x="110" y="90" width="580" height="370" rx="10"/>
    </clipPath>


    <!-- Glow effects -->
    <filter id="glow">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge>
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>

    <!-- Animations -->
    <style type="text/css">
      @keyframes pulse {
        0% { opacity: 0.6; }
        50% { opacity: 1; }
        100% { opacity: 0.6; }
      }

      .voice-wave {
        animation: pulse 1.5s infinite;
      }

      .typing-indicator {
        animation: pulse 2s infinite;
      }

      /* Style for code/test text */
      .monospace-text {
          font-family: "Courier New", "Lucida Console", monospace;
      }
    </style>
  </defs>

  <!-- Background -->
  <rect width="800" height="600" fill="#111827"/>

  <!-- Title -->
  <text x="400" y="60" text-anchor="middle" fill="#c4b5fd" font-family="Arial" font-size="28" font-weight="bold">KAPI IDE</text>

  <!-- Desktop frame -->
  <g id="desktop">
    <!-- Monitor -->
    <rect x="100" y="80" width="600" height="400" rx="20" fill="#1f2937" stroke="#374151" stroke-width="4"/>
    <rect x="110" y="90" width="580" height="370" rx="10" fill="url(#screenGradient)"/>

    <!-- Screen content -->
    <g id="screen-content" clip-path="url(#monitorClip)">

      <!-- Stage 1: Voice Objectives -->
      <g id="stage1" class="stage-content" opacity="1">
        <circle cx="400" cy="270" r="50" fill="#7c3aed" opacity="0.3" filter="url(#glow)"/>
        <path d="M 375 240 L 425 240 L 425 270 L 375 270 Z M 387 270 L 387 300 L 413 300 L 413 270 Z"
              fill="#7c3aed" stroke="#c4b5fd" stroke-width="2"/>

        <!-- Voice waves -->
        <path d="M 360 245 Q 350 270 360 295" stroke="#c4b5fd" stroke-width="2" fill="none" class="voice-wave" opacity="0.7"/>
        <path d="M 340 235 Q 320 270 340 305" stroke="#c4b5fd" stroke-width="2" fill="none" class="voice-wave" opacity="0.5"/>
        <path d="M 320 225 Q 290 270 320 315" stroke="#c4b5fd" stroke-width="2" fill="none" class="voice-wave" opacity="0.3"/>

        <path d="M 440 245 Q 450 270 440 295" stroke="#c4b5fd" stroke-width="2" fill="none" class="voice-wave" opacity="0.7"/>
        <path d="M 460 235 Q 480 270 460 305" stroke="#c4b5fd" stroke-width="2" fill="none" class="voice-wave" opacity="0.5"/>
        <path d="M 480 225 Q 510 270 480 315" stroke="#c4b5fd" stroke-width="2" fill="none" class="voice-wave" opacity="0.3"/>

        <text x="400" y="350" text-anchor="middle" fill="white" font-family="Arial" font-size="20">
          "I want to build an e-commerce platform"
        </text>
        <text x="400" y="380" text-anchor="middle" fill="#9ca3af" font-family="Arial" font-size="16">
          Voice Objectives Collection
        </text>
      </g>

      <!-- Stage 2: Slides Building -->
    <g id="stage2" class="stage-content" opacity="0">
      <!-- Main slide container filling monitor area, with rounded corners and shadow -->
      <rect x="110" y="90" width="580" height="370" rx="10" fill="url(#slideGradient)" filter="url(#dropShadow)"/>

      <!-- Inner content area -->
      <g transform="translate(130, 110)">
        <!-- Header -->
        <rect x="0" y="0" width="540" height="60" rx="8" fill="#4c1d95" stroke="#8b5cf6" stroke-width="2"/>
        <text x="270" y="35" text-anchor="middle" fill="white" font-family="Arial" font-size="22" font-weight="bold">
          Project Overview - E-commerce Platform
        </text>

        <!-- Decorative separator line -->
        <line x1="20" y1="70" x2="520" y2="70" stroke="#8b5cf6" stroke-width="2" stroke-dasharray="4,4"/>

        <!-- Slide content sections -->
        <!-- Section 1 -->
        <rect x="0" y="90" width="540" height="85" rx="8" fill="#1e1b4b" fill-opacity="0.7" stroke="#4c1d95" stroke-width="1"/>
        <circle cx="20" cy="110" r="10" fill="#8b5cf6"/>
        <text x="20" y="113" text-anchor="middle" fill="white" font-family="Arial" font-size="16" font-weight="bold">1</text>
        <text x="40" y="115" fill="white" font-family="Arial" font-size="18" font-weight="bold">User Experience</text>
        <text x="60" y="145" fill="#c4b5fd" font-family="Arial" font-size="15">• User Authentication</text>
        <text x="60" y="165" fill="#c4b5fd" font-family="Arial" font-size="15">• Product Catalog</text>

        <!-- Section 2 -->
        <rect x="0" y="185" width="540" height="85" rx="8" fill="#1e1b4b" fill-opacity="0.7" stroke="#4c1d95" stroke-width="1"/>
        <circle cx="20" cy="205" r="10" fill="#8b5cf6"/>
        <text x="20" y="208" text-anchor="middle" fill="white" font-family="Arial" font-size="16" font-weight="bold">2</text>
        <text x="40" y="210" fill="white" font-family="Arial" font-size="18" font-weight="bold">Core Functionality</text>
        <text x="60" y="240" fill="#c4b5fd" font-family="Arial" font-size="15">• Shopping Cart</text>
        <text x="60" y="260" fill="#c4b5fd" font-family="Arial" font-size="15">• Payment Integration</text>

        <!-- Placeholder for Section 3 -->
        <rect x="0" y="280" width="540" height="55" rx="8" fill="#1e1b4b" fill-opacity="0.7" stroke="#4c1d95" stroke-width="1"/>
        <circle cx="20" cy="300" r="10" fill="#8b5cf6"/>
        <text x="20" y="303" text-anchor="middle" fill="white" font-family="Arial" font-size="16" font-weight="bold">3</text>
        <text x="40" y="305" fill="white" font-family="Arial" font-size="18" font-weight="bold">Future Features (Placeholder)</text>

        <!-- Slide navigation dots -->
        <circle cx="250" cy="345" r="5" fill="#8b5cf6"/> <!-- Highlight current slide -->
        <circle cx="270" cy="345" r="4" fill="#9ca3af"/>
        <circle cx="290" cy="345" r="4" fill="#9ca3af"/>

      </g>
    </g>

      <!-- Stage 3: Mockup Creation -->
      <g id="stage3" class="stage-content" opacity="0">
        <!-- Canvas background -->
        <rect x="180" y="150" width="440" height="300" rx="10" fill="#1e1b4b"/>

        <!-- UI elements being drawn -->
        <rect x="200" y="170" width="400" height="50" rx="5" fill="#4c1d95" stroke="#7c3aed" stroke-width="2"/>
        <text x="400" y="200" text-anchor="middle" fill="#c4b5fd" font-family="Arial" font-size="14">Navigation Bar</text>

        <rect x="200" y="240" width="180" height="180" rx="5" fill="#4c1d95" stroke="#7c3aed" stroke-width="2"/>
        <text x="290" y="330" text-anchor="middle" fill="#c4b5fd" font-family="Arial" font-size="14">Product Grid</text>

        <rect x="420" y="240" width="180" height="180" rx="5" fill="#4c1d95" stroke="#7c3aed" stroke-width="2"/>
        <text x="510" y="330" text-anchor="middle" fill="#c4b5fd" font-family="Arial" font-size="14">Shopping Cart</text>

        <!-- Drawing cursor -->
        <circle cx="510" cy="330" r="4" fill="#7c3aed" filter="url(#glow)"/>
      </g>

      <!-- Stage 4: Tests -->
      <g id="stage4" class="stage-content" opacity="0">
        <rect x="200" y="150" width="400" height="300" rx="10" fill="#1e1b4b"/>

        <!-- Test cases - applied monospace font -->
        <text x="220" y="180" fill="#c4b5fd" font-size="14" class="monospace-text">describe('E-commerce Platform', () => {</text>
        <text x="240" y="210" fill="#a5b4fc" font-size="14" class="monospace-text">it('should load products', () => {</text>
        <text x="260" y="240" fill="#c4b5fd" font-size="14" class="monospace-text">expect(products).toHaveLength(10);</text>
        <text x="240" y="270" fill="#a5b4fc" font-size="14" class="monospace-text">});</text>

        <text x="240" y="300" fill="#a5b4fc" font-size="14" class="monospace-text">it('should add to cart', () => {</text>
        <text x="260" y="330" fill="#c4b5fd" font-size="14" class="monospace-text">expect(cart.items).toContain(product);</text>
        <text x="240" y="360" fill="#a5b4fc" font-size="14" class="monospace-text">});</text>

        <text x="220" y="390" fill="#c4b5fd" font-size="14" class="monospace-text">});</text>

        <!-- Test results -->
        <rect x="220" y="410" width="360" height="30" rx="5" fill="#047857"/>
        <text x="240" y="430" fill="#ecfdf5" font-size="14" class="monospace-text">✓ All tests passing</text>
      </g>

      <!-- Stage 5: Code Generation -->
      <g id="stage5" class="stage-content" opacity="0">
        <rect x="200" y="150" width="400" height="300" rx="10" fill="#1e1b4b"/>

        <!-- Code editor - applied monospace font -->
        <text x="220" y="180" fill="#c4b5fd" font-size="14" class="monospace-text">// ProductList.tsx</text>
        <text x="220" y="210" fill="#a5b4fc" font-size="14" class="monospace-text">function ProductList() {</text>
        <text x="240" y="240" fill="#c4b5fd" font-size="14" class="monospace-text">const [products, setProducts] = </text>
        <text x="260" y="270" fill="#c4b5fd" font-size="14" class="monospace-text">useState&lt;Product[]&gt;([]);</text>

        <text x="240" y="300" fill="#a5b4fc" font-size="14" class="monospace-text">useEffect(() => {</text>
        <text x="260" y="330" fill="#c4b5fd" font-size="14" class="monospace-text">fetchProducts().then(setProducts);</text>
        <text x="240" y="360" fill="#a5b4fc" font-size="14" class="monospace-text">}, []);</text>

        <text x="240" y="390" fill="#a5b4fc" font-size="14" class="monospace-text">return &lt;ProductGrid items={products} /&gt;;</text>
        <text x="220" y="420" fill="#a5b4fc" font-size="14" class="monospace-text">}</text>

        <!-- Typing indicator -->
        <rect x="240" y="425" width="10" height="20" fill="#7c3aed" class="typing-indicator"/>
      </g>
    </g>

    <!-- Monitor stand -->
    <rect x="390" y="480" width="20" height="40" fill="#1f2937"/>
    <rect x="350" y="520" width="100" height="10" rx="5" fill="#1f2937"/>
  </g>

  <!-- Progress indicator -->
  <g id="progress">
    <rect x="100" y="540" width="600" height="4" rx="2" fill="#374151"/>
    <rect x="100" y="540" width="0" height="4" rx="2" fill="#7c3aed">
      <!-- Synchronized progress bar animation -->
      <animate attributeName="width" values="0;120;240;360;480;600" keyTimes="0;0.2;0.4;0.6;0.8;1" dur="10s" repeatCount="indefinite"/>
    </rect>
  </g>

  <!-- Stage labels -->
  <g id="stage-labels">
    <text x="160" y="565" text-anchor="middle" fill="#9ca3af" font-family="Arial" font-size="14">Voice</text>
    <text x="280" y="565" text-anchor="middle" fill="#9ca3af" font-family="Arial" font-size="14">Slides</text>
    <text x="400" y="565" text-anchor="middle" fill="#9ca3af" font-family="Arial" font-size="14">Mockup</text>
    <text x="520" y="565" text-anchor="middle" fill="#9ca3af" font-family="Arial" font-size="14">Tests</text>
    <text x="640" y="565" text-anchor="middle" fill="#9ca3af" font-family="Arial" font-size="14">Code</text>
  </g>

  <!-- Animations for stage transitions - Fixed sequencing -->
  <animate xlink:href="#stage1" attributeName="opacity" values="1;1;0;0;0;0;0" keyTimes="0;0.2;0.2;0.4;0.6;0.8;1" dur="10s" repeatCount="indefinite"/>
  <animate xlink:href="#stage2" attributeName="opacity" values="0;0;1;1;0;0;0" keyTimes="0;0.2;0.2;0.4;0.4;0.6;1" dur="10s" repeatCount="indefinite"/>
  <animate xlink:href="#stage3" attributeName="opacity" values="0;0;0;0;1;1;0" keyTimes="0;0.2;0.4;0.4;0.4;0.6;1" dur="10s" repeatCount="indefinite"/>
  <animate xlink:href="#stage4" attributeName="opacity" values="0;0;0;0;0;0;1;1;0" keyTimes="0;0.2;0.4;0.6;0.6;0.7;0.7;0.9;1" dur="10s" repeatCount="indefinite"/>
  <animate xlink:href="#stage5" attributeName="opacity" values="0;0;0;0;0;0;0;0;1;1" keyTimes="0;0.2;0.4;0.6;0.7;0.8;0.9;0.9;0.9;1" dur="10s" repeatCount="indefinite"/>

</svg>