<svg viewBox="0 0 1200 800" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <rect width="1200" height="800" fill="#1a1825"/>
  
  <!-- Main Dashboard -->
  <rect x="20" y="20" width="1160" height="760" rx="16" fill="#242030" stroke="#8b7fc9" stroke-width="2"/>
  
  <!-- Header -->
  <g transform="translate(40, 40)">
    <text x="0" y="35" fill="#ffffff" font-family="Inter, sans-serif" font-size="28" font-weight="bold">KAPI Team Code Review Dashboard</text>
    <text x="0" y="70" fill="#8b7fc9" font-family="Inter, sans-serif" font-size="16">Multi-User AI Development Workflow</text>
  </g>
  
  <!-- Team Activity Feed -->
  <g transform="translate(40, 120)">
    <rect x="0" y="0" width="380" height="600" rx="12" fill="#2f2b3a"/>
    <text x="20" y="35" fill="#ffffff" font-family="Inter, sans-serif" font-size="18" font-weight="bold">Team Activity Feed</text>
    
    <!-- Activity Items -->
    <!-- Sarah's Push -->
    <g transform="translate(20, 60)">
      <circle cx="20" cy="20" r="18" fill="#8b7fc9" fill-opacity="0.2"/>
      <image href="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%238b7fc9'%3E%3Cpath d='M12 2C6.47 2 2 6.47 2 12s4.47 10 10 10 10-4.47 10-10S17.53 2 12 2zm-1 13h2v2h-2v-2zm0-2h2V7h-2v6z'/%3E%3C/svg%3E" x="7" y="7" width="26" height="26"/>
      <text x="50" y="20" fill="#8b7fc9" font-family="Inter, sans-serif" font-size="12">Sarah Chen</text>
      <text x="50" y="36" fill="#ffffff" font-family="Inter, sans-serif" font-size="14">Pushed to main</text>
      <text x="50" y="52" fill="#8b7fc9" font-family="Inter, sans-serif" font-size="11">2 minutes ago</text>
    </g>
    
    <!-- AI Review -->
    <g transform="translate(20, 130)">
      <rect x="0" y="0" width="340" height="85" rx="8" fill="#6b21a8" fill-opacity="0.1"/>
      <circle cx="20" cy="20" r="18" fill="#6b21a8" fill-opacity="0.2"/>
      <text x="50" y="20" fill="#6b21a8" font-family="Inter, sans-serif" font-size="12">AI Review Bot</text>
      <text x="50" y="36" fill="#ffffff" font-family="Inter, sans-serif" font-size="14">Completed code review</text>
      <text x="50" y="52" fill="#8b7fc9" font-family="Inter, sans-serif" font-size="11">4 minutes ago</text>
      <rect x="50" y="60" width="140" height="20" rx="4" fill="#28c840" fill-opacity="0.2"/>
      <text x="56" y="75" fill="#28c840" font-family="Inter, sans-serif" font-size="11">Quality Score: 94/100</text>
    </g>
    
    <!-- Michael's Collaboration -->
    <g transform="translate(20, 230)">
      <circle cx="20" cy="20" r="18" fill="#7dcfff" fill-opacity="0.2"/>
      <image href="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%237dcfff'%3E%3Cpath d='M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z'/%3E%3C/svg%3E" x="7" y="7" width="26" height="26"/>
      <text x="50" y="20" fill="#7dcfff" font-family="Inter, sans-serif" font-size="12">Michael Torres</text>
      <text x="50" y="36" fill="#ffffff" font-family="Inter, sans-serif" font-size="14">Started code review</text>
      <text x="50" y="52" fill="#8b7fc9" font-family="Inter, sans-serif" font-size="11">6 minutes ago</text>
    </g>
    
    <!-- Emma's Question -->
    <g transform="translate(20, 300)">
      <rect x="0" y="0" width="340" height="90" rx="8" fill="#ff9e64" fill-opacity="0.1"/>
      <circle cx="20" cy="20" r="18" fill="#ff9e64" fill-opacity="0.2"/>
      <text x="50" y="20" fill="#ff9e64" font-family="Inter, sans-serif" font-size="12">Emma Wilson</text>
      <text x="50" y="36" fill="#ffffff" font-family="Inter, sans-serif" font-size="14">Asked for help: "Need review on API"</text>
      <text x="50" y="52" fill="#8b7fc9" font-family="Inter, sans-serif" font-size="11">8 minutes ago</text>
      <rect x="50" y="60" width="60" height="20" rx="4" fill="#6b21a8" fill-opacity="0.2"/>
      <text x="56" y="75" fill="#6b21a8" font-family="Inter, sans-serif" font-size="11">Karma: +2</text>
    </g>
    
    <!-- Human Expert Response -->
    <g transform="translate(20, 405)">
      <circle cx="20" cy="20" r="18" fill="#28c840" fill-opacity="0.2"/>
      <image href="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%2328c840'%3E%3Cpath d='M16 11c1.66 0 2.99-1.34 2.99-3S17.66 5 16 5s-3 1.34-3 3 1.34 3 3 3zm-8 0c1.66 0 2.99-1.34 2.99-3S9.66 5 8 5 5 6.34 5 8s1.34 3 3 3zm0 2c-2.33 0-7 1.17-7 3.5V19h14v-2.5c0-2.33-4.67-3.5-7-3.5zm8 0c-.29 0-.62.02-.97.05 1.16.84 1.97 1.97 1.97 3.45V19h6v-2.5c0-2.33-4.67-3.5-7-3.5z'/%3E%3C/svg%3E" x="7" y="7" width="26" height="26"/>
      <text x="50" y="20" fill="#28c840" font-family="Inter, sans-serif" font-size="12">Expert: Alex Kumar</text>
      <text x="50" y="36" fill="#ffffff" font-family="Inter, sans-serif" font-size="14">Provided API guidance</text>
      <text x="50" y="52" fill="#8b7fc9" font-family="Inter, sans-serif" font-size="11">3 minutes ago</text>
    </g>
    
    <!-- Live Pair Programming -->
    <g transform="translate(20, 480)">
      <rect x="0" y="0" width="340" height="85" rx="8" fill="#7dcfff" fill-opacity="0.1"/>
      <path d="M40,20 C35,20 30,25 30,30 C30,35 35,40 40,40" stroke="#7dcfff" stroke-width="2" fill="none"/>
      <path d="M200,20 C205,20 210,25 210,30 C210,35 205,40 200,40" stroke="#7dcfff" stroke-width="2" fill="none"/>
      <line x1="50" y1="30" x2="190" y2="30" stroke="#7dcfff" stroke-width="2"/>
      <text x="50" y="20" fill="#7dcfff" font-family="Inter, sans-serif" font-size="12">Live Pair Programming</text>
      <text x="50" y="36" fill="#ffffff" font-family="Inter, sans-serif" font-size="14">Sarah & Michael are coding together</text>
      <text x="50" y="52" fill="#8b7fc9" font-family="Inter, sans-serif" font-size="11">Active for 12 minutes</text>
      <circle cx="320" cy="50" r="6" fill="#28c840">
        <animate attributeName="opacity" values="1;0.5;1" dur="1.5s" repeatCount="indefinite"/>
      </circle>
    </g>
  </g>
  
  <!-- Code Review Panel -->
  <g transform="translate(440, 120)">
    <rect x="0" y="0" width="720" height="600" rx="12" fill="#2f2b3a"/>
    <text x="20" y="35" fill="#ffffff" font-family="Inter, sans-serif" font-size="18" font-weight="bold">Multi-User Code Review: feature/new-authentication</text>
    
    <!-- User Avatars in Review -->
    <g transform="translate(580, 10)">
      <image href="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'%3E%3Ccircle cx='50' cy='50' r='40' fill='%238b7fc9'/%3E%3Ctext x='50' y='60' text-anchor='middle' fill='white' font-size='30' font-family='Arial'%3ES%3C/text%3E%3C/svg%3E" x="0" y="0" width="40" height="40" clip-path="circle(50% at 50% 50%)"/>
      <image href="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'%3E%3Ccircle cx='50' cy='50' r='40' fill='%237dcfff'/%3E%3Ctext x='50' y='60' text-anchor='middle' fill='white' font-size='30' font-family='Arial'%3EM%3C/text%3E%3C/svg%3E" x="45" y="0" width="40" height="40" clip-path="circle(50% at 50% 50%)"/>
      <image href="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'%3E%3Ccircle cx='50' cy='50' r='40' fill='%2328c840'/%3E%3Ctext x='50' y='60' text-anchor='middle' fill='white' font-size='30' font-family='Arial'%3EA%3C/text%3E%3C/svg%3E" x="90" y="0" width="40" height="40" clip-path="circle(50% at 50% 50%)"/>
    </g>
    
    <!-- Code Diff View -->
    <g transform="translate(20, 60)">
      <rect x="0" y="0" width="680" height="380" rx="8" fill="#242030"/>
      <text x="10" y="25" fill="#8b7fc9" font-family="Inter, sans-serif" font-size="14">src/auth/authentication.ts</text>
      
      <!-- Diff Content -->
      <g transform="translate(10, 40)">
        <!-- Added lines -->
        <rect x="0" y="0" width="660" height="25" fill="#28c840" fill-opacity="0.1"/>
        <text x="10" y="18" fill="#28c840" font-family="monospace" font-size="12">+ async validateCredentials(email: string, password: string) {</text>
        
        <rect x="0" y="30" width="660" height="25" fill="#28c840" fill-opacity="0.1"/>
        <text x="10" y="48" fill="#28c840" font-family="monospace" font-size="12">+   const user = await this.userRepository.findByEmail(email);</text>
        
        <rect x="0" y="60" width="660" height="25" fill="#28c840" fill-opacity="0.1"/>
        <text x="10" y="78" fill="#28c840" font-family="monospace" font-size="12">+   return bcrypt.compare(password, user.hashedPassword);</text>
        
        <!-- Removed lines -->
        <rect x="0" y="90" width="660" height="25" fill="#ff5f57" fill-opacity="0.1"/>
        <text x="10" y="108" fill="#ff5f57" font-family="monospace" font-size="12">- if (password === user.password) return true;</text>
        
        <!-- Comment indicators -->
        <circle cx="640" cy="12" r="10" fill="#6b21a8"/>
        <text x="640" y="17" text-anchor="middle" fill="#ffffff" font-family="Inter, sans-serif" font-size="11">2</text>
        
        <circle cx="640" cy="102" r="10" fill="#ff9e64"/>
        <text x="640" y="107" text-anchor="middle" fill="#ffffff" font-family="Inter, sans-serif" font-size="11">1</text>
      </g>
    </g>
    
    <!-- Comments Section -->
    <g transform="translate(20, 460)">
      <text x="0" y="25" fill="#ffffff" font-family="Inter, sans-serif" font-size="16" font-weight="bold">Live Comments</text>
      
      <!-- Michael's Comment -->
      <g transform="translate(0, 40)">
        <rect x="0" y="0" width="680" height="60" rx="8" fill="#242030"/>
        <circle cx="20" cy="20" r="16" fill="#7dcfff" fill-opacity="0.2"/>
        <text x="45" y="20" fill="#7dcfff" font-family="Inter, sans-serif" font-size="12">Michael Torres</text>
        <text x="45" y="40" fill="#ffffff" font-family="Inter, sans-serif" font-size="14">Great improvement using bcrypt! Consider adding rate limiting.</text>
        <text x="650" y="20" fill="#8b7fc9" font-family="Inter, sans-serif" font-size="11">2 min ago</text>
      </g>
      
      <!-- AI Suggestion -->
      <g transform="translate(0, 110)">
        <rect x="0" y="0" width="680" height="60" rx="8" fill="#6b21a8" fill-opacity="0.1"/>
        <circle cx="20" cy="20" r="16" fill="#6b21a8" fill-opacity="0.2"/>
        <text x="45" y="20" fill="#6b21a8" font-family="Inter, sans-serif" font-size="12">AI Review Bot</text>
        <text x="45" y="40" fill="#ffffff" font-family="Inter, sans-serif" font-size="14">Suggest adding input validation and try-catch for error handling.</text>
        <rect x="620" y="5" width="50" height="20" rx="4" fill="#6b21a8" fill-opacity="0.2"/>
        <text x="625" y="20" fill="#6b21a8" font-family="Inter, sans-serif" font-size="11">Apply</text>
      </g>
    </g>
  </g>
  
  <!-- Bottom Stats Panel -->
  <g transform="translate(40, 740)">
    <rect x="0" y="0" width="1120" height="60" rx="8" fill="#2f2b3a"/>
    
    <!-- Active Users -->
    <g transform="translate(20, 20)">
      <circle cx="10" cy="10" r="8" fill="#28c840">
        <animate attributeName="opacity" values="1;0.5;1" dur="1s" repeatCount="indefinite"/>
      </circle>
      <text x="25" y="16" fill="#ffffff" font-family="Inter, sans-serif" font-size="14">4 Active Users</text>
    </g>
    
    <!-- Pending Reviews -->
    <g transform="translate(200, 20)">
      <circle cx="10" cy="10" r="8" fill="#ff9e64"/>
      <text x="25" y="16" fill="#ffffff" font-family="Inter, sans-serif" font-size="14">3 Pending Reviews</text>
    </g>
    
    <!-- Quality Score -->
    <g transform="translate(400, 20)">
      <circle cx="10" cy="10" r="8" fill="#6b21a8"/>
      <text x="25" y="16" fill="#ffffff" font-family="Inter, sans-serif" font-size="14">Team Quality: 89/100</text>
    </g>
    
    <!-- AI Assists -->
    <g transform="translate(600, 20)">
      <circle cx="10" cy="10" r="8" fill="#7dcfff"/>
      <text x="25" y="16" fill="#ffffff" font-family="Inter, sans-serif" font-size="14">12 AI Assists Today</text>
    </g>
    
    <!-- Karma Stats -->
    <g transform="translate(820, 20)">
      <circle cx="10" cy="10" r="8" fill="#8b7fc9"/>
      <text x="25" y="16" fill="#ffffff" font-family="Inter, sans-serif" font-size="14">Karma Exchange: +24 Today</text>
    </g>
  </g>
  
  <!-- Real-time indicators -->
  <g transform="translate(1120, 740)">
    <circle cx="20" cy="30" r="10" fill="none" stroke="#28c840" stroke-width="2">
      <animate attributeName="r" values="10;15;10" dur="2s" repeatCount="indefinite"/>
    </circle>
    <text x="40" y="34" fill="#28c840" font-family="Inter, sans-serif" font-size="11">Live Updates</text>
  </g>
</svg>