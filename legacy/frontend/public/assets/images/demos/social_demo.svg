<svg viewBox="0 0 800 600" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <rect width="800" height="600" fill="#1a1825"/>
  
  <!-- Main Container -->
  <rect x="20" y="20" width="760" height="560" rx="12" fill="#242030" stroke="#8b7fc9" stroke-width="1"/>
  
  <!-- Title -->
  <text x="400" y="50" text-anchor="middle" fill="#ffffff" font-family="Inter, sans-serif" font-size="20" font-weight="bold">Community Hub</text>
  <text x="400" y="75" text-anchor="middle" fill="#8b7fc9" font-family="Inter, sans-serif" font-size="14">Developers helping developers</text>
  
  <!-- Left side - Developer profiles -->
  <g transform="translate(40, 100)">
    <!-- Developer 1 -->
    <g transform="translate(0, 0)">
      <rect x="0" y="0" width="320" height="120" rx="8" fill="#2f2b3a">
        <animate attributeName="opacity" values="0;1" dur="0.5s" fill="freeze"/>
      </rect>
      <circle cx="60" cy="60" r="30" fill="#8b7fc9"/>
      <text x="60" y="63" text-anchor="middle" fill="#ffffff" font-family="Inter, sans-serif" font-size="14">MS</text>
      <text x="100" y="45" fill="#ffffff" font-family="Inter, sans-serif" font-size="14">Maria Santos</text>
      <text x="100" y="65" fill="#8b7fc9" font-family="Inter, sans-serif" font-size="12">Senior Developer · TS/React</text>
      <text x="100" y="85" fill="#8b7fc9" font-family="Inter, sans-serif" font-size="10">🌟 4.8 (172 reviews) · 🏆 16 badges</text>
      <rect x="240" y="40" width="70" height="30" rx="4" fill="#8b7fc9"/>
      <text x="275" y="60" text-anchor="middle" fill="#ffffff" font-family="Inter, sans-serif" font-size="12">Mentor</text>
    </g>
    
    <!-- Developer 2 -->
    <g transform="translate(0, 140)">
      <rect x="0" y="0" width="320" height="120" rx="8" fill="#2f2b3a">
        <animate attributeName="opacity" values="0;1" dur="0.5s" begin="0.2s" fill="freeze"/>
      </rect>
      <circle cx="60" cy="60" r="30" fill="#7dcfff"/>
      <text x="60" y="63" text-anchor="middle" fill="#ffffff" font-family="Inter, sans-serif" font-size="14">AL</text>
      <text x="100" y="45" fill="#ffffff" font-family="Inter, sans-serif" font-size="14">Alex Lee</text>
      <text x="100" y="65" fill="#8b7fc9" font-family="Inter, sans-serif" font-size="12">Lead Engineer · Go/Python</text>
      <text x="100" y="85" fill="#8b7fc9" font-family="Inter, sans-serif" font-size="10">🌟 4.9 (203 reviews) · 🔥 Top Contributor</text>
      <rect x="240" y="40" width="70" height="30" rx="4" fill="#8b7fc9"/>
      <text x="275" y="60" text-anchor="middle" fill="#ffffff" font-family="Inter, sans-serif" font-size="12">Available</text>
    </g>
    
    <!-- Developer 3 -->
    <g transform="translate(0, 280)">
      <rect x="0" y="0" width="320" height="120" rx="8" fill="#2f2b3a">
        <animate attributeName="opacity" values="0;1" dur="0.5s" begin="0.4s" fill="freeze"/>
      </rect>
      <circle cx="60" cy="60" r="30" fill="#ff9e64"/>
      <text x="60" y="63" text-anchor="middle" fill="#ffffff" font-family="Inter, sans-serif" font-size="14">RK</text>
      <text x="100" y="45" fill="#ffffff" font-family="Inter, sans-serif" font-size="14">Raj Kumar</text>
      <text x="100" y="65" fill="#8b7fc9" font-family="Inter, sans-serif" font-size="12">Full Stack Developer · JS/Node</text>
      <text x="100" y="85" fill="#8b7fc9" font-family="Inter, sans-serif" font-size="10">🌟 4.6 (98 reviews) · 🚀 Rising Star</text>
      <rect x="240" y="40" width="70" height="30" rx="4" fill="#28c840"/>
      <text x="275" y="60" text-anchor="middle" fill="#ffffff" font-family="Inter, sans-serif" font-size="12">Active</text>
    </g>
  </g>
  
  <!-- Right side - Chat/Help System -->
  <g transform="translate(400, 100)">
    <!-- Chat Window -->
    <rect x="0" y="0" width="360" height="400" rx="8" fill="#2f2b3a">
      <animate attributeName="opacity" values="0;1" dur="0.5s" begin="0.6s" fill="freeze"/>
    </rect>
    <rect x="0" y="0" width="360" height="40" rx="8" fill="#8b7fc9"/>
    <text x="20" y="25" fill="#ffffff" font-family="Inter, sans-serif" font-size="14">Help Request #5821</text>
    <circle cx="320" cy="20" r="5" fill="#28c840"/>
    <text x="340" y="25" fill="#ffffff" font-family="Inter, sans-serif" font-size="10">3 devs responding</text>
    
    <!-- Messages -->
    <!-- Original question -->
    <g transform="translate(20, 60)">
      <rect x="0" y="0" width="280" height="50" rx="8" fill="#242030">
        <animate attributeName="height" values="0;50" dur="0.5s" begin="1s" fill="freeze"/>
      </rect>
      <circle cx="-20" cy="25" r="15" fill="#9ece6a"/>
      <text x="-20" y="30" text-anchor="middle" fill="#ffffff" font-family="Inter, sans-serif" font-size="10">JS</text>
      <text x="10" y="20" fill="#ffffff" font-family="Inter, sans-serif" font-size="12" opacity="0">
        <animate attributeName="opacity" values="0;1" dur="0.5s" begin="1.5s" fill="freeze"/>
        Help with React performance issue?
      </text>
      <text x="10" y="35" fill="#8b7fc9" font-family="Inter, sans-serif" font-size="10" opacity="0">
        <animate attributeName="opacity" values="0;1" dur="0.5s" begin="1.5s" fill="freeze"/>
        My list renders are getting slower with each update
      </text>
    </g>
    
    <!-- First response -->
    <g transform="translate(120, 120)">
      <rect x="0" y="0" width="220" height="60" rx="8" fill="#8b7fc9" fill-opacity="0.2">
        <animate attributeName="height" values="0;60" dur="0.5s" begin="2s" fill="freeze"/>
      </rect>
      <circle cx="-20" cy="30" r="15" fill="#8b7fc9"/>
      <text x="-20" y="34" text-anchor="middle" fill="#ffffff" font-family="Inter, sans-serif" font-size="10">MS</text>
      <text x="10" y="20" fill="#ffffff" font-family="Inter, sans-serif" font-size="12" opacity="0">
        <animate attributeName="opacity" values="0;1" dur="0.5s" begin="2.5s" fill="freeze"/>
        Check if you're using React.memo() for your list items?
      </text>
      <text x="10" y="35" fill="#8b7fc9" font-family="Inter, sans-serif" font-size="10" opacity="0">
        <animate attributeName="opacity" values="0;1" dur="0.5s" begin="2.5s" fill="freeze"/>
        Also make sure your key prop is stable
      </text>
      <text x="10" y="50" fill="#28c840" font-family="Inter, sans-serif" font-size="9" opacity="0">
        <animate attributeName="opacity" values="0;1" dur="0.5s" begin="2.5s" fill="freeze"/>
        +50 karma points received
      </text>
    </g>
    
    <!-- Second response with code -->
    <g transform="translate(20, 190)">
      <rect x="0" y="0" width="320" height="100" rx="8" fill="#242030">
        <animate attributeName="height" values="0;100" dur="0.5s" begin="3s" fill="freeze"/>
      </rect>
      <circle cx="-20" cy="50" r="15" fill="#7dcfff"/>
      <text x="-20" y="54" text-anchor="middle" fill="#ffffff" font-family="Inter, sans-serif" font-size="10">AL</text>
      <text x="10" y="20" fill="#ffffff" font-family="Inter, sans-serif" font-size="12" opacity="0">
        <animate attributeName="opacity" values="0;1" dur="0.5s" begin="3.5s" fill="freeze"/>
        Here's an optimized version:
      </text>
      <rect x="10" y="30" width="300" height="60" rx="4" fill="#1a1825" opacity="0">
        <animate attributeName="opacity" values="0;1" dur="0.5s" begin="4s" fill="freeze"/>
      </rect>
      <text x="15" y="50" fill="#ff9e64" font-family="monospace" font-size="10" opacity="0">
        <animate attributeName="opacity" values="0;1" dur="0.5s" begin="4s" fill="freeze"/>
        const ListItem = React.memo(({ item }) => {
      </text>
      <text x="25" y="65" fill="#7dcfff" font-family="monospace" font-size="10" opacity="0">
        <animate attributeName="opacity" values="0;1" dur="0.5s" begin="4s" fill="freeze"/>
        return &lt;div&gt;{item.name}&lt;/div&gt;;
      </text>
      <text x="15" y="80" fill="#ff9e64" font-family="monospace" font-size="10" opacity="0">
        <animate attributeName="opacity" values="0;1" dur="0.5s" begin="4s" fill="freeze"/>
        });
      </text>
    </g>
    
    <!-- Third response - Solution confirmed -->
    <g transform="translate(120, 300)">
      <rect x="0" y="0" width="220" height="50" rx="8" fill="#28c840" fill-opacity="0.2">
        <animate attributeName="height" values="0;50" dur="0.5s" begin="4.5s" fill="freeze"/>
      </rect>
      <circle cx="-20" cy="25" r="15" fill="#9ece6a"/>
      <text x="-20" y="30" text-anchor="middle" fill="#ffffff" font-family="Inter, sans-serif" font-size="10">JS</text>
      <text x="10" y="20" fill="#ffffff" font-family="Inter, sans-serif" font-size="12" opacity="0">
        <animate attributeName="opacity" values="0;1" dur="0.5s" begin="5s" fill="freeze"/>
        That worked perfectly! Thanks!
      </text>
      <text x="10" y="35" fill="#8b7fc9" font-family="Inter, sans-serif" font-size="10" opacity="0">
        <animate attributeName="opacity" values="0;1" dur="0.5s" begin="5s" fill="freeze"/>
        Rendering time reduced by 80%
      </text>
    </g>
  </g>
  
  <!-- Bottom section - Community Stats -->
  <g transform="translate(40, 520)">
    <rect x="0" y="0" width="720" height="50" rx="8" fill="#2f2b3a"/>
    
    <!-- Stats -->
    <g transform="translate(40, 15)">
      <circle cx="0" cy="10" r="8" fill="#8b7fc9"/>
      <text x="20" y="15" fill="#ffffff" font-family="Inter, sans-serif" font-size="12">1,247 active developers</text>
    </g>
    
    <g transform="translate(240, 15)">
      <circle cx="0" cy="10" r="8" fill="#28c840"/>
      <text x="20" y="15" fill="#ffffff" font-family="Inter, sans-serif" font-size="12">3,892 problems solved today</text>
    </g>
    
    <g transform="translate(480, 15)">
      <circle cx="0" cy="10" r="8" fill="#7dcfff"/>
      <text x="20" y="15" fill="#ffffff" font-family="Inter, sans-serif" font-size="12">10m+ karma points earned</text>
    </g>
  </g>
  
  <!-- Floating notification -->
  <g transform="translate(600, 120)">
    <rect x="0" y="0" width="140" height="40" rx="8" fill="#8b7fc9" fill-opacity="0.9" opacity="0">
      <animate attributeName="opacity" values="0;1;1;0" dur="3s" begin="2s" fill="freeze"/>
    </rect>
    <text x="70" y="25" text-anchor="middle" fill="#ffffff" font-family="Inter, sans-serif" font-size="12" opacity="0">
      <animate attributeName="opacity" values="0;1;1;0" dur="3s" begin="2s" fill="freeze"/>
      +25 karma points!
    </text>
  </g>
  
  <!-- Connection lines -->
  <path d="M360 200 L400 200" stroke="#8b7fc9" stroke-width="2" opacity="0">
    <animate attributeName="opacity" values="0;1" dur="0.5s" begin="1.5s" fill="freeze"/>
  </path>
  <path d="M360 340 L400 220" stroke="#7dcfff" stroke-width="2" opacity="0">
    <animate attributeName="opacity" values="0;1" dur="0.5s" begin="3.5s" fill="freeze"/>
  </path>
  
  <!-- Real-time typing indicator -->
  <g transform="translate(20, 360)">
    <circle cx="10" cy="0" r="3" fill="#8b7fc9">
      <animate attributeName="opacity" values="0;1;0" dur="1s" begin="5s" repeatCount="indefinite"/>
    </circle>
    <circle cx="25" cy="0" r="3" fill="#8b7fc9">
      <animate attributeName="opacity" values="0;1;0" dur="1s" begin="5.2s" repeatCount="indefinite"/>
    </circle>
    <circle cx="40" cy="0" r="3" fill="#8b7fc9">
      <animate attributeName="opacity" values="0;1;0" dur="1s" begin="5.4s" repeatCount="indefinite"/>
    </circle>
    <text x="50" y="5" fill="#8b7fc9" font-family="Inter, sans-serif" font-size="10">Alex is typing...</text>
  </g>
</svg>