<?xml version="1.0" encoding="UTF-8"?>
<svg viewBox="0 0 500 400" xmlns="http://www.w3.org/2000/svg">
  <!-- Main IDE Window -->
  <rect x="50" y="50" width="400" height="300" rx="10" fill="#1E1E3F" />
  
  <!-- Window Header -->
  <rect x="50" y="50" width="400" height="30" rx="10" fill="#2D2B55" />
  <circle cx="70" cy="65" r="6" fill="#FF5F56" />
  <circle cx="90" cy="65" r="6" fill="#FFBD2E" />
  <circle cx="110" cy="65" r="6" fill="#27C93F" />
  <text x="250" y="70" text-anchor="middle" fill="#FFFFFF" font-size="12">KAPI IDE - Software Engineering 2.0</text>
  
  <!-- Left Panel - Project Structure -->
  <rect x="50" y="80" width="100" height="270" fill="#262650" />
  <rect x="60" y="95" width="80" height="15" rx="2" fill="#2D2B55" />
  <text x="70" y="106" fill="#A599E9" font-size="10">Project</text>
  <rect x="65" y="120" width="70" height="12" rx="2" fill="#2D2B55" opacity="0.5" />
  <rect x="65" y="140" width="70" height="12" rx="2" fill="#2D2B55" opacity="0.5" />
  <rect x="65" y="160" width="70" height="12" rx="2" fill="#2D2B55" opacity="0.5" />
  
  <!-- Main Code Area -->
  <rect x="150" y="80" width="200" height="270" fill="#1E1E3F" />
  <text x="160" y="100" fill="#A599E9" font-size="10">// KAPI Backwards Build Approach</text>
  <text x="160" y="120" fill="#FFFFFF" font-size="10">function createAIAgent() {</text>
  <text x="170" y="140" fill="#FF9D00" font-size="10">const agent = new Agent({</text>
  <text x="180" y="160" fill="#FF628C" font-size="10">model: "claude-3-opus",</text>
  <text x="180" y="180" fill="#FF628C" font-size="10">tools: [search, calculator],</text>
  <text x="180" y="200" fill="#FF628C" font-size="10">memory: vectorStore,</text>
  <text x="170" y="220" fill="#FF9D00" font-size="10">});</text>
  <text x="170" y="240" fill="#5ADEEF" font-size="10">return agent;</text>
  <text x="160" y="260" fill="#FFFFFF" font-size="10">}</text>
  
  <!-- Right Panel - Multi-modal Interface -->
  <rect x="350" y="80" width="100" height="270" fill="#262650" />
  <rect x="360" y="95" width="80" height="20" rx="4" fill="#8869ba" />
  <text x="370" y="110" fill="#FFFFFF" font-size="10">Voice Input</text>
  
  <rect x="360" y="125" width="80" height="20" rx="4" fill="#8869ba" opacity="0.8" />
  <text x="370" y="140" fill="#FFFFFF" font-size="10">Sketch Mode</text>
  
  <rect x="360" y="155" width="80" height="20" rx="4" fill="#8869ba" opacity="0.6" />
  <text x="370" y="170" fill="#FFFFFF" font-size="10">Docs First</text>
  
  <rect x="360" y="185" width="80" height="20" rx="4" fill="#8869ba" opacity="0.9" />
  <text x="370" y="200" fill="#FFFFFF" font-size="10">Test-Driven</text>
  
  <!-- Workshop Badge -->
  <rect x="360" y="300" width="80" height="30" rx="15" fill="#c3b5e9" />
  <text x="400" y="320" text-anchor="middle" fill="#1E1E3F" font-size="10" font-weight="bold">Modern AI Pro</text>
  
  <!-- Flow Lines Connecting Components -->
  <path d="M250 320 C 310 320, 310 300, 360 300" stroke="#8869ba" stroke-width="2" fill="none" />
  <path d="M110 300 C 150 300, 150 320, 250 320" stroke="#8869ba" stroke-width="2" fill="none" />
  
  <!-- Decorative Elements -->
  <circle cx="250" cy="350" r="15" fill="#a892d0" opacity="0.5" />
  <circle cx="280" cy="330" r="10" fill="#8869ba" opacity="0.3" />
  <circle cx="220" cy="330" r="8" fill="#c3b5e9" opacity="0.4" />
  
  <!-- Project Identity System Elements -->
  <g opacity="0.7">
    <rect x="60" y="190" width="25" height="25" rx="4" fill="#c3b5e9" />
    <text x="72" y="206" text-anchor="middle" fill="#1E1E3F" font-size="12" font-weight="bold">L</text>
    <rect x="60" y="225" width="25" height="25" rx="4" fill="#a892d0" />
    <text x="72" y="241" text-anchor="middle" fill="#1E1E3F" font-size="12" font-weight="bold">C</text>
    <rect x="60" y="260" width="25" height="25" rx="4" fill="#8869ba" />
    <text x="72" y="276" text-anchor="middle" fill="#1E1E3F" font-size="12" font-weight="bold">B</text>
  </g>
</svg>