<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="800" height="400" xmlns="http://www.w3.org/2000/svg">
  <!-- Background with gradient -->
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#7c3aed;stop-opacity:0.2" />
      <stop offset="100%" style="stop-color:#2563eb;stop-opacity:0.1" />
    </linearGradient>
  </defs>
  <rect width="800" height="400" fill="url(#grad1)" />
  
  <!-- Grid lines -->
  <g stroke="#ddd" stroke-width="1">
    <!-- Vertical grid lines -->
    <line x1="100" y1="50" x2="100" y2="350" />
    <line x1="200" y1="50" x2="200" y2="350" />
    <line x1="300" y1="50" x2="300" y2="350" />
    <line x1="400" y1="50" x2="400" y2="350" />
    <line x1="500" y1="50" x2="500" y2="350" />
    <line x1="600" y1="50" x2="600" y2="350" />
    <line x1="700" y1="50" x2="700" y2="350" />
    
    <!-- Horizontal grid lines -->
    <line x1="50" y1="50" x2="750" y2="50" />
    <line x1="50" y1="100" x2="750" y2="100" />
    <line x1="50" y1="150" x2="750" y2="150" />
    <line x1="50" y1="200" x2="750" y2="200" />
    <line x1="50" y1="250" x2="750" y2="250" />
    <line x1="50" y1="300" x2="750" y2="300" />
    <line x1="50" y1="350" x2="750" y2="350" />
  </g>
  
  <!-- X and Y axes -->
  <g stroke="#333" stroke-width="2">
    <line x1="100" y1="50" x2="100" y2="350" /> <!-- Y-axis -->
    <line x1="50" y1="300" x2="750" y2="300" /> <!-- X-axis (y = 0 line) -->
    
    <!-- X-axis arrow -->
    <polygon points="750,300 740,295 740,305" fill="#333" />
    
    <!-- Y-axis arrow -->
    <polygon points="100,50 95,60 105,60" fill="#333" />
  </g>
  
  <!-- X-axis labels -->
  <g font-family="Arial" font-size="12" fill="#333" text-anchor="middle">
    <text x="100" y="320">0</text>
    <text x="200" y="320">2</text>
    <text x="300" y="320">4</text>
    <text x="400" y="320">6</text>
    <text x="500" y="320">8</text>
    <text x="600" y="320">10</text>
    <text x="700" y="320">12</text>
    <text x="750" y="320">X</text>
  </g>
  
  <!-- Y-axis labels -->
  <g font-family="Arial" font-size="12" fill="#333" text-anchor="end">
    <text x="95" y="350">-10</text>
    <text x="95" y="300">0</text>
    <text x="95" y="250">10</text>
    <text x="95" y="200">20</text>
    <text x="95" y="150">30</text>
    <text x="95" y="100">40</text>
    <text x="95" y="50">50</text>
    <text x="95" y="35">Y</text>
  </g>
  
  <!-- The line Y = 5X - 2 -->
  <path d="M100,310 L700,90" stroke="#7c3aed" stroke-width="3" fill="none" />
  
  <!-- Points of interest with annotations -->
  <!-- X=0, Y=-2 -->
  <circle cx="100" cy="310" r="6" fill="#7c3aed" />
  <text x="120" y="310" font-family="Arial" font-size="14" fill="#7c3aed" font-weight="bold">(-2) No devs with AI</text>
  
  <!-- X=1, Y=3 -->
  <circle cx="150" cy="285" r="6" fill="#7c3aed" />
  <text x="170" y="285" font-family="Arial" font-size="14" fill="#7c3aed" font-weight="bold">1 excellent dev</text>
  
  <!-- X=10, Y=48 -->
  <circle cx="600" cy="100" r="6" fill="#7c3aed" />
  <text x="620" y="100" font-family="Arial" font-size="14" fill="#7c3aed" font-weight="bold">10 excellent devs</text>
  
  <!-- Title -->
  <text x="400" y="30" font-family="Arial" font-size="24" fill="#333" text-anchor="middle" font-weight="bold">The Vibe Coding Law: Y = 5X - 2</text>
</svg>