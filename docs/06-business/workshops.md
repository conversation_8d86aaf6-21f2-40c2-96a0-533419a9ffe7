# Modern AI Pro Workshop Catalog

_Last updated: May 28, 2025_

## Workshop Series Overview

Our intensive workshop series provides hands-on, practical training in AI development for professional developers. Each workshop combines theoretical foundations with real-world project work, preparing participants to build production-ready AI applications.

## Fundamentals Series

### AI Foundations Workshop
**Duration**: 3 days  
**Price**: $300  
**Level**: Beginner to Intermediate  

**What You'll Learn**:
- Core AI/ML concepts and terminology
- Overview of major AI model types (LLMs, vision, audio)
- Understanding model capabilities and limitations
- Basic prompt engineering techniques
- AI development tools and frameworks

**Projects**:
- Build a simple chatbot using OpenAI API
- Create a document summarization tool
- Implement basic sentiment analysis

**Prerequisites**: Basic programming experience (any language)

### Prompt Engineering Masterclass
**Duration**: 2 days  
**Price**: $300  
**Level**: Beginner to Advanced  

**What You'll Learn**:
- Advanced prompt engineering techniques
- Chain-of-thought reasoning
- Few-shot and zero-shot learning
- Prompt optimization and testing
- Model-specific prompting strategies

**Projects**:
- Design complex multi-step reasoning prompts
- Build a prompt library for common tasks
- Create prompt evaluation framework

**Prerequisites**: Basic familiarity with AI models

### RAG Architecture Workshop
**Duration**: 4 days  
**Price**: $300  
**Level**: Intermediate  

**What You'll Learn**:
- Retrieval-Augmented Generation fundamentals
- Vector databases and embeddings
- Document processing and chunking strategies
- RAG system architecture and optimization
- Evaluation metrics and testing approaches

**Projects**:
- Build a knowledge base chatbot
- Implement semantic search system
- Create domain-specific RAG application

**Prerequisites**: Programming experience, basic AI knowledge

## Implementation Series

### Building AI Agents Workshop
**Duration**: 5 days  
**Price**: $300  
**Level**: Intermediate to Advanced  

**What You'll Learn**:
- Agent architecture patterns
- Tool use and function calling
- Multi-agent systems
- Agent memory and state management
- Deployment and monitoring strategies

**Projects**:
- Build a task automation agent
- Create a research assistant with tool access
- Implement multi-agent collaboration system

**Prerequisites**: RAG Architecture workshop or equivalent experience

### Fine-tuning for Production
**Duration**: 4 days  
**Price**: $300  
**Level**: Advanced  

**What You'll Learn**:
- When and how to fine-tune models
- Dataset preparation and validation
- Training infrastructure and costs
- Model evaluation and comparison
- Deployment and serving fine-tuned models

**Projects**:
- Fine-tune a model for specific domain
- Create custom evaluation framework
- Deploy fine-tuned model to production

**Prerequisites**: Strong programming skills, ML experience helpful

### AI-Powered Applications Workshop
**Duration**: 5 days  
**Price**: $300  
**Level**: Intermediate to Advanced  

**What You'll Learn**:
- End-to-end application architecture
- Frontend integration with AI services
- User experience design for AI features
- Performance optimization and caching
- Monitoring and analytics implementation

**Projects**:
- Build complete AI-powered web application
- Implement user authentication and data management
- Deploy to production with monitoring

**Prerequisites**: Web development experience, AI Foundations workshop

## Advanced Topics

### Multimodal AI Workshop
**Duration**: 3 days  
**Price**: $300  
**Level**: Advanced  

**What You'll Learn**:
- Working with text, image, and audio models
- Cross-modal applications and use cases
- Multimodal RAG systems
- Integration challenges and solutions

**Projects**:
- Build image analysis with text generation
- Create audio transcription and analysis system
- Implement multimodal search application

**Prerequisites**: AI Foundations and one implementation workshop

### Safety & Alignment Workshop
**Duration**: 3 days  
**Price**: $300  
**Level**: Intermediate to Advanced  

**What You'll Learn**:
- AI safety principles and practices
- Content filtering and moderation
- Bias detection and mitigation
- Responsible AI development
- Compliance and regulatory considerations

**Projects**:
- Implement content moderation system
- Build bias detection tools
- Create safety evaluation framework

**Prerequisites**: AI Foundations workshop

### Vector Databases Masterclass
**Duration**: 2 days  
**Price**: $300  
**Level**: Intermediate  

**What You'll Learn**:
- Vector database architectures (Pinecone, Weaviate, Chroma)
- Embedding strategies and optimization
- Similarity search algorithms
- Performance tuning and scaling
- Integration with AI applications

**Projects**:
- Compare vector database performance
- Build optimized similarity search system
- Implement hybrid search (vector + traditional)

**Prerequisites**: RAG Architecture workshop or equivalent

## Workshop Formats

### Standard Workshops
- **Live Instruction**: Expert-led sessions with Q&A
- **Hands-on Labs**: Practical coding exercises
- **Project Work**: Real-world application development
- **Peer Collaboration**: Work with other participants
- **Resource Access**: Materials, code samples, continued support

### Private Team Workshops
- **Custom Scheduling**: Flexible timing for your team
- **Tailored Content**: Adapted to your specific use cases
- **Dedicated Instructor**: Full attention for your team
- **Company Materials**: Use your data and requirements
- **Follow-up Support**: Extended support post-workshop

### Enterprise Programs
- **Multi-Workshop Series**: Comprehensive training programs
- **Custom Curriculum**: Designed for your organization
- **Ongoing Support**: Quarterly check-ins and updates
- **Team Integration**: Cross-functional training
- **ROI Tracking**: Measure training impact and outcomes

## Workshop Alumni Benefits

### Continued Learning
- **Alumni Discord**: Private community for graduates
- **Office Hours**: Monthly Q&A sessions with instructors
- **Resource Updates**: New materials and tools
- **Advanced Topics**: Exclusive advanced content

### Kapi IDE Integration (Planned)
- **Extended Trial**: 3-month Kapi IDE access
- **Workshop Templates**: Your projects as IDE starter templates
- **Priority Support**: Enhanced support in Kapi IDE
- **Early Access**: New features and capabilities

### Career Support
- **Portfolio Review**: Feedback on workshop projects
- **Industry Connections**: Networking opportunities
- **Job Board Access**: AI-focused opportunities
- **Reference Letters**: Workshop completion verification

## Upcoming Schedule

### June 2025
- **June 3-5**: AI Foundations (Remote)
- **June 10-11**: Prompt Engineering (In-person, SF)
- **June 17-20**: RAG Architecture (Remote)
- **June 24-28**: Building AI Agents (In-person, NYC)

### July 2025
- **July 1-4**: Fine-tuning for Production (Remote)
- **July 8-12**: AI-Powered Applications (In-person, Austin)
- **July 15-17**: Multimodal AI (Remote)
- **July 22-24**: Safety & Alignment (Remote)

### August 2025
- **August 5-6**: Vector Databases (Remote)
- **August 12-16**: Building AI Agents (Remote)
- **August 19-22**: RAG Architecture (In-person, Seattle)
- **August 26-30**: AI-Powered Applications (Remote)

## Registration & Information

### How to Register
1. **Browse Catalog**: Review workshop descriptions and prerequisites
2. **Check Schedule**: Find convenient dates and formats
3. **Select Workshop**: Choose individual workshops or bundle packages
4. **Complete Registration**: Secure your spot with payment
5. **Prepare**: Receive pre-workshop materials and setup instructions

### Bundle Packages
- **AI Fundamentals Bundle**: 3 workshops for $800 (save $100)
- **Implementation Bundle**: 3 workshops for $800 (save $100)
- **Complete Series**: All workshops for $2,400 (save $600)

### Enterprise Inquiries
For private workshops, custom curriculum, or enterprise programs:
- **Email**: <EMAIL>
- **Phone**: Schedule consultation call
- **Proposal**: Custom pricing and timeline