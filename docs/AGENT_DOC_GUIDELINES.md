# Agent Documentation Guidelines

## Overview

Since agentic design is central to KAPI, maintaining consistent agent documentation is critical. This guide ensures all agent-related documentation stays synchronized.

## Documentation Structure

### 1. Product Documentation (`/docs/02-products/`)
- **Purpose**: Explain features to users
- **Tone**: User-friendly, benefit-focused
- **Details**: High-level, avoid implementation specifics

### 2. Technical Documentation (`/docs/03-technical/`)
- **Purpose**: Guide implementation
- **Tone**: Technical, precise
- **Details**: Architecture, data flow, algorithms

## Key Principles

### 1. Single Source of Truth
- Use `00-agent-reference.md` for canonical terminology
- All metrics and performance targets must match this reference
- Update reference file FIRST, then propagate changes

### 2. Clear Cross-References
- Always link between related sections
- Use consistent anchor names
- Example: `See [Voice Agent Technical Spec](./features/hybrid-agent-spec.md)`

### 3. Consistent Terminology
Use these exact terms across all documentation:

| Concept | Correct Term | NOT |
|---------|--------------|-----|
| Agent modes | `direct_execution`, `react_lite`, `full_react_tag` | execution mode, simple mode |
| Agent types | Evidence Collection Agent | Evidence Agent, Collector |
| Performance | "2 seconds" or "2s" | two seconds, ~2s |
| Token limits | "5000 tokens" | 5K tokens, ~5000 |

### 4. Update Process

When making agent-related changes:

1. **Update Reference**: Modify `00-agent-reference.md` first
2. **Update User Docs**: Reflect changes in `02-ai-agents.md`
3. **Update Technical**: Modify architecture files if needed
4. **Run Validation**: Execute `scripts/doc-sync-check.sh`
5. **Update Checklist**: Mark completed items in reference file

### 5. Documentation Ownership

| File | Primary Owner | Reviewers |
|------|---------------|-----------|
| `00-agent-reference.md` | Tech Lead | All |
| `02-ai-agents.md` | Product Team | Tech Lead |
| `hybrid-agent-spec.md` | Voice Team | Architecture |
| `agent-system.md` | Architecture | Tech Lead |
| `memory-system.md` | Architecture | AI Team |

## Common Pitfalls to Avoid

1. **Don't duplicate detailed specs** - Link instead
2. **Don't use different metrics** - Always check reference
3. **Don't create new agent types** without updating reference
4. **Don't forget cross-references** when adding features

## Validation Checklist

Before committing agent documentation changes:

- [ ] Reference file updated if adding new concepts
- [ ] Terminology matches reference file exactly
- [ ] Cross-references are working (no broken links)
- [ ] Metrics are consistent across all files
- [ ] Run `doc-sync-check.sh` passes
- [ ] Related files are updated together

## Quick Commands

```bash
# Check consistency
./scripts/doc-sync-check.sh

# Find all agent references
grep -r "agent" docs/ --include="*.md"

# Check specific metric consistency
grep -r "5000 tokens" docs/ --include="*.md"
```
