
Current:  
- Deploy to Azure VM (cloud deployment) with production settings in Clerk
- Waitlist build issue
- admin password part

NExt:
-- Finish sketch loading part (Getting updated mockups in return and store it in SVG file)
-- Auto doc generation
-- slide generation.
-- Nova sonic tools
-- Code quality part: Showing the quality of code generated.
-- RAG part
-- Template based website generation.
-- Template based slide deck generation.
-- UI part

## Credentials related:
- kapiadmin / Fu@sK+frN2m~
I have 2 different versions of postgres 16 and 17. I should use only 17 from homebrew. 16 also has kapi db, but completeoy out of date.

- <EMAIL> / admin123
- For using claude with AWS bedrock:
- export CLAUDE_CODE_USE_BEDROCK=1
- export ANTHROPIC_MODEL='us.anthropic.claude-3-7-sonnet-20250219-v1:0'
export DISABLE_PROMPT_CACHING=true




   

