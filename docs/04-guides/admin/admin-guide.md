# KAPI Admin Interface Guide

This comprehensive guide covers everything you need to know about the KAPI Admin Interface, from getting started to advanced customization.

## Table of Contents

1. [Quick Start](#quick-start)
2. [Accessing the Admin Interface](#accessing-the-admin-interface)
3. [Authentication](#authentication)
4. [Using the Admin Interface](#using-the-admin-interface)
5. [Customizing the Admin Interface](#customizing-the-admin-interface)
6. [Troubleshooting](#troubleshooting)
7. [Best Practices](#best-practices)

## Quick Start

### Access the Admin Interface

1. Start your backend server:
   ```
   cd backend
   python -m uvicorn app.main:app --reload
   ```

2. Open your browser and navigate to:
   ```
   http://localhost:8000/admin
   ```

### Login

In development mode, authentication is simplified:

1. If this is your first time, a default admin user will be created automatically
2. Use these credentials:
   - **Username**: `<EMAIL>`
   - **Password**: anything (passwords aren't verified in development mode)

### Create a Custom Admin User

To create your own admin user:

1. Run the admin user creation script:
   ```
   python scripts/create_admin_user.py --email <EMAIL> --first-name Your<PERSON>ame --last-name YourLastName --clerk-id your_clerk_id
   ```

2. Log in with your new admin email

## Accessing the Admin Interface

The admin interface is available at `/admin` on your backend server. For example, if your backend is running on `http://localhost:8000`, you can access the admin interface at `http://localhost:8000/admin`.

## Authentication

### Development Environment

In the development environment, authentication is simplified to make it easier to get started:

1. Navigate to the admin interface URL (`/admin`)
2. You'll be redirected to a login page
3. Enter the following credentials:
   - **Username**: `<EMAIL>` (or the email of any user with admin role)
   - **Password**: Any value (passwords are not verified in development mode)

If no admin users exist in the database, a default admin user will be automatically created with the email `<EMAIL>`.

### Creating Admin Users

To create a specific admin user or promote an existing user to admin role, use the provided script:

1. Navigate to your backend directory
2. Run the admin user creation script with appropriate parameters:
   ```
   python scripts/create_admin_user.py --email <EMAIL> --first-name YourName --last-name YourLastName --clerk-id your_clerk_id
   ```
3. The script will either create a new admin user or update an existing user with the specified email

### Production Authentication

For production environments, it's recommended to implement a more secure authentication system:

1. Integrate with Clerk authentication
2. Implement password verification
3. Use HTTPS to secure the connection

## Using the Admin Interface

### Dashboard

Upon successful login, you'll see the admin dashboard with an overview of your application's data.

### Navigation

The admin interface is organized into several sections, accessible from the sidebar:

- **User Management**: Manage users, user sessions, and onboarding states
- **Project Management**: Manage projects and tasks
- **Templates**: Manage project templates
- **Communication**: View and manage conversations and messages
- **Analytics**: View model usage statistics
- **Blog**: Manage blog posts
- **Learning**: Manage lessons and learning content
- **Programming**: View programming sessions
- **Billing**: Manage subscriptions and payments
- **Workshops**: Manage workshops and events
- **Gamification**: View ELO history and other gamification data

### Common Actions

For most resources, you can perform the following actions:

- **View**: Browse through records in a paginated table
- **Search**: Use the search box to find specific records
- **Sort**: Click on column headers to sort the data
- **Filter**: Use filters to narrow down the displayed records
- **Create**: Add new records using the "Create" button
- **Edit**: Modify existing records by clicking on them or using the edit button
- **Delete**: Remove records using the delete button

### User Management

The User Management section allows you to:

- View all users in the system
- Create new users
- Edit user details, including role (Free, Developer, Admin)
- Activate or deactivate users
- View user sessions and activity

### Project Management

The Project Management section allows you to:

- View all projects
- Create new projects
- Edit project details
- Manage tasks associated with projects

## Customizing the Admin Interface

### Architecture Overview

The KAPI Admin Interface is built using:

- **SQLAdmin**: A FastAPI extension for creating admin interfaces
- **FastAPI**: The web framework powering the backend
- **SQLAlchemy**: The ORM used for database interactions

The admin interface is defined in `backend/app/api/admin.py` and integrated with the main application in `backend/app/main.py`.

### Adding New Models to the Admin Interface

To add a new model to the admin interface:

1. Create a new ModelView class in `admin.py`
2. Add the view to the admin instance in the `setup_admin` function

Example workflow:
1. Define your model in the appropriate models file
2. Create a ModelView class in admin.py
3. Add the view to the admin instance
4. Restart the server to see your changes

### Customizing Existing Views

You can customize existing views by modifying their attributes:

- `column_list`: Controls which columns are displayed in the list view
- `column_searchable_list`: Defines which columns can be searched
- `column_sortable_list`: Specifies which columns can be sorted
- `column_default_sort`: Sets the default sorting
- `form_columns`: Controls which fields appear in the create/edit forms
- `can_create`, `can_edit`, `can_delete`: Control CRUD permissions

### Customizing Authentication

The admin authentication is handled by the `AdminAuthentication` class in `admin.py`.

For development, we use a simplified authentication that doesn't verify passwords. For production, you should implement proper authentication:

1. Modify the `login` method to verify credentials properly
2. Consider integrating with Clerk or implementing a separate admin authentication system

### Adding Custom Actions

To add custom actions to the admin interface:

1. Define a new route in your admin view class
2. Implement the action logic
3. Add a button or link to trigger the action

### Customizing Templates

SQLAdmin uses Jinja2 templates that can be customized:

1. Create a `templates` directory in your app directory
2. Copy and modify the templates from the SQLAdmin package
3. Update the `templates_dir` parameter in the `Admin` constructor

## Troubleshooting

### Login Issues

- If you can't log in, ensure that there's at least one user with admin role in the database
- Check that the email you're using matches an admin user's email
- In development mode, try using `<EMAIL>` as the username

### Missing Data

- If you don't see expected data in the admin interface, check that the corresponding models are properly registered in `admin.py`
- Ensure that the database connection is working correctly

### Server Errors

- Check the server logs for any error messages
- Ensure that all required dependencies are installed
- Verify that the database schema is up to date with the latest migrations

### Known Issues

- **500 Internal Server Error on Details Pages**: There's a known issue with viewing details for models with JSON fields in the admin interface. This affects User, Project, and Template models. The issue is due to JSON serialization problems with complex fields like `skills_graph`, `developer_strengths`, and `tech_stack`. We're working on a fix for this issue.

  **Workaround**:
  - Use the List views to see basic information about records
  - Use the Edit views to modify data
  - Avoid clicking on rows to view details until this issue is fixed
  - If you need to see the full data, you can use the API endpoints or database queries

- **JSON Fields in Forms**: When editing models with JSON fields, you might encounter serialization errors. To avoid this, we've configured the admin interface to exclude these fields from forms.

### Development Issues

- If your changes don't appear, make sure you're restarting the server
- Check the server logs for error messages
- Verify that your models are properly registered with SQLAlchemy
- Ensure that your templates are in the correct location

## Best Practices

### Security

- The simplified authentication in development mode is not secure and should not be used in production
- For production environments, implement proper authentication with Clerk or a separate admin authentication system
- Ensure that only trusted users are granted admin privileges
- Regularly audit admin actions and access logs

### Development

- Keep the admin interface secure by implementing proper authentication
- Limit access to sensitive data and operations
- Use descriptive names for views and actions
- Group related views into categories
- Add helpful documentation for complex fields or actions
- Test thoroughly after making changes

### Resources

- [SQLAdmin Documentation](https://aminalaee.dev/sqladmin/)
- [FastAPI Documentation](https://fastapi.tiangolo.com/)
- [SQLAlchemy Documentation](https://docs.sqlalchemy.org/)
