# KAPI Production Deployment Guide (Complete Step-by-Step)

## Prerequisites
- Ubuntu server (VM or dedicated)
- Your Mac with existing PostgreSQL database
- Domain name pointing to your server
- SSH access to Ubuntu server

---

## PART 1: Ubuntu Server Setup

### Step 1: Connect to Server
```bash
ssh azureuser@your-server-ip
```

### Step 2: Update System
```bash
sudo apt update && sudo apt upgrade -y
```

### Step 3: Install Required Software
```bash
# Install basics
sudo apt install -y curl git nginx postgresql postgresql-contrib redis-server

# Install Node.js LTS
curl -fsSL https://deb.nodesource.com/setup_lts.x | sudo -E bash -
sudo apt install -y nodejs

# Install PM2 globally
sudo npm install -g pm2

# Install PostgreSQL client tools (for database import)
sudo apt install -y postgresql-client
```

---

## PART 2: Database Migration (Mac → Ubuntu)

### Step A: Export Database from Mac

**On your Mac, run these commands:**

```bash
# 1. Export your database schema and data
pg_dump -h localhost -U kapiadmin -d kapi > kapi_backup.sql

# 2. Copy the backup to your Ubuntu server
scp kapi_backup.sql Kapi-Dev:/home/<USER>/
```

### Step B: Setup PostgreSQL on Ubuntu

**On Ubuntu server:**

```bash
# 1. Start PostgreSQL service
sudo systemctl start postgresql
sudo systemctl enable postgresql

# 2. Create database user and database
sudo -u postgres psql
```

**In PostgreSQL prompt, run:**
```sql
CREATE USER kapiadmin WITH PASSWORD 'your_secure_password_here';
CREATE DATABASE kapi_prod OWNER kapiadmin;
GRANT ALL PRIVILEGES ON DATABASE kapi_prod TO kapiadmin;
\q
```

### Step C: Import Database

**On Ubuntu server:**

```bash
# 1. Import the database backup
psql -h localhost -U kapiadmin -d kapi_prod < kapi_backup.sql

# 2. Verify import worked
psql -h localhost -U kapiadmin -d kapi_prod -c "\dt"
```

---

## PART 3: Application Deployment

### Step 4: Clone and Setup Code

```bash
# 1. Clone repository
cd /home/<USER>
git clone https://github.com/your-username/kapi-fresh.git kapi
cd kapi

# 2. Set permissions
chmod +x nodejs_backend/deploy.sh
```

### Step 5: Configure Environment

```bash
# 1. Copy environment file
cp nodejs_backend/.env.example nodejs_backend/.env

# 2. Edit environment file
nano nodejs_backend/.env
```

**Replace ALL values in .env file with your actual values:**

```bash
NODE_ENV=production
PORT=3000
DATABASE_URL=postgresql://kapi_user:your_secure_password_here@localhost:5432/kapi
SESSION_SECRET=your_super_long_random_session_secret_min_32_chars

# Admin Access (IMPORTANT: Change these!)
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=your_secure_admin_password
OWNER_EMAIL=<EMAIL>
OWNER_PASSWORD=your_secure_owner_password

# Your API keys
CLERK_SECRET_KEY=your_clerk_secret
OPENAI_API_KEY=your_openai_key
ANTHROPIC_API_KEY=your_anthropic_key

# Nova Sonic Service
NOVA_SONIC_PORT=3005
NOVA_SONIC_URL=http://localhost:3005

# AWS Credentials (for Nova Sonic)
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_PROFILE=bedrock-test
```

### Step 6: Deploy Applications

```bash
# Run the automated deployment script
./nodejs_backend/deploy.sh
```

**If deployment succeeds, skip to Step 8. If it fails, try manual deployment:**

```bash
# Manual deployment alternative
cd nodejs_backend
npm ci --production
cd src/next && npm ci --production && cd ../..
npm run build:all
npm run migration:run
cd ../services/nova-sonic-service
npm ci --production
npm run build
cd ../..
mkdir -p ~/kapi/pm2-logs
pm2 start nodejs_backend/ecosystem.config.json --env production
pm2 save
pm2 startup
```

### Step 7: Setup PM2 Auto-start

```bash
# Follow the PM2 startup instructions from previous command
# It will show a command like:
# sudo env PATH=$PATH:/usr/bin /usr/lib/node_modules/pm2/bin/pm2 startup systemd -u azureuser --hp /home/<USER>

# Copy and run that exact command
```

---

## PART 4: Web Server & SSL Setup

### Step 8: Configure Nginx

```bash
# 1. Copy nginx configuration
sudo cp /home/<USER>/kapi/nodejs_backend/nginx/kapihq.com.conf /etc/nginx/sites-available/

# 2. Update server name in config (replace kapihq.com with your domain)
sudo nano /etc/nginx/sites-available/kapihq.com.conf

# 3. Enable the site
sudo ln -s /etc/nginx/sites-available/kapihq.com.conf /etc/nginx/sites-enabled/

# 4. Remove default nginx site
sudo rm /etc/nginx/sites-enabled/default
```

### Step 9: Setup SSL Certificates

**For Cloudflare SSL:**

```bash
# 1. Create SSL directory
sudo mkdir -p /etc/ssl/cloudflare

# 2. Add your certificate (get from Cloudflare dashboard)
sudo nano /etc/ssl/cloudflare/kapihq.com.pem
# Paste your certificate content here

# 3. Add your private key
sudo nano /etc/ssl/cloudflare/kapihq.com.key  
# Paste your private key here

# 4. Set secure permissions
sudo chmod 600 /etc/ssl/cloudflare/kapihq.com.key
sudo chmod 644 /etc/ssl/cloudflare/kapihq.com.pem
```

**For Let's Encrypt SSL (alternative):**

```bash
# Install certbot
sudo apt install -y certbot python3-certbot-nginx

# Get certificate (replace yourdomain.com)
sudo certbot --nginx -d yourdomain.com -d www.yourdomain.com
```

### Step 10: Start Nginx

```bash
# 1. Test nginx configuration
sudo nginx -t

# 2. Start nginx
sudo systemctl start nginx
sudo systemctl enable nginx

# 3. Reload nginx
sudo systemctl reload nginx
```

---

## PART 5: Admin Panel Setup & Access

### How the Admin Panel Works

The admin panel is a web interface built into the nodejs_backend that provides:
- User management
- Database table viewing
- Conversation monitoring  
- Model usage tracking
- System health monitoring
- Nova Sonic voice service testing

### Step 11: Create Admin User

**Option A: Use environment credentials (already set in .env)**
- The admin panel will use `ADMIN_EMAIL` and `ADMIN_PASSWORD` from your .env file

**Option B: Create database admin user**

```bash
# Navigate to backend directory
cd /home/<USER>/kapi/nodejs_backend

# Create admin user interactively
npm run create-admin-user

# Or create simple admin user
npm run create-admin-user-simple
```

### Step 12: Access Admin Panel

**Once deployed, access admin at:**
- **URL**: `https://yourdomain.com/admin/login`
- **Login**: Use credentials from `ADMIN_EMAIL` and `ADMIN_PASSWORD` in your .env file

**Admin Panel Features:**
- **Dashboard**: `/admin/` - Overview of system stats
- **Users**: `/admin/users` - Manage users
- **Database**: `/admin/tables` - View database tables
- **Conversations**: `/admin/conversations` - Monitor conversations
- **Models**: `/admin/models` - Manage AI models
- **Nova Sonic**: `/admin/nova-sonic/test` - Test voice service
- **System Status**: `/admin/system-status` - Health monitoring

---

### Step 13: Setup Firewall

```bash
# 1. Enable firewall
sudo ufw enable

# 2. Allow SSH (IMPORTANT: Do this first!)
sudo ufw allow ssh

# 3. Allow HTTP and HTTPS
sudo ufw allow 'Nginx Full'

# 4. Check firewall status
sudo ufw status
```

### Step 14: Optional Security Enhancements

```bash
# Install fail2ban to prevent brute force attacks
sudo apt install -y fail2ban

# Start and enable fail2ban
sudo systemctl start fail2ban
sudo systemctl enable fail2ban
```

---

## PART 7: DNS & Final Configuration

### Step 15: DNS Setup (Cloudflare)

1. **Add DNS Records:**
   - Point `yourdomain.com` and `www.yourdomain.com` to your server IP
   - Set SSL/TLS mode: **Full (strict)**
   - Enable Proxy (orange cloud icon)

2. **Wait for DNS propagation** (usually 5-15 minutes)

### Step 16: Final Verification

```bash
# 1. Check all services are running
pm2 status

# 2. Check logs for errors
pm2 logs kapi-backend
pm2 logs nova-sonic-service

# 3. Test health endpoints
curl http://localhost:3000/health
curl http://localhost:3005/health

# 4. Test through nginx
curl https://yourdomain.com/health
curl https://yourdomain.com/nova-sonic/health
```

---

## PART 8: Post-Deployment Management

### Daily Management Commands

```bash
# Check service status
pm2 status

# View logs
pm2 logs kapi-backend --lines 50
pm2 logs nova-sonic-service --lines 50

# Restart services if needed
pm2 restart kapi-backend
pm2 restart nova-sonic-service

# Deploy updates
cd /home/<USER>/kapi
git pull origin main
./nodejs_backend/deploy.sh
```

### System Monitoring

```bash
# Check system resources
htop
df -h
free -h

# Check nginx status
sudo systemctl status nginx

# Check database
sudo systemctl status postgresql
psql -h localhost -U kapi_user -d kapi -c "SELECT version();"
```

---

## 🎯 SUCCESS CHECKLIST

After completing all steps, you should have:

- ✅ **Main Application**: `https://yourdomain.com` (Next.js frontend + API)
- ✅ **Admin Panel**: `https://yourdomain.com/admin/login` 
- ✅ **Nova Sonic API**: `https://yourdomain.com/nova-sonic/`
- ✅ **Database**: PostgreSQL running with migrated data
- ✅ **Services**: Both kapi-backend and nova-sonic-service running under PM2
- ✅ **SSL**: HTTPS working correctly
- ✅ **Security**: Firewall configured

### First Login Test

1. **Visit**: `https://yourdomain.com/admin/login`
2. **Login with**: Your `ADMIN_EMAIL` and `ADMIN_PASSWORD` from .env
3. **Should see**: Admin dashboard with system statistics

---

## 🚨 TROUBLESHOOTING

### Common Issues

**503 Service Unavailable:**
```bash
pm2 restart all
sudo systemctl reload nginx
```

**Database Connection Error:**
```bash
# Check if PostgreSQL is running
sudo systemctl status postgresql

# Test connection
psql -h localhost -U kapi_user -d kapi -c "SELECT 1;"
```

**SSL Certificate Issues:**
```bash
# Test nginx config
sudo nginx -t

# Check certificate files
sudo ls -la /etc/ssl/cloudflare/
```

**PM2 Services Not Starting:**
```bash
# Check PM2 logs
pm2 logs

# Restart from ecosystem config
cd /home/<USER>/kapi
pm2 delete all
pm2 start nodejs_backend/ecosystem.config.json --env production
```

**Admin Panel Not Loading:**
```bash
# Check if port 3000 is listening
sudo netstat -tlnp | grep :3000

# Check backend logs
pm2 logs kapi-backend --lines 100
```

### Emergency Recovery

```bash
# Complete service restart
pm2 delete all
cd /home/<USER>/kapi
./nodejs_backend/deploy.sh

# If deploy.sh fails, manual restart:
cd nodejs_backend
npm run build:all
cd ../services/nova-sonic-service  
npm run build
cd ../..
pm2 start nodejs_backend/ecosystem.config.json --env production
```

---

**🎉 Your KAPI platform is now fully deployed with both backend services and admin panel!**