# KAPI Git Pull & Update Guide

## Quick Update Process

After running `git pull`, follow these steps based on what changed:

### 1. Frontend Changes (Next.js)
```bash
cd /home/<USER>/kapi/kapi/nodejs_backend/src/next
npm install  # Only if package.json changed
npm run build
pm2 restart kapi-frontend
```

### 2. Backend Changes (Express/TypeScript)
```bash
cd /home/<USER>/kapi/kapi/nodejs_backend
npm install  # Only if package.json changed
npm run build
pm2 restart kapi-backend
```

### 3. Nova Sonic Service Changes
```bash
cd /home/<USER>/kapi/kapi/services/nova-sonic-service
npm install  # Only if package.json changed
npm run build
pm2 restart nova-sonic-service
```

### 4. Database Changes
```bash
cd /home/<USER>/kapi/kapi/nodejs_backend
npm run migration:run  # Only if new migrations exist
```

### 5. Environment Variables
```bash
# Check if .env.example has new variables
diff .env .env.example
# Add any new variables to .env
nano .env
# Restart affected services
```

### 6. Nginx Config Changes
```bash
# If nginx config changed
sudo cp /home/<USER>/kapi/kapi/nodejs_backend/nginx/kapihq.com.conf /etc/nginx/sites-available/
sudo nginx -t
sudo systemctl reload nginx
```

## Complete Update Script

For most updates, run this sequence:

```bash
#!/bin/bash
cd /home/<USER>/kapi/kapi

# Pull latest changes
git pull

# Backend
cd nodejs_backend
npm install
npm run build

# Frontend
cd src/next
npm install
npm run build

# Nova Sonic
cd ../../../services/nova-sonic-service
npm install
npm run build

# Restart all services
pm2 restart all

# Check status
pm2 status
```

## What to Check After Update

1. **Service Status**: `pm2 status` - All should be "online"
2. **Logs**: `pm2 logs --lines 20` - Check for errors
3. **Website**: Visit https://kapihq.com - Should load
4. **API**: `curl https://kapihq.com/api/health` - Should return OK

## When to Skip Steps

- **Skip npm install**: If package.json didn't change
- **Skip build**: If only markdown/docs changed
- **Skip migration**: If no new files in `/migrations`
- **Skip nginx reload**: If nginx config didn't change

## Emergency Rollback

If something breaks after update:

```bash
# Note the current commit
git log -1 --oneline

# Rollback to previous commit
git reset --hard HEAD~1

# Or rollback to specific commit
git reset --hard <commit-hash>

# Rebuild and restart
npm run build
pm2 restart all
```
