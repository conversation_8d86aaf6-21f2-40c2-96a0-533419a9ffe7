# Task Router Guide

The Intelligent Task Router is a powerful backend system that uses Gemini 2.0 Flash function calling to automatically classify and route user requests to the appropriate AI task handlers.

## Overview

Instead of manually specifying task types, users can describe what they want in natural language, and the system will automatically determine the best approach and execute it with the optimal AI model.

## Architecture

```
User Input → Gemini Function Calling → Task Classification → Appropriate Strategy → Claude/Specialized Model → Result
```

### Components

1. **Task Router** (`/task-router` endpoint)
2. **Gemini 2.0 Flash** (Function calling for classification)
3. **Task Strategies** (Slide, Mockup, Tests, Code generation)
4. **Specialized Models** (Claude 3.7 Sonnet for quality output)

## Supported Task Types

### 1. Slide Generation (`generate_slides`)

**Triggers on requests like:**
- "Create a slide deck for my banking app"
- "Generate a presentation about AI technology"
- "Make slides for my startup pitch"
- "Build a reveal.js presentation"

**What it does:**
- Uses `SlideGenerationTaskStrategy`
- Loads the complete reveal.js template from `templates/slides/reveal-template.html`
- Generates professional presentations with dark theme and teal accents
- Uses Claude 3.7 Sonnet with 8,192 tokens
- Outputs complete HTML with proper reveal.js integration

**Template Features:**
- Night theme with vibrant teal (#00c4b4) colors
- 9 structured slides: Title, Problem, Solution, Target Market, Features, Benefits, Technology, Roadmap, Call-to-Action
- Interactive navigation with keyboard support
- Mobile-responsive design
- Professional typography and layout

### 2. Mockup Generation (`generate_mockup`)

**Triggers on requests like:**
- "Create a wireframe for my dashboard"
- "Generate a UI mockup for mobile app"
- "Design a landing page layout"
- "Make a prototype interface"

**What it does:**
- Uses `SvgMockupTaskStrategy`
- Generates clean SVG mockups and wireframes
- Focuses on responsive design and accessibility
- Outputs scalable vector graphics

### 3. Test Generation (`generate_tests`)

**Triggers on requests like:**
- "Write unit tests for this function"
- "Generate test cases for my API"
- "Create integration tests"
- "Build a test suite"

**What it does:**
- Uses `TestCasesTaskStrategy`
- Generates comprehensive test coverage
- Supports multiple testing frameworks (Jest, Mocha, Pytest, etc.)
- Creates unit, integration, and e2e tests

### 4. Code Generation (`generate_code`)

**Triggers on requests like:**
- "Build a React component"
- "Create a REST API"
- "Write a Python script"
- "Implement this feature"

**What it does:**
- Uses `CodeGenerationTaskStrategy`
- Generates clean, well-documented code
- Supports multiple programming languages
- Includes error handling and best practices

## Usage

### Canvas Mode Integration

Canvas Mode automatically uses the task router for all requests:

1. Enter your request in the text input
2. The system analyzes your request with Gemini
3. Routes to the appropriate task handler
4. Generates results with specialized models
5. Saves output to appropriate folders (slides, etc.)

### Direct API Usage

```bash
POST /task-router
Content-Type: application/json
Authorization: Bearer <token>

{
  "prompt": "Create a professional slide deck about AI in healthcare",
  "conversationId": 123
}
```

### Response Format

The endpoint returns a streaming response:

```json
// Content chunks
{"type": "content", "content": "<!DOCTYPE html>..."}

// Completion
{
  "type": "done",
  "usage": {...},
  "model": "claude-3.7-sonnet", 
  "taskType": "slide_generation",
  "routedBy": "gemini-function-calling"
}
```

## Function Definitions

The router uses these function schemas for Gemini:

### generate_slides
```json
{
  "name": "generate_slides",
  "description": "Generate a professional slide presentation using reveal.js",
  "parameters": {
    "topic": "Main presentation topic",
    "style": "Visual preferences (dark theme, professional, modern)",
    "audience": "Target audience (business, technical, general)"
  }
}
```

### generate_mockup
```json
{
  "name": "generate_mockup", 
  "description": "Generate UI/UX mockups or wireframes in SVG format",
  "parameters": {
    "description": "Description of interface to create",
    "type": "Interface type (web, mobile, desktop)",
    "style": "Design style preferences"
  }
}
```

### generate_tests
```json
{
  "name": "generate_tests",
  "description": "Generate test cases, unit tests, or testing code", 
  "parameters": {
    "codeToTest": "Code that needs testing",
    "testFramework": "Testing framework (jest, mocha, pytest)",
    "testType": "Test type (unit, integration, e2e)"
  }
}
```

### generate_code
```json
{
  "name": "generate_code",
  "description": "Generate code, functions, classes, or applications",
  "parameters": {
    "requirements": "Detailed code requirements",
    "language": "Programming language",
    "framework": "Framework or library to use"
  }
}
```

## Configuration

### Model Selection

The task router enforces optimal model selection:

- **Slide Generation**: Claude 3.7 Sonnet (best for structured HTML/templates)
- **Mockup Generation**: Claude 3.5 Haiku (efficient for SVG)
- **Test Generation**: Claude 3.5 Haiku (good for systematic testing)
- **Code Generation**: Claude 3.7 Sonnet (comprehensive code quality)

### Token Limits

All tasks use generous token limits:
- **Slide Generation**: 8,192 tokens (full presentations)
- **Other Tasks**: 8,192 tokens (comprehensive output)
- **Routing**: 1,000 tokens (efficient classification)

### Template Integration

Slide generation automatically loads templates:
- **Template Path**: `templates/slides/reveal-template.html`
- **System Prompt**: `config/prompts.yaml` → `slides` section
- **Placeholder Replacement**: All `{{VARIABLE}}` placeholders filled automatically

## Debugging

### Backend Logs

```bash
🔧 [TASK-ROUTER] Routing task for user: "create slides..."
🔧 [TASK-ROUTER] Routed to function: generate_slides  
🔧 [TASK-ROUTER] Executing task type: slide_generation
🔧 [UNIFIED-CONVERSATION] Strategy selected: SlideGenerationTaskStrategy
🔧 [SLIDE-STRATEGY] Loading template from: .../reveal-template.html
🔧 [AI-SERVICE] Streaming with provider: claude, model: claude-3.7-sonnet
```

### Frontend Logs

```bash
🔧 [Canvas] Using intelligent task router...
🔧 [Canvas] Sending to task router...
📝 Generating: 1500 characters...
✅ Task completed! Routed by: gemini-function-calling
🎯 Task type: slide_generation
🤖 Model: claude-3.7-sonnet
```

## Benefits

### Intelligence
- **Natural Language Processing**: No need to specify task types manually
- **Context Understanding**: Gemini analyzes intent accurately
- **Automatic Routing**: Best strategy selected automatically

### Quality
- **Specialized Models**: Each task uses the optimal AI model
- **Template Integration**: Professional templates for consistent output
- **Token Optimization**: Generous limits ensure complete generation

### Reliability
- **Backend Routing**: Bypasses frontend configuration issues
- **Error Handling**: Graceful fallbacks and comprehensive logging
- **Streaming Response**: Real-time feedback during generation

### Extensibility
- **New Task Types**: Easy to add new functions and strategies
- **Model Updates**: Centralized model selection and configuration
- **Template System**: Reusable templates for consistent branding

## Examples

### Slide Generation
**Input**: "Create a professional banking application presentation with dark theme"

**Output**: Complete reveal.js presentation with:
- Dark theme with teal accents (#00c4b4)
- 9 professional slides
- Banking-specific content
- Interactive navigation
- Mobile-responsive design

### Mockup Generation  
**Input**: "Design a dashboard for project management"

**Output**: SVG wireframe with:
- Clean, structured layout
- Project management components
- Responsive design considerations
- Accessibility features

### Test Generation
**Input**: "Write Jest tests for my API endpoints"

**Output**: Comprehensive test suite with:
- Unit tests for each endpoint
- Error case handling
- Mock implementations
- Coverage optimization

### Code Generation
**Input**: "Build a React component for user profiles"

**Output**: Complete component with:
- TypeScript interfaces
- Props validation
- Event handlers
- Styling integration
- Documentation

## Troubleshooting

### Common Issues

1. **Wrong Task Classification**
   - Check function descriptions in `task-router.ts`
   - Verify Gemini model has latest function definitions
   - Add more specific keywords to function descriptions

2. **Template Loading Errors**
   - Verify template file exists at `templates/slides/reveal-template.html`
   - Check file permissions and path resolution
   - Review template syntax and placeholder format

3. **Model Selection Issues**
   - Confirm model IDs in strategy configurations
   - Verify API keys for Claude, Gemini services
   - Check model availability and rate limits

4. **Token Limit Problems**
   - Monitor token usage in logs
   - Adjust maxTokens in route configuration
   - Optimize prompt length and template size

### Performance Optimization

1. **Cache Templates**: Load templates once at startup
2. **Model Pooling**: Reuse model connections when possible
3. **Streaming Optimization**: Buffer chunks for smoother delivery
4. **Error Caching**: Cache routing decisions for similar requests

## Future Enhancements

- **Custom Functions**: User-defined task types and handlers
- **Multi-Modal Input**: Image, audio, and video task analysis
- **Collaborative Routing**: Team-specific routing preferences
- **Performance Analytics**: Task completion metrics and optimization
- **Template Marketplace**: Community-contributed templates and strategies