# Canvas Mode Guide

## Overview
Canvas mode allows you to sketch UI mockups and get AI-generated SVG improvements with automatic file saving.

## Quick Start

1. **Switch to Sketch Mode** - Click "✏️ Sketch Mode" in VoiceAgent
2. **Add AI Instructions** - Type in text box above canvas (e.g., "Focus on navigation layout")
3. **Draw Your Mockup** - Use tools: pencil, rectangle, circle, arrow, text
4. **Send to AI** - Click "🤖 Send to AI" button

## AI Processing

- **Input**: Your sketch + instructions
- **Output**: Clean SVG mockup (auto-saved to `kapi/` folder)
- **Auto-features**: 
  - Creates `kapi/` folder if missing
  - Saves as `kapi-design-YYYY-MM-DD_HH-MM-SS.svg`
  - Loads SVG in canvas background
  - Clears previous annotations

## Tools Available

- **✏️ Pencil** - Freehand drawing
- **▢ Rectangle** - UI elements/containers  
- **➡️ Arrow** - Flow indicators
- **⭕ Circle** - Highlights/emphasis
- **🧹 Eraser** - Remove annotations
- **T Text** - Add labels/notes

## Colors
- **Black** - Main elements
- **Red** - Emphasis/corrections

## Workflow

1. Draw initial mockup
2. Add AI instructions
3. Get AI-generated SVG
4. Draw annotations on AI design
5. Send again for refinement
6. Repeat iteratively

All designs auto-save to `kapi/` folder for version history.