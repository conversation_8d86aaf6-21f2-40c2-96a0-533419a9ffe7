# KAPI IDE Search Guide

The KAPI IDE provides four powerful search modes to help you navigate and explore your codebase efficiently. Each mode is optimized for different types of searches.

## Search Interface

Access the search functionality through the search bar at the top of the IDE. The search bar has four tabs:

1. **Files** (📁) - Search for files and directories by name
2. **Content** (🔍) - Search within file contents using text patterns
3. **Structure** (⚡) - Search for code symbols using AST (Abstract Syntax Tree) analysis
4. **Semantic** (🧠) - AI-powered natural language search through documentation

## 1. Files Search

Search for files and directories by their names or paths.

### When to Use Files Search
- Finding specific files: `package.json`, `tsconfig.json`
- Locating components: `SearchBar`, `FileExplorer`
- Finding test files: `test`, `spec`
- Discovering configuration files: `config`, `env`

### File Search Patterns

#### **Exact File Names**
```
package.json          # Find package.json files
tsconfig.json         # Find TypeScript config files
README.md             # Find README files
.gitignore           # Find git ignore files
```

#### **File Extensions**
```
.tsx                 # Find all TypeScript React files
.css                 # Find all CSS files
.md                  # Find all Markdown files
.json               # Find all JSON files
```

#### **Component Names**
```
SearchBar            # Find SearchBar component files
FileExplorer         # Find FileExplorer component files
Button               # Find Button-related files
Modal                # Find Modal components
```

#### **Partial Names**
```
context              # Find files with "context" in the name
service              # Find service-related files
utils                # Find utility files
test                 # Find test files
```

#### **Directory Patterns**
```
components           # Find components directories
src                  # Find source directories
docs                 # Find documentation directories
assets               # Find asset directories
```

### File Search Tips
- Search is case-insensitive
- Partial matches work well
- Include file extensions for precision
- Use directory names to scope searches

## 2. Content Search

Search within the actual content of files using text patterns and regular expressions.

### When to Use Content Search
- Finding specific code implementations
- Locating error messages or strings
- Searching for API calls or imports
- Finding configuration values

### Content Search Patterns

#### **Function Calls**
```
useState(            # Find React useState hook usage
useEffect(           # Find React useEffect hook usage
console.log(         # Find console.log statements
fetch(               # Find fetch API calls
```

#### **Import Statements**
```
import React         # Find React imports
import { useState    # Find specific React hook imports
from './             # Find relative imports
from 'react'         # Find React library imports
```

#### **String Literals**
```
"Error:"             # Find error messages
'localhost'          # Find localhost references
`http://             # Find HTTP URLs in template literals
TODO:                # Find TODO comments
```

#### **CSS Classes and Styles**
```
className=           # Find className assignments
.file-explorer       # Find CSS class definitions
background-color     # Find background color styles
flex                 # Find flexbox usage
```

#### **Configuration Values**
```
PORT=                # Find port configurations
API_URL              # Find API URL configurations
NODE_ENV             # Find environment variable usage
process.env          # Find environment variable access
```

#### **Error Handling**
```
try {                # Find try-catch blocks
catch (              # Find error catching
throw new            # Find error throwing
Error(               # Find Error constructors
```

### Content Search Options
- **Case Sensitive**: Match exact case
- **Whole Word**: Match complete words only
- **Regular Expressions**: Use regex patterns for advanced matching

### Regular Expression Examples
```
/function\s+\w+/     # Find function declarations
/const\s+\w+\s*=/    # Find const variable declarations
/interface\s+\w+/    # Find TypeScript interfaces
/class\s+\w+/        # Find class declarations
```

## 3. Structure Search (AST)

Search for code symbols using Abstract Syntax Tree analysis. This is the most powerful search mode for understanding code structure.

### When to Use Structure Search
- Finding function definitions
- Locating class declarations
- Searching for interfaces and types
- Finding variable declarations
- Exploring code architecture

### Structure Search Patterns

#### **React Components & Hooks**
```
useState             # Find useState hook usage
useEffect            # Find useEffect hook usage
useContext           # Find context hook usage
Component            # Find React components
Provider             # Find context providers
FC                   # Find functional component types
```

#### **Function Names**
```
handleClick          # Find click handlers
handleSubmit         # Find form submission handlers
render               # Find render methods
initialize           # Find initialization functions
validate             # Find validation functions
fetch                # Find fetch functions
```

#### **Class Names**
```
Service              # Find service classes
Manager              # Find manager classes
Provider             # Find provider classes
Component            # Find component classes
```

#### **Interface & Type Names**
```
Props                # Find Props interfaces
State                # Find State interfaces
Config               # Find configuration interfaces
ApiResponse          # Find API response types
UserData             # Find user data types
```

#### **Variable Names**
```
config               # Find config variables
apiUrl               # Find API URL variables
isLoading            # Find loading state variables
userToken            # Find token variables
```

#### **Project-Specific Symbols**
Based on your KAPI IDE codebase:
```
FileExplorer         # Find FileExplorer component
SearchBar            # Find SearchBar component
EditorContext        # Find editor context
ProjectContext       # Find project context
useProject           # Find project hook usage
setCurrentFile       # Find file setter functions
openProject          # Find project opening logic
refreshFileStructure # Find file refresh logic
```

#### **TypeScript Specific**
```
interface            # Find interface definitions
type                 # Find type aliases
enum                 # Find enumeration types
namespace            # Find namespace declarations
```

### Structure Search Tips
- Search for specific symbol names, not generic terms
- Use camelCase or PascalCase as they appear in code
- Great for code navigation and understanding relationships
- Perfect for refactoring and finding all usages

## 4. Semantic Search (🧠) - AI-Powered Documentation Search

**Semantic Search** is KAPI's most advanced search feature that uses AI to understand natural language queries and find relevant documentation, comments, and explanations in your codebase.

### What Makes Semantic Search Different

Unlike traditional text search, semantic search understands **meaning** and **context**:
- Ask questions in plain English
- Find conceptual information, not just literal text matches
- Get explanations of "what" and "why", not just "where" and "how"
- Search through documentation, comments, and architectural decisions

### What Gets Indexed for Semantic Search

**✅ Documentation Content (Indexed):**
- 📝 **Markdown files**: README.md, documentation files, technical specs
- 💬 **Code comments**: `//`, `/* */`, `/** */` style comments
- 📋 **Special documentation comments**: `/// Summary:`, `/// Purpose:`, `/// Intent:`
- 📄 **.doc.json files**: Machine-readable documentation format
- 🐍 **Python docstrings**: `"""` style documentation

**❌ Raw Code (Not Indexed):**
- Function implementations and logic
- Variable declarations and assignments
- Import/export statements
- Raw TypeScript/JavaScript/Python code

### When to Use Semantic Search

#### **Understanding Concepts** 🤔
```
"How does authentication work?"
"What's our error handling strategy?"
"Why did we choose this architecture?"
"What is the purpose of the FileExplorer component?"
```

#### **Finding Documentation** 📚
```
"Where is the API documentation?"
"Show me setup instructions"
"Find deployment guidelines"
"What are the contributing guidelines?"
```

#### **Exploring Explanations** 💡
```
"What are the architectural decisions?"
"Why did we choose React over Vue?"
"How is data flow handled?"
"What's the testing approach?"
```

#### **Learning from Comments** 📝
```
"Developer explanations about complex logic"
"Intent behind code changes"
"Business requirements and constraints"
"Performance considerations"
```

#### **Project Knowledge** 🏗️
```
"Team decisions and design rationale"
"Best practices followed in this project"
"Known limitations and workarounds"
"Future plans and roadmap items"
```

### Semantic Search Examples

#### **Good Semantic Queries:**
```
✅ "How does user authentication work?"
✅ "What's our approach to error handling?"
✅ "Where are the React components?"
✅ "Explain the project architecture"
✅ "What are the API endpoints?"
✅ "How is state management handled?"
✅ "What's the build process?"
✅ "Security considerations for this project"
```

#### **Better Suited for Other Search Types:**
```
❌ "Find the authenticate function" → Use Structure Search
❌ "Show me all useState calls" → Use Content Search
❌ "List all .tsx files" → Use Files Search
❌ "Find the LoginComponent" → Use Structure Search
```

### First-Time Setup

#### **Automatic Indexing**
The first time you use semantic search:

1. **Click the 🧠 Semantic tab**
2. **Automatic indexing starts** - this processes your project's documentation
3. **Wait for completion** - typically 30-60 seconds for most projects
4. **Start searching** - semantic search is now ready!

#### **What Happens During Indexing:**
1. **Scans project files** for documentation content
2. **Extracts comments and docs** (ignores raw code)
3. **Generates AI embeddings** using Azure OpenAI
4. **Stores locally** in browser's IndexedDB for persistence
5. **Ready for search** - sub-100ms response times

### Persistent Storage & Cost Efficiency

#### **Your Embeddings Are Safe** 💰
- **Persistent across sessions**: Embeddings saved in browser's IndexedDB
- **Survives app restarts**: No re-indexing needed when you reopen KAPI
- **Cost efficient**: Only pay for embedding generation once per documentation change
- **Fast recovery**: Instant search availability after app restart

#### **When Embeddings Are Preserved:**
- ✅ **Browser restart** - Embeddings automatically restored
- ✅ **App restart** - Instant search availability
- ✅ **System reboot** - IndexedDB survives across reboots
- ✅ **Multiple projects** - Each project has separate persistent storage

#### **When Re-indexing Is Needed:**
- 🔄 **Documentation changes** - New comments, updated README files
- 🔄 **Manual reindex** - Using File->Reindex Documentation or reindex button
- 🔄 **Browser data cleared** - If you clear browser storage/IndexedDB

### Manual Reindexing

#### **When to Reindex:**
- After adding significant new documentation
- When comments or README files are updated
- If search results seem outdated
- After major project restructuring

#### **How to Reindex:**

**Method 1: File Menu**
1. Go to **File** menu in the top menu bar
2. Click **"Reindex Documentation"**
3. Wait for completion (shows "Reindexing..." during process)

**Method 2: Search Bar Button** (when semantic tab is active)
1. Open the **🧠 Semantic** search tab
2. Click the **🔄 Reindex** button on the right
3. Watch for completion feedback

#### **Reindexing Process:**
1. **Clears existing embeddings** from both memory and storage
2. **Re-scans project** for documentation content
3. **Generates fresh embeddings** for updated content
4. **Saves to persistent storage** for future sessions
5. **Ready for search** with updated content

### Search Results & Confidence Scoring

#### **Understanding Results:**
- **Confidence percentage**: 60-100% relevance score
- **Document type indicators**: Shows if result is from comments, markdown, etc.
- **File location**: Direct link to source file
- **Content preview**: Snippet of matching documentation

#### **Result Quality Tips:**
- **Higher confidence** = more relevant match
- **Use natural language** - ask questions like talking to a colleague
- **Be specific** - "authentication process" vs. just "auth"
- **Ask "what" and "why"** questions for best results

### Performance & Technical Details

#### **Speed:**
- **Initial indexing**: 30-60 seconds (one-time per project)
- **Search speed**: <100ms after indexing complete
- **Recovery speed**: <5 seconds to restore from storage

#### **Storage:**
- **Location**: Browser's IndexedDB database
- **Size**: ~10-50MB per project (documentation only)
- **Scope**: Per-project isolation (each project has separate embeddings)

#### **Privacy:**
- **Local-first**: Embeddings stored locally in your browser
- **No cloud storage**: Documentation content never leaves your machine
- **Project isolation**: Each project's embeddings are separate

### Troubleshooting

#### **If Semantic Search Isn't Working:**
1. **Check console logs** for error messages
2. **Try manual reindexing** via File->Reindex Documentation
3. **Ensure project is open** - semantic search requires an active project
4. **Wait for indexing** - first-time setup takes 30-60 seconds

#### **If Results Are Poor:**
1. **Use natural language** - ask questions, don't just use keywords
2. **Check confidence scores** - look for 70%+ matches
3. **Try broader queries** - "authentication" vs. "JWT token validation"
4. **Reindex after documentation changes**

#### **Storage Issues:**
1. **Browser storage full** - Clear other browser data or use incognito mode
2. **IndexedDB disabled** - Enable in browser settings
3. **Storage corruption** - Clear KAPI storage and reindex

### Best Practices

#### **Optimize Your Documentation for Semantic Search:**
1. **Write descriptive comments** - explain the "why", not just the "what"
2. **Use README files** for high-level concepts
3. **Add purpose comments** - `/// Purpose: Handles user authentication flow`
4. **Document architectural decisions** in markdown files
5. **Include business context** in comments

#### **Search Strategy:**
1. **Start broad, then narrow** - "authentication" → "JWT token validation"
2. **Use complete questions** - "How does login work?" vs. "login"
3. **Combine with other search types** - semantic for concepts, structure for implementation
4. **Review confidence scores** - focus on 70%+ matches

## Search Strategy Recommendations

### **Understanding a New Codebase** 🎯
1. **Semantic** search: "What is this project about?" → Get high-level overview
2. **Files** search: Explore README.md, package.json → Understand structure
3. **Semantic** search: "How does authentication work?" → Learn key concepts
4. **Structure** search: Find main components and entry points
5. **Content** search: Look for specific implementation details

### **Finding Files** 📁
1. Start with **Files** search for broad discovery
2. Use **Content** search to verify file contents
3. Use **Structure** search to understand code organization
4. Use **Semantic** search to find documentation about specific files

### **Debugging Issues** 🐛
1. **Semantic** search: "How does error handling work?" → Understand approach
2. **Content** search: Find error messages or logs in code
3. **Structure** search: Find related functions and error handlers
4. **Files** search: Locate test files and error documentation

### **Code Refactoring** 🔧
1. **Semantic** search: "Why was this designed this way?" → Understand intent
2. **Structure** search: Find all symbol usages across codebase
3. **Content** search: Find string references and hardcoded values
4. **Files** search: Find related files and tests

### **Learning Codebase Architecture** 🏗️
1. **Semantic** search: "What's the project architecture?" → Get overview
2. **Semantic** search: "How is data flow handled?" → Understand patterns
3. **Structure** search: Explore main components and services
4. **Files** search: Understand project structure and organization
5. **Content** search: Find implementation details and configurations

### **Adding New Features** ✨
1. **Semantic** search: "How are similar features implemented?" → Learn patterns
2. **Structure** search: Find related components to extend
3. **Files** search: Locate test files and documentation to update
4. **Content** search: Find configuration and setup code to modify

### **Documentation and Comments** 📚
1. **Semantic** search: Perfect for finding explanations and decisions
2. **Content** search: Find specific comment text or TODO items
3. **Files** search: Locate README and documentation files
4. **Structure** search: Find documented functions and interfaces

### **When to Use Each Search Type**

| Goal | Best Search Type | Example Query |
|------|-----------------|---------------|
| **Understand concepts** | 🧠 Semantic | "How does user login work?" |
| **Find explanations** | 🧠 Semantic | "Why did we choose this approach?" |
| **Locate specific functions** | ⚡ Structure | `handleLogin`, `validateUser` |
| **Find implementation** | 🔍 Content | `fetch('/api/login')` |
| **Discover files** | 📁 Files | `login`, `.tsx`, `auth` |
| **Learn architecture** | 🧠 Semantic | "What's the overall architecture?" |
| **Debug errors** | 🔍 Content | `"Error:"`, `console.error` |
| **Find usage patterns** | ⚡ Structure | `useState`, `useEffect` |

## Performance Tips

- **Files Search**: Fastest (~50ms), searches file system metadata
- **Content Search**: Moderate speed (~200ms), searches file contents  
- **Structure Search**: Comprehensive (~300ms), requires AST parsing
- **Semantic Search**: Fast after indexing (<100ms), requires one-time setup

### **Performance Strategy:**
1. **First-time users**: Allow 30-60 seconds for semantic search indexing
2. **Regular use**: Semantic search is fastest for conceptual queries
3. **Large codebases**: Start with Files search to narrow scope
4. **Complex queries**: Use Semantic search for "what/why" questions
5. **Specific symbols**: Use Structure search for precise code navigation

### **Optimization Tips:**
- **Semantic search** becomes faster than Content search for conceptual queries
- **IndexedDB storage** makes semantic search instant on subsequent sessions  
- **Combine search types** for comprehensive exploration
- **Use appropriate search type** for your specific need (see table above)

## Keyboard Shortcuts

- `Ctrl+P` (or `Cmd+P`): Quick file search
- `Ctrl+Shift+F` (or `Cmd+Shift+F`): Open search modal
- `Ctrl+F` (or `Cmd+F`): Search within current file

## Summary

KAPI IDE's four-tier search system provides comprehensive codebase exploration:

- **🧠 Semantic Search**: AI-powered natural language queries for understanding concepts and documentation
- **⚡ Structure Search**: AST-based symbol navigation for precise code exploration  
- **🔍 Content Search**: Text-based pattern matching for finding specific implementations
- **📁 Files Search**: Fast file and directory discovery

**Pro Tip**: Start with semantic search to understand concepts, then drill down with structure and content search for implementation details. The persistent storage ensures your AI embeddings are always ready for instant conceptual exploration!

Use the search functionality strategically to navigate your codebase efficiently, understand code relationships, and discover architectural decisions and documentation that make your development process faster and more informed.