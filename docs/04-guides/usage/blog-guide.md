# KAPI Blog Usage Guide

This guide provides quick examples for using the blog feature in the KAPI backend.

## Creating Blog Posts

### Basic Post Creation
```bash
curl -X POST http://localhost:8000/blog/ \
  -F "title=My First Post" \
  -F "slug=my-first-post" \
  -F "content=# Hello World\n\nThis is my first blog post with **markdown** support." \
  -F "author_id=1"
```

### With Featured Image
```bash
curl -X POST http://localhost:8000/blog/ \
  -F "title=Post With Image" \
  -F "slug=post-with-image" \
  -F "content=Check out this cool featured image!" \
  -F "author_id=1" \
  -F "featured_image=@/path/to/your/image.jpg"
```

### With Tags
```bash
# First create tags
curl -X POST http://localhost:8000/blog/tags \
  -H "Content-Type: application/json" \
  -d '{"name": "Tutorial", "slug": "tutorial"}'

# Then use tags when creating a post (assuming tag id is 1)
curl -X POST http://localhost:8000/blog/ \
  -F "title=Tagged Post" \
  -F "slug=tagged-post" \
  -F "content=This post has tags." \
  -F "author_id=1" \
  -F "tag_ids=1,2,3"
```

## Getting Posts

### List All Posts
```bash
curl http://localhost:8000/blog/
```

### Get Specific Post
```bash
# By ID
curl http://localhost:8000/blog/1

# By Slug
curl http://localhost:8000/blog/by-slug/my-first-post
```

### Get Post Content with Reading Time
```bash
# By ID
curl http://localhost:8000/blog/1/content

# By Slug
curl http://localhost:8000/blog/slug/my-first-post/content
```

### Filter Posts by Tag
```bash
# By Tag ID
curl http://localhost:8000/blog/by-tag/1

# By Tag Slug
curl http://localhost:8000/blog/by-tag-slug/tutorial
```

## Working with Images

### Upload an Image
```bash
curl -X POST http://localhost:8000/blog/images \
  -F "image=@/path/to/your/image.jpg"

# Response will include the image_path to use in your markdown
```

### Reference Images in Markdown
```markdown
# Post with Images

Here's an image:

![Alt text](/blog/images/your-image-filename.jpg)
```

### View an Image
```
GET http://localhost:8000/blog/images/your-image-filename.jpg
```

## Managing Tags

### Create a Tag
```bash
curl -X POST http://localhost:8000/blog/tags \
  -H "Content-Type: application/json" \
  -d '{"name": "Tutorial", "slug": "tutorial"}'
```

### List All Tags
```bash
curl http://localhost:8000/blog/tags
```

### Add/Remove Tag from Post
```bash
# Add tag (tag ID 2) to post (post ID 1)
curl -X POST http://localhost:8000/blog/1/tags/2

# Remove tag from post
curl -X DELETE http://localhost:8000/blog/1/tags/2
```

## Publishing Workflow

### Publish a Post
```bash
curl -X POST http://localhost:8000/blog/1/publish
```

### Unpublish a Post
```bash
curl -X POST http://localhost:8000/blog/1/unpublish
```

### List Only Published Posts
```bash
curl "http://localhost:8000/blog/?published_only=true"
```

## Python Example

```python
import requests

# Create a new blog post
files = {
    'title': (None, 'Python API Example'),
    'slug': (None, 'python-api-example'),
    'content': (None, '# Hello from Python\n\nThis post was created using the Python API.'),
    'author_id': (None, '1'),
    'featured_image': ('image.jpg', open('path/to/image.jpg', 'rb'), 'image/jpeg')
}

response = requests.post('http://localhost:8000/blog/', files=files)
post = response.json()
print(f"Created post with ID: {post['id']}")
```

## Frontend Example

```javascript
// Example of creating a post from a React frontend
async function createBlogPost() {
  const formData = new FormData();
  formData.append('title', 'Frontend Post');
  formData.append('slug', 'frontend-post');
  formData.append('content', '# Posted from Frontend\n\nThis works great!');
  formData.append('author_id', '1');
  
  // Add featured image if available
  if (featuredImageFile) {
    formData.append('featured_image', featuredImageFile);
  }
  
  // Add tags if selected
  if (selectedTags.length > 0) {
    formData.append('tag_ids', selectedTags.join(','));
  }
  
  const response = await fetch('http://localhost:8000/blog/', {
    method: 'POST',
    body: formData,
  });
  
  const data = await response.json();
  console.log('Post created:', data);
}
```
