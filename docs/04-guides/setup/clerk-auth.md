# 🔐 Clerk Authentication Setup

> **Quick Start**: <PERSON><PERSON><PERSON> uses <PERSON> for secure authentication with JWT tokens, supporting social logins and user management.

## 📋 Overview

```mermaid
graph LR
    A[Frontend: React/Next.js] --> B[Clerk JWT <PERSON>]
    B --> C[Backend: Node.js/Express]
    C --> D[Database: Prisma/PostgreSQL]
    
    E[Clerk Dashboard] --> F[User Management]
    F --> G[Webhooks]
    G --> C
```

**Key Features:**
- 🔑 JWT token validation with JWKS
- 👥 User sign-up/sign-in flows  
- 🔄 Automatic user registration
- 🪝 Webhook integration
- 🛡️ Route protection middleware

---

## 🚀 Quick Setup

### 1️⃣ Prerequisites
- [Clerk account](https://clerk.dev) with application created
- Node.js project with Prisma database

### 2️⃣ Install Dependencies
```bash
# Frontend (new_ide/)
npm install @clerk/nextjs

# Backend (nodejs_backend/)
npm install jsonwebtoken jwk-to-pem @clerk/clerk-sdk-node
```

### 3️⃣ Environment Variables
```bash
# Frontend (.env.local)
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_...
CLERK_SECRET_KEY=sk_test_...
NEXT_PUBLIC_CLERK_SIGN_IN_URL=/sign-in
NEXT_PUBLIC_CLERK_AFTER_SIGN_IN_URL=/dashboard

# Backend (.env)
CLERK_API_KEY=sk_test_...
CLERK_SECRET_KEY=sk_test_...
CLERK_FRONTEND_API=https://your-app.clerk.accounts.dev
CLERK_JWKS_URL=https://your-app.clerk.accounts.dev/.well-known/jwks.json
CLERK_WEBHOOK_SECRET=whsec_...
```

---

## 🎨 Frontend Integration

### ClerkProvider Setup
```tsx
// app/layout.tsx
import { ClerkProvider } from '@clerk/nextjs';

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <ClerkProvider>
      <html lang="en">
        <body>{children}</body>
      </html>
    </ClerkProvider>
  );
}
```

### Route Protection
```tsx
// middleware.ts
import { clerkMiddleware } from "@clerk/nextjs/server";

export default clerkMiddleware();

export const config = {
  matcher: ['/((?!.*\\..*|_next).*)', '/', '/(api|trpc)(.*)'],
};
```

---

## ⚙️ Backend Integration

### Route Protection Middleware
```typescript
// Usage in routes
import { clerkAuthMiddleware } from '../middleware/clerk.middleware';

router.get('/protected', clerkAuthMiddleware, (req, res) => {
  // req.userId contains the authenticated Clerk user ID
  res.json({ userId: req.userId });
});
```

### Optional Authentication
```typescript
// For routes that work with/without auth
router.get('/public', optionalClerkAuthMiddleware, (req, res) => {
  const message = req.userId 
    ? `Hello user ${req.userId}` 
    : 'Hello guest';
  res.json({ message });
});
```

### Webhook Handler
```typescript
// Automatic user sync via webhooks
// Located at: /api/webhooks/clerk
// Handles: user.created, user.updated, user.deleted
```

---

## 🔧 Architecture Details

### Token Flow
```
1. User signs in → Clerk issues JWT
2. Frontend sends JWT to backend
3. Backend validates via JWKS (cached 1hr)
4. User ID extracted & attached to request
5. Database operations use Clerk user ID
```

### Database Integration
- Users linked via `clerk_id` field in User model
- Automatic user creation on first API call
- Webhook sync for user updates/deletions

### Caching Strategy
- JWKS cached for 1 hour to reduce API calls
- Fallback to cached JWKS during Clerk outages
- Development mode: relaxed validation

---

## 🧪 Testing & Verification

### Manual Testing
```bash
# Start services
cd nodejs_backend && npm run dev
cd new_ide && npm run dev

# Test flow:
# 1. Visit app → Sign in
# 2. Check browser network tab for JWT
# 3. Verify database user creation
# 4. Test protected API endpoints
```

### Automated Testing
```bash
# Run Clerk middleware tests
cd nodejs_backend
npm test -- --testPathPattern=clerk.middleware
```

---

## 🚨 Troubleshooting

| Issue | Solution |
|-------|----------|
| **401 Unauthorized** | ✅ Check environment variables<br/>✅ Verify token format (Bearer token)<br/>✅ Confirm JWKS URL accessibility |
| **User not created** | ✅ Check webhook endpoint<br/>✅ Verify webhook secret<br/>✅ Check database connection |
| **Token validation fails** | ✅ Ensure JWKS URL is correct<br/>✅ Check NODE_ENV setting<br/>✅ Verify token not expired |

### Debug Mode
```bash
# Enable detailed logging
DEBUG=clerk:* npm run dev
```

---

## 📚 Implementation Files

**Key Files Created:**
- `src/middleware/clerk.middleware.ts` - JWT verification
- `src/services/clerk.service.ts` - Clerk API integration  
- `src/routes/auth/index.ts` - Auth endpoints
- `src/routes/webhooks/index.ts` - Webhook handlers
- `src/common/types/authenticated-request.ts` - TypeScript types

**Migration Status:** ✅ Complete - Migrated from Python/FastAPI to Node.js/Express

---

## 🔗 Resources

- [Clerk Documentation](https://clerk.dev/docs)
- [Next.js Integration](https://clerk.dev/docs/nextjs)
- [Node.js Backend SDK](https://clerk.dev/docs/backend-requests/handling/nodejs)
