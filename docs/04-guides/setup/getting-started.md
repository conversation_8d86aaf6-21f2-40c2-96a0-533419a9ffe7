Check if the DB is running
    brew services info postgresql@17
    Connect to database: `psql -U kapiadmin -d kapi`

For DB:
    npx prisma generate (first time)
    migrations: npx prisma migrate dev --name init
    npx prisma db pull

    🧭 Summary of the project Setup:
Port 3000: Express app for admin routes + APIs
Port 3001: Next.js app for frontend (user-facing UI)



To enable exceptiosn for public urls: nodejs_backend/src/next/middleware.ts
Start the Main server:
    cd nodejs_backend
    npm run dev



