# KAPI Database Models

This document provides a quick reference for all database models in the KAPI platform. Each model is described with a concise one-line summary of its purpose.

## Core Models

| Model | Description |
|-------|-------------|
| User | Central user entity with authentication, profile, and relationship information |
| Project | Core project model representing a developer's workspace with backwards build components |
| Subscription | Tracks user subscription status, billing history, and plan details |
| ModelUsage | Records AI model usage metrics including tokens, cost, and context |
| UserSession | Tracks authenticated user sessions for security and auditing |

## Project Components (Backwards Build)

| Model | Description |
|-------|-------------|
| Objective | Project objectives and high-level goals |
| TechStack | Technology stack components used in a project |
| GitRepo | Git repository information with URL and authentication |
| Slide | Presentation slides created in the backwards build approach |
| Test | Test cases and code for the project |
| Documentation | Project documentation including API docs, architecture, user guides |
| CodeQuality | Metrics tracking code quality over time |
| Dependency | Project dependencies with versioning and security information |
| Visualization | Visual representations of project architecture and flows |

## Task Management

| Model | Description |
|-------|-------------|
| Task | Task management with backwards build integration and AI assistance tracking |
| TaskComment | Comments on tasks with code snippet support |
| TaskDependency | Tracks dependencies between tasks for workflow management |
| UserStory | Agile user stories with acceptance criteria and task relationships |

## Social and Community

| Model | Description |
|-------|-------------|
| Channel | Discussion channels for group interaction with project integration |
| SocialMessage | Social messages with code snippet and project artifact references |
| MessageReaction | User reactions to messages for engagement and karma tracking |
| Notification | System notifications for social interactions and project events |

## Q&A System

| Model | Description |
|-------|-------------|
| Question | Questions with code snippets and project context |
| Answer | Answers to questions with code snippets and acceptance tracking |
| Tag | Categorization tags for questions to improve discoverability |
| QuestionVote | Voting system for questions to highlight valuable content |
| AnswerVote | Voting system for answers to highlight valuable solutions |
| KarmaTransaction | Records karma points earned through community contributions |
| Bookmark | User bookmarks for saving valuable content |

## Learning System (Modern AI Pro)

| Model | Description |
|-------|-------------|
| Workshop | Structured workshop offering with syllabus and enrollment tracking |
| Module | Learning modules that organize lesson content |
| Lesson | Individual lessons with content, duration, and completion tracking |
| LessonCompletion | Records user completion of lessons with optional feedback |
| Resource | Learning resources attached to workshops and lessons |
| ProjectTemplate | Templates for workshop projects that connect to KAPI IDE |

## Code Analysis

| Model | Description |
|-------|-------------|
| CodeAnalysisRun | Tracks individual code analysis sessions with quality metrics |
| CodeAnalysisResult | Detailed findings from code analysis with fix suggestions |
| CodeMetric | Historical tracking of code quality metrics |
| AICodeSuggestion | AI-generated code improvements with implementation tracking |

## AI Integration

| Model | Description |
|-------|-------------|
| AIAgent | AI agents configured for specific workflows and capabilities |
| AIConversation | Conversation sessions with AI agents including context and file references |
| AIMessage | Individual messages in AI conversations with code and token metrics |
| AIModelUsage | Detailed tracking of AI model usage and costs |
| AIPromptTemplate | Reusable AI prompt templates for common tasks |
| AIAgentAction | Records actions taken by AI agents on projects and files |

## Template System

| Model | Description |
|-------|-------------|
| Template | Base template model for projects, components, documentation, tests, and slides |
| TemplateFile | Individual files within templates with variable support |
| TemplateVariable | Variables for template customization with validation |
| TemplateTag | Tags for template categorization and search |
| TemplateUsage | Tracks individual uses of templates with customization data |
| TemplateCollection | Curated collections of related templates |

## Blog System

| Model | Description |
|-------|-------------|
| BlogPost | Blog posts for knowledge sharing and marketing |
| BlogCategory | Categories for organizing blog content |
| BlogComment | User comments on blog posts |

## Gamification

| Model | Description |
|-------|-------------|
| Achievement | User achievements for completing platform milestones |
| EloHistory | Tracks changes in user ELO ratings over time |
| Badge | Visual recognition of user accomplishments |
| Challenge | Daily coding challenges with skill-based difficulty |
