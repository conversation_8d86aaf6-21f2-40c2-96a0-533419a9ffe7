# Archived IDE Technical Documentation

This directory contains legacy IDE technical documentation that has been consolidated into the main IDE product specification.

## Archived Files

- `ide_product_flow.md` - Electron application flow and entry points

## Consolidation Summary

The technical IDE flow documentation has been merged with the core IDE product spec at:
**[📁 /docs/02-products/core_ide/02-ide-core.md](../02-products/core_ide/02-ide-core.md)**

### What Was Merged

✅ **Electron Architecture Details**
- Main process (main.ts) initialization flow
- Renderer process (renderer.tsx) bootstrapping
- Preload bridge (preload.ts) security layer
- IPC communication patterns

✅ **Application Lifecycle**
- Complete startup sequence
- User interaction flows
- File operation examples
- Project opening workflow

✅ **Technical Implementation**
- Entry point execution order
- Process communication patterns
- Security best practices
- TypeScript integration

### Enhanced in Consolidation

🎨 **Visual Architecture Diagrams** - Added Mermaid diagrams for:
- Electron process structure
- Application lifecycle flow
- User interaction sequences
- Context system architecture

⚡ **Performance Optimizations** - Added details on:
- IPC message batching
- React optimization strategies
- Web worker usage
- Caching mechanisms

🏗️ **System Integration** - Connected to:
- Core IDE features and UI components
- AI service integration patterns
- File management system
- Terminal service architecture

## Purpose of Original Document

The `ide_product_flow.md` provided essential technical details about:

1. **Electron Process Architecture**: How main and renderer processes interact
2. **Entry Points**: Order of file execution during application startup  
3. **IPC Communication**: Secure communication between processes
4. **User Workflow Examples**: Step-by-step interaction patterns

This technical information is now integrated with product features to provide a complete understanding of both what the IDE does and how it works.

## Reference

For current IDE documentation, see: **[🖥️ IDE Core Features](../02-products/core_ide/02-ide-core.md)**