# Backend Technical Specification

_Last updated: April 30, 2025_

## Overview

This document outlines the technical specifications for the KAPI backend, focusing on user management, authentication, subscriptions, and payment processing. The backend is built with FastAPI and SQLAlchemy, using PostgreSQL as the database and Clerk for authentication.

## Tech Stack

| Component | Technology |
|-----------|------------|
| API Framework | FastAPI |
| ORM | SQLAlchemy + Alembic |
| Database | PostgreSQL on AWS RDS |
| Authentication | Clerk with JWT validation |
| Payment Processing | Stripe |
| Email | Mailgun |
| Deployment | AWS EC2 + Gunicorn + Nginx |

## Core Data Models

### User Management
- **Users**: Stores user profiles and authentication details
  - Supports multiple user roles: Free, Developer, and Admin
  - Tracks user activity and profile information
  - Integrates with Clerk for authentication

### Subscription Management
- **Subscriptions**: Manages user subscription states
  - Supports multiple tiers: Free, Developer, and Team
  - Tracks subscription status and billing periods
  - Integrates with Strip<PERSON> for subscription billing

### Payment Processing
- **Payments**: Handles all financial transactions
  - Supports multiple payment types: Workshop, Subscription, One-time
  - Integrates with various payment methods (Stripe, UPI)
  - Maintains comprehensive payment history and status tracking
  - Handles refunds and payment reconciliation

### Workshop Management
- **Workshops**: Manages Modern AI Pro workshop offerings
  - Supports four workshop types:
    1. Modern AI Pro Essentials (for AI beginners)
    2. Modern AI Pro Practitioner (for experts)
    3. Modern AI Pro Vibecoders (AI augmented coding)
    4. Modern AI Pro Agentic AI (enterprise implementation)
  - Tracks workshop schedules, participants, and payments
  - Manages workshop metadata (syllabus, location, cost)

## Key Features

### Authentication System
- JWT-based authentication using Clerk
- Role-based access control
- Secure session management
- OAuth integration for social logins

### Payment Integration
- Secure payment processing with Stripe
- Multiple payment method support
- Automated subscription billing
- Payment status tracking and notifications
- Refund handling

### Workshop System
- Workshop enrollment and management
- Participant tracking
- Payment processing for workshops
- Schedule management
- Geographic location support

### Database Design
- Optimized indexes for common queries
- Proper relationship management
- Audit trails for important operations
- UTC timestamp standardization

## Security Considerations

1. **Authentication**
   - JWT token validation
   - Role-based access control
   - Session management
   - Rate limiting

2. **Payment Security**
   - PCI compliance through Stripe
   - Secure payment information handling
   - Audit logging for financial transactions

3. **Data Protection**
   - Encrypted sensitive data
   - Secure password handling
   - GDPR compliance measures

## Deployment Architecture

1. **Production Environment**
   - AWS EC2 for application hosting
   - AWS RDS for database
   - Nginx as reverse proxy
   - Gunicorn for WSGI serving

2. **Monitoring and Logging**
   - Application performance monitoring
   - Error tracking and reporting
   - Transaction monitoring
   - Security audit logging

## API Documentation

The API documentation is available through:
1. OpenAPI/Swagger UI at `/docs`
2. ReDoc at `/redoc`
3. Detailed endpoint documentation in separate API spec files
