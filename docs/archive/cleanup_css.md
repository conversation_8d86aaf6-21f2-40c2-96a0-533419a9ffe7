# CSS Cleanup Documentation

## Original Styling Strategy
You have a strong mix of:
- The original template's very specific component and layout classes (like sec-title, template-btn, price-block_one, cta-one, about-one, main-menu, auto-container, row, col-lg-*).
- Bootstrap grid/flex classes (row, col-*, d-flex).
- Pure Tailwind utility classes (mostly added in Terms.tsx, the second CTA.tsx, and parts of Footer.tsx, AIAnswers.tsx).
- Custom classes and inline styles you added for new sections or elements.
- Classes needed purely for JavaScript interaction (tab-btn, accordion, wow, odometer, Swiper classes, mobile menu toggles).

## Recommended Long-Term Approach
Option B (Tailwind-First Migration): Stop loading bootstrap.css and significantly strip down style.css in layout.tsx. Replace all Bootstrap grid/flex classes (row, col-*, d-flex, etc.) with Tailwind's grid and flex utilities (grid, grid-cols-*, flex, flex-col, justify-between, items-center, gap-*, container, mx-auto). Replicate the styles of the original template's component classes (sec-title, template-btn, price-block_one, etc.) using pure Tailwind utilities applied directly to the JSX or using Tailwind's @apply within custom CSS classes. This is more work upfront but results in a cleaner, more consistent, and more maintainable codebase using a single styling system.

## Implemented Solution: Restricting Theme Styles to Index Page Only

As a first step toward cleaning up the CSS, we've implemented a solution to restrict theme-specific styles to only the index page (`/`) while ensuring other pages like `/blog`, `/admin`, etc. use minimal styling.

### Key Components

1. **Middleware**: Added a custom middleware that sets the current pathname as a header
2. **Conditional Loading**: Updated the root layout to conditionally load theme-specific styles based on the current path
3. **Index-specific CSS**: Created a dedicated CSS file for index-specific styles
4. **Minimal Global CSS**: Kept only essential styles in the global CSS file

### Files Modified

#### 1. `frontend/src/middleware.ts`

Updated to include pathname information in headers:

```typescript
import { clerkMiddleware, createRouteMatcher } from "@clerk/nextjs/server";
import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";

// Create a middleware function that combines Clerk's middleware with our custom logic
const middleware = (req: NextRequest) => {
  // Call Clerk's middleware
  const clerkResponse = clerkMiddleware()(req);

  // Get the pathname
  const pathname = req.nextUrl.pathname;

  // Create a new response by cloning the Clerk response
  const response = NextResponse.next();

  // Add the pathname as a header
  response.headers.set("x-pathname", pathname);

  return response;
};

export default middleware;

export const config = {
  matcher: [
    // Skip Next.js internals and all static files, unless found in search params
    '/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)',
    // Always run for API routes
    '/(api|trpc)(.*)',
  ],
};
```

#### 2. `frontend/src/app/layout.tsx`

Updated to conditionally load theme-specific styles and scripts:

```typescript
import './styles/globals.css';
import type { Metadata } from 'next';
import { Inter, Lora } from 'next/font/google';
import Script from 'next/script';
import { ClerkProvider } from '@clerk/nextjs';
import { headers } from 'next/headers';

// Font definitions...

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  // Get the current path to determine if we're on the index page
  const headersList = headers();
  const pathname = headersList.get('x-pathname') || '';
  const isIndexPage = pathname === '/';

  return (
    <ClerkProvider>
      <html lang="en">
        {/* Theme-specific styles only for the index page */}
        {isIndexPage && (
          <>
            <link rel="stylesheet" href="/assets/css/bootstrap.css" precedence="high" />
            <link rel="stylesheet" href="/assets/css/animate.css" precedence="high" />
            <link rel="stylesheet" href="/assets/css/custom-animate.css" precedence="high" />
            <link rel="stylesheet" href="/assets/css/jquery-ui.css" precedence="high" />
            <link rel="stylesheet" href="/assets/css/swiper.min.css" precedence="high" />
            <link rel="stylesheet" href="/assets/css/icomoon-style.css" precedence="high" />
            <link rel="stylesheet" href="/assets/css/font-awesome.css" precedence="high" />
            <link rel="stylesheet" href="/assets/css/magnific-popup.css" precedence="high" />
            <link rel="stylesheet" href="/assets/css/odometer-theme-default.css" precedence="high" />
            <link rel="stylesheet" href="/assets/css/jquery.bootstrap-touchspin.css" precedence="high" />
            <link rel="stylesheet" href="/assets/css/global.css" precedence="high" />
            <link rel="stylesheet" href="/assets/css/header.css" precedence="high" />
            <link rel="stylesheet" href="/assets/css/footer.css" precedence="high" />
            <link rel="stylesheet" href="/assets/css/style.css" precedence="high" />
            <link rel="stylesheet" href="/assets/css/meanmenu.min.css" precedence="high" />
            <link rel="stylesheet" href="/assets/css/responsive.css" precedence="low" />
          </>
        )}

        {/* Error handling script */}
        <Script id="message-channel-error-handler" strategy="afterInteractive">
          {/* Script content */}
        </Script>

        <body className={`${lora.variable} ${inter.variable}`}>
          {children}

          {/* Theme-specific scripts only for the index page */}
          {isIndexPage && (
            <>
              <Script src="/assets/js/jquery.js" strategy="beforeInteractive" />
              <Script src="/assets/js/popper.min.js" strategy="afterInteractive" />
              <Script src="/assets/js/bootstrap.min.js" strategy="afterInteractive" />
              {/* Additional scripts... */}
              <Script src="/assets/js/script.js" strategy="lazyOnload" />
              <Script src="/assets/js/custom-menu.js" strategy="lazyOnload" />
            </>
          )}
        </body>
      </html>
    </ClerkProvider>
  );
}
```

#### 3. `frontend/src/app/styles/globals.css`

Simplified to include only essential styles:

```css
@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
  --font-lora: var(--font-lora);
  --font-inter: var(--font-inter);
}

/* Force light mode for all users */
@media (prefers-color-scheme: dark) {
  :root {
    --background: #ffffff;
    --foreground: #171717;
  }
}

body {
  font-family: var(--font-inter), sans-serif;
  background: #ffffff;
  color: #000000;
  line-height: 1.6;
}

/* Minimal styles for non-index pages */
h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  color: #000000;
  margin-bottom: 1rem;
}

p {
  margin-bottom: 1rem;
}
```

#### 4. `frontend/src/app/styles/index-theme.css`

Created a new file with index-specific styles:

```css
/* This file contains styles that should only be applied to the index page */

/* Theme Color */
:root {
  /* Color variables... */
}

/* Typography styles for the index page */
h1 {
  font-size: 2.5rem;
  font-weight: 700;
  margin-top: 2rem;
  margin-bottom: 1.5rem;
  color: #000000;
}

/* Additional styles... */

/* Essential styles for the page wrapper */
.page-wrapper {
  position: relative;
  margin: 0 auto;
  width: 100%;
  min-width: 300px;
  overflow: hidden;
}

/* Auto container */
.auto-container {
  position: static;
  max-width: 1200px;
  padding: 0px 15px;
  margin: 0 auto;
}

/* Additional component-specific styles... */
```

#### 5. `frontend/src/app/page.tsx`

Updated to import index-specific styles:

```typescript
'use client';

import { usePreloader } from './hooks/usePreloader';
import Header from './components/Header';
// Other component imports...

// Import theme styles only for the index page
import './styles/index-theme.css';

export default function Home() {
  // Use the preloader hook to hide the preloader after the page loads
  usePreloader();

  return (
    <div className="page-wrapper">
      {/* Page content... */}
    </div>
  );
}
```

### How It Works

1. When a user visits any page, the middleware adds the current pathname as a header
2. The root layout checks this pathname and only loads theme-specific styles and scripts if the pathname is "/"
3. The index page imports its own specific CSS file with additional styles
4. Other pages only get the minimal styles from globals.css

### Benefits

1. **Performance**: Other pages load faster without unnecessary CSS
2. **Maintainability**: Clearer separation of concerns between index page and other pages
3. **Flexibility**: Other pages can have their own distinct styling without conflicts

## Recent Fixes: Header and Menu Styling

After the initial CSS cleanup, we encountered issues with the Header component and mobile menu. The following fixes were implemented:

1. **Menu CSS Import**: Added explicit import of menu.css in page.tsx
2. **Mobile Menu Toggler**: Fixed the mobile menu toggler to only display on mobile devices
3. **Mobile Menu Styling**: Enhanced mobile menu styles to ensure proper display and transitions
4. **Additional Header Fixes**: Created a header-fix.css file with specific overrides for the Header component

### Files Modified

#### 1. `frontend/src/app/page.tsx`

Updated to import menu.css and header-fix.css:

```typescript
// Import theme styles only for the index page
import './styles/index-theme.css';
import './styles/menu.css';
import './styles/header-fix.css';
```

#### 2. `frontend/src/app/components/Header.tsx`

Fixed the mobile menu toggler to only display on mobile:

```jsx
{/* Mobile Navigation Toggler - Position controlled by CSS */}
<div className="mobile-nav-toggler d-md-none d-block" onClick={toggleMobileMenu}>
  <svg xmlns="http://www.w3.org/2000/svg" className="icon icon-tabler icon-tabler-menu-2" width="24" height="24" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" fill="none" strokeLinecap="round" strokeLinejoin="round">
    <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
    <path d="M4 6l16 0" />
    <path d="M4 12l16 0" />
    <path d="M4 18l16 0" />
  </svg>
</div>
```

#### 3. `frontend/src/app/styles/header-fix.css`

Created a new file with specific fixes for the Header component:

```css
/* Additional fixes for the Header component */

/* Fix for mobile menu toggler */
.mobile-nav-toggler {
  display: none !important;
}

@media (max-width: 767px) {
  .mobile-nav-toggler {
    display: block !important;
  }
}

/* Fix for mobile menu */
.mobile-menu {
  display: none;
}

.mobile-menu-visible .mobile-menu {
  display: block !important;
}

/* Fix for mobile menu backdrop */
.mobile-menu .menu-backdrop {
  display: none;
}

.mobile-menu-visible .mobile-menu .menu-backdrop {
  display: block !important;
}

/* Fix for mobile menu box */
.mobile-menu .menu-box {
  right: -300px;
}

.mobile-menu-visible .mobile-menu .menu-box {
  right: 0 !important;
}

/* Fix for header layout on mobile */
@media (max-width: 767px) {
  .main-header .inner-container .d-flex {
    flex-direction: row !important;
    justify-content: space-between !important;
    align-items: center !important;
  }
}
```

## Next Steps

1. **Testing**: Verify that the index page displays correctly with all styles, especially the Header and mobile menu
2. **Refinement**: Adjust the index-specific styles as needed
3. **Component Styling**: Consider moving component-specific styles to their respective components
4. **Tailwind Migration**: Continue with the long-term plan to migrate to Tailwind-first styling