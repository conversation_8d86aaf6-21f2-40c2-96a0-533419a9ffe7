# Documentation Cleanup Plan

_Created: May 31, 2025_

## Overview

This plan outlines the reorganization of the `/docs/02-products/` directory to improve discoverability, reduce redundancy, and create a clearer structure for product documentation.

## Current Issues

1. **Content Overlap**: Multiple files contain similar information about the same features
2. **Scope Creep**: `features/01-ide-feature-list.md` has expanded beyond IDE features to include system-wide concepts
3. **Deep Nesting**: Current structure creates 4-5 levels of directory nesting, making discovery difficult
4. **Redundancy**: Features like adaptive modes and conversation management are documented in multiple places
5. **Unclear Boundaries**: Difficult to determine where specific content belongs

## Proposed Structure

### New Directory Layout

```
/docs/02-products/
├── README.md                        # Products overview and navigation guide
├── 00-system-architecture.md        # High-level system design and components -- [DONE]
TODO
├── 01-ide-core.md                  # Core IDE features (editor, terminal, explorer)
├── 02-ai-agents.md                 # Agent system and conversation management
├── 03-interaction-modes.md         # Voice, sketch, text multi-modal interfaces
├── 04-backwards-build.md           # Development methodology and workflow
├── 05-community-features.md        # Karma, chat, collaboration, Q&A
├── 06-project-management.md        # Adaptive modes, setup, lifecycle
├── 07-templates-library.md         # Boilerplates and starter projects
├── 08-quality-assurance.md         # Testing, linting, code standards
├── 09-token-cost-management.md     # Usage tracking and optimization
├── agent-experience.md             # (keep existing)
├── user-experience.md              # (keep existing)
├── user-flow.md                    # (keep existing)
├── known-issues.md                 # (keep existing)
└── features/                       # Specific feature deep-dives
    ├── voice-agent.md
    ├── sketch-to-code.md
    ├── mermaid-support.md
    ├── deep-search.md
    ├── visual-code-representation.md
    ├── learn-while-build.md
    ├── smart-rendering.md
    └── [other existing feature files]
```

## Content Migration Plan

### Phase 1: Create New Structure Files

**Task 1.1**: Create `00-system-architecture.md`
- Extract system overview from `features/01-ide-feature-list.md`
- Move content from `features/00-key-components.md`
- Add component relationship diagrams
- Include integration points between systems

**Task 1.2**: Create numbered core documents (empty templates)
- Create files 01 through 09 with basic headers
- Add table of contents to each
- Include "Content to be migrated" placeholders

### Phase 2: Migrate IDE Features

**Task 2.1**: Populate `01-ide-core.md`
- Move "Core Components" section from `features/01-ide-feature-list.md`
- Integrate content from `features/core-ide-features.md`
- Include: Project Explorer, Code Editor, Terminal, UI/UX specs
- Add cross-references to related features

**Task 2.2**: Clean up redundant IDE content
- Remove migrated content from source files
- Update references in other documents
- Archive or delete empty files

### Phase 3: Migrate AI and Agent Features

**Task 3.1**: Populate `02-ai-agents.md`
- Move "Agent Architecture" from `features/01-ide-feature-list.md`
- Move "Conversation Management" section
- Integrate `features/conversation-management.md`
- Add `features/hybrid-agent-spec.md` content

**Task 3.2**: Update agent-related files
- Update `agent-experience.md` to reference new structure
- Remove redundant content
- Ensure no duplicate information

### Phase 4: Migrate Interaction Modes ✅ [COMPLETED]

**Task 4.1**: Populate `03-interaction-modes.md` ✅
- Move "Core Interaction Modes" from `features/01-ide-feature-list.md` ✅
- Integrate voice, sketching, text sections ✅
- Add multi-device support details ✅
- Include platform-specific features ✅

**Task 4.2**: Update feature deep-dives ✅
- Ensure `features/voice-agent.md` focuses on implementation ✅
- Update `features/sketch-to-code.md` for technical details ✅
- Remove high-level descriptions (now in core doc) ✅

### Phase 5: Migrate Methodology and Workflow ✅ [COMPLETED]

**Task 5.1**: Populate `04-backwards-build.md` ✅
- Extract backwards build content from multiple files ✅
- Create comprehensive methodology guide ✅
- Include benefits and implementation details ✅
- Add workflow diagrams ✅

**Task 5.2**: Populate `07-templates-library.md` ✅
- Move template content from `features/01-ide-feature-list.md` ✅
- Integrate MVP templates from `features/mvp-features.md` ✅
- Organize by category ✅
- Include token reduction metrics ✅

### Phase 6: Migrate Community Features ✅ [COMPLETED]

**Task 6.1**: Populate `05-community-features.md` ✅
- Move "Social & Community Features" section ✅
- Integrate `features/karma-system.md` ✅
- Add collaboration details ✅
- Include gamification elements ✅

### Phase 7: Migrate Project Management ✅ [COMPLETED]

**Task 7.1**: Populate `06-project-management.md` ✅
- Move adaptive modes content ✅
- Integrate `features/adaptive-modes.md` ✅
- Add project setup details ✅
- Include lifecycle management ✅

### Phase 8: Quality and Cost Management ✅ [COMPLETED]

**Task 8.1**: Populate `08-quality-assurance.md` ✅
- Extract quality features from various files ✅
- Add testing methodology ✅
- Include best practices ✅

**Task 8.2**: Populate `09-token-cost-management.md` ✅
- Move token usage content ✅
- Add cost optimization strategies ✅
- Include budget management features ✅

### Phase 9: Cleanup and Validation ✅ [COMPLETED]

**Task 9.1**: Update README and navigation ✅
- Create comprehensive README.md for products ✅
- Add navigation guides ✅
- Include feature matrix ✅

**Task 9.2**: Remove redundant files ✅
- Archive `features/01-ide-feature-list.md` ✅
- Remove empty or redundant files ✅
- Update all cross-references ✅

**Task 9.3**: Validation ✅
- Check for broken links ✅
- Ensure no content was lost ✅
- Verify consistent formatting ✅
- Test navigation flow ✅

## Execution Guidelines

### For Each Migration Task:

1. **Copy** content to new location (don't move initially)
2. **Edit** content to fit new structure and remove redundancy
3. **Update** cross-references and links
4. **Verify** no information is lost
5. **Delete** source content only after validation

### Quality Checks:

- Each document should have a clear, single purpose
- No significant content duplication between files
- Consistent formatting and structure
- Working cross-references
- Logical flow within each document

### Documentation Standards:

- Start each file with title and "Last updated" date
- Include table of contents for longer documents
- Use consistent heading hierarchy
- Add "See also" sections for related content
- Include examples where helpful

## Success Criteria

1. **Reduced Nesting**: Maximum 3 levels deep (`/docs/02-products/features/`)
2. **Clear Organization**: Obvious where each piece of content belongs
3. **No Redundancy**: Each concept documented in one primary location
4. **Easy Navigation**: Numbered files provide clear reading order
5. **Comprehensive Coverage**: No features or concepts lost in migration

## Timeline Estimate

- Phase 1: 30 minutes (create structure)
- Phase 2-8: 45-60 minutes each (content migration)
- Phase 9: 60 minutes (cleanup and validation)
- **Total**: 6-8 hours of focused work

## Notes

- This can be done incrementally over multiple sessions
- Each phase is independent after Phase 1
- Consider using git branches for major changes
- Keep backups of original files until migration is validated

---

_This plan provides a systematic approach to reorganizing product documentation while maintaining quality and completeness._
