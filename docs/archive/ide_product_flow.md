# Entry Points and Typical Flow in KAPI
Electron applications have two main processes, and KAPI follows this standard architecture:
1. Main Process (Node.js Environment)

Entry Point: main.ts
This is the first file executed when the application starts
Responsible for:

Creating the application window (BrowserWindow)
Setting up IPC (Inter-Process Communication) handlers
Registering custom protocols
Handling application lifecycle events (quit, window close)
Managing file system operations
Terminal process management



2. Renderer Process (Chromium Browser Environment)

Entry Point: renderer.tsx
This loads and initializes the React application
Renders the App component into the DOM
Sets up theme providers and global styles

3. Bridge Between Processes: preload.ts

Securely exposes main process functionality to the renderer
Uses contextBridge to create a safe API
Enables the React app to communicate with the main process

The Flow of Execution

When the app starts, main.ts is executed first

It initializes the Electron application
Creates a browser window
Sets up IPC handlers for file system, terminal, etc.


The main process loads preload.js (compiled from preload.ts)

This creates the bridge between main and renderer processes
Exposes APIs like file operations, terminal management


The main process loads index.html which includes renderer.js

renderer.tsx is the entry point for the React application
It sets up the theme and renders the root App component


App.tsx is the root React component

Sets up the application state and context providers
Renders the main application layout
Handles window management through the exposed electron API

Specific Example Flow

User opens the application

main.ts creates the application window
preload.ts exposes APIs
renderer.tsx sets up React and renders App.tsx


User clicks "Open Project"

App.tsx component calls window.electronAPI.selectDirectory()
This is bridged through preload.ts to main.ts
main.ts shows the native file dialog and returns selected directory
React components update to display the project files


User edits a file

Editor component handles edits in memory
When saving, it calls window.electronAPI.saveFile()
main.ts writes to the file system