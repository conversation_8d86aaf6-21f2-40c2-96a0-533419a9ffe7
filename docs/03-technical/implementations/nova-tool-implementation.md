# 🛠️ Nova Sonic Tool Calling Implementation Guide

> **Technical specification for integrating Amazon Nova Sonic's tool calling capabilities into KAPI IDE's Canvas Mode**

**Related Documentation:** [🎙️ Voice Agent Product Spec](../../02-products/ai_assistance/06-voice-agent.md)

## 📋 Table of Contents

1. [Overview](#overview)
2. [Architecture](#architecture)
3. [Tool Definitions](#tool-definitions)
4. [Implementation Steps](#implementation-steps)
5. [Code Examples](#code-examples)
6. [Testing & Validation](#testing--validation)
7. [Security Considerations](#security-considerations)
8. [Performance Optimization](#performance-optimization)

---

## 🌟 Overview

Amazon Nova Sonic's tool calling capability enables the voice agent to execute specific IDE actions through natural language commands. This implementation transforms voice commands like "finish this mockup for me" into concrete actions within Canvas Mode.

### Key Features
- **Native Tool Calling**: Direct integration with Nova Sonic's tool use API
- **Chain-of-Thought Reasoning**: Transparent AI decision-making process
- **Multi-Tool Workflows**: Complex commands trigger sequences of tools
- **Error Recovery**: Graceful handling of tool execution failures
- **Real-time Feedback**: Visual and auditory confirmation of actions

### Technical Requirements
- Amazon Nova Sonic API access
- WebSocket server for bidirectional communication
- Canvas Mode integration in KAPI IDE
- File system access via Electron APIs

---

## 🏗️ Architecture

```mermaid
graph TB
    subgraph "Frontend Layer"
        VOICE[Voice Input]
        CANVAS[Canvas Mode UI]
        WS_CLIENT[WebSocket Client]
    end
    
    subgraph "Nova Sonic Service"
        WS_SERVER[WebSocket Server]
        NOVA_CLIENT[Nova Sonic Client]
        TOOL_REGISTRY[Tool Registry]
    end
    
    subgraph "Tool Execution"
        ANALYZER[Sketch Analyzer]
        DESIGNER[Design Improver]
        CODEGEN[Code Generator]
        FILEOPS[File Operations]
    end
    
    subgraph "Amazon Nova"
        NOVA_API[Nova Sonic API]
        TOOL_ENGINE[Tool Selection Engine]
        REASONING[Chain-of-Thought]
    end
    
    VOICE --> WS_CLIENT
    CANVAS --> WS_CLIENT
    WS_CLIENT <--> WS_SERVER
    WS_SERVER <--> NOVA_CLIENT
    NOVA_CLIENT <--> NOVA_API
    NOVA_API --> TOOL_ENGINE
    TOOL_ENGINE --> REASONING
    REASONING --> NOVA_API
    NOVA_CLIENT --> TOOL_REGISTRY
    TOOL_REGISTRY --> ANALYZER
    TOOL_REGISTRY --> DESIGNER
    TOOL_REGISTRY --> CODEGEN
    TOOL_REGISTRY --> FILEOPS
    
    style NOVA_API fill:#ff9800
    style TOOL_ENGINE fill:#4caf50
    style REASONING fill:#2196f3
```

### Data Flow

1. **Voice Command** → WebSocket → Nova Sonic Service
2. **Nova Sonic** analyzes intent with chain-of-thought reasoning
3. **Tool Selection** based on command context
4. **Tool Execution** in Canvas Mode
5. **Result Return** → Nova Sonic → Natural language response
6. **TTS Output** → User feedback

---

## 🔧 Tool Definitions

### Canvas Mode Tools

```typescript
// canvas-tools.ts
export const canvasTools: ToolHandler[] = [
  {
    name: "analyzeSketch",
    description: "Analyze the current sketch/mockup on the canvas and provide design recommendations",
    schema: JSON.stringify({
      "type": "object",
      "properties": {
        "instructions": {
          "type": "string",
          "description": "Specific analysis instructions or what aspects to focus on"
        },
        "focusAreas": {
          "type": "array",
          "items": { "type": "string" },
          "description": "Areas to focus on: layout, colors, usability, accessibility"
        }
      },
      "required": []
    })
  },
  
  {
    name: "improveDesign",
    description: "Generate an improved version of the current mockup. Use when user asks to 'finish', 'complete', or 'improve' their design",
    schema: JSON.stringify({
      "type": "object",
      "properties": {
        "designStyle": {
          "type": "string",
          "description": "Design style: modern, minimalist, professional, playful"
        },
        "targetPlatform": {
          "type": "string",
          "description": "Target platform: web, mobile, desktop"
        },
        "improvements": {
          "type": "string",
          "description": "Specific improvements to make"
        }
      },
      "required": []
    })
  },
  
  {
    name: "generateCode",
    description: "Generate React/HTML/Vue code from the current design mockup",
    schema: JSON.stringify({
      "type": "object",
      "properties": {
        "framework": {
          "type": "string",
          "description": "Framework: react, html, vue, svelte"
        },
        "typescript": {
          "type": "boolean",
          "description": "Whether to use TypeScript"
        },
        "componentName": {
          "type": "string",
          "description": "Name for the generated component"
        }
      },
      "required": []
    })
  }
  // ... additional tools
];
```

### Tool Selection Matrix

| Voice Command | Selected Tool | Parameters |
|--------------|---------------|------------|
| "Finish this mockup" | `improveDesign` | `{ designStyle: "modern" }` |
| "Analyze my sketch" | `analyzeSketch` | `{ focusAreas: ["layout", "usability"] }` |
| "Generate React code" | `generateCode` | `{ framework: "react" }` |
| "Save as HomePage.jsx" | `createFile` | `{ filename: "HomePage.jsx", path: "src" }` |
| "Clear the canvas" | `clearCanvas` | `{ preserveBackground: false }` |

---

## 📝 Implementation Steps

### 1. Update Nova Sonic Types

```typescript
// types.ts
export interface ToolUseBlock {
  toolUseId: string;
  name: string;
  input: Record<string, any>;
}

export interface ToolResultBlock {
  toolUseId: string;
  content: Array<{
    json?: Record<string, any>;
    text?: string;
  }>;
  status: 'success' | 'error';
}

export interface ToolConfiguration {
  tools: Array<{
    toolSpec: {
      name: string;
      description: string;
      inputSchema: {
        json: Record<string, any>;
      };
    };
  }>;
  toolChoice?: {
    auto?: {};
    any?: {};
    tool?: { name: string };
  };
}
```

### 2. Extend Nova Sonic Client

```typescript
// client.ts additions
class NovaSonicWebSocketClient {
  private tools: ToolHandler[] = [];
  private onToolUse?: (toolName: string, input: any) => Promise<any>;
  
  public registerTools(tools: ToolHandler[]): void {
    this.tools = tools;
    console.log(`[NovaSonic] Registered ${tools.length} tools`);
  }
  
  public setToolUseCallback(
    callback: (toolName: string, input: any) => Promise<any>
  ): void {
    this.onToolUse = callback;
  }
  
  // Updated session initialization with tools
  public async initializeSession(prompt: string): Promise<void> {
    const toolConfig = this.buildToolConfiguration();
    
    const initMessage = {
      sessionConfiguration: {
        // ... existing config
        text: {
          modelConfiguration: {
            // ... existing model config
            toolConfig,
            inferenceConfig: {
              maxTokens: 1000,
              topP: 1,
              temperature: 1
            },
            additionalModelRequestFields: {
              inferenceConfig: { topK: 1 } // Greedy decoding for tools
            }
          }
        }
      }
    };
    
    this.sendMessage(initMessage);
  }
  
  private async handleToolUse(toolUse: ToolUseBlock): Promise<void> {
    console.log('[NovaSonic] Tool use requested:', toolUse);
    
    if (this.onToolUse) {
      try {
        const result = await this.onToolUse(toolUse.name, toolUse.input);
        await this.sendToolResult(toolUse.toolUseId, result, 'success');
      } catch (error) {
        await this.sendToolResult(
          toolUse.toolUseId,
          { error: error.message },
          'error'
        );
      }
    }
  }
}
```

### 3. Canvas Mode Integration

```typescript
// CanvasMode.tsx
import { canvasTools } from '../../../services/nova-sonic-service/src/canvas-tools';

const CanvasMode: React.FC = () => {
  // Tool execution handler
  const handleToolExecution = useCallback(async (
    toolName: string,
    input: any
  ) => {
    setIsExecutingTool(true);
    
    try {
      switch (toolName) {
        case 'analyzeSketch':
          return await executeSketchAnalysis(input);
          
        case 'improveDesign':
          return await executeDesignImprovement(input);
          
        case 'generateCode':
          return await executeCodeGeneration(input);
          
        // ... other tools
      }
    } finally {
      setIsExecutingTool(false);
    }
  }, [/* dependencies */]);
  
  // Register tools when Nova Sonic connects
  useEffect(() => {
    if (isConnected && window.novaSonicClient) {
      window.novaSonicClient.registerTools(canvasTools);
      window.novaSonicClient.setToolUseCallback(handleToolExecution);
    }
  }, [isConnected, handleToolExecution]);
};
```

### 4. Tool Execution Implementation

```typescript
// Tool execution functions
async function executeSketchAnalysis(input: any) {
  const canvas = document.querySelector('.sketch-canvas canvas') as HTMLCanvasElement;
  if (!canvas) throw new Error('No canvas found');
  
  return new Promise((resolve) => {
    canvas.toBlob(async (blob) => {
      if (!blob) {
        resolve({ error: 'Failed to capture canvas' });
        return;
      }
      
      // Trigger existing analysis flow
      await handleAnalyzeSketch(blob, input.instructions);
      resolve({
        success: true,
        message: 'Sketch analysis started',
        focusAreas: input.focusAreas
      });
    });
  });
}

async function executeDesignImprovement(input: any) {
  // Capture current canvas
  const canvas = captureCanvas();
  
  // Build improvement instructions
  const instructions = `
    Improve this design with:
    - Style: ${input.designStyle}
    - Platform: ${input.targetPlatform}
    - Improvements: ${input.improvements}
    
    Generate a polished SVG mockup.
  `;
  
  // Send to multimodal AI
  const result = await sendMultimodalMessage({
    images: [canvas],
    prompt: instructions,
    outputFormat: 'svg'
  });
  
  // Update canvas with improved design
  if (result.svgContent) {
    await saveSvgAndRenderInCanvas(result.svgContent);
  }
  
  return {
    success: true,
    message: 'Design improved',
    parameters: input
  };
}
```

---

## 💻 Code Examples

### Voice Command Processing Flow

```typescript
// Example: "Finish this mockup for me"
async function processVoiceCommand(command: string) {
  // 1. Nova Sonic processes with chain-of-thought
  // <thinking>
  // The user wants me to complete their mockup. I should:
  // 1. Analyze what's currently on the canvas
  // 2. Use the improveDesign tool to enhance it
  // 3. Make it look professional and complete
  // </thinking>
  
  // 2. Tool selection response
  const toolUse = {
    toolUseId: "tooluse_abc123",
    name: "improveDesign",
    input: {
      designStyle: "modern",
      targetPlatform: "web",
      improvements: "Complete unfinished areas, add polish"
    }
  };
  
  // 3. Canvas Mode executes tool
  const result = await handleToolExecution(toolUse.name, toolUse.input);
  
  // 4. Send result back to Nova
  await sendToolResult(toolUse.toolUseId, result, 'success');
  
  // 5. Nova responds naturally
  // "I've improved your design with a modern web style. 
  //  The mockup now includes [description of improvements]..."
}
```

### Multi-Tool Workflow

```typescript
// Example: "Analyze this, improve it, and save as React code"
async function executeMultiToolWorkflow() {
  const workflow = [
    {
      tool: "analyzeSketch",
      input: { focusAreas: ["layout", "usability"] }
    },
    {
      tool: "improveDesign",
      input: { designStyle: "modern" }
    },
    {
      tool: "generateCode",
      input: { framework: "react", typescript: true }
    },
    {
      tool: "createFile",
      input: { 
        filename: "ImprovedComponent.tsx",
        path: "src/components"
      }
    }
  ];
  
  for (const step of workflow) {
    const result = await executeToolStep(step);
    if (!result.success) break;
  }
}
```

---

## 🧪 Testing & Validation

### Unit Tests for Tool Handlers

```typescript
// canvas-tools.test.ts
describe('Canvas Tools', () => {
  it('should analyze sketch with correct parameters', async () => {
    const mockBlob = new Blob([''], { type: 'image/png' });
    const input = {
      instructions: 'Analyze for accessibility',
      focusAreas: ['accessibility', 'colors']
    };
    
    const result = await executeSketchAnalysis(input);
    
    expect(result.success).toBe(true);
    expect(result.focusAreas).toEqual(input.focusAreas);
  });
  
  it('should handle tool execution errors gracefully', async () => {
    const input = { toolName: 'nonexistent' };
    
    const result = await handleToolExecution('nonexistent', input);
    
    expect(result.error).toBeDefined();
    expect(result.error).toContain('Unknown tool');
  });
});
```

### Integration Tests

```typescript
// nova-integration.test.ts
describe('Nova Sonic Integration', () => {
  it('should process voice command end-to-end', async () => {
    const client = new NovaSonicWebSocketClient();
    await client.connect();
    
    // Register mock tools
    client.registerTools(mockCanvasTools);
    client.setToolUseCallback(mockToolHandler);
    
    // Send voice command
    await client.sendAudio(mockAudioBuffer);
    
    // Verify tool was called
    expect(mockToolHandler).toHaveBeenCalledWith(
      'improveDesign',
      expect.any(Object)
    );
  });
});
```

---

## 🔒 Security Considerations

### Input Validation

```typescript
// Validate tool inputs before execution
function validateToolInput(toolName: string, input: any): boolean {
  const schema = toolSchemas[toolName];
  if (!schema) return false;
  
  // Validate against JSON schema
  const validator = new Ajv();
  const valid = validator.validate(schema, input);
  
  if (!valid) {
    console.error('Invalid tool input:', validator.errors);
    return false;
  }
  
  // Additional security checks
  if (toolName === 'createFile') {
    // Prevent path traversal
    if (input.path?.includes('..')) return false;
    // Limit to project directory
    if (!isWithinProjectRoot(input.path)) return false;
  }
  
  return true;
}
```

### Rate Limiting

```typescript
// Prevent tool abuse
const toolRateLimiter = new Map<string, number[]>();

function checkToolRateLimit(toolName: string): boolean {
  const now = Date.now();
  const windowMs = 60000; // 1 minute
  const maxRequests = 10;
  
  const requests = toolRateLimiter.get(toolName) || [];
  const recentRequests = requests.filter(t => now - t < windowMs);
  
  if (recentRequests.length >= maxRequests) {
    return false; // Rate limit exceeded
  }
  
  recentRequests.push(now);
  toolRateLimiter.set(toolName, recentRequests);
  return true;
}
```

---

## ⚡ Performance Optimization

### Tool Execution Caching

```typescript
// Cache expensive operations
const toolResultCache = new Map<string, CachedResult>();

async function executeWithCache(
  toolName: string,
  input: any
): Promise<any> {
  const cacheKey = `${toolName}:${JSON.stringify(input)}`;
  const cached = toolResultCache.get(cacheKey);
  
  if (cached && Date.now() - cached.timestamp < 300000) { // 5 min cache
    return cached.result;
  }
  
  const result = await executeToolDirect(toolName, input);
  
  toolResultCache.set(cacheKey, {
    result,
    timestamp: Date.now()
  });
  
  return result;
}
```

### Streaming Tool Results

```typescript
// Stream large results back to Nova
async function* streamToolResult(toolUseId: string) {
  yield { type: 'start', toolUseId };
  
  // Stream SVG generation progress
  for await (const chunk of generateSvgChunks()) {
    yield {
      type: 'progress',
      toolUseId,
      content: chunk
    };
  }
  
  yield { type: 'complete', toolUseId };
}
```

### Parallel Tool Execution

```typescript
// Execute independent tools in parallel
async function executeParallelTools(tools: ToolCall[]) {
  const independentTools = tools.filter(t => 
    !t.dependsOn && t.canRunParallel
  );
  
  const results = await Promise.all(
    independentTools.map(t => 
      executeToolWithTimeout(t, 30000) // 30s timeout
    )
  );
  
  return results;
}
```

---

## 📊 Monitoring & Analytics

### Tool Usage Metrics

```typescript
interface ToolMetrics {
  toolName: string;
  executionTime: number;
  success: boolean;
  errorType?: string;
  userId: string;
  timestamp: Date;
}

// Track tool performance
async function trackToolExecution(
  toolName: string,
  execution: () => Promise<any>
): Promise<any> {
  const startTime = performance.now();
  let success = true;
  let errorType: string | undefined;
  
  try {
    return await execution();
  } catch (error) {
    success = false;
    errorType = error.constructor.name;
    throw error;
  } finally {
    const metrics: ToolMetrics = {
      toolName,
      executionTime: performance.now() - startTime,
      success,
      errorType,
      userId: getCurrentUserId(),
      timestamp: new Date()
    };
    
    await logMetrics(metrics);
  }
}
```

---

## 🚀 Deployment Checklist

- [ ] **Environment Variables**
  - `NOVA_SONIC_API_KEY`
  - `NOVA_SONIC_MODEL_ID`
  - `WEBSOCKET_PORT`

- [ ] **Infrastructure**
  - WebSocket server deployed
  - SSL certificates configured
  - Rate limiting enabled
  - Monitoring dashboards set up

- [ ] **Testing**
  - Unit tests passing
  - Integration tests passing
  - Load testing completed
  - Security audit passed

- [ ] **Documentation**
  - API documentation updated
  - User guide created
  - Troubleshooting guide available

---

## 🔗 Related Resources

- [Amazon Nova Sonic API Documentation](https://docs.aws.amazon.com/nova)
- [KAPI Voice Agent Product Spec](../../02-products/ai_assistance/06-voice-agent.md)
- [Canvas Mode User Guide](../../02-products/user_experience/canvas-code-modes-spec.md)
- [WebSocket Architecture](../websockets/nova-sonic-websocket.md)

---

<div align="center">
  <p><strong>🛠️ Nova Sonic Tool Calling: Where voice commands become IDE actions</strong></p>
  <p><em>Empowering developers to build with natural language</em></p>
</div>
