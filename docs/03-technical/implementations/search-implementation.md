# Search Implementation Technical Documentation

> **Purpose**: Technical implementation details for KAPI's search system
> **Last Updated**: June 1, 2025

## Overview

This document contains the technical implementation details for KAPI's search functionality, including architecture, APIs, data models, and development notes.

## Implementation Status

### ✅ Completed Features

#### Phase 1: Basic VSCode-style Search
- **Search Bar** (`Ctrl+P`) - Top sliding search interface with tabbed navigation
- **Global Search Modal** (`Ctrl+Shift+F`) - Full-featured search overlay
- **File Name Search** - Fast fuzzy matching using file system metadata
- **Content Search** - Ripgrep-powered text search with Node.js fallback
- **Smart Filtering** - Automatic exclusion of node_modules, .git, binary files
- **Real-time Search** - 300ms debounced search with loading states
- **Cross-platform** - Windows, macOS, Linux support via Electron IPC

#### Phase 2: AST Structure Search
- **TypeScript/JavaScript AST** - Complete integration using ts-morph library
- **Symbol Recognition** - Functions (𝑓), classes (𝒞), interfaces (𝒾), types (𝒯), variables (𝒗)
- **Auto-initialization** - AST service starts automatically when projects open
- **Intelligent Project Detection** - Scans up to 2 directory levels for TypeScript projects
- **Smart Project Selection** - Prioritizes projects with tsconfig.json

### 🔄 In Development

#### Phase 3: Python AST Integration
- Native Python `ast` module integration
- Python symbol types: functions, classes, methods, variables, imports
- Cross-file import resolution and dependency tracking

## Technical Architecture

### System Components

```mermaid
flowchart TD
    subgraph "Frontend (Renderer Process)"
        UI[Search UI Components]
        CTX[Search Context]
        IPC[IPC Client]
    end
    
    subgraph "Backend (Main Process)"
        HAND[IPC Handlers]
        RG[Ripgrep Service]
        AST[AST Service]
        FS[File System Service]
    end
    
    subgraph "External Tools"
        RGE[ripgrep executable]
        TSM[ts-morph library]
    end
    
    UI --> CTX
    CTX --> IPC
    IPC -.-> HAND
    HAND --> RG
    HAND --> AST
    HAND --> FS
    RG --> RGE
    AST --> TSM
    
    style UI fill:#e3f2fd
    style RG fill:#c8e6c9
    style AST fill:#fff9c4
```

### Core Files

```
Frontend:
├── src/renderer/components/SearchBar.tsx         # Quick search UI (Ctrl+P)
├── src/renderer/components/SearchModal.tsx       # Global search UI (Ctrl+Shift+F)
├── src/renderer/contexts/SearchContext.tsx       # Search state management
└── src/renderer/hooks/useSearch.ts              # Search hook utilities

Backend:
├── src/main/services/searchService.ts           # Main search service
├── src/main/services/astService.ts             # AST parsing service
├── src/main/handlers/searchHandlers.ts         # IPC search handlers
└── src/filesystem.ts                            # File system operations
```

## API Reference

### IPC Channels

#### `search:in-files`
Search for content within files using ripgrep or fallback search.

**Request:**
```typescript
interface SearchInFilesRequest {
  projectPath: string;
  query: string;
  options?: {
    caseSensitive?: boolean;
    wholeWord?: boolean;
    useRegex?: boolean;
    maxResults?: number;
    filePattern?: string;
  };
}
```

**Response:**
```typescript
interface SearchResult {
  file: string;
  line: number;
  column: number;
  match: string;
  preview: string;
}
```

#### `ast:initialize`
Initialize AST service for a project.

**Request:**
```typescript
interface ASTInitRequest {
  projectPath: string;
}
```

#### `ast:search-symbols`
Search for code symbols using AST.

**Request:**
```typescript
interface ASTSearchRequest {
  projectPath: string;
  query: string;
  symbolTypes?: ('function' | 'class' | 'interface' | 'type' | 'variable')[];
}
```

**Response:**
```typescript
interface ASTSymbol {
  name: string;
  kind: 'function' | 'class' | 'interface' | 'type' | 'variable';
  filePath: string;
  line: number;
  column: number;
  signature?: string;
  returnType?: string;
  parameters?: string[];
  modifiers?: string[];
}
```

## Data Models

### Unified Search Result Format
```typescript
interface UnifiedSearchResult {
  id: string;                    // Unique result identifier
  type: 'file' | 'content' | 'symbol';
  file: string;                  // File path
  line?: number;                 // Line number (if applicable)
  column?: number;               // Column number (if applicable)
  match: string;                 // Matched text
  preview?: string;              // Preview with context
  
  // File-specific
  name?: string;                 // File name
  directory?: string;            // Parent directory
  
  // Symbol-specific
  kind?: SymbolKind;            // Symbol type
  signature?: string;           // Full signature
  returnType?: string;          // Return type (functions)
  parameters?: string[];        // Function parameters
  modifiers?: string[];         // Access modifiers
}
```

### Search Options
```typescript
interface SearchOptions {
  // Common options
  maxResults: number;           // Default: 1000
  timeout: number;              // Default: 5000ms
  
  // Content search options
  caseSensitive: boolean;       // Default: false
  wholeWord: boolean;          // Default: false
  useRegex: boolean;           // Default: false
  
  // File filtering
  includePatterns: string[];    // File patterns to include
  excludePatterns: string[];    // File patterns to exclude
  
  // Smart filtering
  excludeNodeModules: boolean;  // Default: true
  excludeBinaryFiles: boolean;  // Default: true
  excludeGitIgnored: boolean;   // Default: true
}
```

## Implementation Details

### Ripgrep Integration

```typescript
// src/filesystem.ts - Ripgrep execution
async function executeRipgrep(
  projectPath: string,
  query: string,
  options: SearchOptions
): Promise<SearchResult[]> {
  const args = [
    '--json',                    // JSON output format
    '--max-count', '1000',       // Result limit
    '--max-columns', '200',      // Line length limit
    '--no-heading',              // Inline file names
    '--with-filename',           // Include file names
    '--line-number',             // Include line numbers
    '--column',                  // Include column numbers
  ];
  
  // Add option flags
  if (options.caseSensitive) args.push('--case-sensitive');
  if (options.wholeWord) args.push('--word-regexp');
  if (!options.useRegex) args.push('--fixed-strings');
  
  // Execute ripgrep
  const { stdout } = await execAsync(`rg ${args.join(' ')} "${query}"`, {
    cwd: projectPath,
    maxBuffer: 10 * 1024 * 1024  // 10MB buffer
  });
  
  return parseRipgrepOutput(stdout);
}
```

### AST Service Architecture

```typescript
// src/main/services/astService.ts
class ASTService {
  private projects: Map<string, Project>;
  
  async initializeProject(projectPath: string): Promise<void> {
    // Auto-detect TypeScript projects
    const tsConfigPath = await this.findTsConfig(projectPath);
    
    if (!tsConfigPath) {
      throw new Error('No TypeScript configuration found');
    }
    
    // Create ts-morph project
    const project = new Project({
      tsConfigFilePath: tsConfigPath,
      skipAddingFilesFromTsConfig: false,
    });
    
    this.projects.set(projectPath, project);
  }
  
  async searchSymbols(
    projectPath: string,
    query: string
  ): Promise<ASTSymbol[]> {
    const project = this.projects.get(projectPath);
    const results: ASTSymbol[] = [];
    
    // Search all source files
    for (const sourceFile of project.getSourceFiles()) {
      // Search functions
      const functions = sourceFile.getFunctions();
      results.push(...this.matchFunctions(functions, query));
      
      // Search classes
      const classes = sourceFile.getClasses();
      results.push(...this.matchClasses(classes, query));
      
      // Continue for other symbol types...
    }
    
    return results;
  }
}
```

### Performance Optimizations

#### Debouncing Strategy
```typescript
// 300ms debounce for all search types
const debouncedSearch = useMemo(
  () => debounce(performSearch, 300),
  [performSearch]
);
```

#### Smart Filtering
```typescript
const DEFAULT_EXCLUDE_PATTERNS = [
  'node_modules/**',
  '.git/**',
  'dist/**',
  'build/**',
  'coverage/**',
  '*.min.js',
  '*.map'
];

// Reduces search scope by ~90% on average
```

#### Result Limiting
```typescript
const MAX_RESULTS = 1000;  // Prevent UI freezing
const MAX_PREVIEW_LENGTH = 200;  // Limit preview size
```

## Security Considerations

### IPC Security
```typescript
// Whitelist of allowed IPC channels
const ALLOWED_CHANNELS = [
  'search:in-files',
  'search:find-files',
  'ast:initialize',
  'ast:search-symbols'
];

// Input validation
function validateSearchQuery(query: string): boolean {
  // Prevent command injection
  if (query.includes('`') || query.includes('$')) {
    return false;
  }
  
  // Limit query length
  if (query.length > 1000) {
    return false;
  }
  
  return true;
}
```

### Resource Protection
```typescript
// Timeout protection
const SEARCH_TIMEOUT = 5000;  // 5 seconds

// Memory limits
const MAX_FILE_SIZE = 10 * 1024 * 1024;  // 10MB
const MAX_BUFFER_SIZE = 50 * 1024 * 1024;  // 50MB
```

## Testing Strategy

### Unit Tests
```typescript
describe('SearchService', () => {
  it('should find files by name', async () => {
    const results = await searchService.findFiles('package.json');
    expect(results).toContainFile('package.json');
  });
  
  it('should respect case sensitivity', async () => {
    const results = await searchService.searchContent('TODO', {
      caseSensitive: true
    });
    expect(results).not.toContainMatch('todo');
  });
});
```

### Performance Tests
```typescript
describe('Search Performance', () => {
  it('should search 10k files in <300ms', async () => {
    const start = Date.now();
    await searchService.searchContent('test');
    const duration = Date.now() - start;
    expect(duration).toBeLessThan(300);
  });
});
```

## Future Implementation Plans

### Phase 4: Semantic Search RAG Implementation

#### Vector Database Schema
```sql
CREATE EXTENSION IF NOT EXISTS vector;

CREATE TABLE document_embeddings (
  id UUID PRIMARY KEY,
  project_id UUID NOT NULL,
  file_path TEXT NOT NULL,
  document_type VARCHAR(50),
  content TEXT,
  embedding vector(1536),
  metadata JSONB,
  commit_hash VARCHAR(40),
  created_at TIMESTAMP,
  updated_at TIMESTAMP
);

CREATE INDEX idx_embedding_search ON document_embeddings 
USING ivfflat (embedding vector_cosine_ops);
```

#### Embedding Service
```typescript
class EmbeddingService {
  async generateEmbeddings(texts: string[]): Promise<number[][]> {
    // Use OpenAI API or local model
    const response = await openai.createEmbedding({
      model: "text-embedding-3-small",
      input: texts
    });
    
    return response.data.map(d => d.embedding);
  }
  
  async searchSimilar(
    query: string,
    projectId: string,
    limit: number = 10
  ): Promise<SemanticResult[]> {
    const queryEmbedding = await this.generateEmbedding(query);
    
    // Vector similarity search
    const results = await db.query(`
      SELECT file_path, content, 
             1 - (embedding <=> $1) as similarity
      FROM document_embeddings
      WHERE project_id = $2
      ORDER BY embedding <=> $1
      LIMIT $3
    `, [queryEmbedding, projectId, limit]);
    
    return results;
  }
}
```

### Multi-Language Support Plan

#### Python Integration
```typescript
class PythonASTService {
  async parseFile(filePath: string): Promise<PythonSymbol[]> {
    // Use Python subprocess to parse AST
    const script = `
import ast
import json

with open("${filePath}", "r") as f:
    tree = ast.parse(f.read())
    
# Extract symbols...
    `;
    
    const { stdout } = await execAsync(`python -c "${script}"`);
    return JSON.parse(stdout);
  }
}
```

## Development Guidelines

### Adding New Search Types
1. Define the search type in `SearchType` enum
2. Add IPC handler in `searchHandlers.ts`
3. Implement search logic in appropriate service
4. Add UI tab in `SearchBar.tsx`
5. Update unified result format if needed
6. Add tests for new functionality

### Performance Checklist
- [ ] Implement debouncing (300ms minimum)
- [ ] Add result limits (1000 max)
- [ ] Include loading states
- [ ] Handle errors gracefully
- [ ] Test with large projects (10k+ files)
- [ ] Monitor memory usage

### Code Style
```typescript
// Use consistent naming
interface SearchResult { }     // Types: PascalCase
function searchFiles() { }     // Functions: camelCase
const MAX_RESULTS = 1000;      // Constants: UPPER_SNAKE_CASE

// Always handle errors
try {
  const results = await search(query);
  return results;
} catch (error) {
  logger.error('Search failed:', error);
  return [];
}
```

## Troubleshooting

### Common Issues

#### Ripgrep Not Found
```typescript
// Fallback to Node.js implementation
if (!isRipgrepAvailable()) {
  return fallbackSearch(query, options);
}
```

#### Large File Performance
```typescript
// Skip files over 10MB
if (stats.size > MAX_FILE_SIZE) {
  return { skipped: true, reason: 'File too large' };
}
```

#### Memory Issues
```typescript
// Stream large results
const resultStream = new PassThrough();
ripgrep.stdout.pipe(resultStream);
```

## Monitoring & Analytics

### Search Metrics to Track
```typescript
interface SearchMetrics {
  query: string;
  type: SearchType;
  resultCount: number;
  responseTime: number;
  userSatisfaction?: boolean;
  errorRate: number;
}

// Log search analytics
analytics.track('search_performed', {
  searchType: 'content',
  queryLength: query.length,
  resultCount: results.length,
  duration: endTime - startTime
});
```

---

**Related Documentation:**
- [Search Product Spec](../../02-products/core_ide/04-search.md) - Product vision and features
- [Search Usage Guide](../../04-guides/usage/search-guide.md) - User guide
- [System Architecture](../00-system-design.md) - Overall technical architecture
