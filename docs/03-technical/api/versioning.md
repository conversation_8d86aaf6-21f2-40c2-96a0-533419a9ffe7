# API Versioning Guide

## Overview

This document describes the API versioning approach used in the KAPI API. API versioning allows us to make changes to the API without breaking existing clients.

## Versioning Strategy

We use URL path versioning, where the version is included in the URL path. For example:

```
/api/v1/auth/login
```

### Current Versions

- **v1**: The initial version of the API

## Backward Compatibility

For backward compatibility, we also support non-versioned endpoints. For example, both of these URLs will work:

```
/auth/login
/api/v1/auth/login
```

However, we recommend using the versioned endpoints for all new development, as non-versioned endpoints may be deprecated in the future.

## Version Lifecycle

Each API version goes through the following lifecycle:

1. **Active**: The version is fully supported and may receive new features
2. **Deprecated**: The version is still supported but will not receive new features
3. **Retired**: The version is no longer supported and endpoints will return 410 Gone

We will provide advance notice before deprecating or retiring any API version.

## Documentation

Each API version has its own documentation:

- v1: `/api/v1/docs` or `/api/v1/redoc`

The main documentation at `/docs` or `/redoc` shows the non-versioned API.

## Client Implementation

When implementing a client for the API:

1. Always include the API version in URLs (e.g., `/api/v1/auth/login`)
2. Check for compatibility issues after backend updates
3. Implement feature detection when using newer endpoints
4. Be prepared to handle deprecated endpoints during transition periods

## Future Versions

When a new version is introduced:

1. The new version will be available at `/api/v{n}/...`
2. The previous version will continue to work
3. Documentation for the new version will be available at `/api/v{n}/docs`
