# KAPI IDE API Endpoints

This document lists all the API endpoints used by the KAPI IDE frontend. These endpoints should be supported by your new Node.js backend to ensure full compatibility. I have migrated my backend from python to node.  Look at /Users/<USER>/Code/kapi-fresh/new_ide and identify a list of API calls we are making. We will then check if the new backend supports all these. The auth is handled by <PERSON> unlike in the past.

The location is 

## Authentication API (`/auth/*`)

- `POST /auth/login` - User login with username and password
- `POST /auth/logout` - User logout (invalidates token)
- `GET /auth/me` - Get current authenticated user info
- `POST /auth/register` - Register a new user
- `GET /auth/me/settings` - Get user settings
- `PATCH /auth/me/settings` - Update user settings
- `POST /auth/refresh-token` - Refresh authentication token

## Project API (`/project/*`)

- `POST /project` - Create a new project
- `PUT /project/{projectId}` - Update project details
- `PATCH /project/{projectId}/stage` - Update project stage
- `DELETE /project/{projectId}` - Delete a project
- `GET /project` - Get all projects with pagination
- `GET /project/user/{userId}` - Get projects for a specific user
- `GET /project/me` - Get projects for current user
- `GET /project/{projectId}` - Get project details by ID

## LLM API (`/llm/*`)

- `GET /llm/providers` - Get available LLM providers
- `GET /llm/models` - Get available LLM models
- `POST /llm/completion` - Send completion request
- `GET /llm/task/{taskId}` - Get task status
- `POST /llm/stream` - Create streaming completion request
- `GET /llm/stream/{streamId}` - Setup event source for streaming
- `GET /llm/conversation` - Get list of conversations
- `POST /llm/conversation` - Create a new conversation
- `GET /llm/conversation/{conversationId}` - Get specific conversation
- `DELETE /llm/conversation/{conversationId}` - Delete conversation
- `POST /llm/task/{taskId}/cancel` - Cancel a running LLM task

## Documentation API (`/documentation/*`)

- `POST /documentation/generate` - Generate documentation
- `GET /documentation/project/{projectId}` - Get project documentation
- `GET /documentation/file/{projectId}/{filePath}` - Get file documentation
- `PATCH /documentation/{docId}` - Update documentation
- `POST /documentation/sync/{projectId}` - Sync documentation with code

## Testing API (`/testing/*`)

- `POST /testing/generate` - Generate tests for a file
- `POST /testing/run/{projectId}` - Run tests for a project
- `GET /testing/project/{projectId}` - Get project tests
- `GET /testing/file/{projectId}/{filePath}` - Get file tests
- `PATCH /testing/{testId}` - Update a test case
- `GET /testing/history/{projectId}` - Get test run history

## Audio API (`/audio/*`)

- `POST /audio/voice-command` - Process voice command
- `POST /audio/voice-command/stream` - Process voice command with streaming
- `GET /audio/microphone/available` - Check if microphone is available

## Community API

- `GET /qa/users/{userId}/karma` - Get user karma
- `GET /users/leaderboard` - Get karma leaderboard
- `GET /activity` - Get activity feed with various filters
- `GET /social/channels` - Get available channels
- `GET /users/{userId}` - Get user profile

## WebSocket Endpoints

The application uses WebSockets for real-time communication:
- Chat/LLM streaming responses
- Terminal interactions
- Real-time collaboration
- Event notifications

## Special Notes

1. **Authentication:** The system uses Bearer token authentication
2. **Form Data Uploads:** Some endpoints like `/auth/login` expect form data instead of JSON
3. **Streaming Responses:** Several endpoints use Server-Sent Events (SSE) for streaming responses
4. **Error Handling:** All endpoints should return standardized error responses with appropriate HTTP status codes
