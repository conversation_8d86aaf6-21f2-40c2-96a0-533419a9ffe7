# Kapi System Design

_Last updated: May 28, 2025_

## ⚙️ Technical Infrastructure

### Multi-LLM Support

- **Vendors**: <PERSON> (Anthropic), OpenAI, Gemini, and others
- **Smart Routing**: Task-based model selection
- **Cost Optimization**: Template system reduces tokens by 60-80%
- **Rate Management**: Built-in limiting and graceful fallbacks

## 📁 Repository Structure

```
/kapi-fresh/
├── new_ide/              # Electron-based desktop IDE application
│   ├── src/              # IDE source code (React/TypeScript)
│   ├── electron/         # Electron main process
│   └── public/           # Static assets
├── nodejs_backend/       # Core backend API server
│   ├── src/              # Node.js/Express application
│   ├── prisma/           # Database schema and migrations
│   └── tests/            # Backend test suites
├── services/             # Microservices architecture
│   └── nova-sonic-service/  # Amazon Nova voice integration
├── templates/            # Project templates and boilerplates
│   ├── react-ai-chatbot/ # AI chatbot starter
│   ├── revealjs-slides/  # Presentation template
│   └── ml-pipeline/      # Machine learning template
├── docs/                 # Comprehensive documentation
│   ├── 01-overview/      # Strategic vision
│   ├── 02-products/      # Product specifications
│   ├── 03-technical/     # Technical details
│   ├── 04-guides/        # How-to guides
│   ├── 05-development/   # Dev processes
│   └── 06-business/      # Business docs
├── legacy/               # Previous iteration code (reference)
├── .github/              # GitHub Actions and workflows
└── .kapi/                # Project memory and AI context
```

### Key Components

- **new_ide**: The desktop IDE application built with Electron, React, and TypeScript. Contains all UI components, editor integration, and client-side logic.
- **nodejs_backend**: Core API server handling authentication, AI orchestration, project management, and data persistence.
- **services**: Microservices for specialized functionality, currently featuring the Nova Sonic voice service.
- **templates**: Production-ready project templates that demonstrate the backwards build methodology and reduce token usage.
- **docs**: Living documentation organized by audience and purpose.
- **.kapi**: Project-specific AI memory and context, versioned with the codebase.

## 🏗️ Tech Stack

| Layer        | Technology                                    |
|--------------|-----------------------------------------------|
| Frontend     | Next.js, React, Tailwind, Electron (desktop)  |
| Backend      | Node.js with Express, Prisma, PostgreSQL      |
| Database     | PostgreSQL                                    |
| Auth         | Clerk (unified across products)               |
| IDE Core     | Node-pty, Monaco Editor, AI plugins           |
| Email        | Amazon SES                                    |
| Admin        | Custom admin interface with dashboards        |
| Blog         | Integrated blog system (completed)            |
| Templates    | 50+ pre-built boilerplates                   |
| Deployment   | Azure VM (alpha deployed)                     |

## 🗺️ Development Roadmap

### Phase 1: Core Experience
- Workshop excellence (Modern AI Pro)
- IDE fundamentals (Kapi)
- Cross-product integration

### Phase 2: Enhanced Capabilities
- Advanced voice interactions
- Sketch-to-production pipeline
- Team collaboration features
- Blog system (completed ✅)

### Phase 3: Enterprise & Scale
- B2B team plans
- Custom workshop programs
- Enterprise IDE features
- Advanced security/compliance

## 🔧 System Architecture Patterns

### Repository Pattern
- Clean data access layer abstraction
- Domain-specific repositories
- Base repository with common functionality

### Strategy Pattern
- Task-specific conversation handling
- Pluggable AI model selection
- Adaptive UI based on project mode

### Dependency Injection
- Inversify IoC container
- Service-oriented architecture
- Testable and maintainable code

### Middleware Architecture
- Authentication and authorization
- Rate limiting and cost control
- Error handling and logging
- Request validation and sanitization

## 🔒 Security Architecture

### Authentication & Authorization
- Clerk integration for user management
- JWT-based API security
- Role-based access control (free, developer, admin)
- Multi-factor authentication support

### Data Protection
- Encryption at rest and in transit
- Secure API key management
- Input validation and sanitization
- CORS and security headers

### Infrastructure Security
- Secure deployment practices
- Regular security audits
- Dependency vulnerability scanning
- Incident response procedures

## 📊 Performance & Scalability

### Optimization Strategies
- Efficient database queries and indexing
- Caching strategies for frequently accessed data
- Lazy loading of heavy components
- Optimized asset delivery

### Monitoring & Observability
- Application performance monitoring
- Error tracking and alerting
- Usage analytics and metrics collection
- Health checks and status monitoring

### Scalability Planning
- Horizontal scaling capabilities
- Database sharding considerations
- Microservices architecture potential
- Load testing and capacity planning