# Kapi Database Schema

_Last updated: May 28, 2025_

## Overview

The Kapi database is built on PostgreSQL with Prisma ORM, designed to support both Modern AI Pro workshops and Kapi IDE functionality. The schema follows domain-driven design principles with clear separation of concerns across different feature areas.

## Core User Management

### User Model
```sql
User {
  id                String   @id @default(cuid())
  email             String   @unique
  name              String?
  role              UserRole @default(free)
  created_at        DateTime @default(now())
  updated_at        DateTime @updatedAt
  last_login        DateTime?
  
  -- Clerk integration
  clerk_id          String   @unique
  
  -- Subscription management
  subscription_status String?
  subscription_id     String?
  
  -- Usage tracking
  token_usage_month   Int      @default(0)
  token_limit_month   Int      @default(5000)
  
  -- Relationships
  conversations     Conversation[]
  projects         Project[]
  workshop_enrollments WorkshopEnrollment[]
  model_usage      ModelUsage[]
  social_profile   SocialProfile?
  karma_history    KarmaHistory[]
}

enum UserRole {
  free
  developer
  admin
}
```

## Project Management

### Project Model
```sql
Project {
  id              String      @id @default(cuid())
  name            String
  description     String?
  project_mode    ProjectMode @default(builder)
  tech_stack      Json?       -- Technology choices
  
  -- Backwards build components
  business_context String?    -- Project business goals
  technical_context String?   -- Architecture decisions
  
  -- <PERSON><PERSON><PERSON>
  created_at      DateTime    @default(now())
  updated_at      DateTime    @updatedAt
  
  -- Ownership
  user_id         String
  user            User        @relation(fields: [user_id], references: [id])
  
  -- Relationships
  conversations   Conversation[]
  code_artifacts  CodeArtifact[]
  documentation   Documentation[]
  slides          Slide[]
  tests           Test[]
}

enum ProjectMode {
  learner
  contributor
  builder
}
```

### Code Artifacts
```sql
CodeArtifact {
  id              String   @id @default(cuid())
  filename        String
  file_path       String
  content         String
  language        String
  summary         String?  -- AI-generated summary
  
  -- Metadata
  created_at      DateTime @default(now())
  updated_at      DateTime @updatedAt
  
  -- Relationships
  project_id      String
  project         Project  @relation(fields: [project_id], references: [id])
}
```

## Conversation Management

### Conversation Model
```sql
Conversation {
  id              String            @id @default(cuid())
  title           String?
  conversation_type ConversationType @default(chat)
  
  -- Context and metadata
  context_snapshot Json?            -- Project context at conversation start
  total_tokens     Int              @default(0)
  total_cost       Decimal          @default(0)
  
  -- Timestamps
  created_at       DateTime         @default(now())
  updated_at       DateTime         @updatedAt
  
  -- Relationships
  user_id         String
  user            User             @relation(fields: [user_id], references: [id])
  project_id      String?
  project         Project?         @relation(fields: [project_id], references: [id])
  messages        Message[]
}

enum ConversationType {
  chat
  code_generation
  code_planning
  slide_generation
  svg_mockup
  test_cases
}
```

### Message Model
```sql
Message {
  id              String      @id @default(cuid())
  content         String
  role            MessageRole
  
  -- AI model information
  model_id        String?
  model_provider  String?
  
  -- Token tracking
  prompt_tokens   Int?
  completion_tokens Int?
  total_tokens    Int?
  cost           Decimal?
  
  -- Quality metrics
  user_rating     Int?        -- 1-5 stars
  
  -- Timestamps
  created_at      DateTime    @default(now())
  
  -- Relationships
  conversation_id String
  conversation    Conversation @relation(fields: [conversation_id], references: [id])
}

enum MessageRole {
  user
  assistant
  system
}
```

## AI Model Management

### Model Usage Tracking
```sql
ModelUsage {
  id              String   @id @default(cuid())
  
  -- Model information
  model_id        String
  model_provider  String   -- 'openai', 'anthropic', 'google'
  model_name      String
  
  -- Usage metrics
  prompt_tokens   Int
  completion_tokens Int
  total_tokens    Int
  cost           Decimal
  
  -- Context
  task_type      String?  -- Type of task performed
  project_context String? -- Brief project context
  
  -- Timestamps
  created_at     DateTime @default(now())
  
  -- Relationships
  user_id        String
  user           User     @relation(fields: [user_id], references: [id])
  conversation_id String?
  conversation   Conversation? @relation(fields: [conversation_id], references: [id])
}
```

## Workshop Management

### Workshop Model
```sql
Workshop {
  id              String           @id @default(cuid())
  title           String
  description     String
  duration_days   Int
  price_cents     Int
  max_participants Int
  
  -- Content
  curriculum      Json             -- Structured curriculum data
  materials       Json             -- Workshop materials and resources
  
  -- Scheduling
  start_date      DateTime
  end_date        DateTime
  timezone        String
  
  -- Metadata
  created_at      DateTime         @default(now())
  updated_at      DateTime         @updatedAt
  
  -- Relationships
  enrollments     WorkshopEnrollment[]
  sessions        WorkshopSession[]
}

WorkshopEnrollment {
  id              String           @id @default(cuid())
  enrollment_status EnrollmentStatus @default(enrolled)
  payment_status  PaymentStatus    @default(pending)
  
  -- Progress tracking
  completion_percentage Float       @default(0)
  completed_sessions   String[]     -- Array of completed session IDs
  
  -- Timestamps
  enrolled_at     DateTime         @default(now())
  completed_at    DateTime?
  
  -- Relationships
  user_id         String
  user            User             @relation(fields: [user_id], references: [id])
  workshop_id     String
  workshop        Workshop         @relation(fields: [workshop_id], references: [id])
}

enum EnrollmentStatus {
  enrolled
  active
  completed
  cancelled
}

enum PaymentStatus {
  pending
  paid
  refunded
  failed
}
```

## Social Features

### Social Profile
```sql
SocialProfile {
  id              String   @id @default(cuid())
  display_name    String
  bio             String?
  avatar_url      String?
  
  -- Gamification
  karma_points    Int      @default(0)
  elo_rating      Int      @default(1000)
  achievements    String[] -- Array of achievement IDs
  
  -- Settings
  visibility      Visibility @default(public)
  
  -- Timestamps
  created_at      DateTime @default(now())
  updated_at      DateTime @updatedAt
  
  -- Relationships
  user_id         String   @unique
  user            User     @relation(fields: [user_id], references: [id])
}

enum Visibility {
  private
  friends
  public
}
```

### Community Features
```sql
Question {
  id              String   @id @default(cuid())
  title           String
  content         String
  tags            String[]
  
  -- Status
  status          QuestionStatus @default(open)
  accepted_answer_id String?
  
  -- Metrics
  upvotes         Int      @default(0)
  downvotes       Int      @default(0)
  view_count      Int      @default(0)
  
  -- Timestamps
  created_at      DateTime @default(now())
  updated_at      DateTime @updatedAt
  
  -- Relationships
  author_id       String
  author          User     @relation(fields: [author_id], references: [id])
  answers         Answer[]
}

Answer {
  id              String   @id @default(cuid())
  content         String
  is_accepted     Boolean  @default(false)
  
  -- Metrics
  upvotes         Int      @default(0)
  downvotes       Int      @default(0)
  
  -- Timestamps
  created_at      DateTime @default(now())
  updated_at      DateTime @updatedAt
  
  -- Relationships
  question_id     String
  question        Question @relation(fields: [question_id], references: [id])
  author_id       String
  author          User     @relation(fields: [author_id], references: [id])
}

enum QuestionStatus {
  open
  answered
  closed
}
```

## Memory System

### Context Storage
```sql
UserContext {
  id              String   @id @default(cuid())
  
  -- User preferences
  skill_level     String?
  coding_style    Json?
  ai_preferences  Json?
  preferred_stack Json?
  
  -- Learning context
  learning_focus  String?
  workshop_history String[]
  
  -- Timestamps
  created_at      DateTime @default(now())
  updated_at      DateTime @updatedAt
  
  -- Relationships
  user_id         String   @unique
  user            User     @relation(fields: [user_id], references: [id])
}

ProjectContext {
  id              String   @id @default(cuid())
  
  -- Business context
  business_goals  String?
  target_audience String?
  value_proposition String?
  
  -- Technical context
  architecture    Json?
  constraints     Json?
  design_decisions Json?
  
  -- Timestamps
  created_at      DateTime @default(now())
  updated_at      DateTime @updatedAt
  last_reviewed   DateTime?
  
  -- Relationships
  project_id      String   @unique
  project         Project  @relation(fields: [project_id], references: [id])
}
```

## Payment & Billing

### Subscription Management
```sql
Subscription {
  id              String             @id @default(cuid())
  stripe_subscription_id String      @unique
  
  -- Plan details
  plan_type       SubscriptionPlan
  billing_cycle   BillingCycle
  
  -- Status
  status          SubscriptionStatus
  current_period_start DateTime
  current_period_end   DateTime
  
  -- Usage limits
  token_limit     Int
  feature_access  Json               -- Available features
  
  -- Timestamps
  created_at      DateTime           @default(now())
  updated_at      DateTime           @updatedAt
  cancelled_at    DateTime?
  
  -- Relationships
  user_id         String             @unique
  user            User               @relation(fields: [user_id], references: [id])
}

enum SubscriptionPlan {
  free
  developer
  pro
  team_starter
  team_growth
  team_pro
}

enum BillingCycle {
  monthly
  yearly
}

enum SubscriptionStatus {
  active
  cancelled
  past_due
  unpaid
}
```

## Database Indexes

### Performance Optimization
```sql
-- User lookups
CREATE INDEX idx_users_clerk_id ON User(clerk_id);
CREATE INDEX idx_users_email ON User(email);

-- Conversation queries
CREATE INDEX idx_conversations_user_id ON Conversation(user_id);
CREATE INDEX idx_conversations_project_id ON Conversation(project_id);
CREATE INDEX idx_conversations_created_at ON Conversation(created_at);

-- Message queries
CREATE INDEX idx_messages_conversation_id ON Message(conversation_id);
CREATE INDEX idx_messages_created_at ON Message(created_at);

-- Model usage analytics
CREATE INDEX idx_model_usage_user_id ON ModelUsage(user_id);
CREATE INDEX idx_model_usage_created_at ON ModelUsage(created_at);
CREATE INDEX idx_model_usage_model_provider ON ModelUsage(model_provider);

-- Project queries
CREATE INDEX idx_projects_user_id ON Project(user_id);
CREATE INDEX idx_projects_updated_at ON Project(updated_at);

-- Workshop queries
CREATE INDEX idx_workshop_enrollments_user_id ON WorkshopEnrollment(user_id);
CREATE INDEX idx_workshop_enrollments_workshop_id ON WorkshopEnrollment(workshop_id);

-- Social features
CREATE INDEX idx_questions_author_id ON Question(author_id);
CREATE INDEX idx_questions_created_at ON Question(created_at);
CREATE INDEX idx_answers_question_id ON Answer(question_id);
```

## Migration Strategy

### Schema Evolution
- **Versioned Migrations**: All schema changes through Prisma migrations
- **Backward Compatibility**: Maintain compatibility during updates
- **Data Validation**: Validate data integrity after migrations
- **Rollback Plans**: Prepared rollback procedures for each migration

### Data Seeding
- **Development Data**: Consistent test data for development
- **Production Bootstrap**: Initial data for production deployment
- **Workshop Content**: Pre-populate workshop catalog and materials
- **Model Configurations**: Default AI model settings and pricing