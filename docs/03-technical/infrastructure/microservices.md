# Nova Sonic Microservice Architecture - Implementation Summary

**Date**: May 28, 2025  
**Status**: Implemented and Ready for Testing

## Overview

Successfully separated Nova Sonic voice streaming functionality from the main KAPI application into a standalone microservice to resolve persistent "Duplicate prompt name" errors and improve system stability.

## Architecture

### 1. **Nova Sonic Service** (Port 3005)
- **Location**: `/Users/<USER>/Code/kapi-fresh/services/nova-sonic-service`
- **Purpose**: Standalone service handling AWS Bedrock Nova Sonic voice streaming
- **Key Files**:
  - `src/server.ts` - Express + Socket.IO server
  - `src/client.ts` - AWS Bedrock client implementation (copied from legacy/test)
  - `src/constants.ts`, `src/types.ts`, `src/tools.ts` - Supporting files

### 2. **Main Application** (Port 3000)
- **Location**: `/Users/<USER>/Code/kapi-fresh/nodejs_backend`
- **Nova Sonic Integration**:
  - `src/websockets/nova-sonic-proxy.ts` - Proxy handler forwarding connections
  - `src/server.ts` - Updated to use NovaSonicProxy instead of direct implementation
  - `src/admin/routes/nova-sonic-status-proxy.routes.ts` - Admin routes via HTTP

## Implementation Details

### Nova Sonic Service Flow
1. Client connects to main app at `/ws/nova-sonic`
2. Main app proxy forwards to Nova Sonic service
3. Service sends `connected` event
4. Client sends `initializeSession` event
5. Service creates AWS Bedrock session and responds with `sessionInitialized`
6. Voice streaming can begin with `promptStart`, `systemPrompt`, `audioStart`

### Key Commands

**Build and Run Nova Sonic Service**:
```bash
cd /Users/<USER>/Code/kapi-fresh/services/nova-sonic-service
npm install
npm run build
npm start  # Runs on port 3005
```

**Environment Variables** (in `.env`):
```
NOVA_SONIC_SERVICE_URL=http://localhost:3005
AWS_PROFILE=bedrock-test
AWS_REGION=us-east-1
```

### Benefits Achieved
1. **Process Isolation**: No more session state conflicts
2. **Independent Restarts**: Can restart voice service separately
3. **Clean Debugging**: Separate logs for each service
4. **Better Performance**: Dedicated resources for real-time audio
5. **Scalability**: Can run multiple instances if needed

## Testing

1. Start Nova Sonic service (port 3005)
2. Start main application (port 3000)
3. Navigate to `/admin/nova_sonic/test`
4. Click "Connect" → Should see "Session ready"
5. Click "Start Streaming" → Voice interaction begins

## Health Monitoring

- Nova Sonic Health: `http://localhost:3005/health`
- Admin Status: `http://localhost:3000/api/admin/nova-sonic-status/health`
- Active Sessions: `http://localhost:3005/sessions`

## Important Notes

- The proxy transparently forwards all WebSocket events
- No changes needed in frontend code
- AWS credentials use profile `bedrock-test`
- Session cleanup runs every 60 seconds for inactive sessions
- Using PM2 is recommended for production deployment

## Connection Limits & Timeouts

- **AWS Nova Sonic**: 8-minute max connection time, 300K context window
- **Client inactivity**: 30-second timeout (prevents overbilling)
- **Server cleanup**: 5-minute inactive session removal
- **Extension**: Start new session with previous context for >8min conversations

## Reconnection Fix (May 28, 2025)

### Problem
- Socket.IO auto-reconnect conflicted with session state
- Session ID reuse failed on reconnect

### Solution
**Frontend**: Disabled auto-connect/reconnect, manual connection control
**Server**: Session cleanup before reinitialization

### Testing Reconnect
1. Connect → Start → Stop → Disconnect
2. Connect again → Should work normally

## File Structure
```
services/nova-sonic-service/
├── src/
│   ├── server.ts      # Main server file
│   ├── client.ts      # AWS Bedrock client
│   ├── constants.ts   # Configuration constants
│   ├── types.ts       # TypeScript types
│   └── tools.ts       # Tool implementations
├── dist/              # Compiled JavaScript
├── package.json
├── tsconfig.json
├── .env
└── ecosystem.config.js # PM2 configuration
```

## Troubleshooting

If "Duplicate prompt name" errors return:
1. Check if service is running: `curl http://localhost:3005/health`
2. Verify no lingering sessions: `curl http://localhost:3005/sessions`
3. Check logs for session creation patterns
4. Ensure `npm run build` was run after any code changes
5. Use `npm start` not `npm run dev` for production mode

### Duplicate Prompt Name Error (Admin Integration)
**Problem**: `promptStart` sent multiple times in same session
**Solution**: Send `promptStart` and `systemPrompt` only once during session initialization, not on every stream start

### Session Tracking (Debug)
**Proxy-level tracking** prevents duplicate events:
- Blocks duplicate `initializeSession` calls
- Blocks duplicate `promptStart` calls  
- Tracks: socketId, init count, prompt count, timestamps
- View sessions: `/api/admin/nova-sonic-status/proxy/sessions`

## Next Steps

1. Set up PM2 for process management
2. Add authentication between services
3. Implement monitoring/metrics
4. Configure for production deployment
