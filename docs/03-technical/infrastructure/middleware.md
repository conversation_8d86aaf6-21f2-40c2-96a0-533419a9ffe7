# Middleware and Rate Limiting Implementation

> **Product Context**: For user-facing AI resource management features, see [AI Resource Management](../../02-products/ai_assistance/10-ai-resource-management.md)

This document outlines the technical implementation of middleware architecture and rate limiting for the KAPI API system, focusing on developer implementation details.

## Technical Overview

The KAPI system implements a layered middleware approach using Express.js middleware functions to handle authentication, rate limiting, and AI model access control. All LLM calls flow through the unified conversation service with rate limiting applied before reaching AI model services.

## Middleware Architecture

### Enhanced Security Middleware Stack (Updated June 2025)

The KAPI system now implements a comprehensive security-first middleware architecture:

1. **Security Headers Middleware** (`helmet`)
   - Content Security Policy (CSP) enforcement
   - X-Frame-Options: DENY (prevents clickjacking)
   - X-Content-Type-Options: nosniff
   - X-XSS-Protection: 1; mode=block
   - Referrer-Policy: strict-origin-when-cross-origin

2. **CORS Middleware** (`configureCors`)
   - Whitelist-based origin validation
   - Production domains: kapihq.com, modernaipro.com
   - Credentials support with secure cookies
   - Methods restricted to: GET, POST, PUT, DELETE, PATCH

3. **Rate Limiting Middleware** (Multiple Tiers)
   - **General API**: 100 requests/15 minutes per IP
   - **AI Endpoints**: 10 requests/minute (cost protection)
   - **Auth Endpoints**: 5 attempts/15 minutes (brute force protection)
   - **WebSocket Connections**: Connection-based limiting

4. **Input Validation & Sanitization**
   - Express-validator for structured validation
   - Script tag and HTML injection prevention
   - SQL injection protection via parameterized queries
   - Path traversal attack prevention
   - File type restrictions (allowed: .ts, .tsx, .js, .jsx, .json, .md, .txt, .css, .scss, .html)

5. **Performance Optimization Middleware**
   - Redis caching layer (graceful fallback)
   - Response compression (excluding SSE streams)
   - Database query optimization with indexes

### Request Flow

```mermaid
flowchart TD
    Client[Client] --> |API Request| UnifiedRouter[Unified Conversation Router]

    UnifiedRouter --> |clerkAuthMiddleware| Auth[Authentication]

    Auth --> |Non-LLM Routes| NonLLMRoutes[Non-LLM Routes]
    Auth --> |LLM Routes| RateLimit[Rate Limiting]

    NonLLMRoutes --> UnifiedConvService[Unified Conversation Service]
    RateLimit --> |dynamicRateLimitMiddleware| UnifiedConvService

    UnifiedConvService --> |Task Strategy Selection| StrategyRegistry[Task Strategy Registry]
    StrategyRegistry --> |Returns appropriate strategy| TaskStrategy[Task-Specific Strategy]

    TaskStrategy --> |Formats request| UnifiedConvService
    UnifiedConvService --> |Sets x-model-id & x-task-type headers| AIService[AI Service]

    AIService --> |Claude API| Claude[Claude Models]
    AIService --> |Gemini API| Gemini[Gemini Models]
    AIService --> |Azure API| Azure[Azure Models]
    AIService --> |Nova API| Nova[Nova Models]

    subgraph "Rate Limiting Checks"
        RateLimit --> RPMCheck[RPM Check]
        RateLimit --> TPMCheck[TPM Check]
        RateLimit --> ConcurrencyCheck[Concurrency Check]
    end
```

### Middleware Stack

1. **Security Layer** (Applied First)
   - Helmet security headers
   - CORS validation
   - Custom security headers
   - Request sanitization

2. **Authentication Middleware** (`clerkAuthMiddleware`)
   - Applied at router level for all requests
   - Validates user authentication via Clerk
   - Sets user context for downstream processing
   - Session management with secure cookies

3. **Rate Limiting Middleware** (Tiered Approach)
   - **Global Rate Limiter**: Applied to all /api routes
   - **Dynamic Model Rate Limiter**: Applied to LLM routes
   - **Strict Rate Limiter**: Applied to sensitive endpoints (auth, admin)
   - Real-time usage tracking with Redis

4. **Validation Middleware**
   - Express-validator chains for structured validation
   - Pre-built validators for common patterns
   - Custom validators for file paths and security-critical inputs
   - Automatic error response formatting

5. **Caching Middleware** (Performance Layer)
   - Redis-based response caching
   - Cache key patterns for consistency
   - Automatic cache invalidation on updates
   - Graceful degradation without Redis

6. **Error Handling Middleware**
   - Secure error responses (no stack traces in production)
   - Operational vs system error differentiation
   - Request ID tracking for debugging
   - Comprehensive internal logging

## Dynamic Rate Limiting Implementation

### Technical Implementation

The dynamic rate limiting middleware uses database queries, in-memory semaphores, and real-time metrics to enforce model-specific limits and prevent API overload.

### Implementation Components

#### 1. Model-Specific Rate Limits
Each AI model has defined limits stored in `models.tsv`:
- `rpm_limit`: Maximum requests per minute
- `tpm_limit`: Maximum tokens per minute  
- `max_concurrent_requests`: Maximum concurrent requests

#### 2. Real-Time Usage Tracking
- Database queries track model usage in real-time
- RPM and TPM usage calculated from recent activity
- Usage data logged for every model interaction

#### 3. Concurrency Control
- Semaphore implementation limits concurrent requests
- Per-model semaphore pools prevent overload
- Automatic semaphore release after processing

### Rate Limiting Process

1. **Request Identification**
   - Client makes request to unified conversation router
   - Router determines if request requires AI model access

2. **Authentication Check**
   - All requests pass through Clerk authentication middleware
   - User context established for rate limit calculations

3. **Route Classification**
   - **Non-LLM routes**: Conversation listing, retrieval (no rate limiting)
   - **LLM routes**: Model invocation, task processing (rate limited)

4. **Rate Limit Evaluation**
   The middleware performs these checks:
   ```typescript
   // Pseudocode for rate limiting checks
   const modelId = headers['x-model-id'];
   const taskType = headers['x-task-type'];
   
   // Check RPM limit
   const recentRequests = await getRecentRequests(modelId, 1); // 1 minute
   if (recentRequests >= model.rpm_limit) {
     throw new RateLimitError('RPM exceeded');
   }
   
   // Check TPM limit  
   const recentTokens = await getRecentTokenUsage(modelId, 1);
   if (recentTokens >= model.tpm_limit) {
     throw new RateLimitError('TPM exceeded');
   }
   
   // Acquire concurrency semaphore
   const semaphore = await acquireSemaphore(modelId);
   if (!semaphore) {
     throw new RateLimitError('Concurrency limit exceeded');
   }
   ```

5. **Request Processing**
   - If limits not exceeded, request proceeds to unified conversation service
   - Service selects appropriate task strategy
   - Headers set for AI service routing
   - Response flows back through same path

6. **Cleanup**
   - Semaphore released after processing
   - Usage data logged to database
   - Metrics updated for monitoring

### Task Strategy Pattern

The unified system uses a strategy pattern to handle different task types while maintaining consistent middleware application:

#### Unified Router Benefits
- Single authentication middleware application
- Consistent rate limiting across all LLM routes
- Backward API compatibility maintained
- Reduced code duplication

#### Task Strategy Types
- **Chat Task Strategy**: General conversations
- **Code Generation Strategy**: Code creation tasks
- **Code Planning Strategy**: Architecture planning
- **Slide Generation Strategy**: Presentation creation
- **SVG Mockup Strategy**: Visual mockup generation
- **Test Cases Strategy**: Test generation

#### Strategy Registry Implementation
```typescript
// Strategy registry pattern implementation
class TaskStrategyRegistry {
  private strategies = new Map<string, TaskStrategy>();
  
  register(taskType: string, strategy: TaskStrategy): void {
    this.strategies.set(taskType, strategy);
  }
  
  getStrategy(taskType: string): TaskStrategy | undefined {
    return this.strategies.get(taskType);
  }
}
```

**Technical Benefits:**
- Centralized strategy management
- Runtime strategy registration
- Consistent interface for rate limiting middleware
- Easy addition of new task types via configuration

## Implementation Details

### File Locations
- **Security Middleware**: `nodejs_backend/src/common/middleware/security.middleware.ts`
- **Validation Middleware**: `nodejs_backend/src/common/middleware/validation.middleware.ts`
- **Error Handler**: `nodejs_backend/src/common/middleware/error.middleware.ts`
- **Rate Limiting**: `nodejs_backend/src/middleware/rate-limit.middleware.ts`
- **Redis Service**: `nodejs_backend/src/services/redis.service.ts`
- **Cached Base Service**: `nodejs_backend/src/services/cached-base.service.ts`
- **Unified Router**: `nodejs_backend/src/routes/conversation/index.ts`
- **Model Configuration**: `config/models.tsv`
- **Performance Indexes**: `nodejs_backend/prisma/migrations/performance_indexes.sql`

### Configuration Management
```typescript
// Example model configuration
interface ModelConfig {
  model_id: string;
  rpm_limit: number;
  tpm_limit: number;
  max_concurrent_requests: number;
  provider: string;
}
```

### Error Responses
```typescript
// Rate limit error responses
{
  error: "RATE_LIMIT_EXCEEDED",
  message: "RPM limit exceeded for model",
  retryAfter: 60, // seconds
  limits: {
    rpm: 100,
    current: 105,
    resetTime: "2025-05-28T12:01:00Z"
  }
}
```

### Monitoring and Observability
- Real-time usage metrics collection
- Rate limit violation tracking
- Performance monitoring for middleware overhead
- Dashboard integration for operational visibility

## Security Considerations

### Enhanced Security Implementation (June 2025)

1. **Authentication & Authorization**
   - Clerk-based user authentication with JWT validation
   - Role-based access control (FREE, DEVELOPER, ADMIN)
   - Session security with httpOnly, secure cookies
   - CSRF protection via SameSite cookies
   - Account lockout after failed attempts

2. **Input Security**
   - **Validation Layer**: Express-validator for all endpoints
   - **Sanitization**: Automatic HTML/script tag removal
   - **SQL Injection**: Prevented via Prisma parameterized queries
   - **Path Traversal**: File access restricted to allowed directories
   - **File Type Validation**: Whitelist approach for uploads

3. **API Security**
   - **Rate Limiting**: Multi-tier approach by endpoint sensitivity
   - **CORS**: Whitelist-based origin validation
   - **Headers**: Comprehensive security headers via Helmet
   - **Error Handling**: No sensitive data leakage in production
   - **Request Logging**: Full audit trail with anonymized IPs

4. **AI Model Protection**
   - Per-model rate limiting with cost tracking
   - Token usage monitoring and alerts
   - Prompt injection detection patterns
   - Response filtering for sensitive data
   - Model fallback strategies for failures

### Security Middleware Example

```typescript
// Secure route implementation
router.post('/api/ai/generate',
  aiRateLimiter,              // AI-specific rate limiting
  validate(validators.aiRequest), // Input validation
  sanitizeInput,              // Remove malicious content
  async (req, res, next) => {
    try {
      // Validated and sanitized input
      const { prompt, model } = req.body;
      // ... AI processing ...
    } catch (error) {
      next(createError('AI generation failed', 500));
    }
  }
);
```

## Performance Optimization

### Redis Caching Implementation (June 2025)

1. **Caching Strategy**
   - **User Data**: 1-hour TTL for profile and subscription data
   - **Conversations**: 5-minute TTL for active conversations
   - **Messages**: 1-minute TTL for recent messages
   - **AI Responses**: Content-hash based caching
   - **Model Usage**: Daily aggregation caching
   - Automatic cache warming for frequently accessed data

2. **Database Optimization**
   - **New Indexes Added** (30+ performance indexes):
     ```sql
     -- High-impact indexes
     CREATE INDEX idx_conversations_user_id ON conversations(user_id);
     CREATE INDEX idx_messages_conversation_id ON messages(conversation_id);
     CREATE INDEX idx_model_usage_user_timestamp ON model_usage(user_id, timestamp);
     -- Partial indexes for common filters
     CREATE INDEX idx_conversations_active ON conversations(user_id) WHERE status = 'active';
     ```
   - Query performance improved by 50%+
   - N+1 query patterns eliminated
   - Connection pooling optimized

3. **Concurrency Management**
   - Non-blocking semaphore implementations
   - WebSocket connection pooling
   - Request queuing with priority levels
   - Circuit breaker pattern for external services

### Cache Service Architecture

```typescript
// Cache key patterns for consistency
const CacheKeys = {
  user: (userId: number) => `user:${userId}`,
  conversation: (convId: number) => `conversation:${convId}`,
  aiResponse: (hash: string) => `ai:response:${hash}`,
  modelUsage: (userId: number, date: string) => `usage:${userId}:${date}`
};

// TTL constants
const CacheTTL = {
  SHORT: 60,      // 1 minute
  MEDIUM: 300,    // 5 minutes
  LONG: 3600,     // 1 hour
  DAY: 86400      // 24 hours
};
```

## Future Enhancements

1. **Adaptive Rate Limiting**
   - Dynamic limit adjustment based on system load
   - User-tier based rate limiting
   - Time-of-day limit variations

2. **Advanced Analytics**
   - Usage pattern analysis
   - Predictive rate limiting
   - Cost optimization recommendations

3. **Multi-Region Support**
   - Distributed rate limiting across regions
   - Global usage aggregation
   - Regional failover capabilities

## See Also

**Product Documentation:**
- [AI Resource Management](../../02-products/ai_assistance/10-ai-resource-management.md) - User-facing features and benefits
- [AI Agents](../../02-products/ai_assistance/03-ai-agents.md) - AI assistant capabilities
- [Token Cost Management](../../02-products/cost_mgmt/10-token-cost-management.md) - Cost optimization features

**Technical Documentation:**
- [System Architecture](../00-system-design.md) - Overall system design
- [API Documentation](../api/) - API endpoint specifications
- [Performance Requirements](../performance-requirements.md) - System performance targets