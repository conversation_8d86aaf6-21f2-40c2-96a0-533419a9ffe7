# RAG (Retrieval-Augmented Generation) Technical Implementation

> **Product Context**: For user-facing semantic search features, see [Semantic Search & RAG](../../02-products/core_ide/04-search.md)

## 🚀 Implementation Status: ✅ COMPLETED

**Current Status**: Semantic search is **fully implemented** using local ChromaDB with Azure OpenAI embeddings.

- ✅ **Local ChromaDB**: Implemented in IDE (`new_ide/src/renderer/services/`)
- ✅ **Azure Integration**: Uses existing backend service for embeddings
- ✅ **User Interface**: Semantic search tab in SearchBar component
- ✅ **Auto-indexing**: Smart project scanning and document processing
- ❌ **Server-side RAG**: Marked for cleanup (unused PostgreSQL implementation)

This guide provides technical implementation details for the RAG-based semantic search system in KAPI IDE.

## Overview

The RAG implementation enables semantic search across your project's **natural language documentation only** - comments, markdown files, and structured documentation. It specifically excludes raw code to focus on "what" and "why" explanations rather than implementation details. Uses Azure OpenAI's text-embedding-3-small model to generate vector embeddings for efficient similarity search.

## Current Implementation Architecture

The semantic search is **fully implemented** using a local-first, documentation-focused approach with the following components:

### Frontend Services (new_ide/src/renderer/services/)

1. **ChromaDbService.ts** - Persistent vector database management
   - ChromaDB instance in browser memory for fast search
   - IndexedDB for persistent storage across browser sessions
   - Handles embedding storage and similarity search for documentation only
   - Provides sub-100ms query performance for natural language content

2. **DocumentProcessingService.ts** - Documentation extraction and chunking
   - **Documentation-only focus**: Extracts comments, markdown, .doc.json files
   - **Multi-format support**: Handles /// Summary: style comments, JSDoc, Python docstrings
   - **Smart filtering**: Skips raw code, focuses on human explanations
   - Document chunking (800 tokens + 100 token overlap)

3. **SemanticSearchService.ts** - High-level orchestration
   - Coordinates documentation indexing and search operations
   - Progress tracking with user feedback
   - Batch processing for optimal performance

### Backend Integration

- **Embeddings API** (`/api/ai/embeddings`) - Uses existing Azure OpenAI service
- **Unified Authentication** - Integrates with existing auth middleware
- **Cost Tracking** - Monitors embedding generation costs

### User Interface

- **SearchBar Component** - New 🧠 Semantic tab for documentation search
- **Persistent Documentation Indexing** - Scans and indexes only natural language content
- **Confidence Scoring** - Shows match percentages (60-100%) for documentation relevance
- **Natural Language Queries** - "How does authentication work?" "Why did we choose this pattern?"

### Persistent Storage Architecture

- **Hybrid Storage**: ChromaDB in-memory + IndexedDB persistence
- **Automatic Persistence**: Embeddings saved to IndexedDB after indexing
- **Session Recovery**: Embeddings restored from IndexedDB on app startup
- **Cost Efficient**: Expensive embeddings preserved across sessions
- **Performance**: Sub-100ms search after initial load from IndexedDB

### What Gets Indexed

**✅ Included (Documentation Only):**
- 📝 **Markdown files**: README.md, docs/, technical specifications
- 💬 **Code comments**: `//`, `/**/`, `///` style comments and explanations
- 📋 **Special documentation comments**: `/// Summary:`, `/// Purpose:`, `/// Intent:`
- 📄 **.doc.json files**: Machine-readable documentation format
- 🐍 **Python docstrings**: `"""` style documentation

**❌ Excluded (Raw Code):**
- Function implementations and logic
- Variable declarations and assignments
- Import/export statements
- Control flow structures (if/else, loops)
- Raw TypeScript/JavaScript/Python code

## Architecture Decision: Local-First Approach

```mermaid
graph TB
    subgraph "Development Environment"
        A[Developer's Machine]
        B[Local Chroma DB]
        C[Project Files]
        D[Azure OpenAI API]
        
        C -->|Process| B
        A -->|Query| B
        B -->|Embeddings| D
        D -->|Generate Once| B
    end
    
    subgraph "Production Environment"
        E[Web/Cloud IDE]
        F[PostgreSQL + pgvector]
        G[Shared Knowledge Base]
        
        E -->|Query| F
        F -->|Cross-Project| G
    end
    
    B -.->|Export/Sync| F
    
    style A fill:#e8f5e9
    style B fill:#4caf50
    style F fill:#2196f3
```

### Why Local-First for Development?

1. **Performance & Latency**
   - Sub-500ms response time requirement for semantic search
   - Eliminate network latency for vector searches
   - Instant feedback during development (<300ms)

2. **Privacy & Security**
   - Code and documentation stay on developer's machine
   - No transmission of proprietary codebases
   - Secure processing of architectural decisions and interviews

3. **Developer Experience**
   - Offline capability for uninterrupted development
   - No API rate limits during heavy usage
   - Faster iteration cycles when testing RAG improvements
   - Easier debugging of embedding quality

4. **Cost Optimization**
   - Avoid repeated embedding costs during development
   - One-time embedding generation, infinite local searches
   - No cloud storage costs for development data

5. **Architecture Alignment**
   - Consistent with existing local-first patterns (ripgrep, AST parsing)
   - Natural fit with KAPI's file system operations
   - Seamless integration with local development workflow

## Setup

### Development Setup (Recommended): Local Chroma

#### 1. Install Chroma

```bash

# For JavaScript/TypeScript projects
npm install chromadb @xenova/transformers
```

#### 2. Initialize Chroma (Automatic in KAPI)

The ChromaDB setup is handled automatically by the `ChromaDbService`:

```typescript
// Automatically initialized when semantic search is first used
import { ChromaClient } from 'chromadb';
import path from 'path';

const client = new ChromaClient({
  path: path.join(homeDir, '.kapi', 'chroma_db', projectId)
});

// Create or get collection for project documentation
const collection = await client.getOrCreateCollection({
  name: "project_documents",
  metadata: { 
    "hnsw:space": "cosine",
    "project_id": projectId 
  }
});
```

#### 3. Local Embedding Storage Structure

```mermaid
graph TD
    A[~/.kapi/chroma_db/] --> B[project_id_1/]
    A --> C[project_id_2/]
    
    B --> D[embeddings.parquet]
    B --> E[metadata.json]
    B --> F[index/]
    
    C --> G[embeddings.parquet]
    C --> H[metadata.json]
    C --> I[index/]
    
    style A fill:#f5f5f5
    style D fill:#e1f5fe
    style E fill:#fff9c4
    style F fill:#e8f5e9
```

### Production Setup: PostgreSQL with pgvector

### 1. Database Setup

First, you need to set up PostgreSQL with pgvector extension:

```bash
# Connect to your PostgreSQL database and run:
psql -U your_username -d your_database -f scripts/add_document_embeddings_table.sql
```

### 2. Environment Variables

Ensure your `.env` file has the Azure OpenAI configuration:

```env
AZURE_ENDPOINT=https://your-endpoint.cognitiveservices.azure.com/
AZURE_API_KEY=your-api-key
```

### 3. Update Prisma Schema (Optional)

If you're using Prisma, add the document embeddings model to your schema:

```prisma
model DocumentEmbedding {
  id           String   @id @default(uuid())
  projectId    String   @map("project_id")
  filePath     String   @map("file_path")
  documentType String   @map("document_type")
  content      String
  embedding    Float[]  // Note: Prisma doesn't have native vector type
  metadata     Json     @default("{}")
  commitHash   String?  @map("commit_hash")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")
  
  project      Project  @relation(fields: [projectId], references: [id], onDelete: Cascade)
  
  @@map("document_embeddings")
  @@index([projectId])
  @@index([filePath])
  @@index([documentType])
}
```

## Usage

### Documentation Extraction Process

The system extracts documentation content from different file types:

#### Supported Documentation Formats

1. **Markdown Files (.md, .txt)**
   ```markdown
   # API Authentication
   
   Our authentication system uses JWT tokens with a 24-hour expiration.
   The tokens are validated on each request using middleware.
   ```

2. **Special Documentation Comments**
   ```typescript
   /// Summary: Validates invite tokens for the self-signup flow
   /// Purpose: Ensures that only users with valid invites can create accounts
   /// Context: Part of the user registration security layer
   function validateInviteToken(token: string): boolean {
     // Implementation details are NOT indexed
   }
   ```

3. **Regular Code Comments**
   ```typescript
   // This function handles user authentication flow
   // It validates credentials against our OAuth provider
   async function authenticate(credentials) {
     // Only comments are indexed, not the implementation
   }
   ```

4. **Machine-Readable Documentation (.doc.json)**
   ```json
   {
     "purpose": "Validates invite tokens for self-signup flow",
     "intent": "Ensures uniqueness of user emails",
     "context": "Security layer for user registration",
     "businessLogic": "Only invited users can create accounts"
   }
   ```


### Local Development Workflow

#### 1. Documentation Processing with TypeScript

The documentation processing is handled by the `DocumentProcessingService` which extracts only natural language content:

```typescript
// Extract documentation from various file types
const docs = await documentProcessor.processProjectDirectory(projectPath, {
  includeCodeComments: true,
  includeDocumentation: true,
  chunkSize: 800,
  overlap: 100
});

// Generate embeddings via backend API
const embeddings = await chromaService.processDocuments(docs);

// Store locally in ChromaDB
console.log(`Indexed ${docs.length} documentation chunks`);
```

#### 2. Hybrid Search Implementation

```typescript
// Combine local Chroma search with traditional search
class HybridSearchService {
  private chromaClient: ChromaClient;
  private ripgrepService: RipgrepService;
  
  async search(query: string, projectId: string): Promise<SearchResults> {
    // Parallel search execution
    const [semanticResults, textResults] = await Promise.all([
      this.semanticSearch(query, projectId),
      this.textSearch(query, projectId)
    ]);
    
    // Merge and rank results
    return this.mergeResults(semanticResults, textResults);
  }
  
  private async semanticSearch(query: string, projectId: string) {
    const collection = await this.chromaClient.getCollection({
      name: `project_${projectId}`
    });
    
    const results = await collection.query({
      queryTexts: [query],
      nResults: 20,
      include: ["documents", "metadatas", "distances"]
    });
    
    return this.formatSemanticResults(results);
  }
}
```

### 1. Generate Embeddings

Use the Azure service to generate embeddings for your documents:

```typescript
import azureService from './services/ai/azure.service';

// Single text embedding
const result = await azureService.generateEmbedding('Your text content here');
console.log('Embedding dimension:', result.embedding.length); // 1536
console.log('Cost:', result.usage.cost);

// Batch embeddings
const batchResult = await azureService.generateEmbeddings([
  'First document',
  'Second document',
  'Third document'
]);
console.log('Generated', batchResult.embeddings.length, 'embeddings');
```

### 2. Process Project Documentation

Process all documentation in a project:

```typescript
import { DocumentProcessingService } from './services/document-processing.service';

const docService = container.get<DocumentProcessingService>(TYPES.DocumentProcessingService);

// Process automated docs, READMEs, and code comments
await docService.processAutomatedDocs('/path/to/project', 'project-id');

// Process interview responses
await docService.processInterviewResponses([
  {
    question: 'What does this authentication module do?',
    answer: 'It handles JWT token validation and user session management',
    context: {
      filePath: 'src/auth/auth.service.ts',
      lineNumber: 42
    },
    timestamp: new Date()
  }
], 'project-id');
```

### 3. Search Using RAG

Search for documents using natural language queries:

```typescript
// Via REST API
POST /api/search/rag/query
{
  "query": "How does the authentication system work?",
  "projectId": "your-project-id",
  "limit": 10,
  "filters": {
    "documentTypes": ["auto-doc", "interview"],
    "fileTypes": ["*.ts", "*.js"]
  }
}

// Response
{
  "results": [
    {
      "id": "rag_project_0",
      "file": "src/auth/auth.service.ts",
      "type": "semantic",
      "confidence": 92,
      "documentType": "auto-doc",
      "matchedContent": "The authentication system validates JWT tokens...",
      "metadata": {
        "lastModified": "2025-05-31T10:00:00Z"
      }
    }
  ],
  "usage": {
    "tokensUsed": 245,
    "cost": 0.0000049,
    "durationMs": 350
  },
  "totalResults": 3
}
```

### 4. Frontend Integration

Add the semantic search tab to your SearchBar component:

```typescript
// The SearchBar already has the structure ready
// Just need to implement the semantic search mode

if (mode === 'semantic') {
  const response = await fetch('/api/search/rag/query', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      query: searchQuery,
      projectId: projectId,
      limit: 50
    })
  });
  
  const data = await response.json();
  // Transform results to SearchResult format
}
```

## Implementation Strategy

### Migration Path

```mermaid
graph LR
    subgraph "Phase 1: Local Development"
        A[Local Chroma] --> B[Fast Iteration]
        B --> C[Validate Quality]
    end
    
    subgraph "Phase 2: Hybrid Mode"
        D[Local Chroma] --> E[Personal Projects]
        F[PostgreSQL] --> G[Shared Knowledge]
    end
    
    subgraph "Phase 3: Enterprise"
        H[Centralized Vector DB] --> I[Team Search]
        I --> J[Cross-Project Insights]
    end
    
    C -->|Export| D
    E -->|Sync| H
    G -->|Scale| H
    
    style A fill:#e8f5e9
    style D fill:#fff9c4
    style H fill:#e3f2fd
```

### Local Storage Best Practices

1. **Security**
   - Encrypt local Chroma database: `~/.kapi/chroma_db`
   - Exclude from version control: add to `.gitignore`
   - Clear on project deletion
   - Optional cloud sync with user consent

2. **Performance Optimization**
   - Use parquet format for efficient storage
   - Implement incremental updates for changed files
   - Batch process documents (20-50 at a time)
   - Cache frequently accessed embeddings in memory

3. **Storage Management**
   ```typescript
   // Built into ChromaDbService for automatic cleanup
   async getStats(): Promise<{ documentCount: number; lastUpdated: string }> {
     const count = await this.collection!.count();
     return {
       documentCount: count,
       lastUpdated: new Date().toISOString()
     };
   }
   
   // Clear project documentation
   async clearDocuments(): Promise<void> {
     const results = await this.collection!.get();
     if (results.ids && results.ids.length > 0) {
       await this.collection!.delete({ ids: results.ids });
     }
   }
   ```

## Best Practices

### 1. Document Processing

- Process documents in batches to optimize API usage
- Use the 800-token chunk size with 100-token overlap
- Store metadata for better filtering and context

### 2. Cost Optimization

- Cache embeddings for frequently accessed documents
- Batch embedding generation (up to 20 texts at once)
- Monitor usage with the provided metrics

### 3. Search Quality

- Use descriptive queries for better results
- Combine with traditional search for comprehensive results
- Set appropriate similarity thresholds (default: 0.7)

## API Endpoints

### Generate Embeddings (Internal Use)

```typescript
// Service method
const embeddings = await azureService.generateEmbeddings(texts);
```

### Search Documents

```
POST /api/search/rag/query
```

### Process Project Documents

```
POST /api/search/rag/process
```

### Get Embedding Statistics

```
POST /api/search/rag/stats
```

## Monitoring and Debugging

### Check Embedding Generation

```typescript
// Enable debug logging
import { logger } from './common/logger';
logger.level = 'debug';

// Monitor in logs:
// - Token usage per request
// - Cost calculations
// - Processing duration
```

### Database Queries

```sql
-- Check embeddings for a project
SELECT count(*), document_type 
FROM document_embeddings 
WHERE project_id = 'your-project-id'
GROUP BY document_type;

-- Find similar documents manually
SELECT file_path, 
       1 - (embedding <=> (SELECT embedding FROM document_embeddings WHERE id = 'target-id')) as similarity
FROM document_embeddings
WHERE project_id = 'your-project-id'
ORDER BY embedding <=> (SELECT embedding FROM document_embeddings WHERE id = 'target-id')
LIMIT 10;
```

## Troubleshooting

### Common Issues

1. **"No embeddings found"**
   - Ensure documents have been processed
   - Check if the project ID is correct
   - Verify database connection

2. **"High costs"**
   - Reduce chunk sizes
   - Implement caching
   - Batch more aggressively

3. **"Poor search results"**
   - Improve document quality
   - Adjust chunk overlap
   - Use more descriptive queries

## Chroma vs PostgreSQL Comparison

| Feature | Chroma (Local) | PostgreSQL + pgvector |
|---------|----------------|----------------------|
| **Setup Complexity** | Simple pip/npm install | Requires extension setup |
| **Performance** | <100ms local queries | Network latency (200-500ms) |
| **Offline Support** | ✅ Full functionality | ❌ Requires connection |
| **Storage** | Local disk (~10-50MB/project - docs only) | Cloud storage costs |
| **Scaling** | Single machine | Horizontal scaling |
| **Team Features** | ❌ Individual only | ✅ Shared embeddings |
| **Backup** | Manual/Git LFS | Automated cloud backup |
| **Cost** | Free (local compute) | Cloud hosting + API calls |

## Future Enhancements

1. **Multi-language support** - Extend beyond English
2. **Code-aware embeddings** - Use specialized models
3. **Cross-project search** - Search across multiple projects
4. **Smart caching** - Predictive cache warming
5. **Query expansion** - Automatic synonym inclusion

## Cost Estimation (Documentation-Only Approach)

**Significantly Reduced Costs Due to Documentation-Only Indexing:**

- Embedding generation: $0.00002 per 1K tokens
- Average documentation chunk: ~200-400 tokens (comments/docs vs. ~800 for full code)
- Cost per documentation chunk: ~$0.000004-$0.000008
- Typical project (1,000 documentation chunks): ~$0.004-$0.008
- Large project (5,000 documentation chunks): ~$0.02-$0.04

**Cost Savings vs. Full Code Indexing:**
- **90% reduction** in tokens to embed (docs vs. full codebase)
- **90% reduction** in storage requirements
- **10x faster** indexing and search

Monitor your usage with the provided statistics endpoint.

## 🧹 Server-Side RAG Cleanup Required

The following server-side RAG components were built but are **unused** and should be cleaned up:

### Files to Remove/Archive

```bash
# Backend services (nodejs_backend/src/services/)
- vector.service.ts           # Placeholder vector service
- document-processing.service.ts  # Server-side document processing

# API controllers
- api/search/rag-search.controller.ts  # RAG search endpoints (unused)

# Database schema
- scripts/add_document_embeddings_table.sql  # PostgreSQL schema
- prisma/schema additions for document_embeddings  # Prisma model
```

### Why These Are Unused

1. **Local-First Decision**: We chose ChromaDB in the IDE for better performance and privacy
2. **DRY Violation**: Server-side services duplicated functionality now in the frontend
3. **Complexity**: PostgreSQL + pgvector setup unnecessary for local development
4. **Performance**: Local ChromaDB provides sub-100ms queries vs 200-500ms network calls

### Cleanup Action Items

- [ ] Remove unused `vector.service.ts` from backend
- [ ] Remove `rag-search.controller.ts` and unregister routes
- [ ] Archive PostgreSQL schema files 
- [ ] Update documentation to reflect local-first implementation
- [ ] Remove any dead imports or references

### Migration Benefits

By implementing documentation-only local RAG:
- ✅ **Simplified architecture** - No server-side vector storage complexity
- ✅ **Better performance** - <100ms local documentation search vs network calls
- ✅ **Enhanced privacy** - Documentation and explanations stay local
- ✅ **Focused results** - Search targets "what" and "why", not implementation details
- ✅ **Reduced costs** - 90% reduction in embedding and storage costs
- ✅ **Faster indexing** - Only documentation content, not entire codebases
- ✅ **Better user experience** - Results focus on human explanations

## See Also

**Product Documentation:**
- [Semantic Search & RAG](../../02-products/ai_assistance/14-semantic-search-rag.md) - User-facing features and experience
- [AI Memory & Context](../../02-products/ai_assistance/11-ai-memory-context.md) - How search integrates with memory system
- [Deep Search](../../02-products/core_ide/14-deep-search.md) - Traditional search capabilities

**Technical Documentation:**
- [Vector Service Implementation](../ai-models.md) - Vector database and embedding services
- [Azure Service Configuration](../ai-models.md) - Azure OpenAI setup and configuration
- [Database Schema](../database-schema.md) - Complete database design including embeddings tables