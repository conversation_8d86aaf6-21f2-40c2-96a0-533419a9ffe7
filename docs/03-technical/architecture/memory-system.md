# KAPI Memory System: Technical Implementation

> **Product Context**: For user-facing memory features and benefits, see [AI Memory & Context Intelligence](../../02-products/ai_assistance/11-ai-memory-context.md)

This document outlines the technical implementation of KAPI's memory architecture, focusing on data structures, algorithms, and system design for developer implementation.

## In Brief 🧠 Memory System (.kapi Folder)

**Format**: YAML + Markdown files

**Scope**:
- **User Memory**: Personal preferences, coding style, AI interaction preferences
- **Project Memory**: Business logic, architectural decisions, conventions
- **Team Memory**: Shared patterns, best practices, knowledge base

**Features**:
- Transparent and editable by users
- Version-controlled alongside code
- Daily AI-driven reviews and quizzes
- Cached during sessions, synced post-task

## Technical Goals & Constraints

- **Token Budget**: Maximum 5,000 tokens per LLM context window
- **Memory Allocation**: 70% Static context (~3,500 tokens), 30% Dynamic (~1,500 tokens)
- **Storage Format**: YAML + Markdown for human readability and version control
- **Update Frequency**: Real-time for active tasks, batch updates for static context
- **Performance Target**: <100ms context assembly, <1MB memory footprint per project

## Core Memory Components (User-Visible)

Five structured, markdown-based slide decks form the static foundation for reasoning and communication:

### 1. User Context
- Stored as a compact **key-value slide deck** (editable markdown)
- Target size: **~200 tokens**
- Captures long-lived traits and current learning focus
- Readable and editable by user
- Automatically updated based on interactions

**Example format in YAML:**
```yaml
user_context:
  skill_level: Intermediate
  prefers_voice_input: true
  preferred_stack: Python, FastAPI, Postgres
  learning_focus: Vector DBs, AI Agents
  prior_workshops: Modern AI Pro, Vibe Coding
  dev_style: Minimalist, Command-line oriented
  current_motivation_mode: Builder
  project_frequency: High
```

### 2. Project Business Context
- Stored as a **structured markdown slide deck** (~300 tokens)
- Defines the **"why"** behind the project
- User-editable with LLM assistance
- Stable across sessions and visible to all agents

**Typical sections (one slide per bullet):**
```markdown
# Slide 1: Purpose
Build an agentic IDE that accelerates Software Engineering 2.0 with voice-first, multimodal workflows.

# Slide 2: Target Audience
Indie developers, early-stage startup teams, engineers adopting AI-assisted workflows.

# Slide 3: Core Value Proposition
Reduce boilerplate, improve debugging speed, and promote structured software thinking through adaptive agent support.

# Slide 4: Differentiators
- Voice-driven project setup
- Adaptive memory model (Backwards Build)
- Deep slide-based context understanding
```

### 3. Project Technical Context
- Stored as a **markdown-based slide deck**, often auto-generated or LLM-assisted
- Captures the **"how"** of the system: architecture, stack, design constraints
- Target size: **500–1,000 tokens**, selectively loaded based on task

**Typical slides:**
```markdown
# Slide 1: Tech Stack
- Frontend: Electron + Vite
- Backend: FastAPI, LangChain agents
- Memory: pgvector + Redis cache
- Infra: Azure VM, Dockerized services

# Slide 2: Architecture Overview
```mermaid
graph TD
  ChatPanel --> ContextManager
  ContextManager --> VectorStore
  ContextManager --> TokenBudgeter
```
```

**Known constraints:**
- Can pull auto-summarized file/module comments via `@summary` blocks
- Slide order is flexible; some projects may include:
  - Feature roadmap
  - Security assumptions
  - Compatibility notes

### 4. Code Context
- File layout, active modules, key functions
- Maintained via inline `@summary` comments
- Aggregated hierarchically to modules and folders (~300 tokens)

### 5. Task Context (Folder)
- One markdown per task: objectives, status, blockers, diffs
- Only active task(s) loaded in context (~1,000–1,500 tokens)
- Old tasks stored for memory and audit

## Agent-Specific Memory (Private, Non-Visible)

Each agent (e.g., `CodeGenAgent`, `EvidenceCollector`) has:
- Selective, filtered views of core memory
- Internal memory (hypotheses, work logs, state)
- Update permissions on certain decks (configurable)

## Background Processes

Five essential background processes power the memory architecture:

### 1. User Context Manager
- **Purpose**: Extract, normalize, and update key-value pairs for the user context.
- **Inputs**: Conversational history, onboarding inputs, behavioral patterns.
- **Tasks**:
   - Parse user statements like "I'm in SF" or "I prefer TypeScript" into structured fields.
   - Update learning focus based on workshop completions.
   - Surface stale or missing fields for agent suggestion.
- **Output**: Updated `user_context.yaml`.

### 2. Documentation Summarizer
- **Purpose**: Maintain lightweight `@summary` blocks across source files.
- **Inputs**: Code files, diffs, commits.
- **Tasks**:
   - Run low-cost model to generate 2–3 line summaries.
   - Embed `@summary` comments or write to `.kapi-summary`.
   - Aggregate summaries folder-wise and module-wise.
- **Output**: File-level summaries → module summaries → tech context deck.

### 3. Context Freshness Monitor
- **Purpose**: Detect when core memory components go stale or need recomputation.
- **Inputs**: Git history, file timestamps, slide edit logs.
- **Tasks**:
   - Flag outdated summaries or architecture diagrams.
   - Track when task context references stale file paths.
   - Notify agents/UI for refresh opportunities.
- **Output**: `last_updated` metadata + soft refresh triggers.

### 4. Slide Builder & Aggregator
- **Purpose**: Assemble and maintain markdown slide decks for each memory layer.
- **Inputs**: Raw memory files (summaries, structured notes).
- **Tasks**:
   - Build `project_business.md`, `tech_context.md`, etc.
   - Ensure Reveal.js compliance.
   - Normalize formatting and section titles.
- **Output**: Clean, LLM-readable and human-readable decks.

### 5. Token Budget Planner
- **Purpose**: Dynamically score and allocate context slices per agent invocation.
- **Inputs**: Current prompt, user mode, available memory views.
- **Tasks**:
   - Assign token quotas per memory layer (based on 70/30 rule).
   - Rank slices by relevance score and freshness.
   - Summarize or drop less critical pieces to stay under 5k tokens.
- **Output**: Optimized context block for LLM call.

## Memory Compression Strategy
- Use low-cost models to generate and update 2–3 line `@summary` blocks per file
- Summaries aggregated hierarchically → module-level → project-level
- Used for both internal context and user-facing documentation (Reveal.js / Mintlify style)

## Context Management Principles
- **Global Context Registry**: Centralized reference to memory components
- **Freshness & Versioning**: Timestamps and change detection for each memory slice
- **Agent Policy Layer**: Defines read/write scopes and safe update rules
- **Context Debugger UI (Planned)**: Pin/unpin context, view history, inspect agent memory slices
## Implementation Flow Diagram

```
┌─────────────┐     ┌─────────────────┐     ┌───────────────┐
│ Git Changes │────▶│ Freshness       │────▶│ Documentation │
└─────────────┘     │ Monitor         │     │ Summarizer    │
                    └─────────────────┘     └───────┬───────┘
                           ▲                        │
                           │                        ▼
┌─────────────┐     ┌─────┴───────────┐     ┌───────────────┐     ┌───────────────┐
│ User        │────▶│ Slide Builder & │◀────┤ File & Module │     │ Token Budget  │
│ Interaction │     │ Aggregator      │     │ Summaries     │     │ Planner       │
└─────────────┘     └─────────────────┘     └───────────────┘     └───────┬───────┘
                           │                                              │
                           ▼                                              ▼
                    ┌─────────────────┐                           ┌───────────────┐
                    │ Core Memory     │◀─────────────────────────┤ Agent Context │
                    │ Slide Decks     │                          │ Assembly      │
                    └─────────────────┘                          └───────────────┘
```

## Integration with Kapi Features

### Adaptive Project Identity System
- Memory architecture adapts based on project motivation mode:
  - **Learner Mode**: More emphasis on explanatory context
  - **Contributor Mode**: Focus on git history and codebase navigation
  - **Builder Mode**: Full-featured context with business requirements

### Multi-Modal Development
- Voice interactions are transcribed, summarized, and incorporated into memory
- Sketches and diagrams are converted to mermaid/D2 or described textually
- Visual elements (UI mockups, wireframes) are referenced and described

### Backwards Build Approach
- Documentation-first workflow is reflected in memory prioritization
- Slide decks serve dual purpose as documentation and LLM context
- Testing information is prioritized over implementation details

## Implementation Considerations

### Memory Persistence
- Store slide decks as markdown files in a `.kapi` folder within projects
- Use a lightweight database (SQLite) for metadata and freshness tracking
- Consider vector embeddings for retrieval during context assembly

### Progressive Rollout
1. Start with User Context Manager and Documentation Summarizer
2. Add Token Budget Planner as it becomes critical at scale
3. Implement Slide Builder & Aggregator and Context Freshness Monitor as refinements

### User Experience Design
- Make context visible and editable in a dedicated "Project Memory" tab
- Show which context components were used in generating a response
- Allow pinning critical context components that shouldn't be dropped

## Database Schema Implementation

### Memory Tables
```sql
-- Core memory metadata tracking
CREATE TABLE memory_components (
  id UUID PRIMARY KEY,
  project_id UUID REFERENCES projects(id),
  component_type VARCHAR(50) NOT NULL, -- 'user', 'business', 'technical', 'code', 'task'
  file_path VARCHAR(255) NOT NULL,
  token_count INTEGER NOT NULL,
  last_updated TIMESTAMP DEFAULT NOW(),
  freshness_score DECIMAL(3,2) DEFAULT 1.0,
  created_by UUID REFERENCES users(id)
);

-- Track which context was used in LLM calls
CREATE TABLE context_usage (
  id UUID PRIMARY KEY,
  conversation_id UUID REFERENCES conversations(id),
  memory_component_id UUID REFERENCES memory_components(id),
  tokens_used INTEGER NOT NULL,
  relevance_score DECIMAL(3,2) NOT NULL,
  timestamp TIMESTAMP DEFAULT NOW()
);

-- Cache computed context assemblies
CREATE TABLE context_cache (
  cache_key VARCHAR(255) PRIMARY KEY,
  assembled_context TEXT NOT NULL,
  token_count INTEGER NOT NULL,
  expires_at TIMESTAMP NOT NULL,
  created_at TIMESTAMP DEFAULT NOW()
);
```

### File System Structure
```
.kapi/
├── memory/
│   ├── user_context.yaml           # User preferences & style
│   ├── project_business.md         # Business context slides
│   ├── project_technical.md        # Technical architecture slides
│   ├── code_structure.md           # Auto-generated code summaries
│   └── tasks/
│       ├── active/                 # Current task contexts
│       └── completed/              # Historical task data
├── cache/
│   ├── context_assembly.json       # Pre-computed context blocks
│   └── summaries/                  # Cached file summaries
└── metadata/
    ├── freshness.json              # Component update tracking
    └── usage_stats.json            # Context usage analytics
```

## Algorithm Implementation

### Context Assembly Algorithm
```typescript
interface ContextAssemblyOptions {
  maxTokens: number;
  taskType: string;
  userMode: 'learner' | 'contributor' | 'builder';
  priorityComponents?: string[];
}

class ContextAssembler {
  async assembleContext(options: ContextAssemblyOptions): Promise<AssembledContext> {
    // 1. Load component metadata with freshness scores
    const components = await this.loadComponentMetadata(options.taskType);
    
    // 2. Calculate relevance scores based on task type and user mode
    const scoredComponents = components.map(component => ({
      ...component,
      relevanceScore: this.calculateRelevance(component, options)
    }));
    
    // 3. Sort by composite score (relevance * freshness)
    scoredComponents.sort((a, b) => 
      (b.relevanceScore * b.freshnessScore) - (a.relevanceScore * a.freshnessScore)
    );
    
    // 4. Greedy selection within token budget
    const selectedComponents = [];
    let tokenCount = 0;
    
    for (const component of scoredComponents) {
      if (tokenCount + component.tokenCount <= options.maxTokens) {
        selectedComponents.push(component);
        tokenCount += component.tokenCount;
      }
    }
    
    // 5. Assemble final context with proper formatting
    return this.formatContext(selectedComponents);
  }
  
  private calculateRelevance(component: MemoryComponent, options: ContextAssemblyOptions): number {
    // Relevance scoring algorithm based on component type and task
    const baseScores = {
      'user': options.userMode === 'learner' ? 0.9 : 0.6,
      'business': options.taskType.includes('architecture') ? 0.8 : 0.4,
      'technical': 0.7,
      'code': options.taskType.includes('generation') ? 0.9 : 0.5,
      'task': 1.0 // Always highly relevant
    };
    
    return baseScores[component.type] || 0.5;
  }
}
```

### Background Process Implementation
```typescript
// Documentation Summarizer Service
class DocumentationSummarizer {
  async processFileChanges(changedFiles: string[]): Promise<void> {
    for (const filePath of changedFiles) {
      const content = await fs.readFile(filePath, 'utf-8');
      const summary = await this.generateSummary(content, filePath);
      
      // Update or create @summary comment
      await this.updateFileSummary(filePath, summary);
      
      // Update memory component metadata
      await this.updateMemoryComponent('code', filePath, summary);
    }
  }
  
  private async generateSummary(content: string, filePath: string): Promise<string> {
    // Use low-cost model for summary generation
    const prompt = `Generate a 2-3 line summary for this ${path.extname(filePath)} file:\n\n${content}`;
    return await this.llmService.generate(prompt, { model: 'gemini-flash', maxTokens: 100 });
  }
}

// Context Freshness Monitor
class FreshnessMonitor {
  async checkFreshness(): Promise<void> {
    const components = await this.db.query(`
      SELECT * FROM memory_components 
      WHERE last_updated < NOW() - INTERVAL '24 hours'
    `);
    
    for (const component of components) {
      const freshnessScore = await this.calculateFreshness(component);
      await this.updateFreshnessScore(component.id, freshnessScore);
      
      if (freshnessScore < 0.3) {
        await this.queueForRefresh(component);
      }
    }
  }
}
```

## Performance Optimizations

### Caching Strategy
```typescript
class MemoryCache {
  private contextCache = new Map<string, CachedContext>();
  private summaryCache = new Map<string, string>();
  
  async getCachedContext(cacheKey: string): Promise<AssembledContext | null> {
    const cached = this.contextCache.get(cacheKey);
    if (cached && cached.expiresAt > Date.now()) {
      return cached.context;
    }
    return null;
  }
  
  async setCachedContext(cacheKey: string, context: AssembledContext, ttlMs: number): Promise<void> {
    this.contextCache.set(cacheKey, {
      context,
      expiresAt: Date.now() + ttlMs
    });
    
    // Persist to database for cross-session caching
    await this.db.query(`
      INSERT INTO context_cache (cache_key, assembled_context, token_count, expires_at)
      VALUES ($1, $2, $3, $4)
      ON CONFLICT (cache_key) DO UPDATE SET
        assembled_context = $2, token_count = $3, expires_at = $4
    `, [cacheKey, JSON.stringify(context), context.tokenCount, new Date(Date.now() + ttlMs)]);
  }
}
```

## Future Technical Enhancements

1. **Vector-Based Context Retrieval**
   - Embed memory components for semantic similarity matching
   - Hybrid search combining relevance scores with vector similarity

2. **Distributed Memory Architecture**
   - Redis-based memory component caching for team synchronization
   - Eventual consistency model for cross-team memory updates

3. **Memory Compression Algorithms**
   - Adaptive summarization based on available token budget
   - Hierarchical memory with drill-down capabilities

4. **Context Prediction Engine**
   - ML model to predict which context components will be needed
   - Pre-assembly of likely context combinations

## See Also

**Product Documentation:**
- [AI Memory & Context Intelligence](../../02-products/ai_assistance/11-ai-memory-context.md) - User-facing features and benefits
- [AI Agents](../../02-products/ai_assistance/03-ai-agents.md) - How agents use memory system
- [Backwards Build](../../02-products/dev_workflow/05-backwards-build.md) - Documentation-driven development

**Technical Documentation:**
- [Database Schema](../database-schema.md) - Complete database design
- [System Architecture](../00-system-design.md) - Overall system design
- [Performance Requirements](../performance-requirements.md) - System performance targets
