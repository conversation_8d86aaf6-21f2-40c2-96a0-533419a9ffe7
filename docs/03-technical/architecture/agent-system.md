# KAPI Agentic Assistant Overview

*Last updated: May 13, 2025*

## Introduction

The **KAPI Agentic Assistant** is a multi-agent system integrated into the KAPI IDE that enables users to describe high-level tasks in natural language and have them executed autonomously. Unlike a single monolithic agent, this assistant coordinates specialized agents across different stages of the software development process, reflecting the **Backwards Build** philosophy.

Each agent operates independently but sequentially, visualized through a dynamic interface. The system emphasizes high-level reasoning and process clarity, rather than just code generation.

## Multi-Agent Flow Visualization

The assistant's interface visualizes the orchestration of multiple agents working across a four-stage development pipeline:

### 🚦 Backwards Build Pipeline

1. **Evidence Collection Agent**

   * Gathers requirements from user prompts, documents, or previous interactions
   * Summarized output is minimized but accessible

2. **Documentation & Slides Agent** *(Active)*

   * Generates documentation (e.g., README, API specs, architecture slides)
   * Uses structured reasoning: Thinking → Action → Result → Progress

3. **Code Generation Agent** *(Waiting)*

   * Triggered only once sufficient documentation exists
   * Dimmed UI with "waiting" indicators

4. **Code Quality Agent** *(Waiting)*

   * Final agent to run tests, lint, and perform refactoring suggestions
   * Dependent on code being ready

### 🔄 Artifact Transformation

A separate visual panel tracks the evolution of project assets:

* Requirements → Documentation → Code
* Real-time progress indicators
* Emphasizes dependencies between artifacts

## User Benefits

* **Multi-Agent Coordination**: Users see task decomposition across multiple AI assistants
* **Sequential Spotlight**: Only the active agent is highlighted to reduce cognitive load
* **Stage Awareness**: Each agent waits for its prerequisite to complete
* **Context Retention**: Completed stages remain visible in a minimized state

## Core Agentic Features

* Natural language command interface
* ReAct loop (Reason → Action → Observation)
* Shell command execution with sandboxing
* Smart file editing with diff tracking
* Markdown-formatted progress updates
* Awareness of project structure, code language, and user intent

## User Workflow

1. User opens the Agent Sidebar in KAPI IDE
2. Describes task (e.g., "Add login tests")
3. Evidence Agent gathers context
4. Documentation Agent outlines required files and documentation
5. Code Agent implements the logic
6. Quality Agent reviews and tests code
7. Final summary and diffs shown to user

## Project Mode Integration

| Project Mode | Agent Behavior                           |
| ------------ | ---------------------------------------- |
| Learner      | Explanations, coaching-style reasoning   |
| Contributor  | Conservative changes, focus on standards |
| Builder      | Bold changes, architectural suggestions  |

## Safeguards

* Whitelisted shell commands only
* Timeout enforcement on commands (30s)
* Path validation and file extension filters
* Max iteration caps and cooldowns

## Next Steps

* Complete frontend visualization with real-time updates
* Finalize logic handoffs between agents
* Test in varied project modes
* Gather user feedback for iteration

## Summary

The KAPI Agentic Assistant exemplifies a collaborative AI system where specialized agents work in harmony across a structured software lifecycle. This multi-agent flow enforces discipline, encourages documentation-first workflows, and brings the Backwards Build philosophy to life for every user.

<?xml version="1.0" encoding="UTF-8"?>
<svg viewBox="0 0 1200 800" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <rect width="1200" height="800" fill="#1e2227" />
  
  <!-- Pipeline Visualization -->
  <rect x="150" y="30" width="900" height="60" rx="30" fill="#282c34" />
  
  <!-- Pipeline Stages -->
  <circle cx="250" cy="60" r="25" fill="#8869ba" stroke="#ffffff" stroke-width="2" />
  <text x="250" y="65" font-family="Arial" font-size="12" fill="white" text-anchor="middle">1</text>
  <rect x="275" y="58" width="150" height="4" fill="#8869ba" />
  
  <circle cx="450" cy="60" r="25" fill="#6a549e" stroke="#ffffff" stroke-width="2" />
  <text x="450" y="65" font-family="Arial" font-size="12" fill="white" text-anchor="middle">2</text>
  <rect x="475" y="58" width="150" height="4" fill="#6a549e" />
  
  <circle cx="650" cy="60" r="25" fill="#444" stroke="#8869ba" stroke-width="2" stroke-dasharray="4" />
  <text x="650" y="65" font-family="Arial" font-size="12" fill="#8869ba" text-anchor="middle">3</text>
  <rect x="675" y="58" width="150" height="4" fill="#444" />
  
  <circle cx="850" cy="60" r="25" fill="#444" stroke="#8869ba" stroke-width="2" stroke-dasharray="4" />
  <text x="850" y="65" font-family="Arial" font-size="12" fill="#8869ba" text-anchor="middle">4</text>
  
  <!-- Pipeline Labels -->
  <text x="250" y="100" font-family="Arial" font-size="12" fill="#ffffff" text-anchor="middle">Evidence</text>
  <text x="250" y="115" font-family="Arial" font-size="12" fill="#50fa7b" text-anchor="middle">Complete</text>
  
  <text x="450" y="100" font-family="Arial" font-size="12" fill="#ffffff" text-anchor="middle">Documentation</text>
  <text x="450" y="115" font-family="Arial" font-size="12" fill="#56b6c2" text-anchor="middle">In Progress</text>
  
  <text x="650" y="100" font-family="Arial" font-size="12" fill="#8869ba" text-anchor="middle">Code</text>
  <text x="650" y="115" font-family="Arial" font-size="12" fill="#8869ba" text-anchor="middle">Waiting</text>
  
  <text x="850" y="100" font-family="Arial" font-size="12" fill="#8869ba" text-anchor="middle">Quality</text>
  <text x="850" y="115" font-family="Arial" font-size="12" fill="#8869ba" text-anchor="middle">Waiting</text>
  
  <!-- Handoff Animation Indicator -->
  <text x="600" y="25" font-family="Arial" font-size="14" fill="#56b6c2">Evidence → Documentation Handoff Complete</text>
  
  <!-- Evidence Collection Agent (Completed, Minimized) -->
  <rect x="50" y="150" width="250" height="180" rx="10" fill="#282c34" stroke="#8869ba" stroke-width="2" />
  <rect x="50" y="150" width="250" height="40" rx="10" fill="#8869ba" />
  <text x="70" y="175" font-family="Arial" font-size="16" fill="white">Evidence Collection Agent</text>
  <circle cx="280" cy="170" r="10" fill="#50fa7b" />
  <text x="280" cy="170" font-family="Arial" font-size="14" fill="#282c34" text-anchor="middle">✓</text>
  
  <!-- Evidence Collection Summary -->
  <rect x="60" y="200" width="230" height="120" rx="5" fill="#21252b" />
  <text x="70" y="220" font-family="Arial" font-size="14" fill="#d0d0d0">Completed Requirements Analysis</text>
  
  <text x="70" y="250" font-family="Arial" font-size="12" fill="#56b6c2">Identified Core Requirements:</text>
  <text x="70" y="270" font-family="Arial" font-size="12" fill="#98c379">• Notification system for events</text>
  <text x="70" y="290" font-family="Arial" font-size="12" fill="#98c379">• User subscription management</text>
  <text x="70" y="310" font-family="Arial" font-size="12" fill="#98c379">• Integration with API client</text>
  
  <!-- Documentation & Slides Agent (Active) -->
  <rect x="50" y="350" width="1100" height="400" rx="10" fill="#282c34" stroke="#6a549e" stroke-width="2" />
  <rect x="50" y="350" width="1100" height="40" rx="10" fill="#6a549e" />
  <text x="70" y="375" font-family="Arial" font-size="16" fill="white">Documentation & Slides Agent</text>
  <circle cx="1130" cy="370" r="10" fill="#56b6c2" />
  
  <!-- Documentation Process -->
  <rect x="60" y="400" width="1080" height="340" rx="5" fill="#21252b" />
  
  <!-- Thinking Process Panel -->
  <rect x="70" y="410" width="1060" height="320" rx="5" fill="#282c34" />
  
  <!-- Thinking, Action, Result, Complete Stages -->
  <rect x="80" y="420" width="1040" height="60" rx="5" fill="#323842" />
  <text x="100" y="460" font-family="Arial" font-size="18" fill="#56b6c2">Thinking: Creating documentation for notification system...</text>
  
  <rect x="80" y="490" width="1040" height="60" rx="5" fill="#323842" />
  <text x="100" y="530" font-family="Arial" font-size="18" fill="#56b6c2">Action: Drafting README.md and API specification</text>
  
  <rect x="80" y="560" width="1040" height="60" rx="5" fill="#323842" />
  <text x="100" y="600" font-family="Arial" font-size="18" fill="#50fa7b">Result: Defined NotificationService class interface and usage patterns</text>
  
  <rect x="80" y="630" width="1040" height="60" rx="5" fill="#323842" />
  <text x="100" y="655" font-family="Arial" font-size="18" fill="#d0d0d0">Building documentation sections:</text>
  <text x="100" y="680" font-family="Arial" font-size="16" fill="#d0d0d0">• Class overview (Done) • Method specifications (70%) • Usage examples (40%) • Integration guide (Pending)</text>
  
  <!-- Code Generation Agent (Waiting, Dimmed) -->
  <rect x="320" y="150" width="250" height="180" rx="10" fill="#282c34" stroke="#444" stroke-width="2" stroke-dasharray="4" />
  <rect x="320" y="150" width="250" height="40" rx="10" fill="#444" />
  <text x="340" y="175" font-family="Arial" font-size="16" fill="#8869ba">Code Generation Agent</text>
  <circle cx="550" cy="170" r="10" fill="#444" />
  
  <!-- Code Generation Waiting -->
  <rect x="330" y="200" width="230" height="120" rx="5" fill="#21252b" />
  <text x="340" y="240" font-family="Arial" font-size="14" fill="#8869ba" text-anchor="middle">Waiting for Documentation</text>
  <text x="340" y="270" font-family="Arial" font-size="40" fill="#8869ba" text-anchor="middle">⋯</text>
  
  <!-- Code Quality Agent (Waiting, Dimmed) -->
  <rect x="590" y="150" width="250" height="180" rx="10" fill="#282c34" stroke="#444" stroke-width="2" stroke-dasharray="4" />
  <rect x="590" y="150" width="250" height="40" rx="10" fill="#444" />
  <text x="610" y="175" font-family="Arial" font-size="16" fill="#8869ba">Code Quality Agent</text>
  <circle cx="820" cy="170" r="10" fill="#444" />
  
  <!-- Code Quality Waiting -->
  <rect x="600" y="200" width="230" height="120" rx="5" fill="#21252b" />
  <text x="610" y="240" font-family="Arial" font-size="14" fill="#8869ba" text-anchor="middle">Waiting for Code Generation</text>
  <text x="610" y="270" font-family="Arial" font-size="40" fill="#8869ba" text-anchor="middle">⋯</text>
  
  <!-- Artifact Transformation Visual (Backwards Build) -->
  <rect x="860" y="150" width="290" height="180" rx="10" fill="#282c34" stroke="#6a549e" stroke-width="2" />
  <rect x="860" y="150" width="290" height="40" rx="10" fill="#6a549e" />
  <text x="880" y="175" font-family="Arial" font-size="16" fill="white">Artifact Transformation</text>
  
  <!-- Transformation Stages -->
  <rect x="870" y="200" width="270" height="120" rx="5" fill="#21252b" />
  
  <!-- Requirements Doc (Complete) -->
  <rect x="880" y="210" width="60" height="80" rx="3" fill="#282c34" stroke="#50fa7b" stroke-width="2" />
  <text x="910" y="255" font-family="Arial" font-size="30" fill="#50fa7b" text-anchor="middle">📋</text>
  <text x="910" y="300" font-family="Arial" font-size="10" fill="#50fa7b" text-anchor="middle">Requirements</text>
  
  <!-- Documentation (In Progress) -->
  <rect x="960" y="210" width="60" height="80" rx="3" fill="#282c34" stroke="#56b6c2" stroke-width="2" />
  <text x="990" y="255" font-family="Arial" font-size="30" fill="#56b6c2" text-anchor="middle">📄</text>
  <text x="990" y="300" font-family="Arial" font-size="10" fill="#56b6c2" text-anchor="middle">Documentation</text>
  
  <!-- Code File (Waiting) -->
  <rect x="1040" y="210" width="60" height="80" rx="3" fill="#282c34" stroke="#444" stroke-width="2" stroke-dasharray="4" />
  <text x="1070" y="255" font-family="Arial" font-size="30" fill="#444" text-anchor="middle">📝</text>
  <text x="1070" y="300" font-family="Arial" font-size="10" fill="#444" text-anchor="middle">Code</text>
  
  <!-- Transformation Arrows -->
  <line x1="945" y1="250" x2="955" y2="250" stroke="#50fa7b" stroke-width="2" />
  <line x1="955" y1="250" x2="955" y2="250" stroke="#50fa7b" stroke-width="2" marker-end="url(#arrow)" />
  
  <line x1="1025" y1="250" x2="1035" y2="250" stroke="#56b6c2" stroke-width="2" stroke-dasharray="4" />
  <line x1="1035" y1="250" x2="1035" y2="250" stroke="#56b6c2" stroke-width="2" stroke-dasharray="4" marker-end="url(#arrow)" />
  
  <defs>
    <marker id="arrow" markerWidth="10" markerHeight="10" refX="9" refY="3" orient="auto" markerUnits="strokeWidth">
      <path d="M0,0 L0,6 L9,3 z" fill="#56b6c2" />
    </marker>
  </defs>
  
  <!-- Input Area -->
  <rect x="50" y="760" width="1100" height="30" rx="15" fill="#323842" />
  <text x="70" y="780" font-family="Arial" font-size="14" fill="#abb2bf">Describe your task...</text>
</svg>