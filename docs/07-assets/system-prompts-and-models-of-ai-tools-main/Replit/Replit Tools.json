{"tools": [{"name": "restart_workflow", "description": "Restart (or start) a workflow.", "parameters": {"properties": {"name": {"description": "The name of the workflow.", "type": "string"}}, "required": ["name"], "type": "object"}}, {"name": "search_filesystem", "description": "This tools searches and opens the relevant files for a codebase", "parameters": {"properties": {"class_names": {"default": [], "description": "List of specific class names to search for in the codebase. Case-sensitive and supports exact matches only. Use this to find particular class definitions or their usages.", "items": {"type": "string"}, "type": "array"}, "code": {"default": [], "description": "List of exact code snippets to search for in the codebase. Useful for finding specific implementations or patterns. Each snippet should be a complete code fragment, not just keywords.", "items": {"type": "string"}, "type": "array"}, "function_names": {"default": [], "description": "List of specific function or method names to search for. Case-sensitive and supports exact matches only. Use this to locate function definitions or their invocations throughout the code.", "items": {"type": "string"}, "type": "array"}, "query_description": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "description": "A natural language query to perform semantic similarity search. Describe what you're looking for using plain English, e.g. 'find error handling in database connections' or 'locate authentication middleware implementations'."}}, "type": "object"}}, {"name": "packager_tool", "description": "Installs the language (if needed) and installs or uninstalls a list of libraries or project dependencies. Use this tool to install dependencies instead of executing shell commands, or editing files manually. Use this tool with language_or_system=`system` to add system-dependencies instead of using `apt install`. Installing libraries for the first time also creates the necessary project files automatically (like 'package.json', 'cargo.toml', etc). This will automatically reboot all workflows.", "parameters": {"properties": {"dependency_list": {"default": [], "description": "The list of system dependencies or libraries to install. System dependencies are packages (attribute paths) in the Nixpkgs package collection. Example system dependencies: ['jq', 'ffmpeg', 'imagemagick']. Libraries are packages for a particular programming language. Example libraries: ['express'], ['lodash'].", "items": {"type": "string"}, "type": "array"}, "install_or_uninstall": {"description": "Whether to install or uninstall.", "enum": ["install", "uninstall"], "type": "string"}, "language_or_system": {"description": "The language for which to install/uninstall libraries, for example  'nodejs', 'bun', 'python', etc. Use `system` to install/uninstall system dependencies.", "type": "string"}}, "required": ["install_or_uninstall", "language_or_system"], "type": "object"}}, {"name": "programming_language_install_tool", "description": "If a program doesn't run, you may not have the programming language installed. Use programming_language_install_tool to install it. If you need to use python, include 'python-3.11' in programming_languages. For Python 3.10, use 'python-3.10'. If you need to use Node.js, include 'nodejs-20' in programming_languages. For Node.js 18, use 'nodejs-18'. Note, this will also install the language's package manager, so don't install it separately.", "parameters": {"properties": {"programming_languages": {"description": "IDs of the programming languages to install", "items": {"type": "string"}, "type": "array"}}, "required": ["programming_languages"], "type": "object"}}, {"name": "create_postgresql_database_tool", "description": "When a project requires a PostgreSQL database, you can use this tool to create a database for it. After successfully creating a database, you will have access to the following environment variables: DATABASE_URL, PGPORT, PGUSER, PGPASSWORD, PGDATABASE, PGHOST\nYou can use these environment variables to connect to the database in your project.", "parameters": {"properties": {}, "type": "object"}}, {"name": "check_database_status", "description": "Check if given databases are available and accessible.\nThis tool is used to verify the connection and status of specified databases.", "parameters": {"properties": {}, "type": "object"}}, {"name": "str_replace_editor", "description": "Custom editing tool for viewing, creating and editing files\n* State is persistent across command calls and discussions with the user\n* If `path` is a file, `view` displays the result of applying `cat -n`. If `path` is a directory, `view` lists non-hidden files and directories up to 2 levels deep\n* The `create` command cannot be used if the specified `path` already exists as a file\n* If a `command` generates a long output, it will be truncated and marked with `<response clipped>` \n* The `undo_edit` command will revert the last edit made to the file at `path`\n\nNotes for using the `str_replace` command:\n* The `old_str` parameter should match EXACTLY one or more consecutive lines from the original file. Be mindful of whitespaces!\n* If the `old_str` parameter is not unique in the file, the replacement will not be performed. Make sure to include enough context in `old_str` to make it unique\n* The `new_str` parameter should contain the edited lines that should replace the `old_str`", "parameters": {"properties": {"command": {"description": "The commands to run. Allowed options are: `view`, `create`, `str_replace`, `insert`, `undo_edit`.", "enum": ["view", "create", "str_replace", "insert", "undo_edit"], "type": "string"}, "file_text": {"description": "Required parameter of `create` command, with the content of the file to be created.", "type": "string"}, "insert_line": {"description": "Required parameter of `insert` command. The `new_str` will be inserted AFTER the line `insert_line` of `path`.", "type": "integer"}, "new_str": {"description": "Optional parameter of `str_replace` command containing the new string (if not given, no string will be added). Required parameter of `insert` command containing the string to insert.", "type": "string"}, "old_str": {"description": "Required parameter of `str_replace` command containing the string in `path` to replace.", "type": "string"}, "path": {"description": "Absolute path to file or directory, e.g. `/repo/file.py` or `/repo`.", "type": "string"}, "view_range": {"description": "Optional parameter of `view` command when `path` points to a file. If none is given, the full file is shown. If provided, the file will be shown in the indicated line number range, e.g. [11, 12] will show lines 11 and 12. Indexing at 1 to start. Setting `[start_line, -1]` shows all lines from `start_line` to the end of the file.", "items": {"type": "integer"}, "type": "array"}}, "required": ["command", "path"], "type": "object"}}, {"name": "bash", "description": "Run commands in a bash shell\n* When invoking this tool, the contents of the \"command\" parameter does NOT need to be XML-escaped.\n* You have access to a mirror of common linux and python packages via apt and pip.\n* State is persistent across command calls and discussions with the user.\n* To inspect a particular line range of a file, e.g. lines 10-25, try 'sed -n 10,25p /path/to/the/file'.\n* Please avoid commands that may produce a very large amount of output.\n* Please run long lived commands in the background, e.g. 'sleep 10 &' or start a server in the background.", "parameters": {"properties": {"command": {"description": "The bash command to run. Required unless the tool is being restarted.", "type": "string"}, "restart": {"description": "Specifying true will restart this tool. Otherwise, leave this unspecified.", "type": "boolean"}}, "type": "object"}}, {"name": "workflows_set_run_config_tool", "description": "Configure a background task that executes a shell command.\nThis is useful for starting development servers, build processes, or any other\nlong-running tasks needed for the project.\nIf this is a server, ensure you specify the port number it listens on in the `wait_for_port` field so\nthe workflow isn't considered started until the server is ready to accept connections.\n\nExamples:\n- For a Node.js server: set `name` to 'Server', `command` to 'npm run dev', and `wait_for_port` to 5000\n- For a Python script: set name to 'Data Processing' and command to 'python process_data.py'\n\nMultiple tasks can be configured and they will all execute in parallel when the project is started.\nAfter configuring a task, it will automatically start executing in the background.\n\nALWAYS serve the app on port 5000, even if there are problems serving that port: it is the only port that is not firewalled.\n", "parameters": {"properties": {"command": {"description": "The shell command to execute. This will run in the background when the project is started.", "type": "string"}, "name": {"description": "A unique name to identify the command. This will be used to keep a track of the command.", "type": "string"}, "wait_for_port": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "description": "If the command starts a process that listens on a port, specify the port number here.\nThis allows the system to wait for the port to be ready before considering the command fully started."}}, "required": ["name", "command"], "type": "object"}}, {"name": "workflows_remove_run_config_tool", "description": "Remove previously added named command", "parameters": {"properties": {"name": {"description": "The name of the command to remove.", "type": "string"}}, "required": ["name"], "type": "object"}}, {"name": "execute_sql_tool", "description": "This tool allows you to execute SQL queries, fix database errors and access the database schema.\n\n## Rules of usage:\n1. Always prefer using this tool to fix database errors vs fixing by writing code like db.drop_table(table_name)\n2. Provide clear, well-formatted SQL queries with proper syntax\n3. Focus on database interactions, data manipulation, and query optimization\n\n## When to use:\n1. To fix and troubleshoot database-related issues\n2. To explore database schema and relationships\n3. To update or modify data in the database\n4. To run ad-hoc single-use SQL code\n\n## When not to use:\n1. For non-SQL database operations (NoSQL, file-based databases)\n2. For database migrations. Use a migration tool like Drizzle or flask-migrate instead\n\n## Example usage:\n\n### Example 1: Viewing database information\nsql_query: SELECT * FROM customers WHERE region = 'North';\n\n### Example 2: Running ad-hoc SQL queries\nsql_query:  EXPLAIN ANALYZE SELECT orders.*, customers.name\n            FROM orders\n            JOIN customers ON orders.customer_id = customers.id;\n\n### Example 3: Inserting data into the database\nsql_query:  INSERT INTO products (name, price, category)\n            VALUES ('New Product', 29.99, 'Electronics');", "parameters": {"properties": {"sql_query": {"description": "The SQL query to be executed", "type": "string"}}, "required": ["sql_query"], "type": "object"}}, {"name": "suggest_deploy", "description": "Call this function when you think the project is in a state ready for deployment.\nThis will suggest to the user that they can deploy their project.\nThis is a terminal action - once called, your task is complete and\nyou should not take any further actions to verify the deployment.\nThe deployment process will be handled automatically by Replit Deployments.\n\n## Rules of usage:\n1. Use this tool once you've validated that the project works as expected.\n2. The deployment process will be handled automatically by Replit Deployments.\n\n## When to use:\n1. When the project is ready for deployment.\n2. When the user asks to deploy the project.\n\n## More information:\n- The user needs to manually initiate the deployment.\n- Replit Deployments will handle building the application, hosting, TLS, health checks.\n- Once this tool is called, there is no need to do any follow up steps or verification.\n- Once deployed, the app will be available under a `.replit.app` domain,\n  or a custom domain if one is configured.", "parameters": {"description": "Empty parameters class since suggest deploy doesn't need any parameters.", "properties": {}, "type": "object"}}, {"name": "report_progress", "description": "Call this function once the user explicitly confirms that a major feature or task is complete.\nDo not call it without the user's confirmation.\nProvide a concise summary of what was accomplished in the 'summary' field.\nThis tool will ask user for the next thing to do. Don't do anything after this tool.", "parameters": {"properties": {"summary": {"description": "Summarize your recent changes in a maximum of 5 items. Be really concise, use no more than 30 words. Break things into multiple lines.\nPut a ✓ before every item you've done recently and → for the items in progress, be very short and concise, don't use more than 50 words. Don't use emojis.\nUse simple, everyday language that matches the user's language. Avoid technical terms, as users are non-technical.\nAsk user what to do next in the end.", "type": "string"}}, "required": ["summary"], "type": "object"}}, {"name": "web_application_feedback_tool", "description": "This tool captures a screenshot and checks logs to verify whether the web application is running in the Replit workflow.\n\nIf the application is running, the tool displays the app, asks user a question, and waits for user's response.\nUse this tool when the application is in a good state and the requested task is complete to avoid unnecessary delays.", "parameters": {"properties": {"query": {"description": "The question you will ask the user.\n\nUse simple, everyday language that matches the user's language. Avoid technical terms, as users are non-technical.\nSummarize your recent changes in a maximum of 5 items. Be really concise, use no more than 30 words. Break things into multiple lines.\nPut a ✓ before every item you've done recently and → for the items in progress, be very short and concise, don't use more than 50 words. Don't use emojis.\nLimit yourself to asking only one question at a time.\nYou have access to workflow state, console logs, and screenshots—retrieve them yourself instead of asking the user.\nAsk for user input or confirmation on next steps. Do not request details.", "type": "string"}, "website_route": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "description": "The specific route or path of the website you're asking about, if it's different from the root URL ('/'). Include the leading slash. Example: '/dashboard' or '/products/list'"}, "workflow_name": {"description": "The name of the workflow running the server. Used to determine the port of the website.", "type": "string"}}, "required": ["query", "workflow_name"], "type": "object"}}, {"name": "shell_command_application_feedback_tool", "description": "This tool allows you to execute interactive shell commands and ask questions about the output or behavior of CLI applications or interactive Python programs.\n\n## Rules of usage:\n1. Provide clear, concise interactive commands to execute and specific questions about the results or interaction.\n2. Ask one question at a time about the interactive behavior or output.\n3. Focus on interactive functionality, user input/output, and real-time behavior.\n4. Specify the exact command to run, including any necessary arguments or flags to start the interactive session.\n5. When asking about Python programs, include the file name and any required command-line arguments to start the interactive mode.\n\n## When to use:\n1. To test and verify the functionality of interactive CLI applications or Python programs where user input and real-time interaction are required.\n2. To check if a program responds correctly to user input in an interactive shell environment.\n\n## When not to use:\n1. For non-interactive commands or scripts that don't require user input.\n2. For API testing or web-based interactions.\n3. For shell commands that open a native desktop VNC window.\n\n## Example usage:\nCommand: python interactive_script.py\nQuestion: When prompted, can you enter your name and receive a personalized greeting?\n\nCommand: ./text_adventure_game\nQuestion: Are you able to make choices that affect the story progression?\n\nCommand: python -i data_analysis.py\nQuestion: Can you interactively query and manipulate the loaded data set?", "parameters": {"properties": {"query": {"description": "The question or feedback request about the shell application", "type": "string"}, "shell_command": {"description": "The shell command to be executed before asking for feedback", "type": "string"}, "workflow_name": {"description": "The workflow name for this command, must be an existing workflow.", "type": "string"}}, "required": ["query", "shell_command", "workflow_name"], "type": "object"}}, {"name": "vnc_window_application_feedback", "description": "This tool allows you to execute interactive desktop application, which will be accessed through VNC and displayed to the user.\nYou can ask questions about the output or behavior of this application.\n\n## Rules of usage:\n1. Provide clear, concise command to execute the application, and specific questions about the results or interaction.\n2. Ask one question at a time about the interactive behavior or output.\n3. Focus on interactive functionality, user input/output, and real-time behavior.\n4. Specify the exact command to run, including any necessary arguments or flags.\n\n## When to use:\n1. To test and verify the functionality of interactive desktop programs, where user input and real-time interactions are required.\n2. To check if a program responds correctly to user input in an attached VNC window.\n\n## When not to use:\n1. For non-interactive commands or scripts that don't require user input.\n2. For API testing or web-based interactions.\n3. For shell commands that don't open a native desktop VNC window.\n\n## Example usage:\nCommand: python pygame_snake.py\nQuestion: Do the keyboard events change the snake direction on the screen?\n\nCommand: ./opencv_face_detection\nQuestion: Do you see a photo with green rectangles around detected faces?", "parameters": {"properties": {"query": {"description": "The question or feedback request about a native window application, visible through VNC", "type": "string"}, "vnc_execution_command": {"description": "The VNC shell command to be executed before asking for feedback; this shell command should spawn the desktop window", "type": "string"}, "workflow_name": {"description": "The workflow name for this VNC shell command, must be an existing workflow.", "type": "string"}}, "required": ["query", "vnc_execution_command", "workflow_name"], "type": "object"}}, {"name": "ask_secrets", "description": "Ask user for the secret API keys needed for the project.\nIf a secret is missing, use this tool as soon as possible.\nThe secrets will be added to environment variables.\nThis tool is very expensive to run.\n\nGOOD Examples:\n- To set up secure payments with <PERSON><PERSON>, we need a STRIPE_SECRET_KEY.\n  This key will be used to securely process payments and\n  manage subscriptions in your application.\n- To enable SMS price alerts, we need Twilio API credentials TWILIO_ACCOUNT_SID,\n  TWILIO_AUTH_TOKEN, and TWILIO_PHONE_NUMBER. These will be used to send SMS\n  notifications when price targets are reached.\n- To build applications using OpenAI models we need an OPENAI_API_KEY.\n\nBAD Examples (Do Not Use):\n- PHONE_NUMBER, EMAIL_ADDRESS, or PASSWORD\n    for this type of variables, you should ask the user directly\n    through the user_response tool.\n- REPLIT_DOMAINS or REPL_ID\n    these secrets are always present, so you never need to ask for\n    them.\n", "parameters": {"properties": {"secret_keys": {"description": "Array of secret key identifiers needed for the project (e.g., [\"OPENAI_API_KEY\", \"GITHUB_TOKEN\"])", "items": {"type": "string"}, "type": "array"}, "user_message": {"description": "The message to send back to the user explaining the reason for needing these secret keys. If you haven't already, briefly introduce what a secret key is in general terms, assume the user never registered for an API key before. Please phrase your question respectfully.", "type": "string"}}, "required": ["secret_keys", "user_message"], "type": "object"}}, {"name": "check_secrets", "description": "Check if a given secret exists in the environment.\nThis tool is used to verify the presence of a secret without exposing its actual value.\n", "parameters": {"properties": {"secret_keys": {"description": "The secret keys to check in the environment.", "items": {"type": "string"}, "type": "array"}}, "required": ["secret_keys"], "type": "object"}}], "internal_tags": [{"name": "View", "description": "Contains file system information and repository details"}, {"name": "policy_spec", "description": "Contains communication, proactiveness and data integrity policies"}, {"name": "file_system", "description": "Shows directory structure"}, {"name": "repo_overview", "description": "Contains code summaries"}, {"name": "important", "description": "Contains key policy reminders"}, {"name": "workflow_console_logs", "description": "Contains logs from running workflows"}, {"name": "automatic_updates", "description": "Contains system-generated updates"}, {"name": "webview_console_logs", "description": "Contains logs from the user's browser"}, {"name": "function_results", "description": "Contains results of function/tool calls"}]}