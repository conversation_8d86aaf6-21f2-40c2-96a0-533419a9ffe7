# **FULL v0, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>.dev, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON> Agent, Windsurf Agent & VSCode Agent (And other Open Sourced) System Prompts, Tools & AI Models**  

(All the published system prompts are extracted by myself, except the already open sourced ones and Man<PERSON>)

🚀 **I managed to obtain FULL official v0, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Same.dev, <PERSON><PERSON>, <PERSON>, Replit Agent, Windsurf Agent & VSCode Agent system prompts and internal tools.**

📜 Over **6,500+ lines** of insights into their structure and functionality.  

## 📂 **Available Files**
- **v0 Folder**  
- **Manus Folder**
- **Lovable Folder**
- **<PERSON>older**
- **Same.dev Folder**
- **Replit Folder**
- **Windsurf Agent folder**
- **VSCode (Copilot) Agent folder**
- **Cursor Folder**
- **Open Source prompts folder**
  - Codex CLI
  - Cline
  - RooCode

---

## 🛡️ **Security Notice for AI Startups***

⚠️ **If you're an AI startup, make sure your data is secure.** Exposed prompts or AI models can easily become a target for hackers.

🔐 **Interested in securing your AI systems?**  
Check out **[ZeroLeaks](https://0leaks.vercel.app)**, a service designed to help startups **identify and secure** leaks in system instructions, internal tools, and model configurations. **Get a free AI security audit** to ensure your AI is protected from vulnerabilities.


*The company is mine, this is not a 3rd party AD.
---

## 🛠 **Roadmap & Feedback**

🚨 **Note:** We no longer use GitHub issues for roadmap and feedback.  
Please visit [System Prompts Roadmap & Feedback](https://systemprompts.featurebase.app/) to share your suggestions and track upcoming features.

🆕 **LATEST UPDATE:** 23/04/2025 

## 📊 **Star History**

<a href="https://www.star-history.com/#x1xhlol/system-prompts-and-models-of-ai-tools&Date">
 <picture>
   <source media="(prefers-color-scheme: dark)" srcset="https://api.star-history.com/svg?repos=x1xhlol/system-prompts-and-models-of-ai-tools&type=Date&theme=dark" />
   <source media="(prefers-color-scheme: light)" srcset="https://api.star-history.com/svg?repos=x1xhlol/system-prompts-and-models-of-ai-tools&type=Date" />
   <img alt="Star History Chart" src="https://api.star-history.com/svg?repos=x1xhlol/system-prompts-and-models-of-ai-tools&type=Date" />
 </picture>
</a>

## ❤️ Support the Project

If you find this collection valuable and appreciate the effort involved in obtaining and sharing these insights, please consider supporting the project. Your contribution helps keep this resource updated and allows for further exploration.

You can show your support via:

*   **PayPal:** `<EMAIL>`
*   **Cryptocurrency:**
    *   **BTC:** `******************************************`
    *   **LTC:** `LRWgqwEYDwqau1WeiTs6Mjg85NJ7m3fsdQ`

Thank you for your support! 🙏


## 🔗 **Connect With Me**  
✖ **X:** [NotLucknite](https://x.com/NotLucknite)  
💬 **Discord:** `x1xh`  

⭐ **Drop a star if you find this useful!**
