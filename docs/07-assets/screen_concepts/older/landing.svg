<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 800">
  <!-- Background with subtle gradient -->
  <defs>
    <linearGradient id="bg-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#f8f9fa" />
      <stop offset="100%" stop-color="#e9ecef" />
    </linearGradient>
    <style>
      .header-text { font-family: 'Arial', sans-serif; font-weight: bold; }
      .body-text { font-family: 'Arial', sans-serif; }
      .btn-primary { cursor: pointer; }
      .btn-primary:hover { filter: brightness(1.1); }
      .btn-secondary { cursor: pointer; }
      .btn-secondary:hover { filter: brightness(1.1); }
      .project-item { cursor: pointer; }
      .project-item:hover { filter: brightness(0.95); }
    </style>
  </defs>
  
  <!-- Main background -->
  <rect width="1200" height="800" fill="url(#bg-gradient)" />
  
  <!-- Left sidebar with logo and tagline -->
  <g>
    <!-- Logo area -->
    <rect x="0" y="0" width="400" height="800" fill="#343a40" />
    
    <!-- Logo placeholder -->
    <text x="200" y="200" font-family="Arial" font-size="48" fill="white" text-anchor="middle" font-weight="bold">KAPI</text>
    <text x="200" y="240" font-family="Arial" font-size="16" fill="#adb5bd" text-anchor="middle">A new way to build software</text>
    
    <!-- Backwards Build illustration -->
    <g transform="translate(100, 320)">
      <!-- Documentation -->
      <rect x="0" y="0" width="200" height="60" rx="5" fill="#228be6" />
      <text x="100" y="35" font-family="Arial" font-size="16" fill="white" text-anchor="middle">Documentation</text>
      
      <!-- Tests -->
      <rect x="20" y="80" width="160" height="60" rx="5" fill="#40c057" />
      <text x="100" y="115" font-family="Arial" font-size="16" fill="white" text-anchor="middle">Tests</text>
      
      <!-- Code -->
      <rect x="40" y="160" width="120" height="60" rx="5" fill="#fab005" />
      <text x="100" y="195" font-family="Arial" font-size="16" fill="white" text-anchor="middle">Code</text>
      
      <!-- Connecting lines -->
      <line x1="100" y1="60" x2="100" y2="80" stroke="white" stroke-width="2" />
      <line x1="100" y1="140" x2="100" y2="160" stroke="white" stroke-width="2" />
    </g>
    
    <!-- Tagline at the bottom -->
    <text x="200" y="650" font-family="Arial" font-size="18" fill="white" text-anchor="middle" font-style="italic">Build better software,</text>
    <text x="200" y="680" font-family="Arial" font-size="18" fill="white" text-anchor="middle" font-style="italic">backwards.</text>
  </g>
  
  <!-- Main content area -->
  <g transform="translate(450, 80)">
    <!-- Welcome header -->
    <text x="0" y="0" font-family="Arial" font-size="32" fill="#212529" class="header-text">Welcome to Kapi IDE</text>
    <text x="0" y="40" font-family="Arial" font-size="18" fill="#495057" class="body-text">The intelligent development environment that puts documentation first</text>
    
    <!-- Recent Projects Section -->
    <g transform="translate(0, 80)">
      <text x="0" y="0" font-family="Arial" font-size="22" fill="#343a40" class="header-text">Recent Projects</text>
      
      <!-- Project list - empty state or with projects -->
      <g transform="translate(0, 30)">
        <!-- Project item 1 -->
        <rect x="0" y="0" width="600" height="70" rx="5" fill="white" stroke="#dee2e6" stroke-width="1" class="project-item" />
        <text x="20" y="30" font-family="Arial" font-size="18" fill="#212529" class="body-text">My First Kapi Project</text>
        <text x="20" y="50" font-family="Arial" font-size="14" fill="#6c757d" class="body-text">/Users/<USER>/projects/first-kapi-project</text>
        <text x="580" y="40" font-family="Arial" font-size="14" fill="#6c757d" text-anchor="end" class="body-text">2 days ago</text>
        
        <!-- Project item 2 -->
        <rect x="0" y="85" width="600" height="70" rx="5" fill="white" stroke="#dee2e6" stroke-width="1" class="project-item" />
        <text x="20" y="115" font-family="Arial" font-size="18" fill="#212529" class="body-text">AI Chat Application</text>
        <text x="20" y="135" font-family="Arial" font-size="14" fill="#6c757d" class="body-text">/Users/<USER>/projects/ai-chat-app</text>
        <text x="580" y="125" font-family="Arial" font-size="14" fill="#6c757d" text-anchor="end" class="body-text">Yesterday</text>
      </g>
      
      <!-- Empty state (shown when no projects) -->
      <g transform="translate(0, 200)" opacity="0">
        <rect x="0" y="0" width="600" height="100" rx="5" fill="#f8f9fa" stroke="#dee2e6" stroke-width="1" />
        <text x="300" y="55" font-family="Arial" font-size="16" fill="#6c757d" text-anchor="middle" class="body-text">No recent projects. Create a new one to get started!</text>
      </g>
    </g>
    
    <!-- Action buttons -->
    <g transform="translate(0, 350)">
      <!-- Create New Project -->
      <rect x="0" y="0" width="290" height="60" rx="5" fill="#228be6" class="btn-primary" />
      <text x="145" y="37" font-family="Arial" font-size="18" fill="white" text-anchor="middle" font-weight="bold">Create New Project</text>
      
      <!-- Open Existing Project -->
      <rect x="310" y="0" width="290" height="60" rx="5" fill="#495057" class="btn-secondary" />
      <text x="455" y="37" font-family="Arial" font-size="18" fill="white" text-anchor="middle" font-weight="bold">Open Existing Project</text>
    </g>
    
    <!-- Getting Started section -->
    <g transform="translate(0, 450)">
      <text x="0" y="0" font-family="Arial" font-size="22" fill="#343a40" class="header-text">Getting Started</text>
      
      <!-- Quick links -->
      <rect x="0" y="20" width="190" height="50" rx="5" fill="#e9ecef" class="btn-secondary" />
      <text x="95" y="50" font-family="Arial" font-size="16" fill="#495057" text-anchor="middle">Documentation</text>
      
      <rect x="205" y="20" width="190" height="50" rx="5" fill="#e9ecef" class="btn-secondary" />
      <text x="300" y="50" font-family="Arial" font-size="16" fill="#495057" text-anchor="middle">Video Tutorial</text>
      
      <rect x="410" y="20" width="190" height="50" rx="5" fill="#e9ecef" class="btn-secondary" />
      <text x="505" y="50" font-family="Arial" font-size="16" fill="#495057" text-anchor="middle">Sample Projects</text>
    </g>
    
    <!-- Version info -->
    <text x="600" y="560" font-family="Arial" font-size="14" fill="#adb5bd" text-anchor="end" class="body-text">Kapi IDE v0.1</text>
  </g>
</svg>