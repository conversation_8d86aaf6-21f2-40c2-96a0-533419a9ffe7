<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 800">
  <!-- Dark background -->
  <rect width="1200" height="800" fill="#1a1a1a"/>
  
  <!-- Left sidebar -->
  <rect x="0" y="0" width="60" height="800" fill="#242424"/>
  <circle cx="30" cy="35" r="15" fill="#444444"/>
  <circle cx="30" cy="95" r="15" fill="#e87f5e"/>
  <rect x="15" cy="135" width="30" height="30" fill="#444444"/>
  <circle cx="30" cy="175" r="15" fill="#444444"/>
  
  <!-- Main content area -->
  <rect x="60" y="0" width="840" height="800" fill="#1a1a1a"/>
  
  <!-- Header -->
  <text x="80" y="45" font-family="Arial" font-size="24" fill="#ffffff"><PERSON>pi</text>
  <text x="140" y="45" font-family="Arial" font-size="16" fill="#777777">Private</text>
  
  <!-- Sub-header -->
  <text x="80" y="85" font-family="Arial" font-size="18" fill="#aaaaaa">Building an advanced vibe coding platform.</text>
  
  <!-- Chat area -->
  <rect x="80" y="110" width="800" height="590" rx="8" ry="8" fill="#242424"/>
  
  <!-- Welcome message -->
  <rect x="100" y="140" width="500" height="80" rx="8" ry="8" fill="#303030"/>
  <text x="120" y="170" font-family="Arial" font-size="18" fill="#ffffff">Welcome to Kapi! I'll help you get started with the</text>
  <text x="120" y="195" font-family="Arial" font-size="18" fill="#ffffff">backwards build approach to development.</text>
  
  <!-- Question 1 -->
  <rect x="100" y="240" width="560" height="80" rx="8" ry="8" fill="#303030"/>
  <text x="120" y="270" font-family="Arial" font-size="18" fill="#ffffff">First, let's understand your project's business context.</text>
  <text x="120" y="295" font-family="Arial" font-size="18" fill="#ffffff">What problem are you trying to solve with this application?</text>
  
  <!-- User response -->
  <rect x="320" y="340" width="560" height="60" rx="8" ry="8" fill="#3a536b"/>
  <text x="340" y="370" font-family="Arial" font-size="16" fill="#ffffff">I'm building a task management system for remote teams</text>
  <text x="340" y="395" font-family="Arial" font-size="16" fill="#ffffff">that integrates with calendar and communication tools.</text>
  
  <!-- Question 2 -->
  <rect x="100" y="420" width="560" height="80" rx="8" ry="8" fill="#303030"/>
  <text x="120" y="450" font-family="Arial" font-size="18" fill="#ffffff">Great! Now, what are the key features you want to include</text>
  <text x="120" y="475" font-family="Arial" font-size="18" fill="#ffffff">in your first version?</text>
  
  <!-- Input area -->
  <rect x="80" y="660" width="800" height="40" rx="8" ry="8" fill="#303030"/>
  <text x="110" y="685" font-family="Arial" font-size="16" fill="#777777">Type your response...</text>
  <circle cx="95" cy="680" r="8" fill="#777777"/>
  <rect x="135" cy="680" width="16" height="2" fill="#777777"/>
  <circle cx="165" cy="680" r="8" stroke="#777777" stroke-width="2" fill="none"/>
  <text x="170" cy="685" font-family="Arial" font-size="16" fill="#777777">13</text>
  
  <!-- Send button -->
  <rect x="740" y="660" width="40" height="40" rx="8" ry="8" fill="#e87f5e"/>
  <polygon points="755,675 755,685 765,680" fill="#ffffff"/>
  
  <!-- Right panel -->
  <rect x="900" y="0" width="300" height="800" fill="#242424"/>
  
  <!-- Project knowledge section -->
  <text x="920" y="45" font-family="Arial" font-size="20" fill="#ffffff">Project knowledge</text>
  <rect x="1170" y="35" width="20" height="20" rx="2" ry="2" stroke="#777777" stroke-width="2" fill="none"/>
  <text x="1178" y="49" font-family="Arial" font-size="16" fill="#777777">+</text>
  
  <!-- Onboarding progress tracker -->
  <rect x="920" y="80" width="260" height="80" rx="8" ry="8" fill="#303030"/>
  <text x="940" y="110" font-family="Arial" font-size="16" fill="#ffffff">Onboarding progress</text>
  <rect x="940" y="130" width="220" height="10" rx="5" ry="5" fill="#444444"/>
  <rect x="940" y="130" width="66" height="10" rx="5" ry="5" fill="#e87f5e"/>
  <text x="940" y="155" font-family="Arial" font-size="14" fill="#aaaaaa">2 of 5 steps completed</text>
  
  <!-- Knowledge capacity -->
  <text x="920" y="205" font-family="Arial" font-size="16" fill="#ffffff">12% of knowledge capacity used</text>
  <rect x="920" y="220" width="260" height="10" rx="5" ry="5" fill="#444444"/>
  <rect x="920" y="220" width="31" height="10" rx="5" ry="5" fill="#3a536b"/>
  
  <!-- Project files -->
  <rect x="920" y="260" width="120" height="120" rx="8" ry="8" fill="#303030"/>
  <text x="940" y="285" font-family="Arial" font-size="14" fill="#ffffff">business-context.md</text>
  <text x="940" y="310" font-family="Arial" font-size="12" fill="#aaaaaa">67 lines</text>
  <rect x="940" y="330" width="30" height="20" rx="4" ry="4" fill="#444444"/>
  <text x="947" y="344" font-family="Arial" font-size="12" fill="#ffffff">MD</text>
  
  <rect x="1050" y="260" width="120" height="120" rx="8" ry="8" fill="#303030"/>
  <text x="1070" y="285" font-family="Arial" font-size="14" fill="#ffffff">features.md</text>
  <text x="1070" y="310" font-family="Arial" font-size="12" fill="#aaaaaa">42 lines</text>
  <rect x="1070" y="330" width="30" height="20" rx="4" ry="4" fill="#444444"/>
  <text x="1077" y="344" font-family="Arial" font-size="12" fill="#ffffff">MD</text>
  
  <!-- Onboarding stages -->
  <rect x="920" y="400" width="260" height="280" rx="8" ry="8" fill="#303030"/>
  <text x="940" y="430" font-family="Arial" font-size="16" fill="#ffffff">Backwards Build Stages</text>
  
  <circle cx="940" cy="460" r="10" fill="#e87f5e"/>
  <text x="960" y="465" font-family="Arial" font-size="14" fill="#ffffff">Business Context</text>
  <text x="1160" y="465" font-family="Arial" font-size="14" fill="#e87f5e">✓</text>
  
  <circle cx="940" cy="500" r="10" fill="#e87f5e"/>
  <text x="960" y="505" font-family="Arial" font-size="14" fill="#ffffff">Features & Requirements</text>
  <text x="1160" y="505" font-family="Arial" font-size="14" fill="#e87f5e">✓</text>
  
  <circle cx="940" cy="540" r="10" stroke="#777777" stroke-width="2" fill="none"/>
  <text x="960" y="545" font-family="Arial" font-size="14" fill="#777777">Documentation</text>
  
  <circle cx="940" cy="580" r="10" stroke="#777777" stroke-width="2" fill="none"/>
  <text x="960" y="585" font-family="Arial" font-size="14" fill="#777777">Tests</text>
  
  <circle cx="940" cy="620" r="10" stroke="#777777" stroke-width="2" fill="none"/>
  <text x="960" y="625" font-family="Arial" font-size="14" fill="#777777">Implementation</text>
  
  <!-- Brand element -->
  <text x="980" y="700" font-family="Arial" font-size="14" fill="#777777">Powered by Claude 3.7 Sonnet</text>
  
  <!-- Decorative starburst icon like in the reference image -->
  <path d="M950,700 L952,708 L960,710 L952,712 L950,720 L948,712 L940,710 L948,708 Z" fill="#e87f5e"/>
</svg>