<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 800 500">
  <!-- Background -->
  <rect width="800" height="500" fill="#f8f9fa" rx="10" ry="10"/>
  
  <!-- Title -->
  <text x="400" y="35" font-family="Arial" font-size="24" text-anchor="middle" font-weight="bold">Git Bisect Visualizer</text>
  
  <!-- Timeline track -->
  <rect x="100" y="100" width="600" height="25" rx="12.5" ry="12.5" fill="#e9ecef"/>
  
  <!-- Commits -->
  <circle cx="130" cy="112.5" r="15" fill="#28a745" stroke="#212529" stroke-width="2"/>
  <circle cx="200" cy="112.5" r="15" fill="#28a745" stroke="#212529" stroke-width="2"/>
  <circle cx="270" cy="112.5" r="15" fill="#fd7e14" stroke="#212529" stroke-width="2" stroke-dasharray="5,3"/>
  <circle cx="340" cy="112.5" r="15" fill="#fd7e14" stroke="#212529" stroke-width="2" stroke-dasharray="5,3"/>
  <circle cx="410" cy="112.5" r="15" fill="#dc3545" stroke="#212529" stroke-width="2"/>
  <circle cx="480" cy="112.5" r="15" fill="#6c757d" stroke="#212529" stroke-width="2"/>
  <circle cx="550" cy="112.5" r="15" fill="#6c757d" stroke="#212529" stroke-width="2"/>
  <circle cx="620" cy="112.5" r="15" fill="#6c757d" stroke="#212529" stroke-width="2"/>
  
  <!-- Current focus -->
  <rect x="390" y="90" width="40" height="45" rx="5" ry="5" fill="none" stroke="#007bff" stroke-width="3" stroke-dasharray="8,4"/>
  
  <!-- Commit details view -->
  <rect x="100" y="160" width="600" height="300" rx="10" ry="10" fill="white" stroke="#dee2e6" stroke-width="2"/>
  
  <!-- Commit header -->
  <rect x="100" y="160" width="600" height="50" rx="10" ry="10" fill="#e9ecef"/>
  <text x="130" y="192" font-family="Arial" font-size="18" fill="#212529" font-weight="bold">Commit 4a3bcf8 - Bug Introduced</text>
  <text x="650" y="192" font-family="Arial" font-size="14" fill="#6c757d" text-anchor="end">2 days ago</text>
  
  <!-- File diff view -->
  <rect x="120" y="230" width="560" height="35" rx="5" ry="5" fill="#f1f3f5" stroke="#ced4da" stroke-width="1"/>
  <text x="140" y="252" font-family="monospace" font-size="14" fill="#212529">src/components/UserProfile.tsx (2 changes)</text>
  <text x="650" y="252" font-family="Arial" font-size="14" fill="#dc3545" text-anchor="end">+16 -5</text>
  
  <!-- Code diff -->
  <rect x="120" y="280" width="560" height="160" rx="5" ry="5" fill="#f8f9fa" stroke="#ced4da" stroke-width="1"/>
  <rect x="120" y="320" width="560" height="20" fill="#ffebee"/>
  <rect x="120" y="340" width="560" height="20" fill="#ffebee"/>
  <rect x="120" y="360" width="560" height="20" fill="#e8f5e9"/>
  
  <text x="140" y="300" font-family="monospace" font-size="14" fill="#495057">function fetchUserData(userId) {</text>
  <text x="140" y="335" font-family="monospace" font-size="14" fill="#b71c1c" font-weight="bold">-  if (cache.has(userId)) return cache.get(userId);</text>
  <text x="140" y="355" font-family="monospace" font-size="14" fill="#b71c1c" font-weight="bold">-  return api.getUser(userId).then(cacheResponse);</text>
  <text x="140" y="375" font-family="monospace" font-size="14" fill="#1b5e20" font-weight="bold">+  return api.getUser(userId, {skipCache: true});</text>
  <text x="140" y="395" font-family="monospace" font-size="14" fill="#495057">}</text>
  
  <!-- Legend -->
  <circle cx="130" cy="450" r="10" fill="#28a745"/>
  <text x="150" y="455" font-family="Arial" font-size="14" fill="#212529">Good commit</text>
  
  <circle cx="270" cy="450" r="10" fill="#dc3545"/>
  <text x="290" y="455" font-family="Arial" font-size="14" fill="#212529">Bad commit</text>
  
  <circle cx="420" cy="450" r="10" fill="#fd7e14"/>
  <text x="440" y="455" font-family="Arial" font-size="14" fill="#212529">Testing in progress</text>
  
  <circle cx="600" cy="450" r="10" fill="#6c757d"/>
  <text x="620" y="455" font-family="Arial" font-size="14" fill="#212529">Untested</text>
</svg>