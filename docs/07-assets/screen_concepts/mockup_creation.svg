<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 800 600">
  <!-- Background -->
  <rect width="800" height="600" fill="#f5f5f7" rx="10" ry="10"/>
  
  <!-- Title -->
  <text x="400" y="40" font-family="Arial" font-size="24" text-anchor="middle" font-weight="bold" fill="#333">
    Kapi Wireframe Generator
  </text>
  
  <!-- Split Panel Layout -->
  <rect x="100" y="70" width="600" height="500" rx="10" ry="10" fill="white" stroke="#ddd" stroke-width="1"/>
  
  <!-- Left Panel (Prompt & Controls) -->
  <rect x="100" y="70" width="290" height="500" rx="10 0 0 10" ry="10 0 0 10" fill="#F9FAFB" stroke="#ddd" stroke-width="1"/>
  
  <!-- Prompt Section -->
  <text x="120" y="100" font-family="Arial" font-size="16" fill="#333" font-weight="bold">
    Wireframe Prompt
  </text>
  
  <rect x="120" y="110" width="250" height="140" rx="5" ry="5" fill="white" stroke="#D1D5DB" stroke-width="1"/>
  <text font-family="Arial" font-size="12" fill="#6B7280">
    <tspan x="130" y="130">Create a mobile app dashboard with</tspan>
    <tspan x="130" y="150">sales metrics, order statuses, and</tspan>
    <tspan x="130" y="170">inventory alerts. Include a search</tspan>
    <tspan x="130" y="190">bar and navigation menu.</tspan>
  </text>
  
  <!-- Template Options -->
  <text x="120" y="275" font-family="Arial" font-size="16" fill="#333" font-weight="bold">
    Wireframe Template
  </text>
  
  <rect x="120" y="285" width="120" height="30" rx="15" ry="15" fill="#8B5CF6"/>
  <text x="180" y="305" font-family="Arial" font-size="12" text-anchor="middle" fill="white" font-weight="bold">
    Mobile App
  </text>
  
  <rect x="250" y="285" width="120" height="30" rx="15" ry="15" fill="#F3F4F6" stroke="#D1D5DB" stroke-width="1"/>
  <text x="310" y="305" font-family="Arial" font-size="12" text-anchor="middle" fill="#4B5563">
    Web Dashboard
  </text>
  
  <!-- Detail Level -->
  <text x="120" y="345" font-family="Arial" font-size="16" fill="#333" font-weight="bold">
    Detail Level
  </text>
  
  <rect x="120" y="355" width="250" height="40" rx="5" ry="5" fill="white" stroke="#D1D5DB" stroke-width="1"/>
  
  <!-- Slider -->
  <line x1="130" y1="375" x2="360" y2="375" stroke="#E5E7EB" stroke-width="4" stroke-linecap="round"/>
  <circle cx="245" cy="375" r="10" fill="#8B5CF6"/>
  
  <text x="130" y="395" font-family="Arial" font-size="10" fill="#6B7280">
    Low Fidelity
  </text>
  <text x="330" y="395" font-family="Arial" font-size="10" text-anchor="end" fill="#6B7280">
    High Fidelity
  </text>
  
  <!-- Style Options -->
  <text x="120" y="425" font-family="Arial" font-size="16" fill="#333" font-weight="bold">
    Style
  </text>
  
  <rect x="120" y="435" width="80" height="30" rx="5" ry="5" fill="#8B5CF6"/>
  <text x="160" y="455" font-family="Arial" font-size="12" text-anchor="middle" fill="white">
    Minimal
  </text>
  
  <rect x="205" y="435" width="80" height="30" rx="5" ry="5" fill="#F3F4F6" stroke="#D1D5DB" stroke-width="1"/>
  <text x="245" y="455" font-family="Arial" font-size="12" text-anchor="middle" fill="#4B5563">
    Modern
  </text>
  
  <rect x="290" y="435" width="80" height="30" rx="5" ry="5" fill="#F3F4F6" stroke="#D1D5DB" stroke-width="1"/>
  <text x="330" y="455" font-family="Arial" font-size="12" text-anchor="middle" fill="#4B5563">
    Colorful
  </text>
  
  <!-- Generate Button -->
  <rect x="120" y="485" width="250" height="45" rx="5" ry="5" fill="#8B5CF6"/>
  <text x="245" y="515" font-family="Arial" font-size="16" text-anchor="middle" fill="white" font-weight="bold">
    Generate Wireframe
  </text>
  
  <!-- Right Panel (Wireframe Preview) -->
  <rect x="390" y="70" width="310" height="500" rx="0 10 10 0" ry="0 10 10 0" fill="white"/>
  
  <!-- Preview Header -->
  <text x="410" y="100" font-family="Arial" font-size="16" fill="#333" font-weight="bold">
    Wireframe Preview
  </text>
  
  <!-- Mobile Wireframe -->
  <rect x="465" y="120" width="160" height="320" rx="20" ry="20" fill="#F3F4F6" stroke="#9CA3AF" stroke-width="2"/>
  
  <!-- Status Bar -->
  <rect x="465" y="120" width="160" height="20" rx="20 20 0 0" ry="20 20 0 0" fill="#E5E7EB"/>
  
  <!-- Header -->
  <rect x="465" y="140" width="160" height="40" fill="#E5E7EB"/>
  <text x="485" y="165" font-family="Arial" font-size="12" fill="#4B5563" font-weight="bold">
    Dashboard
  </text>
  <rect x="585" y="150" width="20" height="20" rx="10" ry="10" fill="#9CA3AF"/>
  
  <!-- Search Bar -->
  <rect x="475" y="190" width="140" height="25" rx="5" ry="5" fill="white" stroke="#9CA3AF" stroke-width="1"/>
  <rect x="595" y="195" width="15" height="15" rx="7.5" ry="7.5" fill="#9CA3AF"/>
  
  <!-- Sales Section -->
  <rect x="475" y="225" width="140" height="70" rx="5" ry="5" fill="white" stroke="#9CA3AF" stroke-width="1"/>
  <text x="495" y="245" font-family="Arial" font-size="10" fill="#4B5563" font-weight="bold">
    Sales Metrics
  </text>
  <line x1="485" y1="255" x2="605" y2="255" stroke="#D1D5DB" stroke-width="1"/>
  <rect x="485" y="265" width="50" height="20" fill="#D1D5DB"/>
  <rect x="545" y="265" width="60" height="20" fill="#D1D5DB"/>
  
  <!-- Orders Section -->
  <rect x="475" y="305" width="140" height="70" rx="5" ry="5" fill="white" stroke="#9CA3AF" stroke-width="1"/>
  <text x="495" y="325" font-family="Arial" font-size="10" fill="#4B5563" font-weight="bold">
    Order Status
  </text>
  <line x1="485" y1="335" x2="605" y2="335" stroke="#D1D5DB" stroke-width="1"/>
  <circle cx="495" cy="350" r="5" fill="#10B981"/>
  <line x1="505" y1="350" x2="545" y2="350" stroke="#D1D5DB" stroke-width="1"/>
  <circle cx="495" cy="365" r="5" fill="#F59E0B"/>
  <line x1="505" y1="365" x2="565" y2="365" stroke="#D1D5DB" stroke-width="1"/>
  
  <!-- Navigation Bar -->
  <rect x="465" y="400" width="160" height="40" fill="#E5E7EB"/>
  <rect x="485" y="410" width="20" height="20" fill="#9CA3AF"/>
  <rect x="535" y="410" width="20" height="20" fill="#9CA3AF"/>
  <rect x="585" y="410" width="20" height="20" fill="#9CA3AF"/>
  
  <!-- Action Buttons -->
  <rect x="410" y="460" width="90" height="35" rx="5" ry="5" fill="#F3F4F6" stroke="#9CA3AF" stroke-width="1"/>
  <text x="455" y="482" font-family="Arial" font-size="12" text-anchor="middle" fill="#4B5563">
    Edit
  </text>
  
  <rect x="510" y="460" width="90" height="35" rx="5" ry="5" fill="#8B5CF6"/>
  <text x="555" y="482" font-family="Arial" font-size="12" text-anchor="middle" fill="white">
    Save
  </text>
  
  <rect x="610" y="460" width="70" height="35" rx="5" ry="5" fill="#EC4899"/>
  <text x="645" y="482" font-family="Arial" font-size="12" text-anchor="middle" fill="white">
    Share
  </text>
  
  <!-- Export Options -->
  <text x="410" y="515" font-family="Arial" font-size="12" fill="#6B7280">
    Export as:
  </text>
  <rect x="470" y="500" width="60" height="25" rx="3" ry="3" fill="#F3F4F6" stroke="#D1D5DB" stroke-width="1"/>
  <text x="500" y="517" font-family="Arial" font-size="10" text-anchor="middle" fill="#4B5563">
    SVG
  </text>
  
  <rect x="540" y="500" width="60" height="25" rx="3" ry="3" fill="#F3F4F6" stroke="#D1D5DB" stroke-width="1"/>
  <text x="570" y="517" font-family="Arial" font-size="10" text-anchor="middle" fill="#4B5563">
    PNG
  </text>
  
  <rect x="610" y="500" width="60" height="25" rx="3" ry="3" fill="#F3F4F6" stroke="#D1D5DB" stroke-width="1"/>
  <text x="640" y="517" font-family="Arial" font-size="10" text-anchor="middle" fill="#4B5563">
    Figma
  </text>
</svg>