<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 800">
  <!-- Background -->
  <rect width="1200" height="800" fill="#f5f5f7" />
  
  <!-- Header -->
  <rect x="0" y="0" width="1200" height="60" fill="#2d3748" />
  <text x="30" y="38" font-family="Arial" font-size="24" fill="white" font-weight="bold"><PERSON>pi IDE</text>
  <text x="170" y="38" font-family="Arial" font-size="20" fill="#a0aec0">Project: E-Commerce Dashboard</text>
  
  <!-- Left sidebar with project overview -->
  <rect x="0" y="60" width="250" height="740" fill="#334155" />
  
  <!-- Project Info in Sidebar -->
  <text x="20" y="90" font-family="Arial" font-size="16" fill="white" font-weight="bold">PROJECT OVERVIEW</text>
  <rect x="20" y="100" width="210" height="1" fill="#4a5568" />
  
  <text x="20" y="130" font-family="Arial" font-size="14" fill="#e2e8f0">Type: Web Application</text>
  <text x="20" y="155" font-family="Arial" font-size="14" fill="#e2e8f0">Tech: React, Node.js, MongoDB</text>
  <text x="20" y="180" font-family="Arial" font-size="14" fill="#e2e8f0">Timeline: 12 weeks</text>
  <text x="20" y="205" font-family="Arial" font-size="14" fill="#e2e8f0">Team: 4 developers</text>
  
  <text x="20" y="245" font-family="Arial" font-size="16" fill="white" font-weight="bold">PROJECT HEALTH</text>
  <rect x="20" y="255" width="210" height="1" fill="#4a5568" />
  
  <!-- File Structure -->
  <text x="20" y="290" font-family="Arial" font-size="14" fill="white">Files</text>
  <rect x="20" y="300" width="210" height="10" rx="5" ry="5" fill="#4a5568" />
  <rect x="20" y="300" width="180" height="10" rx="5" ry="5" fill="#68d391" />
  
  <!-- Components -->
  <text x="20" y="330" font-family="Arial" font-size="14" fill="white">Components</text>
  <rect x="20" y="340" width="210" height="10" rx="5" ry="5" fill="#4a5568" />
  <rect x="20" y="340" width="120" height="10" rx="5" ry="5" fill="#68d391" />
  
  <!-- Documentation -->
  <text x="20" y="370" font-family="Arial" font-size="14" fill="white">Documentation</text>
  <rect x="20" y="380" width="210" height="10" rx="5" ry="5" fill="#4a5568" />
  <rect x="20" y="380" width="190" height="10" rx="5" ry="5" fill="#68d391" />
  
  <!-- Tests -->
  <text x="20" y="410" font-family="Arial" font-size="14" fill="white">Tests</text>
  <rect x="20" y="420" width="210" height="10" rx="5" ry="5" fill="#4a5568" />
  <rect x="20" y="420" width="150" height="10" rx="5" ry="5" fill="#f6ad55" />
  
  <!-- Main Content Area -->
  <rect x="250" y="60" width="950" height="740" fill="white" />
  
  <!-- Grid Layout Sections -->
  <!-- Top Section: Code Quality & Linting -->
  <rect x="270" y="80" width="450" height="350" rx="8" ry="8" fill="white" stroke="#e2e8f0" stroke-width="2" />
  <text x="290" y="110" font-family="Arial" font-size="18" fill="#2d3748" font-weight="bold">Code Quality</text>
  
  <!-- Linting Results -->
  <rect x="290" y="130" width="410" height="40" rx="4" ry="4" fill="#f7fafc" stroke="#e2e8f0" stroke-width="1" />
  <circle cx="310" cy="150" r="10" fill="#68d391" />
  <text x="330" y="155" font-family="Arial" font-size="14" fill="#2d3748">ESLint: 0 errors, 3 warnings</text>
  
  <rect x="290" y="180" width="410" height="40" rx="4" ry="4" fill="#f7fafc" stroke="#e2e8f0" stroke-width="1" />
  <circle cx="310" cy="200" r="10" fill="#f6ad55" />
  <text x="330" y="205" font-family="Arial" font-size="14" fill="#2d3748">Code Complexity: 3 files exceed threshold</text>
  
  <rect x="290" y="230" width="410" height="40" rx="4" ry="4" fill="#f7fafc" stroke="#e2e8f0" stroke-width="1" />
  <circle cx="310" cy="250" r="10" fill="#68d391" />
  <text x="330" y="255" font-family="Arial" font-size="14" fill="#2d3748">Type Coverage: 94%</text>
  
  <rect x="290" y="280" width="410" height="40" rx="4" ry="4" fill="#f7fafc" stroke="#e2e8f0" stroke-width="1" />
  <circle cx="310" cy="300" r="10" fill="#68d391" />
  <text x="330" y="305" font-family="Arial" font-size="14" fill="#2d3748">Best Practices: 98% compliance</text>
  
  <!-- Action Buttons -->
  <rect x="290" y="340" width="180" height="40" rx="4" ry="4" fill="#3182ce" />
  <text x="350" y="365" font-family="Arial" font-size="14" fill="white" font-weight="bold">RUN LINT</text>
  
  <rect x="480" y="340" width="180" height="40" rx="4" ry="4" fill="#48bb78" />
  <text x="530" y="365" font-family="Arial" font-size="14" fill="white" font-weight="bold">EDIT CODE</text>
  
  <!-- Top Right Section: Tests -->
  <rect x="740" y="80" width="450" height="350" rx="8" ry="8" fill="white" stroke="#e2e8f0" stroke-width="2" />
  <text x="760" y="110" font-family="Arial" font-size="18" fill="#2d3748" font-weight="bold">Tests (143 total)</text>
  
  <!-- Test Results -->
  <rect x="760" y="130" width="410" height="120" rx="4" ry="4" fill="#f7fafc" stroke="#e2e8f0" stroke-width="1" />
  
  <!-- Circular Test Progress -->
  <circle cx="810" cy="190" r="50" fill="white" stroke="#e2e8f0" stroke-width="8" />
  <circle cx="810" cy="190" r="50" fill="transparent" stroke="#68d391" stroke-width="8" stroke-dasharray="282.7 314.1" stroke-dashoffset="0" />
  <text x="810" y="195" font-family="Arial" font-size="20" fill="#2d3748" font-weight="bold" text-anchor="middle">90%</text>
  <text x="810" y="215" font-family="Arial" font-size="12" fill="#718096" text-anchor="middle">passing</text>
  
  <!-- Test Statistics -->
  <text x="900" y="165" font-family="Arial" font-size="14" fill="#2d3748">Passing: 129 tests</text>
  <text x="900" y="195" font-family="Arial" font-size="14" fill="#2d3748">Failing: 14 tests</text>
  <text x="900" y="225" font-family="Arial" font-size="14" fill="#2d3748">Coverage: 82%</text>
  
  <!-- Latest Run -->
  <rect x="760" y="260" width="410" height="40" rx="4" ry="4" fill="#f7fafc" stroke="#e2e8f0" stroke-width="1" />
  <text x="780" y="285" font-family="Arial" font-size="14" fill="#2d3748">Last run: Today at 10:32 AM</text>
  
  <!-- Action Button -->
  <rect x="760" y="340" width="180" height="40" rx="4" ry="4" fill="#3182ce" />
  <text x="810" y="365" font-family="Arial" font-size="14" fill="white" font-weight="bold">RUN TESTS</text>
  
  <!-- Bottom Left Section: Combined Project Objectives & Slides -->
  <rect x="270" y="450" width="450" height="330" rx="8" ry="8" fill="white" stroke="#e2e8f0" stroke-width="2" />
  <text x="290" y="480" font-family="Arial" font-size="18" fill="#2d3748" font-weight="bold">Project Objectives & Presentation</text>
  
  <!-- Combined Tab Selector -->
  <rect x="290" y="500" width="410" height="40" rx="4" ry="4" fill="#f7fafc" stroke="#e2e8f0" stroke-width="1" />
  <rect x="290" y="500" width="205" height="40" rx="4" ry="4" fill="#ebf8ff" stroke="#3182ce" stroke-width="1" />
  <text x="380" y="525" font-family="Arial" font-size="14" fill="#3182ce" font-weight="bold" text-anchor="middle">OBJECTIVES</text>
  <text x="585" y="525" font-family="Arial" font-size="14" fill="#718096" font-weight="bold" text-anchor="middle">SLIDES</text>
  
  <!-- Objectives Content -->
  <rect x="290" y="550" width="410" height="160" rx="4" ry="4" fill="#f7fafc" stroke="#e2e8f0" stroke-width="1" />
  
  <text x="310" y="575" font-family="Arial" font-size="16" fill="#4a5568" font-weight="bold">Hypothesis (AI-generated)</text>
  <text x="310" y="600" font-family="Arial" font-size="14" fill="#4a5568">This appears to be an e-commerce dashboard with</text>
  <text x="310" y="620" font-family="Arial" font-size="14" fill="#4a5568">focus on real-time analytics and inventory management.</text>
  <text x="310" y="640" font-family="Arial" font-size="14" fill="#4a5568">Target users are store managers who need daily sales</text>
  <text x="310" y="660" font-family="Arial" font-size="14" fill="#4a5568">reporting and product performance metrics.</text>
  
  <text x="310" y="690" font-family="Arial" font-size="16" fill="#4a5568" font-weight="bold">Is this correct?</text>
  
  <!-- Buttons -->
  <rect x="310" y="720" width="120" height="30" rx="4" ry="4" fill="#3182ce" />
  <text x="350" y="740" font-family="Arial" font-size="14" fill="white" font-weight="bold" text-anchor="middle">YES</text>
  
  <rect x="440" y="720" width="120" height="30" rx="4" ry="4" fill="white" stroke="#3182ce" stroke-width="2" />
  <text x="500" y="740" font-family="Arial" font-size="14" fill="#3182ce" font-weight="bold" text-anchor="middle">NO</text>
  
  <!-- Bottom Right Section: Project Management -->
  <rect x="740" y="450" width="450" height="330" rx="8" ry="8" fill="white" stroke="#e2e8f0" stroke-width="2" />
  <text x="760" y="480" font-family="Arial" font-size="18" fill="#2d3748" font-weight="bold">Project Management</text>
  
  <!-- Task Status Tabs -->
  <rect x="760" y="500" width="410" height="40" rx="4" ry="4" fill="#f7fafc" stroke="#e2e8f0" stroke-width="1" />
  <rect x="760" y="500" width="102" height="40" rx="4" ry="4" fill="#ebf8ff" stroke="#3182ce" stroke-width="1" />
  <text x="810" y="525" font-family="Arial" font-size="14" fill="#3182ce" font-weight="bold" text-anchor="middle">TODO</text>
  <text x="912" y="525" font-family="Arial" font-size="14" fill="#718096" font-weight="bold" text-anchor="middle">IN PROGRESS</text>
  <text x="1045" y="525" font-family="Arial" font-size="14" fill="#718096" font-weight="bold" text-anchor="middle">DONE</text>
  
  <!-- Task List -->
  <rect x="760" y="550" width="410" height="210" rx="4" ry="4" fill="#f7fafc" stroke="#e2e8f0" stroke-width="1" />
  
  <!-- Task 1 -->
  <rect x="770" y="560" width="390" height="50" rx="4" ry="4" fill="white" stroke="#e2e8f0" stroke-width="1" />
  <text x="790" y="585" font-family="Arial" font-size="14" fill="#2d3748" font-weight="bold">Implement Product Filter Component</text>
  <text x="790" y="600" font-family="Arial" font-size="12" fill="#718096">Priority: High • Assigned: Alex • Est: 4h</text>
  
  <!-- Task 2 -->
  <rect x="770" y="620" width="390" height="50" rx="4" ry="4" fill="white" stroke="#e2e8f0" stroke-width="1" />
  <text x="790" y="645" font-family="Arial" font-size="14" fill="#2d3748" font-weight="bold">Fix User Authentication Edge Cases</text>
  <text x="790" y="660" font-family="Arial" font-size="12" fill="#718096">Priority: Medium • Assigned: Jamie • Est: 6h</text>
  
  <!-- Task 3 -->
  <rect x="770" y="680" width="390" height="50" rx="4" ry="4" fill="white" stroke="#e2e8f0" stroke-width="1" />
  <text x="790" y="705" font-family="Arial" font-size="14" fill="#2d3748" font-weight="bold">Update API Documentation</text>
  <text x="790" y="720" font-family="Arial" font-size="12" fill="#718096">Priority: Low • Assigned: Taylor • Est: 2h</text>
  
  <!-- Add Task Button -->
  <rect x="970" y="740" width="200" height="30" rx="4" ry="4" fill="#3182ce" />
  <text x="1055" y="760" font-family="Arial" font-size="14" fill="white" font-weight="bold" text-anchor="middle">ADD NEW TASK</text>
  
  <!-- Voice Assistant Indicator -->
  <circle cx="1150" cy="30" r="15" fill="#68d391" />
  <text x="1150" y="35" font-family="Arial" font-size="14" fill="white" font-weight="bold" text-anchor="middle">AI</text>
</svg>