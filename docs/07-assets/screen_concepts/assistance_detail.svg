<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 800">
  <!-- Dark background -->
  <rect width="1200" height="800" fill="#1a1a1a"/>
  
  <!-- Left sidebar -->
  <rect x="0" y="0" width="60" height="800" fill="#242424"/>
  <circle cx="30" cy="35" r="15" fill="#444444"/>
  <circle cx="30" cy="95" r="15" fill="#e87f5e"/>
  <rect x="15" cy="135" width="30" height="30" fill="#444444"/>
  <circle cx="30" cy="175" r="15" fill="#444444"/>
  
  <!-- Main content area (reduced) -->
  <rect x="60" y="0" width="600" height="800" fill="#1a1a1a"/>
  
  <!-- Header -->
  <text x="80" y="45" font-family="Arial" font-size="24" fill="#ffffff">Kapi</text>
  <text x="140" y="45" font-family="Arial" font-size="16" fill="#777777">Private</text>
  
  <!-- Sub-header -->
  <text x="80" y="85" font-family="Arial" font-size="18" fill="#aaaaaa">Building an advanced vibe coding platform.</text>
  
  <!-- Main work area (reduced size to make room for assistance) -->
  <rect x="80" y="110" width="560" height="650" rx="8" ry="8" fill="#242424"/>
  
  <!-- Documentation panel (giving more prominence) -->
  <rect x="95" y="130" width="530" height="610" rx="8" ry="8" fill="#303030"/>
  <text x="115" y="160" font-family="Arial" font-size="18" fill="#ffffff">Documentation</text>
  <rect x="115" y="175" width="490" height="545" rx="4" ry="4" fill="#262626"/>
  <text x="135" y="205" font-family="Arial" font-size="14" fill="#aaaaaa">## Task Management API</text>
  <text x="135" y="235" font-family="Arial" font-size="14" fill="#aaaaaa">The Task Management API allows clients to create,</text>
  <text x="135" y="255" font-family="Arial" font-size="14" fill="#aaaaaa">update, and delete tasks within the system.</text>
  <text x="135" y="285" font-family="Arial" font-size="14" fill="#aaaaaa">### Endpoints</text>
  <text x="135" y="315" font-family="Arial" font-size="14" fill="#aaaaaa">- `GET /api/tasks` - List all tasks</text>
  <text x="135" y="335" font-family="Arial" font-size="14" fill="#aaaaaa">- `POST /api/tasks` - Create a new task</text>
  <text x="135" y="355" font-family="Arial" font-size="14" fill="#aaaaaa">- `GET /api/tasks/{id}` - Get task details</text>
  
  <!-- Chat panel (expanded from the side) -->
  <rect x="660" y="0" width="540" height="800" fill="#242424"/>
  
  <!-- Chat header -->
  <rect x="660" y="0" width="540" height="60" rx="0" ry="0" fill="#3a536b"/>
  <text x="685" y="35" font-family="Arial" font-size="18" fill="#ffffff">Assistance</text>
  <text x="1170" y="35" font-family="Arial" font-size="18" fill="#ffffff">−</text>
  
  <!-- Chat messages container -->
  <rect x="660" y="60" width="540" height="680" fill="#242424"/>
  
  <!-- Initial question -->
  <rect x="680" y="80" width="500" height="60" rx="8" ry="8" fill="#303030"/>
  <text x="700" y="110" font-family="Arial" font-size="14" fill="#ffffff">I notice you're working on API documentation. Would you like</text>
  <text x="700" y="130" font-family="Arial" font-size="14" fill="#ffffff">help with endpoint structure or authentication methods?</text>
  
  <!-- User response -->
  <rect x="880" y="150" width="300" height="40" rx="8" ry="8" fill="#3a536b"/>
  <text x="900" y="175" font-family="Arial" font-size="14" fill="#ffffff">Tell me about endpoint structure</text>
  
  <!-- Assistant response with code example -->
  <rect x="680" y="200" width="500" height="240" rx="8" ry="8" fill="#303030"/>
  <text x="700" y="230" font-family="Arial" font-size="14" fill="#ffffff">For API endpoints, a good structure follows REST principles:</text>
  
  <rect x="700" y="250" width="460" height="120" rx="4" ry="4" fill="#262626"/>
  <text x="720" y="270" font-family="Arial" font-size="12" fill="#d4d4d4">// Collection endpoints</text>
  <text x="720" y="290" font-family="Arial" font-size="12" fill="#d4d4d4">GET /api/tasks         // List all tasks</text>
  <text x="720" y="310" font-family="Arial" font-size="12" fill="#d4d4d4">POST /api/tasks        // Create a new task</text>
  <text x="720" y="330" font-family="Arial" font-size="12" fill="#d4d4d4">// Individual resource endpoints</text>
  <text x="720" y="350" font-family="Arial" font-size="12" fill="#d4d4d4">GET /api/tasks/{id}    // Get a specific task</text>
  <text x="720" y="370" font-family="Arial" font-size="12" fill="#d4d4d4">PUT /api/tasks/{id}    // Update a task</text>
  
  <text x="700" y="390" font-family="Arial" font-size="14" fill="#ffffff">Would you like me to help you with query parameters</text>
  <text x="700" y="410" font-family="Arial" font-size="14" fill="#ffffff">or response format standards?</text>
  
  <!-- User follow-up -->
  <rect x="780" y="450" width="400" height="40" rx="8" ry="8" fill="#3a536b"/>
  <text x="800" y="475" font-family="Arial" font-size="14" fill="#ffffff">Yes, help with response format standards please</text>
  
  <!-- Assistant response about formats -->
  <rect x="680" y="500" width="500" height="150" rx="8" ry="8" fill="#303030"/>
  <text x="700" y="530" font-family="Arial" font-size="14" fill="#ffffff">For response formats, I recommend using a consistent</text>
  <text x="700" y="550" font-family="Arial" font-size="14" fill="#ffffff">structure across all endpoints:</text>
  
  <rect x="700" y="570" width="460" height="60" rx="4" ry="4" fill="#262626"/>
  <text x="720" y="590" font-family="Arial" font-size="12" fill="#d4d4d4">{</text>
  <text x="720" y="610" font-family="Arial" font-size="12" fill="#d4d4d4">  "data": [ /* array or object */ ],</text>
  <text x="720" y="630" font-family="Arial" font-size="12" fill="#d4d4d4">  "meta": { "total": 100, "page": 1 }</text>
  <text x="720" y="650" font-family="Arial" font-size="12" fill="#d4d4d4">}</text>
  
  <!-- Input area -->
  <rect x="680" y="740" width="480" height="40" rx="8" ry="8" fill="#303030"/>
  <text x="700" y="765" font-family="Arial" font-size="14" fill="#777777">Ask a follow-up question...</text>
  
  <!-- Send button -->
  <rect x="1170" y="740" width="30" height="40" rx="5" ry="5" fill="#e87f5e"/>
  <polygon points="1180,755 1180,765 1190,760" fill="#ffffff"/>
  
  <!-- Quick action buttons -->
  <rect x="680" y="690" width="180" height="30" rx="15" ry="15" fill="#444444"/>
  <text x="700" y="710" font-family="Arial" font-size="13" fill="#ffffff">Error handling patterns</text>
  
  <rect x="870" y="690" width="150" height="30" rx="15" ry="15" fill="#444444"/>
  <text x="890" y="710" font-family="Arial" font-size="13" fill="#ffffff">Pagination examples</text>
  
  <rect x="1030" y="690" width="150" height="30" rx="15" ry="15" fill="#444444"/>
  <text x="1050" y="710" font-family="Arial" font-size="13" fill="#ffffff">Generate OpenAPI</text>
</svg>