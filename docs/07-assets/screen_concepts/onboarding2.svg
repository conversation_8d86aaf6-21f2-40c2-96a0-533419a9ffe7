<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 800">
  <!-- Background -->
  <rect width="1200" height="800" fill="#0c1220"/>
  
  <!-- Left sidebar for navigation -->
  <rect x="0" y="0" width="80" height="800" fill="#151c2c"/>
  <circle cx="40" cy="60" r="20" fill="#4b6bab"/>
  <text x="40" y="65" font-family="Arial" font-size="20" fill="#ffffff" text-anchor="middle">K</text>
  
  <rect x="20" y="120" width="40" height="40" rx="5" ry="5" fill="#243047"/>
  <circle cx="40" cy="200" r="20" fill="#243047"/>
  <circle cx="40" cy="260" r="20" fill="#243047"/>
  
  <!-- Header -->
  <text x="140" y="70" font-family="Arial" font-size="28" fill="#ffffff">New Project Setup</text>
  
  <!-- Main area with agent conversation -->
  <rect x="120" y="120" width="700" height="620" rx="8" ry="8" fill="#151c2c"/>
  
  <!-- Progress bar at top -->
  <rect x="140" y="140" width="660" height="8" rx="4" ry="4" fill="#243047"/>
  <rect x="140" y="140" width="165" height="8" rx="4" ry="4" fill="#4b6bab"/>
  <text x="140" y="170" font-family="Arial" font-size="14" fill="#aaaaaa">Step 1 of 4: Project Goals</text>
  
  <!-- Assistant bubble -->
  <rect x="140" y="200" width="660" height="200" rx="15" ry="15" fill="#1f2b46"/>
  <text x="170" y="230" font-family="Arial" font-size="18" fill="#ffffff">Let me create a plan for your project:</text>
  
  <rect x="170" y="250" width="600" height="120" rx="8" ry="8" fill="#243047"/>
  <text x="190" y="280" font-family="Arial" font-size="16" fill="#ffffff" font-weight="bold">🎯 Project Goals</text>
  <text x="190" y="310" font-family="Arial" font-size="14" fill="#aaaaaa">1. Build a backwards-first development environment for Electron</text>
  <text x="190" y="335" font-family="Arial" font-size="14" fill="#aaaaaa">2. Focus on documentation and testing before implementation</text>
  <text x="190" y="360" font-family="Arial" font-size="14" fill="#aaaaaa">3. Integrate with 3rd party linting and quality tools</text>
  
  <!-- Feature checkboxes -->
  <text x="170" y="420" font-family="Arial" font-size="16" fill="#ffffff">Would you like any of these additional features in your project?</text>
  
  <rect x="170" y="440" width="20" height="20" stroke="#4b6bab" stroke-width="2" fill="none"/>
  <text x="200" y="455" font-family="Arial" font-size="14" fill="#aaaaaa">Integrated terminal with code context</text>
  
  <rect x="170" y="470" width="20" height="20" stroke="#4b6bab" stroke-width="2" fill="none"/>
  <text x="200" y="485" font-family="Arial" font-size="14" fill="#aaaaaa">AI-assisted code completion</text>
  
  <rect x="170" y="500" width="20" height="20" stroke="#4b6bab" stroke-width="2" fill="none"/>
  <text x="200" y="515" font-family="Arial" font-size="14" fill="#aaaaaa">Visual documentation preview</text>
  
  <rect x="170" y="530" width="20" height="20" stroke="#4b6bab" stroke-width="2" fill="none"/>
  <text x="200" y="545" font-family="Arial" font-size="14" fill="#aaaaaa">Token usage tracking and optimization</text>
  
  <rect x="170" y="560" width="20" height="20" stroke="#4b6bab" stroke-width="2" fill="none"/>
  <text x="200" y="575" font-family="Arial" font-size="14" fill="#aaaaaa">Developer network with karma points system</text>
  
  <!-- Timeline -->
  <text x="170" y="615" font-family="Arial" font-size="16" fill="#ffffff">Project Timeline</text>
  
  <circle cx="180" cy="645" r="10" fill="#4b6bab"/>
  <text x="200" y="650" font-family="Arial" font-size="14" fill="#aaaaaa">Documentation setup (1-2 days)</text>
  
  <line x1="180" y1="655" x2="180" y2="675" stroke="#4b6bab" stroke-width="2"/>
  
  <circle cx="180" cy="685" r="10" fill="#4b6bab"/>
  <text x="200" y="690" font-family="Arial" font-size="14" fill="#aaaaaa">Test infrastructure (2-3 days)</text>
  
  <line x1="180" y1="695" x2="180" y2="715" stroke="#4b6bab" stroke-width="2"/>
  
  <circle cx="180" cy="725" r="10" fill="#243047"/>
  <text x="200" y="730" font-family="Arial" font-size="14" fill="#aaaaaa">Implementation (~1 week)</text>
  
  <!-- Input area -->
  <rect x="140" y="760" width="660" height="60" rx="30" ry="30" fill="#243047" stroke="#2d4064" stroke-width="2"/>
  <text x="170" y="795" font-family="Arial" font-size="16" fill="#8a9bb7">Any changes to the project plan?</text>
  <rect x="760" y="770" width="40" height="40" rx="20" ry="20" fill="#4b6bab"/>
  <text x="780" y="795" font-family="Arial" font-size="16" fill="#ffffff" text-anchor="middle">✓</text>
  
  <!-- Right panel -->
  <rect x="860" y="120" width="300" height="620" rx="8" ry="8" fill="#151c2c"/>
  <text x="900" y="170" font-family="Arial" font-size="18" fill="#ffffff">Project Knowledge</text>
  
  <!-- Knowledge building visualization -->
  <rect x="880" y="200" width="260" height="100" rx="8" ry="8" fill="#1f2b46"/>
  <text x="900" y="230" font-family="Arial" font-size="16" fill="#ffffff">Documentation</text>
  <rect x="900" y="250" width="220" height="10" rx="5" ry="5" fill="#243047"/>
  <rect x="900" y="250" width="55" height="10" rx="5" ry="5" fill="#4b6bab"/>
  <text x="900" y="280" font-family="Arial" font-size="14" fill="#aaaaaa">25% complete</text>
  
  <rect x="880" y="320" width="260" height="100" rx="8" ry="8" fill="#1f2b46"/>
  <text x="900" y="350" font-family="Arial" font-size="16" fill="#ffffff">Tests</text>
  <rect x="900" y="370" width="220" height="10" rx="5" ry="5" fill="#243047"/>
  <rect x="900" y="370" width="22" height="10" rx="5" ry="5" fill="#4b6bab"/>
  <text x="900" y="400" font-family="Arial" font-size="14" fill="#aaaaaa">10% complete</text>
  
  <rect x="880" y="440" width="260" height="100" rx="8" ry="8" fill="#1f2b46"/>
  <text x="900" y="470" font-family="Arial" font-size="16" fill="#ffffff">Implementation</text>
  <rect x="900" y="490" width="220" height="10" rx="5" ry="5" fill="#243047"/>
  <rect x="900" y="490" width="11" height="10" rx="5" ry="5" fill="#4b6bab"/>
  <text x="900" y="520" font-family="Arial" font-size="14" fill="#aaaaaa">5% complete</text>
  
  <!-- Resource recommendations -->
  <text x="900" y="570" font-family="Arial" font-size="16" fill="#ffffff">Recommended Resources</text>
  
  <rect x="880" y="590" width="260" height="50" rx="8" ry="8" fill="#243047"/>
  <text x="900" y="620" font-family="Arial" font-size="14" fill="#aaaaaa">Electron App Architecture Guide</text>
  
  <rect x="880" y="650" width="260" height="50" rx="8" ry="8" fill="#243047"/>
  <text x="900" y="680" font-family="Arial" font-size="14" fill="#aaaaaa">ESLint Setup for Electron</text>
</svg>