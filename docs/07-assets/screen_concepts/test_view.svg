<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 800 600">
  <!-- Background -->
  <rect width="800" height="600" fill="#f5f5f7" rx="10" ry="10"/>
  
  <!-- Title -->
  <text x="400" y="40" font-family="Arial" font-size="24" text-anchor="middle" font-weight="bold" fill="#333">
    <PERSON><PERSON> Backwards Build Approach
  </text>
  
  <!-- Progress Bar -->
  <rect x="100" y="70" width="600" height="10" rx="5" ry="5" fill="#E5E7EB"/>
  <rect x="100" y="70" width="450" height="10" rx="5" ry="5" fill="#F59E0B"/>
  <text x="100" y="95" font-family="Arial" font-size="12" fill="#6B7280">Step 4 of 5: Define Tests</text>
  
  <!-- Split Panel Layout -->
  <rect x="100" y="110" width="600" height="450" rx="10" ry="10" fill="white" stroke="#ddd" stroke-width="1"/>
  
  <!-- Left Panel (Test Categories) -->
  <rect x="100" y="110" width="200" height="450" rx="10 0 0 10" ry="10 0 0 10" fill="#F9FAFB" stroke="#ddd" stroke-width="1"/>
  
  <!-- Test Categories -->
  <rect x="110" y="130" width="180" height="40" rx="5" ry="5" fill="#F59E0B"/>
  <text x="135" y="155" font-family="Arial" font-size="14" fill="white" font-weight="bold">
    Unit Tests
  </text>
  
  <rect x="110" y="180" width="180" height="40" rx="5" ry="5" fill="#F3F4F6"/>
  <text x="135" y="205" font-family="Arial" font-size="14" fill="#4B5563">
    Integration Tests
  </text>
  
  <rect x="110" y="230" width="180" height="40" rx="5" ry="5" fill="#F3F4F6"/>
  <text x="135" y="255" font-family="Arial" font-size="14" fill="#4B5563">
    End-to-End Tests
  </text>
  
  <rect x="110" y="280" width="180" height="40" rx="5" ry="5" fill="#F3F4F6"/>
  <text x="135" y="305" font-family="Arial" font-size="14" fill="#4B5563">
    Performance Tests
  </text>
  
  <rect x="110" y="330" width="180" height="40" rx="5" ry="5" fill="#F3F4F6"/>
  <text x="135" y="355" font-family="Arial" font-size="14" fill="#4B5563">
    Security Tests
  </text>
  
  <!-- AI Test Generator -->
  <rect x="110" y="480" width="180" height="60" rx="5" ry="5" fill="#FEF3C7" stroke="#F59E0B" stroke-width="1"/>
  <text x="200" y="510" font-family="Arial" font-size="14" text-anchor="middle" fill="#D97706" font-weight="bold">
    AI Test Generator
  </text>
  <text x="200" y="530" font-family="Arial" font-size="12" text-anchor="middle" fill="#92400E">
    Generate from documentation
  </text>
  
  <!-- Right Panel (Test Editor) -->
  <rect x="300" y="110" width="400" height="450" rx="0 10 10 0" ry="0 10 10 0" fill="white"/>
  
  <!-- Test Editor Header -->
  <rect x="310" y="120" width="380" height="40" rx="5" ry="5" fill="#F9FAFB"/>
  <text x="340" y="145" font-family="Arial" font-size="14" fill="#4B5563" font-weight="bold">
    Unit Tests
  </text>
  <rect x="600" y="125" width="70" height="30" rx="5" ry="5" fill="#F59E0B"/>
  <text x="635" y="145" font-family="Arial" font-size="12" text-anchor="middle" fill="white" font-weight="bold">
    Add Test
  </text>
  
  <!-- Test List -->
  <rect x="310" y="170" width="380" height="60" rx="5" ry="5" fill="white" stroke="#E5E7EB" stroke-width="1"/>
  <text x="330" y="195" font-family="Arial" font-size="14" fill="#333" font-weight="bold">
    Test: Validate Sales Data Calculations
  </text>
  <text x="330" y="215" font-family="Arial" font-size="12" fill="#6B7280">
    Ensures sales totals match line item sums across currencies
  </text>
  
  <rect x="310" y="240" width="380" height="60" rx="5" ry="5" fill="white" stroke="#E5E7EB" stroke-width="1"/>
  <text x="330" y="265" font-family="Arial" font-size="14" fill="#333" font-weight="bold">
    Test: Inventory Alert Threshold
  </text>
  <text x="330" y="285" font-family="Arial" font-size="12" fill="#6B7280">
    Verifies alerts trigger when stock falls below defined levels
  </text>
  
  <rect x="310" y="310" width="380" height="60" rx="5" ry="5" fill="white" stroke="#E5E7EB" stroke-width="1"/>
  <text x="330" y="335" font-family="Arial" font-size="14" fill="#333" font-weight="bold">
    Test: Customer Segment Classification
  </text>
  <text x="330" y="355" font-family="Arial" font-size="12" fill="#6B7280">
    Confirms customers are properly categorized by purchase history
  </text>
  
  <!-- Test Detail Preview -->
  <rect x="310" y="380" width="380" height="130" rx="5" ry="5" fill="#FFFBEB" stroke="#F59E0B" stroke-width="1"/>
  <text x="330" y="405" font-family="Arial" font-size="14" fill="#92400E" font-weight="bold">
    Test Details
  </text>
  <text font-family="Arial" font-size="12" fill="#92400E">
    <tspan x="330" y="430">- Input: Sample sales data with known totals</tspan>
    <tspan x="330" y="450">- Expected Output: Calculated totals match expected values</tspan>
    <tspan x="330" y="470">- Edge Cases: Zero sales, negative returns, multi-currency</tspan>
    <tspan x="330" y="490">- Related Features: Real-time sales monitoring</tspan>
  </text>
  
  <!-- Sync Status -->
  <rect x="320" y="520" width="360" height="30" rx="5" ry="5" fill="#F0FDF4" stroke="#10B981" stroke-width="1"/>
  
  <text x="340" y="540" font-family="Arial" font-size="12" fill="#10B981">
    Last reviewed: March 28, 2025
  </text>
  
  <rect x="540" y="525" width="120" height="20" rx="10" ry="10" fill="#10B981"/>
  <text x="600" y="540" font-family="Arial" font-size="12" text-anchor="middle" fill="white" font-weight="bold">
    Review Now
  </text>
  
  <circle cx="510" y="535" r="8" fill="#10B981"/>
  <path d="M505,535 L510,540 L515,530" stroke="white" stroke-width="2" fill="none"/>
  <text x="483" y="540" font-family="Arial" font-size="12" text-anchor="end" fill="#10B981">
    Synced
  </text>
</svg>