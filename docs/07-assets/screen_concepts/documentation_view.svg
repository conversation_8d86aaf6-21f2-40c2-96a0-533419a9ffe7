<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 800 600">
  <!-- Background -->
  <rect width="800" height="600" fill="#f5f5f7" rx="10" ry="10"/>
  
  <!-- Title -->
  <text x="400" y="40" font-family="Arial" font-size="24" text-anchor="middle" font-weight="bold" fill="#333">
    <PERSON><PERSON> Backwards Build Approach
  </text>
  
  <!-- Progress Bar -->
  <rect x="100" y="70" width="600" height="10" rx="5" ry="5" fill="#E5E7EB"/>
  <rect x="100" y="70" width="300" height="10" rx="5" ry="5" fill="#EC4899"/>
  <text x="100" y="95" font-family="Arial" font-size="12" fill="#6B7280">Step 3 of 5: Create Documentation & Slides</text>
  
  <!-- Split Panel Layout -->
  <rect x="100" y="110" width="600" height="450" rx="10" ry="10" fill="white" stroke="#ddd" stroke-width="1"/>
  
  <!-- Left Panel (Navigation) -->
  <rect x="100" y="110" width="200" height="450" rx="10 0 0 10" ry="10 0 0 10" fill="#F9FAFB" stroke="#ddd" stroke-width="1"/>
  
  <!-- Nav Items -->
  <rect x="110" y="130" width="180" height="40" rx="5" ry="5" fill="#EC4899"/>
  <text x="135" y="155" font-family="Arial" font-size="14" fill="white" font-weight="bold">
    Project Overview
  </text>
  
  <rect x="110" y="180" width="180" height="40" rx="5" ry="5" fill="#F3F4F6"/>
  <text x="135" y="205" font-family="Arial" font-size="14" fill="#4B5563">
    User Requirements
  </text>
  
  <rect x="110" y="230" width="180" height="40" rx="5" ry="5" fill="#F3F4F6"/>
  <text x="135" y="255" font-family="Arial" font-size="14" fill="#4B5563">
    Features
  </text>
  
  <rect x="110" y="280" width="180" height="40" rx="5" ry="5" fill="#F3F4F6"/>
  <text x="135" y="305" font-family="Arial" font-size="14" fill="#4B5563">
    System Architecture
  </text>
  
  <rect x="110" y="330" width="180" height="40" rx="5" ry="5" fill="#F3F4F6"/>
  <text x="135" y="355" font-family="Arial" font-size="14" fill="#4B5563">
    Tests
  </text>
  
  <rect x="110" y="380" width="180" height="40" rx="5" ry="5" fill="#F3F4F6"/>
  <text x="135" y="405" font-family="Arial" font-size="14" fill="#4B5563">
    Usage Guide
  </text>
  
  <rect x="110" y="430" width="180" height="40" rx="5" ry="5" fill="#F3F4F6"/>
  <text x="135" y="455" font-family="Arial" font-size="14" fill="#4B5563">
    Presentation Slides
  </text>
  
  <rect x="110" y="480" width="180" height="40" rx="5" ry="5" fill="#F3F4F6"/>
  <text x="135" y="505" font-family="Arial" font-size="14" fill="#4B5563">
    Data Models
  </text>
  
  <!-- Right Panel (Content Editor) -->
  <rect x="300" y="110" width="400" height="450" rx="0 10 10 0" ry="0 10 10 0" fill="white"/>
  
  <!-- Editor Toolbar -->
  <rect x="310" y="120" width="380" height="40" rx="5" ry="5" fill="#F9FAFB"/>
  
  <!-- Toolbar Icons -->
  <rect x="320" y="130" width="20" height="20" rx="3" ry="3" fill="#6B7280"/>
  <rect x="350" y="130" width="20" height="20" rx="3" ry="3" fill="#6B7280"/>
  <rect x="380" y="130" width="20" height="20" rx="3" ry="3" fill="#6B7280"/>
  <rect x="410" y="130" width="20" height="20" rx="3" ry="3" fill="#6B7280"/>
  <rect x="440" y="130" width="20" height="20" rx="3" ry="3" fill="#6B7280"/>
  
  <!-- Editor Content -->
  <text x="320" y="190" font-family="Arial" font-size="18" fill="#333" font-weight="bold">
    Project Overview
  </text>
  
  <rect x="320" y="200" width="360" height="2" fill="#E5E7EB"/>
  
  <text font-family="Arial" font-size="14" fill="#4B5563">
    <tspan x="320" y="230">This document outlines the e-commerce dashboard</tspan>
    <tspan x="320" y="250">project for XYZ Company. The dashboard will provide</tspan>
    <tspan x="320" y="270">real-time sales analytics, inventory management, and</tspan>
    <tspan x="320" y="290">customer insights to help the business make data-driven</tspan>
    <tspan x="320" y="310">decisions.</tspan>
  </text>
  
  <text x="320" y="340" font-family="Arial" font-size="16" fill="#333" font-weight="bold">
    Key Features
  </text>
  
  <text font-family="Arial" font-size="14" fill="#4B5563">
    <tspan x="340" y="365">• Real-time sales monitoring and reporting</tspan>
    <tspan x="340" y="385">• Inventory tracking and alerts</tspan>
    <tspan x="340" y="405">• Customer segmentation and analytics</tspan>
    <tspan x="340" y="425">• Order processing workflow</tspan>
  </text>
  
  <!-- Section Navigation -->
  <rect x="320" y="455" width="360" height="30" rx="5" ry="5" fill="#F9FAFB" stroke="#D1D5DB" stroke-width="1"/>
  
  <text x="335" y="475" font-family="Arial" font-size="14" fill="#6B7280">Jump to:</text>
  
  <rect x="410" y="460" width="70" height="20" rx="10" ry="10" fill="#EEF2FF" stroke="#6366F1" stroke-width="1"/>
  <text x="445" y="475" font-family="Arial" font-size="12" text-anchor="middle" fill="#6366F1">Tests</text>
  
  <rect x="490" y="460" width="70" height="20" rx="10" ry="10" fill="#EEF2FF" stroke="#6366F1" stroke-width="1"/>
  <text x="525" y="475" font-family="Arial" font-size="12" text-anchor="middle" fill="#6366F1">Usage</text>
  
  <rect x="570" y="460" width="85" height="20" rx="10" ry="10" fill="#EEF2FF" stroke="#6366F1" stroke-width="1"/>
  <text x="612" y="475" font-family="Arial" font-size="12" text-anchor="middle" fill="#6366F1">Features</text>
  
  <!-- Sync Status -->
  <rect x="320" y="500" width="360" height="40" rx="5" ry="5" fill="#F0FDF4" stroke="#10B981" stroke-width="1"/>
  
  <text x="340" y="522" font-family="Arial" font-size="12" fill="#10B981">
    Last reviewed: March 28, 2025
  </text>
  
  <rect x="540" y="507" width="120" height="26" rx="13" ry="13" fill="#10B981"/>
  <text x="600" y="524" font-family="Arial" font-size="12" text-anchor="middle" fill="white" font-weight="bold">
    Review Now
  </text>
  
  <circle cx="510" y="520" r="10" fill="#10B981"/>
  <path d="M505,520 L510,525 L515,515" stroke="white" stroke-width="2" fill="none"/>
  <text x="483" y="524" font-family="Arial" font-size="12" text-anchor="end" fill="#10B981">
    Synced
  </text>
</svg>