<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 800">
  <!-- Dark background -->
  <rect width="1200" height="800" fill="#1a1a1a"/>
  
  <!-- Left sidebar -->
  <rect x="0" y="0" width="60" height="800" fill="#242424"/>
  <circle cx="30" cy="35" r="15" fill="#444444"/>
  <circle cx="30" cy="95" r="15" fill="#e87f5e"/>
  <rect x="15" cy="135" width="30" height="30" fill="#444444"/>
  <circle cx="30" cy="175" r="15" fill="#444444"/>
  
  <!-- Main content area -->
  <rect x="60" y="0" width="840" height="800" fill="#1a1a1a"/>
  
  <!-- Header -->
  <text x="80" y="45" font-family="Arial" font-size="24" fill="#ffffff"><PERSON>pi</text>
  <text x="140" y="45" font-family="Arial" font-size="16" fill="#777777">Private</text>
  
  <!-- Sub-header -->
  <text x="80" y="85" font-family="Arial" font-size="18" fill="#aaaaaa">Building an advanced vibe coding platform.</text>
  
  <!-- Main work area (reduced size to make room for assistance) -->
  <rect x="80" y="110" width="800" height="450" rx="8" ry="8" fill="#242424"/>
  
  <!-- Documentation panel (giving more prominence than code) -->
  <rect x="100" y="140" width="460" height="400" rx="8" ry="8" fill="#303030"/>
  <text x="120" y="170" font-family="Arial" font-size="18" fill="#ffffff">Documentation</text>
  <rect x="120" y="185" width="420" height="335" rx="4" ry="4" fill="#262626"/>
  <text x="140" y="215" font-family="Arial" font-size="14" fill="#aaaaaa">## Task Management API</text>
  <text x="140" y="245" font-family="Arial" font-size="14" fill="#aaaaaa">The Task Management API allows clients to create,</text>
  <text x="140" y="265" font-family="Arial" font-size="14" fill="#aaaaaa">update, and delete tasks within the system.</text>
  <text x="140" y="295" font-family="Arial" font-size="14" fill="#aaaaaa">### Endpoints</text>
  <text x="140" y="325" font-family="Arial" font-size="14" fill="#aaaaaa">- `GET /api/tasks` - List all tasks</text>
  <text x="140" y="345" font-family="Arial" font-size="14" fill="#aaaaaa">- `POST /api/tasks` - Create a new task</text>
  <text x="140" y="365" font-family="Arial" font-size="14" fill="#aaaaaa">- `GET /api/tasks/{id}` - Get task details</text>
  
  <!-- Code panel (smaller) -->
  <rect x="580" y="140" width="280" height="400" rx="8" ry="8" fill="#303030"/>
  <text x="600" y="170" font-family="Arial" font-size="18" fill="#ffffff">Implementation</text>
  <rect x="600" y="185" width="240" height="335" rx="4" ry="4" fill="#262626"/>
  <text x="620" y="215" font-family="Arial" font-size="14" fill="#6a9955">// Task controller implementation</text>
  <text x="620" y="245" font-family="Arial" font-size="14" fill="#569cd6">async function</text>
  <text x="710" y="245" font-family="Arial" font-size="14" fill="#dcdcaa">getTasks</text>
  <text x="770" y="245" font-family="Arial" font-size="14" fill="#d4d4d4">(req, res) {</text>
  <text x="620" y="265" font-family="Arial" font-size="14" fill="#d4d4d4">  // Code implementation</text>
  <text x="620" y="285" font-family="Arial" font-size="14" fill="#d4d4d4">}</text>
  
  <!-- Help button that triggers assistance -->
  <rect x="840" y="110" width="40" height="40" rx="20" ry="20" fill="#3a536b"/>
  <text x="854" y="134" font-family="Arial" font-size="20" font-weight="bold" fill="#ffffff">?</text>
  
  <!-- Assistance panel -->
  <rect x="80" y="580" width="800" height="170" rx="8" ry="8" fill="#303030"/>
  
  <!-- Assistance header -->
  <rect x="80" y="580" width="800" height="40" rx="8" ry="8" fill="#3a536b"/>
  <text x="100" y="605" font-family="Arial" font-size="16" fill="#ffffff">Assistance</text>
  <text x="850" y="605" font-family="Arial" font-size="16" fill="#ffffff">×</text>
  
  <!-- Assistant message -->
  <rect x="100" y="630" width="500" height="60" rx="8" ry="8" fill="#242424"/>
  <text x="120" y="655" font-family="Arial" font-size="14" fill="#ffffff">I notice you're working on API documentation. Would you like</text>
  <text x="120" y="675" font-family="Arial" font-size="14" fill="#ffffff">help with endpoint structure or authentication methods?</text>
  
  <!-- Quick action buttons -->
  <rect x="100" y="700" width="180" height="30" rx="15" ry="15" fill="#444444"/>
  <text x="130" y="720" font-family="Arial" font-size="14" fill="#ffffff">Endpoint structure</text>
  
  <rect x="290" y="700" width="230" height="30" rx="15" ry="15" fill="#444444"/>
  <text x="320" y="720" font-family="Arial" font-size="14" fill="#ffffff">Authentication methods</text>
  
  <rect x="530" y="700" width="190" height="30" rx="15" ry="15" fill="#444444"/>
  <text x="560" y="720" font-family="Arial" font-size="14" fill="#ffffff">Generate REST API</text>
  
  <!-- Input area -->
  <rect x="100" y="740" width="740" height="40" rx="8" ry="8" fill="#242424"/>
  <text x="120" y="763" font-family="Arial" font-size="14" fill="#777777">Ask for help with your task...</text>
  
  <!-- Send button -->
  <rect x="850" y="740" width="30" height="40" rx="8" ry="8" fill="#e87f5e"/>
  <polygon points="860,755 860,765 870,760" fill="#ffffff"/>
  
  <!-- Right panel -->
  <rect x="900" y="0" width="300" height="800" fill="#242424"/>
  
  <!-- Project knowledge section -->
  <text x="920" y="45" font-family="Arial" font-size="20" fill="#ffffff">Project knowledge</text>
  
  <!-- Knowledge capacity -->
  <text x="920" y="85" font-family="Arial" font-size="16" fill="#ffffff">25% of knowledge capacity used</text>
  <rect x="920" y="100" width="260" height="10" rx="5" ry="5" fill="#444444"/>
  <rect x="920" y="100" width="65" height="10" rx="5" ry="5" fill="#3a536b"/>
  
  <!-- Project health metrics -->
  <rect x="920" y="130" width="260" height="200" rx="8" ry="8" fill="#303030"/>
  <text x="940" y="155" font-family="Arial" font-size="16" fill="#ffffff">Project Health</text>
  
  <!-- Documentation coverage -->
  <text x="940" y="185" font-family="Arial" font-size="14" fill="#aaaaaa">Documentation</text>
  <rect x="940" y="195" width="220" height="8" rx="4" ry="4" fill="#444444"/>
  <rect x="940" y="195" width="176" height="8" rx="4" ry="4" fill="#4ec9b0"/>
  <text x="1170" y="185" font-family="Arial" font-size="14" fill="#4ec9b0">80%</text>
  
  <!-- Test coverage -->
  <text x="940" y="225" font-family="Arial" font-size="14" fill="#aaaaaa">Test Coverage</text>
  <rect x="940" y="235" width="220" height="8" rx="4" ry="4" fill="#444444"/>
  <rect x="940" y="235" width="132" height="8" rx="4" ry="4" fill="#569cd6"/>
  <text x="1170" y="225" font-family="Arial" font-size="14" fill="#569cd6">60%</text>
  
  <!-- Code Quality -->
  <text x="940" y="265" font-family="Arial" font-size="14" fill="#aaaaaa">Code Quality</text>
  <rect x="940" y="275" width="220" height="8" rx="4" ry="4" fill="#444444"/>
  <rect x="940" y="275" width="198" height="8" rx="4" ry="4" fill="#6a9955"/>
  <text x="1170" y="265" font-family="Arial" font-size="14" fill="#6a9955">90%</text>
  
  <!-- Implementation -->
  <text x="940" y="305" font-family="Arial" font-size="14" fill="#aaaaaa">Implementation</text>
  <rect x="940" y="315" width="220" height="8" rx="4" ry="4" fill="#444444"/>
  <rect x="940" y="315" width="44" height="8" rx="4" ry="4" fill="#e87f5e"/>
  <text x="1170" y="305" font-family="Arial" font-size="14" fill="#e87f5e">20%</text>
  
  <!-- Contextual help -->
  <rect x="920" y="350" width="260" height="200" rx="8" ry="8" fill="#303030"/>
  <text x="940" y="375" font-family="Arial" font-size="16" fill="#ffffff">Related Resources</text>
  
  <rect x="940" y="395" width="220" height="40" rx="4" ry="4" fill="#3a536b"/>
  <text x="960" y="420" font-family="Arial" font-size="14" fill="#ffffff">REST API Best Practices</text>
  
  <rect x="940" y="445" width="220" height="40" rx="4" ry="4" fill="#3a536b"/>
  <text x="960" y="470" font-family="Arial" font-size="14" fill="#ffffff">Authentication Patterns</text>
  
  <rect x="940" y="495" width="220" height="40" rx="4" ry="4" fill="#3a536b"/>
  <text x="960" y="520" font-family="Arial" font-size="14" fill="#ffffff">OpenAPI Specification</text>
  
  <!-- Assistance history -->
  <rect x="920" y="570" width="260" height="210" rx="8" ry="8" fill="#303030"/>
  <text x="940" y="595" font-family="Arial" font-size="16" fill="#ffffff">Previous Assistance</text>
  
  <rect x="940" y="615" width="220" height="40" rx="4" ry="4" fill="#242424"/>
  <text x="960" y="640" font-family="Arial" font-size="14" fill="#aaaaaa">Setting up testing framework</text>
  
  <rect x="940" y="665" width="220" height="40" rx="4" ry="4" fill="#242424"/>
  <text x="960" y="690" font-family="Arial" font-size="14" fill="#aaaaaa">Configuring authentication</text>
  
  <rect x="940" y="715" width="220" height="40" rx="4" ry="4" fill="#242424"/>
  <text x="960" y="740" font-family="Arial" font-size="14" fill="#aaaaaa">Creating documentation</text>
</svg>