<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 800">
  <!-- Definitions -->
  <defs>
    <linearGradient id="bg-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#0c1220" />
      <stop offset="100%" stop-color="#0a101c" />
    </linearGradient>
  </defs>

  <!-- Background -->
  <rect width="1200" height="800" fill="url(#bg-gradient)"/>
  
  <!-- Left sidebar from second SVG concept -->
  <rect x="0" y="0" width="280" height="800" fill="#343a40" />
  
  <!-- Logo -->
  <text x="140" y="80" font-family="Arial" font-size="48" fill="white" text-anchor="middle" font-weight="bold">KAPI</text>
  <text x="140" y="120" font-family="Arial" font-size="16" fill="#adb5bd" text-anchor="middle">A new way to build software</text>
  
  <!-- Backwards Build illustration -->
  <g transform="translate(40, 200)">
    <!-- Documentation -->
    <rect x="0" y="0" width="200" height="60" rx="5" fill="#228be6" />
    <text x="100" y="38" font-family="Arial" font-size="16" fill="white" text-anchor="middle">Documentation</text>
    
    <!-- Tests -->
    <rect x="20" y="80" width="160" height="60" rx="5" fill="#40c057" />
    <text x="100" y="118" font-family="Arial" font-size="16" fill="white" text-anchor="middle">Tests</text>
    
    <!-- Code -->
    <rect x="40" y="160" width="120" height="60" rx="5" fill="#fab005" />
    <text x="100" y="198" font-family="Arial" font-size="16" fill="white" text-anchor="middle">Code</text>
    
    <!-- Connecting lines -->
    <line x1="100" y1="60" x2="100" y2="80" stroke="white" stroke-width="2" />
    <line x1="100" y1="140" x2="100" y2="160" stroke="white" stroke-width="2" />
  </g>
  
  <!-- Tagline at the bottom -->
  <text x="140" y="650" font-family="Arial" font-size="18" fill="white" text-anchor="middle" font-style="italic">Build better software,</text>
  <text x="140" y="680" font-family="Arial" font-size="18" fill="white" text-anchor="middle" font-style="italic">backwards.</text>
  
  <!-- Main chat area -->
  <rect x="320" y="40" width="840" height="720" rx="10" ry="10" fill="#151c2c" stroke="#244275" stroke-width="2"/>
  
  <!-- Welcome header -->
  <text x="380" y="90" font-family="Arial" font-size="28" fill="#ffffff">Welcome to Kapi!</text>
  <text x="380" y="125" font-family="Arial" font-size="18" fill="#adb5bd">Let's personalize your experience and help you build better.</text>
  
  <!-- Chat container -->
  <rect x="360" y="150" width="760" height="540" rx="8" ry="8" fill="#1f2b46"/>
  
  <!-- Assistant message 1 -->
  <rect x="380" y="170" width="600" height="70" rx="15" ry="15" fill="#243047"/>
  <text x="410" y="200" font-family="Arial" font-size="16" fill="#ffffff">Hi there! I'm your Kapi assistant. Let's get to know you a bit better.</text>
  <text x="410" y="225" font-family="Arial" font-size="16" fill="#aaaaaa">What's your experience with development tools?</text>
  
  <!-- IDE experience options -->
  <rect x="400" y="260" width="220" height="60" rx="8" ry="8" fill="#4b6bab" stroke="#3b5998" stroke-width="2"/>
  <text x="510" y="297" font-family="Arial" font-size="16" fill="#ffffff" text-anchor="middle">VSCode</text>
  
  <rect x="640" y="260" width="220" height="60" rx="8" ry="8" fill="#1f2b46" stroke="#2d4064" stroke-width="1"/>
  <text x="750" y="297" font-family="Arial" font-size="16" fill="#ffffff" text-anchor="middle">Cursor</text>
  
  <rect x="400" y="330" width="220" height="60" rx="8" ry="8" fill="#1f2b46" stroke="#2d4064" stroke-width="1"/>
  <text x="510" y="367" font-family="Arial" font-size="16" fill="#ffffff" text-anchor="middle">JetBrains IDEs</text>
  
  <rect x="640" y="330" width="220" height="60" rx="8" ry="8" fill="#1f2b46" stroke="#2d4064" stroke-width="1"/>
  <text x="750" y="367" font-family="Arial" font-size="16" fill="#ffffff" text-anchor="middle">Other/Multiple</text>
  
  <!-- Assistant message 2 -->
  <rect x="380" y="410" width="600" height="70" rx="15" ry="15" fill="#243047"/>
  <text x="410" y="440" font-family="Arial" font-size="16" fill="#ffffff">Which AI models would you like to use with Kapi?</text>
  <text x="410" y="465" font-family="Arial" font-size="14" fill="#aaaaaa">We can help you optimize for speed, quality, or cost.</text>
  
  <!-- AI model options -->
  <rect x="400" y="500" width="160" height="60" rx="8" ry="8" fill="#1f2b46" stroke="#2d4064" stroke-width="1"/>
  <text x="480" y="537" font-family="Arial" font-size="16" fill="#ffffff" text-anchor="middle">Anthropic</text>
  
  <rect x="580" y="500" width="160" height="60" rx="8" ry="8" fill="#1f2b46" stroke="#2d4064" stroke-width="1"/>
  <text x="660" y="537" font-family="Arial" font-size="16" fill="#ffffff" text-anchor="middle">OpenAI</text>
  
  <rect x="760" y="500" width="160" height="60" rx="8" ry="8" fill="#1f2b46" stroke="#2d4064" stroke-width="1"/>
  <text x="840" y="537" font-family="Arial" font-size="16" fill="#ffffff" text-anchor="middle">Groq</text>
  
  <rect x="940" y="500" width="160" height="60" rx="8" ry="8" fill="#1f2b46" stroke="#2d4064" stroke-width="1"/>
  <text x="1020" y="537" font-family="Arial" font-size="16" fill="#ffffff" text-anchor="middle">Not sure yet</text>
  
  <!-- Progress container on the right -->
  <rect x="1000" y="170" width="100" height="240" rx="8" ry="8" fill="#151c2c"/>
  <text x="1050" y="200" font-family="Arial" font-size="14" fill="#ffffff" text-anchor="middle">Progress</text>
  
  <circle cx="1050" cy="240" r="15" fill="#4b6bab"/>
  <text x="1050" y="245" font-family="Arial" font-size="12" fill="#ffffff" text-anchor="middle">1</text>
  <text x="1050" y="270" font-family="Arial" font-size="12" fill="#aaaaaa" text-anchor="middle">Setup</text>
  
  <line x1="1050" y1="290" x2="1050" y2="310" stroke="#aaaaaa" stroke-width="2"/>
  
  <circle cx="1050" cy="330" r="15" stroke="#aaaaaa" stroke-width="2" fill="none"/>
  <text x="1050" y="335" font-family="Arial" font-size="12" fill="#aaaaaa" text-anchor="middle">2</text>
  <text x="1050" y="360" font-family="Arial" font-size="12" fill="#aaaaaa" text-anchor="middle">Preferences</text>
  
  <!-- Navigation buttons at bottom -->
  <rect x="360" y="710" width="380" height="50" rx="25" ry="25" fill="#243047" stroke="#2d4064" stroke-width="2"/>
  <text x="550" y="742" font-family="Arial" font-size="16" fill="#8a9bb7" text-anchor="middle">Back</text>
  
  <rect x="760" y="710" width="380" height="50" rx="25" ry="25" fill="#4b6bab" stroke="#3b5998" stroke-width="2"/>
  <text x="950" y="742" font-family="Arial" font-size="16" fill="#ffffff" text-anchor="middle">Continue</text>
</svg>