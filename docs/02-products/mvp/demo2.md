Demo Spec: “Understand and Improve a Legacy Codebase”
Overview: The user uploads or links a codebase (maybe a Git repo). <PERSON><PERSON> loads it into the IDE and begins a voice-guided deep dive: explaining structure, generating documentation, and reviewing code quality at multiple levels.
Flow:
1. Voice Session Begins
   * User says something like: “This is a legacy CRM we inherited—can you walk me through it?”
   * Center panel: voice waveform + live transcript
   * Right panel: intent and observations
   * Left panel: project file tree
2. File-Level Understanding
   * Clicking a file shows:
      * Pretty-rendered documentation (markdown)
      * Mermaid diagrams (e.g., flowcharts, class hierarchies)
   * <PERSON><PERSON> explains purpose, structure, and quirks in voice + text
3. Component-Level Diagrams
   * For components (React or otherwise), generates interaction diagrams or UI trees
   * Comments on design patterns, antipatterns, or architectural smells
4. Project-Level Documentation
   * Automatically builds:
      * High-level system overview
      * Mermaid architecture diagrams
      * Setup instructions and data flow
   * All richly rendered in IDE markdown/diagram view
5. Deeper Code Review
   * Flags maintainability issues, complex logic, security smells
   * Supports queries like “why is this method so large?”
   * May show visualizations like dependency graphs or even Git bisect insights
   * Option to ask, “How could we refactor this module?”
6. Live Engagement
   * Voice-driven Q&A: “Explain the auth system,” “What’s the biggest risk area?”
   * <PERSON><PERSON> responds with explanations, diffs, or visual insights