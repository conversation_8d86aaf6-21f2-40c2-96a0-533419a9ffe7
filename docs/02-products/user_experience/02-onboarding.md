# Onboarding Experience

> **Purpose**: Transform new users into confident KAPI builders through voice-driven, personalized onboarding
> **Last Updated**: June 1, 2025

## 🎯 Onboarding Goals

```mermaid
graph LR
    A[Sign Up] --> B[Understand Intent]
    B --> C[Personalize Experience]
    C --> D[Create First Project]
    D --> E[Achieve First Success]
    E --> F[Build Confidence]
    
    style A fill:#e3f2fd
    style E fill:#c8e6c9
    style F fill:#4caf50
```

**Target**: 80% of users complete first project within 30 minutes

## 🎤 Voice-First Onboarding

### Welcome Experience
```typescript
// Initial greeting - warm and conversational
"Hey there! Welcome to KAPI. I'm here to help you build something amazing. 
What brings you here today?"

// User responds naturally
"I want to learn how to build AI apps"

// AI understands intent and continues
"Perfect! I'll help you create your first AI-powered app. 
Let's start with something fun - what kind of app interests you?"
```

### Voice Interview Flow
```mermaid
flowchart TD
    A[Welcome Message] --> B[Ask: What brings you here?]
    B --> C{Analyze Response}
    
    C -->|Learning| D[Ask: Experience level?]
    C -->|Building| E[Ask: What project?]
    C -->|Exploring| F[Ask: Background?]
    
    D --> G[Ask: Learning goals?]
    E --> H[Ask: Timeline?]
    F --> I[Ask: Interests?]
    
    G --> J[Determine Mode: Learner]
    H --> K[Determine Mode: Builder]
    I --> L[Determine Mode: Explorer]
    
    J --> M[Personalized Setup]
    K --> M
    L --> M
```

## 📊 Onboarding Stages

### Stage 1: Identity & Intent (0-2 min)
```mermaid
graph TB
    subgraph "Questions Asked"
        Q1[What brings you to KAPI?]
        Q2[Have you built apps before?]
        Q3[What excites you most?]
    end
    
    subgraph "Data Collected"
        D1[Motivation Type]
        D2[Experience Level]
        D3[Interest Areas]
    end
    
    subgraph "Personalization"
        P1[UI Complexity]
        P2[Feature Set]
        P3[Guidance Level]
    end
    
    Q1 --> D1 --> P1
    Q2 --> D2 --> P2
    Q3 --> D3 --> P3
```

### Stage 2: Project Creation (2-5 min)
```yaml
voice_prompts:
  project_type: "What would you like to build? An app, website, or API?"
  description: "Tell me more about your {project_type}. What should it do?"
  audience: "Who will use this?"
  
ai_actions:
  - Extract project requirements
  - Suggest appropriate template
  - Generate project structure
  - Create initial documentation
```

### Stage 3: Environment Setup (5-10 min)
| Step | Voice Prompt | AI Action |
|------|--------------|-----------|
| **Tech Stack** | "I recommend React and Node.js for this. Sound good?" | Configure project |
| **Integrations** | "Want to add authentication? Database?" | Add dependencies |
| **AI Models** | "Which AI model would you prefer? Claude or GPT-4?" | Set preferences |
| **Collaboration** | "Will others work on this with you?" | Configure sharing |

### Stage 4: First Success (10-15 min)
```mermaid
sequenceDiagram
    participant User
    participant KAPI
    participant Project
    
    User->>KAPI: "Show me what we built"
    KAPI->>Project: Generate preview
    Project->>User: Display working app
    KAPI->>User: "Here's your app! Try clicking around"
    User->>KAPI: "This is amazing!"
    KAPI->>User: "Want to customize it? Just tell me what to change"
```

## 🎨 Personalization Modes

### Mode Detection Algorithm
```typescript
interface UserProfile {
  motivation: 'learner' | 'contributor' | 'builder';
  experience: 'beginner' | 'intermediate' | 'expert';
  interests: string[];
  preferredStyle: 'guided' | 'exploratory' | 'minimal';
}

function detectUserMode(responses: ConversationResponses): UserProfile {
  // AI analyzes natural language responses
  const profile = AI.extractProfile(responses);
  
  // Set UI adaptation
  if (profile.motivation === 'learner') {
    enableFeatures(['tutorials', 'explanations', 'quizzes']);
    hideFeatures(['advanced-settings', 'deployment']);
  }
  
  return profile;
}
```

### Mode-Specific Experiences

#### 🎓 Learner Mode
```mermaid
graph LR
    A[Extra Guidance] --> B[Concept Explanations]
    B --> C[Progress Tracking]
    C --> D[Achievement Unlocks]
    D --> E[Suggested Next Steps]
    
    style A fill:#e1bee7
    style D fill:#ce93d8
```

**Features**:
- Step-by-step tutorials
- Hover explanations
- Learning objectives
- Progress celebration

#### 🔨 Builder Mode
```mermaid
graph LR
    A[Quick Setup] --> B[Pro Templates]
    B --> C[Full Features]
    C --> D[Deploy Options]
    D --> E[Team Tools]
    
    style A fill:#b3e5fc
    style D fill:#4fc3f7
```

**Features**:
- Minimal hand-holding
- Advanced features visible
- Production tools ready
- Performance focus

#### 🤝 Contributor Mode
```mermaid
graph LR
    A[Git Integration] --> B[Code Standards]
    B --> C[PR Templates]
    C --> D[Issue Tracking]
    D --> E[Community]
    
    style A fill:#c5e1a5
    style D fill:#8bc34a
```

**Features**:
- Repository import
- Contribution guidelines
- Code review tools
- Community connections

## 📱 Multi-Device Onboarding

### Desktop Flow
- Full-featured setup
- Keyboard shortcuts tutorial
- Multi-window introduction
- Pro tips and tricks

### Mobile Flow
- Voice-first interaction
- Simplified choices
- Touch gesture tutorial
- Quick project creation

### Cross-Device Continuity
```mermaid
flowchart LR
    A[Start on Phone] --> B[Voice Setup]
    B --> C[Continue on Desktop]
    C --> D[Full IDE Experience]
    D --> E[Access Anywhere]
    
    style B fill:#fff9c4
    style E fill:#a5d6a7
```

## 🎯 Success Metrics

### Onboarding Funnel
| Stage | Target | Current | Drop-off |
|-------|--------|---------|----------|
| **Start Onboarding** | 100% | 100% | - |
| **Complete Identity** | 95% | 92% | 8% |
| **Create Project** | 85% | 78% | 14% |
| **First Success** | 80% | 72% | 8% |
| **Day 1 Retention** | 70% | 65% | 7% |

### Time to Value
```mermaid
graph TD
    A[0 min: Sign Up] --> B[2 min: Understand User]
    B --> C[5 min: Project Created]
    C --> D[10 min: First Code Generated]
    D --> E[15 min: App Running]
    E --> F[20 min: First Customization]
    F --> G[30 min: Confident User]
    
    style E fill:#4caf50
    style G fill:#2e7d32
```

## 🔧 Technical Implementation

### API Integration Flow
```mermaid
sequenceDiagram
    participant Frontend
    participant Backend
    participant AI
    participant Database
    
    Frontend->>Backend: POST /api/users/start-onboarding
    Backend->>AI: Create conversation
    AI->>Backend: Return conversation_id
    Backend->>Frontend: {conversation_id, first_question}
    
    loop Voice Interaction
        Frontend->>Backend: POST /api/conversations/message
        Backend->>AI: Process response
        AI->>Backend: Extract preferences
        Backend->>Database: Update user profile
    end
    
    Frontend->>Backend: POST /api/users/complete-onboarding
    Backend->>Database: Mark onboarding complete
    Backend->>Frontend: Return personalized workspace
```

### Data Structure
```typescript
interface OnboardingProfile {
  // Identity
  userId: string;
  completedAt?: Date;
  
  // Preferences
  motivation: 'learner' | 'contributor' | 'builder';
  experience: {
    level: 'beginner' | 'intermediate' | 'expert';
    languages: string[];
    frameworks: string[];
  };
  
  // Personalization
  learningStyle: 'visual' | 'textual' | 'hands-on';
  preferredAI: 'claude' | 'gpt4' | 'both';
  projectType: string;
  
  // Progress
  checkpoints: {
    identityComplete: boolean;
    projectCreated: boolean;
    firstSuccess: boolean;
    customizationDone: boolean;
  };
}
```

## 🚀 Optimization Strategies

### A/B Tests Running
1. **Voice vs Form Onboarding** - 15% better completion with voice
2. **Number of Questions** - Sweet spot is 4-6 questions
3. **Template Suggestions** - Showing 3 options optimal
4. **First Project Type** - "Fun" projects have higher completion

### Common Drop-off Points
```mermaid
graph TD
    A[Too Many Questions] --> B[Simplify to Essential]
    C[Unclear Value] --> D[Show Preview Early]
    E[Technical Issues] --> F[Fallback Options]
    G[Choice Paralysis] --> H[Smart Defaults]
    
    style A fill:#ffcdd2
    style B fill:#a5d6a7
```

## 🎉 Celebration Moments

### Micro-Celebrations
- ✅ Account created: "Welcome aboard!"
- 🎯 Project named: "Great name!"
- 🚀 First code generated: "Look at that!"
- 🎉 App running: "You did it!"
- 💪 First edit: "You're a natural!"

### Achievement Unlocks
```typescript
achievements: [
  { id: 'first_project', name: 'Pioneer', icon: '🚀' },
  { id: 'voice_master', name: 'Voice Commander', icon: '🎤' },
  { id: 'quick_learner', name: 'Fast Track', icon: '⚡' },
  { id: 'customizer', name: 'Make It Yours', icon: '🎨' }
]
```

## 📈 Post-Onboarding

### Immediate Next Steps
1. **Guided Tour** - Interactive IDE walkthrough
2. **Daily Challenge** - Simple task to reinforce learning
3. **Community Intro** - Meet other developers
4. **Resource Library** - Tutorials and documentation

### 24-Hour Follow-up
```yaml
email_sequence:
  - welcome: "Welcome to KAPI! Here's what you built..."
  - tips: "3 quick tips to level up your project"
  - community: "Join our Discord for help"
  - challenge: "Ready for today's coding challenge?"
```

## 🔗 Related Systems

- **Backend**: [Node.js implementation](../../nodejs_backend/docs/user-onboarding.md)
- **Frontend**: [React components](../../new_ide/src/renderer/pages/UserOnboarding.tsx)
- **Analytics**: Track funnel metrics
- **Support**: Common onboarding issues

---

**Previous**: [User Journey](./01-user-journey.md) | **Next**: [Interaction Modes](./03-interaction-modes.md)
