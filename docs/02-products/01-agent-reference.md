# Agent System Reference

_Last updated: May 31, 2025_

> **Purpose**: Single source of truth for agent system terminology, architecture, and cross-references.
> This file ensures consistency across all agent-related documentation.

## Core Terminology

### Agent Types
- **Evidence Collection Agent**: Gathers requirements and context
- **Documentation Agent**: Creates docs, slides, and specifications  
- **Code Generation Agent**: Implements based on documentation
- **Code Quality Agent**: Tests, lints, and refactors
- **Voice Agent**: Natural language interface using Hybrid ReAct-TAG

### Agent Modes
- **Direct Execution**: Simple tasks (complexity 0-3)
- **ReAct-Lite**: Medium tasks with reasoning (complexity 4-7)
- **Full ReAct-TAG**: Complex tasks with deep reasoning (complexity 8-10)

### Memory Components
- **User Context**: ~200 tokens, personal preferences
- **Project Business Context**: ~300 tokens, the "why"
- **Project Technical Context**: 500-1000 tokens, the "how"
- **Code Context**: ~300 tokens, file structure
- **Task Context**: 1000-1500 tokens, active work

## Documentation Map

| Topic | User-Facing | Technical Spec | Architecture |
|-------|-------------|----------------|--------------|
| **Agent Overview** | [02-ai-agents.md](./02-ai-agents.md) | - | [agent-system.md](../03-technical/architecture/agent-system.md) |
| **Voice Agent** | [02-ai-agents.md#voice-agent](./02-ai-agents.md#voice-agent) | [hybrid-agent-spec.md](./features/hybrid-agent-spec.md) | [agent-system.md](../03-technical/architecture/agent-system.md) |
| **Memory/Context** | [02-ai-agents.md#context-management](./02-ai-agents.md#context-management) | - | [memory-system.md](../03-technical/architecture/memory-system.md) |
| **Multi-Agent Flow** | [02-ai-agents.md#agent-architecture](./02-ai-agents.md#agent-architecture) | - | [agent-system.md#multi-agent-flow](../03-technical/architecture/agent-system.md#multi-agent-flow-visualization) |

## Key Metrics (Canonical)

### Performance Targets
- **Voice-to-first-action**: < 2 seconds
- **Simple task completion**: < 1000 tokens
- **Success rates**: 99% simple, 95% medium, 90% complex
- **Context window**: Maximum 5000 tokens (70% static, 30% dynamic)

### Token Budget Allocation
- **User Context**: ~200 tokens
- **Business Context**: ~300 tokens  
- **Technical Context**: 500-1000 tokens
- **Code Context**: ~300 tokens
- **Task Context**: 1000-1500 tokens

## Update Checklist

When updating agent functionality, ensure all relevant docs are updated:

- [ ] Update this reference file with any new terminology or metrics
- [ ] Update user-facing description in `02-ai-agents.md`
- [ ] Update technical specs if implementation details change
- [ ] Update architecture docs if system design changes
- [ ] Run consistency check: `grep -r "agent_modes\|token.*budget\|complexity.*[0-9]" docs/`

## Version History

| Date | Change | Updated Files |
|------|--------|---------------|
| 2025-05-31 | Initial reference doc | All agent files |
