# IDE Core Features

> **Purpose**: Essential development environment components that form the foundation of KAPI IDE
> **Related**: [System Architecture](./00-system-architecture.md) • [AI Agents](./03-ai-agents.md) • [Interaction Modes](./04-interaction-modes.md)

_Last updated: May 31, 2025_

## At a Glance

```mermaid
mindmap
  root((KAPI IDE Core))
    UI Components
      📁 File Explorer
      📝 Code Editor
      💻 Terminal
      🎨 Smart Viewers
    Core Services
      Context System
      File Service
      WebSocket Manager
      API Service
    Features
      🎤 Voice Control
      🤖 AI Integration
      📋 Clipboard
      ⚡ Quick Actions
    Architecture
      Modular Design
      Event System
      State Management
      Performance
```

## Core Architecture

```mermaid
graph TB
    subgraph "User Interface Layer"
        FE[📁 File Explorer]
        ED[📝 Code Editor]
        TM[💻 Terminal]
        AI[🤖 AI Panel]
    end
    
    subgraph "Core Services"
        CS[Context System]
        FS[File Service]
        WS[WebSocket Manager]
        AS[API Service]
    end
    
    subgraph "Feature Modules"
        CM[📋 Clipboard]
        QA[✅ Quality]
        VC[🎤 Voice]
        SC[👁️ Viewers]
    end
    
    FE --> FS
    ED --> CS
    TM --> AS
    AI --> WS
    
    CS --> CM
    CS --> QA
    CS --> VC
    FS --> SC
    
    style FE fill:#e3f2fd
    style CS fill:#c5e1a5
    style CM fill:#fff9c4
```

## Core Components

### 📁 Project Explorer

**VSCode-like file management with AI enhancements**

| Feature | Description | Key Benefit |
|---------|-------------|-------------|
| **Tree Navigation** | Collapsible tree with state persistence | Familiar UX |
| **Context Menus** | Right-click operations (New, Copy, Paste, Delete) | Quick actions |
| **File Operations** | Copy/Cut/Paste with conflict handling | Safe operations |
| **Real-time Updates** | Auto-refresh on external changes | Always in sync |
| **Search Integration** | Ripgrep-powered file search | Fast discovery |

#### Advanced Features
- Auto-expand to show active files
- Smart directory caching for instant re-expansion
- Keyboard navigation with arrow keys
- Git status indicators on files

### 📝 Code Editor

**Monaco Editor (VSCode engine) with AI superpowers**

```mermaid
graph LR
    subgraph "Base Monaco"
        SH[Syntax Highlighting]
        IS[IntelliSense]
        MC[Multi-cursor]
    end
    
    subgraph "AI Features"
        AG[AI Generation<br/>Cmd+K]
        AS[AI Suggestions<br/>Tab to accept]
        AE[AI Explain<br/>On hover]
    end
    
    subgraph "Enhancements"
        VV[Voice Input]
        SV[Smart Viewers]
        QA[Quick Actions]
    end
    
    SH --> AG
    IS --> AS
    MC --> AE
    
    AG --> VV
    AS --> SV
    AE --> QA
```

| Feature | Description | Shortcut |
|---------|-------------|----------|
| **AI Generation** | Generate code at cursor | `Cmd/Ctrl+K` |
| **Quick Save** | Save current file | `Cmd/Ctrl+S` |
| **Find in File** | Search with regex | `Cmd/Ctrl+F` |
| **Multi-cursor** | Edit multiple locations | `Alt+Click` |
| **Voice Coding** | Dictate code | `Cmd/Ctrl+Shift+V` |

### 💻 Integrated Terminal

**Full terminal emulator with AI assistance**

```mermaid
mindmap
  root((Terminal))
    Core Features
      Multiple Tabs
      Split Panes
      Command History
      Shell Selection
    AI Features
      Error Analysis
      Command Suggestions
      Output Processing
      Package Help
    Integration
      File Explorer
      Editor Actions
      Git Commands
      Task Runner
```

| Feature | Benefit | Example |
|---------|---------|---------|
| **Error Analysis** | Understand errors instantly | npm error → solution |
| **Smart Output** | AI processes terminal output | Test results summary |
| **Command Suggest** | Context-aware commands | Git workflow help |
| **Multi-session** | Run parallel terminals | Server + Tests + Git |

### 👁️ Smart Viewers

**Automatic preview rendering for supported files**

| File Type | Viewer | Features |
|-----------|--------|----------|
| `.md` | MarkdownViewer | Live preview, Mermaid support |
| `.html` | HTMLViewer | Sandboxed rendering |
| `.svg` | SVGViewer | Zoom controls, pan |
| `.jsx/.tsx` | ReactViewer | Component preview |
| `.mmd/.mermaid` | MermaidViewer | Diagram rendering |

## File Management System

```mermaid
flowchart LR
    subgraph "File Operations"
        Create[📄 Create]
        Copy[📋 Copy]
        Move[✂️ Cut]
        Paste[📌 Paste]
        Delete[🗑️ Delete]
    end
    
    subgraph "Clipboard Service"
        State[State Tracking]
        Conflict[Conflict Resolution]
        Sync[Cross-component Sync]
    end
    
    subgraph "Features"
        Watch[🔄 File Watching]
        Cache[💾 Smart Caching]
        Undo[↩️ Undo/Redo]
    end
    
    Copy --> State
    Move --> State
    State --> Conflict
    Conflict --> Paste
    
    Create --> Watch
    Paste --> Watch
    Delete --> Undo
```

### Quick API Reference

```typescript
// File operations
await fileService.create('/path/file.js', content);
await fileService.copy('/source', '/dest');
await fileService.delete('/path/file.js');

// Clipboard
fileClipboard.copy(path, name, isDirectory);
await fileClipboard.paste(destination);

// Watching
fileWatcher.on('change', (path) => { /* refresh */ });
```

## UI/UX Design

### Panel Layout System

```mermaid
graph TB
    subgraph "Adaptive Panels"
        Explorer[📁 Explorer<br/>Files & folders]
        Editor[📝 Editor<br/>Code editing]
        Terminal[💻 Terminal<br/>CLI access]
        Social[👥 Social<br/>Chat & Q&A]
        Dashboard[📊 Dashboard<br/>Project stats]
        Voice[🎤 Voice<br/>Hands-free]
    end
    
    subgraph "Layout Features"
        Resize[↔️ Resizable]
        Toggle[👁️ Show/Hide]
        Persist[💾 Save Layout]
        Reset[🔄 Reset Size]
    end
    
    Explorer --> Resize
    Editor --> Toggle
    Terminal --> Persist
    Social --> Reset
```

| Panel | Purpose | Toggle Key |
|-------|---------|------------|
| File Explorer | Navigate files | `Cmd/Ctrl+B` |
| Terminal | Run commands | `Cmd/Ctrl+`` ` |
| AI Chat | Get AI help | `Cmd/Ctrl+I` |
| Social | Community | `Cmd/Ctrl+U` |

## Keyboard Shortcuts

### Essential Operations

| Action | Windows/Linux | macOS |
|--------|---------------|-------|
| **AI Generate** | `Ctrl+K` | `Cmd+K` |
| **Save File** | `Ctrl+S` | `Cmd+S` |
| **Quick Open** | `Ctrl+P` | `Cmd+P` |
| **Command Palette** | `Ctrl+Shift+P` | `Cmd+Shift+P` |
| **Toggle Terminal** | `Ctrl+`` ` | `Cmd+`` ` |

[Full shortcuts reference →](../reference/keyboard-shortcuts.md)

## Technical Architecture

### 🏗️ Electron Application Structure

KAPI IDE follows the standard Electron architecture with main and renderer processes, enhanced with AI-powered development features:

```mermaid
graph TB
    subgraph "🖥️ Main Process (Node.js)"
        MAIN[main.ts<br/>Entry Point]
        IPC[IPC Handlers<br/>File System, Terminal]
        WIN[Window Management<br/>BrowserWindow]
        FS[File System<br/>Operations]
    end
    
    subgraph "🌉 Preload Bridge"
        PRELOAD[preload.ts<br/>Security Bridge]
        API[electronAPI<br/>Exposed Methods]
    end
    
    subgraph "🎨 Renderer Process (Chromium)"
        RENDERER[renderer.tsx<br/>React Entry]
        APP[App.tsx<br/>Root Component]
        CONTEXTS[React Contexts<br/>State Management]
    end
    
    subgraph "⚡ Core Services"
        WS[WebSocket Manager]
        HTTP[API Client]
        LLM[LLM Service]
        CLIP[Clipboard Service]
    end
    
    MAIN --> IPC
    MAIN --> WIN
    IPC --> FS
    WIN --> PRELOAD
    PRELOAD --> API
    API --> RENDERER
    RENDERER --> APP
    APP --> CONTEXTS
    CONTEXTS --> WS
    CONTEXTS --> HTTP
    CONTEXTS --> LLM
    CONTEXTS --> CLIP
    
    style MAIN fill:#ff9800
    style PRELOAD fill:#4caf50
    style RENDERER fill:#2196f3
    style WS fill:#9c27b0
```

### 🔄 Application Flow & Entry Points

#### **1️⃣ Main Process Initialization** (`main.ts`)
```typescript
// First file executed when application starts
app.whenReady().then(() => {
  // Create application window
  const mainWindow = new BrowserWindow({
    webPreferences: {
      preload: path.join(__dirname, 'preload.js'),
      contextIsolation: true,
      nodeIntegration: false
    }
  });
  
  // Setup IPC handlers
  setupFileSystemHandlers();
  setupTerminalHandlers();
  setupProjectHandlers();
});
```

**Responsibilities:**
- 🪟 **Window Management**: Create/destroy BrowserWindow instances
- 📡 **IPC Setup**: Register Inter-Process Communication handlers
- 📁 **File System**: Handle file operations securely
- 💻 **Terminal Management**: Spawn and manage terminal processes
- 🔐 **Security**: Enforce security policies and permissions

#### **2️⃣ Preload Bridge** (`preload.ts`)
```typescript
// Secure bridge between main and renderer processes
contextBridge.exposeInMainWorld('electronAPI', {
  // File operations
  selectDirectory: () => ipcRenderer.invoke('select-directory'),
  saveFile: (path: string, content: string) => 
    ipcRenderer.invoke('save-file', path, content),
  
  // Terminal operations  
  createTerminal: () => ipcRenderer.invoke('create-terminal'),
  executeCommand: (command: string) => 
    ipcRenderer.invoke('execute-command', command),
    
  // Project operations
  openProject: (path: string) => 
    ipcRenderer.invoke('open-project', path)
});
```

**Security Features:**
- 🛡️ **Context Isolation**: Prevents renderer from accessing Node.js APIs
- 🔒 **Controlled API**: Only expose necessary functionality
- ✅ **Type Safety**: TypeScript interfaces for all exposed methods

#### **3️⃣ Renderer Process** (`renderer.tsx`)
```typescript
// React application entry point
const root = ReactDOM.createRoot(document.getElementById('root')!);
root.render(
  <React.StrictMode>
    <ThemeProvider theme={theme}>
      <App />
    </ThemeProvider>
  </React.StrictMode>
);
```

**Responsibilities:**
- 🎨 **UI Rendering**: React component tree
- 🌐 **Theme System**: Dark/light mode management
- 📊 **State Management**: Global application state
- 🔄 **Event Handling**: User interactions and IPC communication

### 🎯 User Interaction Flow Examples

#### **📂 Project Opening Flow**
```mermaid
sequenceDiagram
    participant U as User
    participant A as App.tsx
    participant P as Preload
    participant M as Main Process
    participant F as File System
    
    U->>A: Click "Open Project"
    A->>P: electronAPI.selectDirectory()
    P->>M: ipcRenderer.invoke('select-directory')
    M->>F: dialog.showOpenDialog()
    F-->>M: Selected directory path
    M-->>P: Return path
    P-->>A: Directory path
    A->>A: Update project state
    A->>U: Display project files
```

#### **💾 File Editing & Saving Flow**
```mermaid
sequenceDiagram
    participant U as User
    participant E as Editor
    participant A as App.tsx
    participant P as Preload
    participant M as Main Process
    
    U->>E: Edit file content
    E->>E: Update in-memory state
    U->>E: Cmd+S (Save)
    E->>A: Trigger save action
    A->>P: electronAPI.saveFile(path, content)
    P->>M: ipcRenderer.invoke('save-file')
    M->>M: fs.writeFile()
    M-->>P: Success confirmation
    P-->>A: File saved
    A->>U: Show save indicator
```

### 🧠 Context System Architecture

```mermaid
graph TD
    subgraph "🔑 Core Contexts"
        Auth[AuthContext<br/>🔐 User & Session]
        Project[ProjectContext<br/>📁 Files & State]
        Editor[EditorContext<br/>📝 Code & Cursor]
        Focus[FocusContext<br/>⌨️ Keyboard Nav]
        Chat[ChatContext<br/>💬 AI Conversations]
    end
    
    subgraph "⚡ Service Layer"
        WS[WebSocket Manager<br/>🔄 Real-time]
        API[API Client<br/>🌐 HTTP Calls]
        LLM[LLM Service<br/>🤖 AI Models]
        File[File Service<br/>📂 FS Operations]
        Term[Terminal Service<br/>💻 Shell Access]
    end
    
    subgraph "🖥️ Electron Bridge"
        IPC[IPC Communication<br/>📡 Main ↔ Renderer]
        PRELOAD[Preload APIs<br/>🌉 Secure Bridge]
    end
    
    Auth --> WS
    Project --> File
    Editor --> LLM
    Focus --> API
    Chat --> WS
    
    File --> IPC
    Term --> IPC
    IPC --> PRELOAD
    
    style Auth fill:#e1bee7
    style WS fill:#c5e1a5
    style IPC fill:#ffcc02
    style PRELOAD fill:#ff7043
```

### 🚀 Complete Application Lifecycle

```mermaid
flowchart TD
    subgraph "🏁 Startup Phase"
        A[App Launch]
        B[main.ts Execution]
        C[Window Creation]
        D[Preload Injection]
    end
    
    subgraph "🎨 Renderer Init"
        E[renderer.tsx Load]
        F[React App Mount]
        G[Context Providers]
        H[Component Tree]
    end
    
    subgraph "🔄 Runtime Operations"
        I[User Interactions]
        J[IPC Communication]
        K[File Operations]
        L[AI Processing]
    end
    
    subgraph "🔚 Shutdown"
        M[Window Close]
        N[Cleanup Handlers]
        O[App Terminate]
    end
    
    A --> B --> C --> D
    D --> E --> F --> G --> H
    H --> I --> J
    J --> K --> L
    L --> I
    I --> M --> N --> O
    
    style A fill:#4caf50
    style H fill:#2196f3
    style I fill:#ff9800
    style O fill:#f44336
```

#### **Detailed Execution Flow**

**1. Application Startup:**
```typescript
// main.ts - First execution
1. Electron app.whenReady() fires
2. Create BrowserWindow with security settings
3. Load preload.js script
4. Register IPC handlers for all services
5. Load index.html → renderer.js
```

**2. Renderer Process Initialization:**
```typescript
// renderer.tsx - React bootstrapping  
1. ReactDOM.createRoot() initializes
2. Theme providers wrap application
3. App.tsx renders root component
4. Context providers establish global state
5. Component tree mounts with initial UI
```

**3. User Interaction Examples:**
```typescript
// Typical user workflows

// Project Opening:
User clicks "Open Project" 
→ App.tsx calls electronAPI.selectDirectory()
→ Preload bridges to main process
→ Main shows dialog.showOpenDialog()
→ File path returned through IPC chain
→ React state updates, UI refreshes

// File Editing:
User edits in Monaco Editor
→ Editor state updates in memory
→ User saves (Cmd+S)
→ electronAPI.saveFile() called
→ Main process writes to filesystem
→ Success confirmation flows back
```

### ⚡ Performance Optimizations

| Optimization | Technique | Impact | Implementation |
|--------------|-----------|--------|----------------|
| **Lazy Loading** | Dynamic imports | -60% initial load | `React.lazy()` for routes |
| **Virtual Scrolling** | Render visible only | Handle 100k+ lines | Monaco Editor built-in |
| **Debouncing** | Batch operations | -80% API calls | Custom hooks |
| **Caching** | Smart memoization | <100ms responses | React.memo, useMemo |
| **Web Workers** | Offload processing | Non-blocking UI | File parsing, search |
| **IPC Optimization** | Batch messages | -50% overhead | Message queuing |

## Quick Reference

### Component Integration Flow

```mermaid
sequenceDiagram
    participant User
    participant Editor
    participant AI
    participant Terminal
    participant Files
    
    User->>Editor: Write code
    Editor->>AI: Request assistance
    AI-->>Editor: Suggest improvements
    Editor->>Files: Save changes
    Files->>Terminal: Trigger build
    Terminal-->>User: Show results
```

## See Also

- [Project Management](./02-project-management.md) - Adaptive modes and lifecycle
- [AI Terminal](./03-ai-terminal.md) - Intelligent command line interface
- [Deep Search](./04-deep-search.md) - Advanced code search capabilities
- [Smart Rendering](./05-smart-rendering.md) - Visual file previews and diagrams
