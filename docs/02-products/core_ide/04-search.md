# Search & Discovery

> **Purpose**: Unified search system that helps developers find anything in their codebase - from files and code to concepts and knowledge
> **Last Updated**: June 1, 2025

## 🎯 Vision

KAPI's search system combines traditional IDE search with AI-powered semantic understanding, creating a comprehensive discovery experience that adapts to how developers think and work.

```mermaid
graph TB
A[Developer Need] --> B{Search Type}
B -->|"Where is..."| C[📁 Files Search]
B -->|"Find text..."| D[🔍 Content Search]
B -->|"Show me functions..."| E[⚡ Structure Search]
B -->|"How does...work?"| F[🧠 Semantic Search]
B -->|"Docs for..."| G[🌐 Web Search]

C --> H[Instant File Location]
D --> I[Text Pattern Matching]
E --> J[Code Symbol Navigation]
F --> K[AI-Powered Understanding]
G --> L[Context-Aware Documentation]

    style F fill:#e1bee7
    style K fill:#9c27b0
    style G fill:#b3e5fc
    style L fill:#4fc3f7
```

**Goal**: Make finding anything instant and intuitive, whether searching for files, code, or concepts

## 🚀 Current Implementation Status

### ✅ Phase 1: Core Search (COMPLETED)
- **VSCode-style Search Interface** - Familiar keyboard shortcuts and UI
- **Dual Search Modes** - Quick search bar (`Ctrl+P`) and global search (`Ctrl+Shift+F`)
- **High Performance** - Sub-300ms response times using ripgrep
- **Smart Filtering** - Automatic exclusion of node_modules, binaries, etc.

### ✅ Phase 2: Structure Search (COMPLETED)
- **AST-based Symbol Search** - Find functions, classes, interfaces, types
- **TypeScript/JavaScript Support** - Full ts-morph integration
- **Intelligent Project Detection** - Auto-discovers TypeScript projects
- **Rich Symbol Information** - Shows signatures, parameters, return types

### ✅ Phase 3: Semantic Search (COMPLETED)
- **Local ChromaDB Implementation** - Privacy-first vector storage in `~/.kapi/chroma_db/`
- **Azure OpenAI Integration** - Uses existing backend service for embeddings
- **Documentation-Focused RAG** - Indexes only natural language explanations, not raw code
- **Multi-Format Documentation Support** - Markdown, comments, .doc.json files
- **Special Comment Recognition** - Extracts /// Summary:, /// Purpose: style documentation
- **Sub-100ms Search** - Local vector database for instant responses
- **Confidence Scoring** - Shows match percentage for semantic relevance

### 🔄 Phase 4: Python Support (IN PROGRESS)
- Python AST integration for symbol search
- Cross-language symbol navigation

### 📋 Phase 5: Web Search (PLANNED)
- Context-aware documentation search
- Brave Search API integration
- Version-specific results
- Stack-aware filtering

## 🔍 Search Modes

### 1. Files Search (📁)
**Purpose**: Quickly locate files and directories by name

```
Examples:
- "package.json" → Find all package.json files
- "SearchBar" → Find SearchBar component files  
- ".tsx" → Find all TypeScript React files
- "test" → Find test-related files
```

**Features**:
- Fuzzy matching for partial names
- Real-time results as you type
- Directory path display
- Case-insensitive search

### 2. Content Search (🔍)
**Purpose**: Search within file contents using text patterns

```
Examples:
- "useState(" → Find React hook usage
- "TODO:" → Find TODO comments
- "console.log" → Find debug statements
- "/regex pattern/" → Use regular expressions
```

**Features**:
- Ripgrep-powered for speed
- Syntax highlighting in results
- Options: case-sensitive, whole-word, regex
- Preview with context lines

### 3. Structure Search (⚡)
**Purpose**: Navigate code symbols using AST analysis

```
Examples:
- "handleClick" → Find click handler functions
- "UserInterface" → Find interface definitions
- "authService" → Find service classes
- "useState" → Find hook declarations
```

**Features**:
- Symbol types: Functions (𝑓), Classes (𝒞), Interfaces (𝒾), Types (𝒯), Variables (𝒗)
- Full signature display with parameters and return types
- Auto-initialization when projects open
- Currently supports TypeScript/JavaScript

### 4. Web Search (🌐) - PLANNED
**Purpose**: Search documentation and resources specific to your project's stack

```
Examples:
- "useEffect" → React 18 hooks documentation (matches your version)
- "connection error" → PostgreSQL + Node.js specific solutions
- "mongoose.connect" → Mongoose 6.x docs (not outdated versions)
- "TypeError map undefined" → React-specific solutions
```

**Planned Features**:
- **Context-Aware Enhancement**: Automatically adds version numbers and stack context
- **Smart Filtering**: Shows only relevant results for your tech stack
- **Version Matching**: Prioritizes documentation matching your dependencies
- **Source Prioritization**: Official docs → Stack Overflow → Tutorials → Blogs
- **Code-to-Search**: Right-click any code to search its documentation

### 5. Semantic Search (🧠) - ✅ IMPLEMENTED
**Purpose**: Find information using natural language queries

```
Live Examples:
- "How does authentication work?" → Finds auth services, middleware, and docs
- "What's our API structure?" → Shows API patterns and route definitions
- "Error handling approach?" → Surfaces error handling patterns and try-catch blocks
- "Where are the React components?" → Locates component files and their usage
```

**Current Features**:
- ✅ Natural language understanding via Azure OpenAI embeddings
- ✅ Local ChromaDB vector storage (`~/.kapi/chroma_db/{projectId}/`)
- ✅ Documentation-only indexing (comments, markdown, .doc.json files)
- ✅ Special documentation comment support (/// Summary:, /// Purpose:)
- ✅ Confidence scoring (60-100% match threshold)
- ✅ Multi-format document recognition (markdown, documentation, comments)
- ✅ Sub-100ms search response times
- ✅ Privacy-preserving local-first architecture
- ✅ Cost-effective: focused embedding generation, unlimited searches

#### Local-First Architecture with Chroma

```mermaid
graph TD
    subgraph "Semantic Search Pipeline"
        A[Natural Language Query] --> B[Query Processor]
        B --> C[Embedding Generation]
        C --> D[Local Chroma DB]
        
        D --> E[Vector Similarity Search]
        E --> F[Result Ranking]
        F --> G[AI Enhancement]
        G --> H[Contextual Results]
        
        I[Project Files] --> J[Document Processor]
        J --> K[Chunking & Embedding]
        K --> D
    end
    
    style C fill:#e1bee7
    style D fill:#ba68c8
    style G fill:#9c27b0
```

**Implementation Benefits**:
- **Zero-latency searches**: Local vector DB eliminates network overhead
- **Privacy-first**: Code never leaves developer's machine
- **Offline capable**: Full semantic search without internet
- **Cost-effective**: One-time embedding, unlimited searches

## 💡 User Experience

### Search Interface
```mermaid
graph TD
    subgraph "Quick Search Bar - Ctrl+P"
        A1[Tabbed Interface]
        A2["Files / Content / Structure / Web / Semantic"]
        A3[Real-time Results]
        A4[Keyboard Navigation]
    end
    
    subgraph "Global Search - Ctrl+Shift+F"
        B1[Full Search Modal]
        B2[Advanced Options]
        B3[Detailed Previews]
        B4[Result Actions]
    end
    
    A1 --> A2
    A2 --> A3
    A3 --> A4
    
    B1 --> B2
    B2 --> B3
    B3 --> B4
    
    style A1 fill:#e3f2fd
    style B1 fill:#c5e1a5
```

### Key Features
- **Instant Feedback**: Results appear as you type (300ms debounce)
- **Smart Defaults**: Intelligent filtering and ranking
- **Keyboard First**: Full keyboard navigation support
- **Context Aware**: Shows relevant information for each result type
- **Visual Indicators**: Icons and badges for different result types

## 📊 Performance Metrics

| Search Type | Response Time | Scope | Implementation |
|-------------|---------------|-------|----------------|
| **Files** | <100ms | File names & paths | File system metadata |
| **Content** | <300ms | File contents | Ripgrep integration |
| **Structure** | <300ms | Code symbols | AST parsing (ts-morph) |
| **Semantic** | <100ms ✅ | Natural language queries | Local ChromaDB + Azure embeddings |
| **Web** | <800ms (planned) | External docs | Brave Search API |

### Scale Testing
- ✅ Handles projects with 10k+ files efficiently
- ✅ Smart filtering reduces search scope by ~90%
- ✅ Memory usage under 50MB for typical projects
- ✅ Cross-platform performance (Windows, Mac, Linux)

## 🎯 Search Strategy Guide

### When to Use Each Mode

#### Use **Files Search** for:
- Finding specific configuration files
- Locating components by name
- Discovering project structure
- Quick file navigation

#### Use **Content Search** for:
- Finding specific code implementations
- Locating TODO comments or debug code
- Searching for string literals or URLs
- Pattern matching with regex

#### Use **Structure Search** for:
- Understanding code architecture
- Finding all usages of a symbol
- Navigating between related functions
- Refactoring assistance

#### Use **Web Search** (future) for:
- Finding official documentation for libraries
- Resolving error messages with stack-specific solutions
- Learning best practices for your tech stack
- Checking API references with version accuracy

#### Use **Semantic Search** ✅ for:
- **Understanding concepts**: "How does authentication work?" "What's our error handling strategy?"
- **Finding documentation**: "Where is the API documentation?" "Show me README files"
- **Exploring explanations**: "What are the architectural decisions?" "Why did we choose this pattern?"
- **Learning from comments**: "Developer explanations about complex logic" "Intent behind code changes"
- **Project knowledge**: Perfect for understanding team decisions and design rationale

**What Gets Indexed**:
- 📝 Markdown files (README, docs)
- 💬 Code comments (// /* /// style)
- 📋 Special documentation comments (/// Summary:, /// Purpose:)
- 📄 .doc.json machine-readable documentation
- ❌ Raw code implementation (use Structure search for that)

**Pro Tips**:
- First time using semantic search will trigger automatic project indexing (one-time setup)
- Use natural language - ask questions like you would to a colleague
- Results show confidence percentages to help gauge relevance
- Works offline once indexed - no internet required for searches
- Focuses on "what" and "why", not "how" (implementation details)

## 🔮 Future Enhancements

### Web Search Implementation (Phase 4)

#### Context-Aware Documentation Search
```mermaid
flowchart TD
    A[User Query] --> B[Context Analyzer]
    B --> C[Extract Project Info]
    
    C --> D[package.json]
    C --> E[Import Statements]
    C --> F[Error Context]
    
    D --> G[Version Numbers]
    E --> H[Used Libraries]
    F --> I[Error Type]
    
    G --> J[Enhanced Query]
    H --> J
    I --> J
    
    J --> K[Brave Search API]
    K --> L[Filtered Results]
    
    L --> M[Rank by Relevance]
    M --> N[Display with Context]
    
    style B fill:#b3e5fc
    style J fill:#4fc3f7
    style M fill:#0288d1
```

#### Smart Query Enhancement
```typescript
interface WebSearchEnhancement {
  originalQuery: string;
  enhancedQuery: string;
  context: {
    language: 'typescript' | 'javascript' | 'python';
    framework: string;      // 'react', 'express', 'django'
    versions: Record<string, string>;  // { 'react': '18.2.0' }
    errorContext?: string;  // If searching from error
  };
}

// Examples:
// "useEffect" → "useEffect React 18.2.0 hooks documentation"
// "ECONNREFUSED" → "ECONNREFUSED PostgreSQL Node.js connection error"
```

#### Result Filtering & Ranking
```typescript
interface WebSearchResult {
  url: string;
  title: string;
  snippet: string;
  
  // Relevance scoring
  relevance: {
    versionMatch: boolean;     // Matches project versions
    officialDoc: boolean;      // From official sources
    stackSpecific: boolean;    // Matches tech stack
    recency: Date;            // How recent the content is
    communityScore: number;   // SO votes, GitHub stars
  };
  
  // Context indicators
  warnings: string[];         // "Deprecated in your version"
  highlights: string[];       // Key relevant sections
}
```

#### Integration Features
- **Code-to-Search**: Right-click any symbol → "Search documentation"
- **Error-to-Search**: Click error message → "Find solutions"
- **Version Awareness**: Automatic version detection from dependencies
- **Stack Detection**: Infer tech stack from project structure
- **Result Caching**: Cache frequent searches for offline access

### Semantic Search Implementation (Phase 5)

#### Architecture Overview
```mermaid
flowchart TD
    A[User Query] --> B[Query Processor]
    B --> C{Query Type}
    
    C -->|Natural Language| D[Semantic Engine]
    C -->|Keywords| E[Traditional Search]
    
    D --> F[Local Chroma DB]
    F --> G[Vector Search]
    G --> H[RAG Enhancement]
    
    E --> I[Ripgrep Results]
    
    H --> J[Merged Results]
    I --> J
    
    J --> K[Unified Response]
    
    subgraph "Local Storage"
        L[~/.kapi/chroma_db/]
        M[Project Embeddings]
        N[Document Chunks]
        O[Metadata Index]
        
        L --> M
        L --> N
        L --> O
    end
    
    F -.-> L
    
    style D fill:#e1bee7
    style F fill:#ba68c8
    style G fill:#9c27b0
    style L fill:#f3e5f5
```

#### Local Chroma Implementation

```typescript
// Semantic search service with local Chroma
class SemanticSearchService {
  private chromaClient: ChromaClient;
  private embeddingCache: Map<string, Float32Array>;
  
  constructor(projectId: string) {
    // Initialize local Chroma instance
    this.chromaClient = new ChromaClient({
      path: path.join(os.homedir(), '.kapi', 'chroma_db', projectId)
    });
    
    this.embeddingCache = new Map();
  }
  
  async indexProject(projectPath: string) {
    // Process project files
    const documents = await this.extractDocuments(projectPath);
    
    // Chunk documents for optimal search
    const chunks = this.chunkDocuments(documents, {
      chunkSize: 800,
      overlap: 100
    });
    
    // Generate embeddings locally or via API
    const embeddings = await this.generateEmbeddings(chunks);
    
    // Store in local Chroma
    const collection = await this.chromaClient.getOrCreateCollection({
      name: 'project_docs',
      metadata: { 'hnsw:space': 'cosine' }
    });
    
    await collection.add({
      ids: chunks.map((_, i) => `doc_${i}`),
      embeddings: embeddings,
      documents: chunks.map(c => c.text),
      metadatas: chunks.map(c => ({
        file_path: c.filePath,
        chunk_index: c.index,
        type: c.documentType
      }))
    });
  }
  
  async search(query: string, options: SearchOptions) {
    const collection = await this.chromaClient.getCollection({
      name: 'project_docs'
    });
    
    // Generate query embedding
    const queryEmbedding = await this.generateEmbedding(query);
    
    // Local vector search - sub-100ms
    const results = await collection.query({
      queryEmbeddings: [queryEmbedding],
      nResults: options.limit || 20,
      include: ['documents', 'metadatas', 'distances']
    });
    
    // Format and enhance results
    return this.enhanceResults(results, query);
  }
}
```

#### Planned Capabilities
1. **Natural Language Understanding**
   - Ask questions in plain English
   - Understand intent and context
   - Provide explanations, not just locations

2. **Knowledge Integration**
   - Search documentation
   - Include code comments and discussions
   - Surface team knowledge and decisions

3. **Intelligent Results**
   - Relevance ranking based on context
   - Code examples with explanations
   - Related concept suggestions

4. **Learning Integration**
   - Tutorial-style responses for beginners
   - Deep technical details for experts
   - Progressive disclosure of complexity

#### Local Storage Architecture

```mermaid
graph TD
    subgraph "Chroma Local Storage"
        A[~/.kapi/chroma_db/] --> B[{project_id}/]
        
        B --> C[collections/]
        B --> D[embeddings.parquet]
        B --> E[metadata.json]
        
        C --> F[project_docs/]
        C --> G[code_symbols/]
        C --> H[interviews/]
        
        F --> I[chunks & vectors]
        G --> J[function embeddings]
        H --> K[Q&A embeddings]
    end
    
    subgraph "Benefits"
        L["🚀 <100ms queries"]
        M["🔒 Private & secure"]
        N["📡 Works offline"]
        O["💰 No API costs"]
    end
    
    B -.-> L
    B -.-> M
    B -.-> N
    B -.-> O
    
    style A fill:#f5f5f5
    style D fill:#e1f5fe
    style L fill:#e8f5e9
    style M fill:#fff9c4
    style N fill:#e3f2fd
    style O fill:#f3e5f5
```

### Additional Enhancements

#### Multi-Language Support
- Python AST integration (Phase 3 - In Progress)
- Go, Rust, Java support planned
- Cross-language symbol references

#### Advanced Features
- Search history and saved queries
- Custom search scopes and filters
- Team search patterns and insights
- Search-driven refactoring tools

#### Performance Optimizations
- Incremental indexing for large codebases
- Background index updates
- Distributed search for monorepos
- Caching for frequent queries
- **Local vector DB**: Sub-100ms semantic search with Chroma
- **Smart chunking**: Optimal 800-token chunks with 100-token overlap
- **Hybrid search**: Combine vector and text search for best results

## 🔧 Technical Architecture

### Current Implementation
```mermaid
graph TD
    subgraph "Frontend"
        UI[Search UI]
        SC[Search Context]
        KB[Keyboard Shortcuts]
    end
    
    subgraph "IPC Layer"
        CH[IPC Channels]
        VL[Validation]
    end
    
    subgraph "Backend"
        RG[Ripgrep Engine]
        AST[AST Parser]
        FS[File System]
    end
    
    UI --> SC
    SC --> KB
    SC --> CH
    CH --> VL
    VL --> RG
    VL --> AST
    VL --> FS
    
    style UI fill:#e3f2fd
    style RG fill:#c8e6c9
    style AST fill:#fff9c4
```

### Security & Performance
- **Whitelist-based IPC**: Only approved search operations
- **Input sanitization**: Prevent injection attacks
- **Resource limits**: Max results and timeout controls
- **Smart filtering**: 90% reduction in search scope

## 📈 Success Metrics

### Current Achievements
- ✅ **Search Latency**: <100ms for semantic search, <300ms for all other modes
- ✅ **Semantic Search**: Fully implemented with 60%+ confidence threshold
- ✅ **Local-First Privacy**: Code never leaves developer's machine
- ✅ **User Adoption**: Primary navigation method for most users
- ✅ **Accuracy**: 98% correct results for structure search
- ✅ **Performance**: Handles large codebases efficiently
- ✅ **Cost Efficiency**: One-time embedding generation, unlimited local searches

### Future Goals
- 🎯 **Cross-Language Support**: Python AST integration, 5+ languages total
- 🎯 **Web Search Integration**: Context-aware documentation search
- 🎯 **Knowledge Coverage**: 100% of project documentation indexed
- 🎯 **User Satisfaction**: >90% find what they need across all search modes

## 🔗 Related Documentation

**User Guides**:
- [Search Usage Guide](../../../04-guides/usage/search-guide.md) - How to use search effectively

**Technical**:
- [Search Implementation](../../../03-technical/implementations/search-implementation.md) - Technical implementation details
- [System Architecture](../../00-system-architecture.md) - Overall system design

**Related Features**:
- [AI Agents](../ai_assistance/01-ai-agents.md) - AI features that use search
- [Project Management](./02-project-management.md) - Project-aware search context

---

<div align="center">
  <p><strong>🔍 Search Everything: Files, Code, Concepts, Knowledge</strong></p>
  <p><em>From finding files to understanding systems - search that thinks like you do</em></p>
</div>
