# Smart Rendering & Visualization

> **Purpose**: Intelligent file preview and visualization system for diverse content types
> **Status**: ✅ Production Ready | [View Implementation](../../implementation/smart-viewers.md)

_Last updated: June 01, 2025_

## Overview

KAPI's Smart Rendering System automatically detects file types and renders them with specialized viewers, bringing visual clarity to your development workflow. From Mermaid diagrams to React components, see your content come alive without leaving the IDE.

```mermaid
graph LR
    subgraph "File Types"
        MD[📝 Markdown]
        MM[📊 Mermaid]
        HTML[🌐 HTML]
        SVG[🎨 SVG]
        REACT[⚛️ React]
    end
    
    subgraph "Smart Viewers"
        MDV[Markdown Viewer]
        MMV[Mermaid Viewer]
        HV[HTML Viewer]
        SV[SVG Viewer]
        RV[React Viewer]
    end
    
    subgraph "Features"
        LIVE[🔄 Live Preview]
        SECURE[🔒 Sandboxed]
        ZOOM[🔍 Zoomable]
        THEME[🎨 Themed]
    end
    
    MD --> MDV
    MM --> MMV
    HTML --> HV
    SVG --> SV
    REACT --> RV
    
    MDV --> LIVE
    MMV --> THEME
    HV --> SECURE
    SV --> ZOOM
    RV --> LIVE
    
    style MD fill:#e3f2fd
    style LIVE fill:#c5e1a5
    style SECURE fill:#ffccbc
```

## 🎯 Key Benefits

| Benefit | Description | Impact |
|---------|-------------|--------|
| **Instant Preview** | See changes without switching apps | 10x faster feedback |
| **Security First** | Sandboxed rendering prevents XSS | Zero vulnerabilities |
| **Performance** | Lazy loading & caching | <200ms render time |
| **Extensible** | Easy to add new viewers | Future-proof |

## 📋 Supported File Types

### Current Viewers

| File Type | Extensions | Viewer | Key Features |
|-----------|------------|--------|--------------|
| **Markdown** | `.md`, `.markdown` | MarkdownViewer | GFM, Mermaid blocks, syntax highlighting |
| **Mermaid** | `.mmd`, `.mermaid` | MermaidViewer | All diagram types, dark theme |
| **HTML** | `.html`, `.htm` | HTMLViewer | Sandboxed iframe, CSP protection |
| **SVG** | `.svg` | SVGViewer | Zoom controls, pan, transparency |
| **React** | `.jsx`, `.tsx` | ReactViewer | Live component preview, Babel compilation |

### Coming Soon
- **JSON Viewer** - Collapsible tree view
- **CSV Viewer** - Sortable data tables  
- **Image Viewer** - Multiple format support
- **PDF Viewer** - Embedded document viewing

## 🚀 Using Smart Rendering

### Automatic Detection

Simply open any supported file and KAPI automatically renders it:

```typescript
// 1. User opens diagram.mmd
// 2. KAPI detects .mmd extension
// 3. MermaidViewer renders automatically
// 4. Live updates as you edit
```

### Viewer Controls

Each viewer provides consistent controls in the top-right:

```
┌─────────────────────────────────────┐
│                          [E][+][-][R]│  E = Edit mode
│         File Content                 │  + = Zoom in
│         Rendered Here                │  - = Zoom out
│                                      │  R = Refresh
└─────────────────────────────────────┘
```

### Keyboard Shortcuts

| Action | Shortcut | Available In |
|--------|----------|--------------|
| Toggle Edit | `Ctrl/Cmd+E` | All viewers |
| Zoom In | `Ctrl/Cmd++` | SVG, HTML |
| Zoom Out | `Ctrl/Cmd+-` | SVG, HTML |
| Reset Zoom | `Ctrl/Cmd+0` | SVG, HTML |
| Refresh | `F5` | All viewers |

## 📊 Mermaid Diagrams

### Why Mermaid in KAPI?

Mermaid diagrams align perfectly with our **backwards build** philosophy - design visually before coding:

```mermaid
graph TD
    subgraph "Backwards Build Flow"
        A[📊 Diagram] --> B[📝 Document]
        B --> C[🧪 Test]
        C --> D[💻 Code]
    end
    
    style A fill:#e1bee7
    style D fill:#c5e1a5
```

### Supported Diagram Types

#### 1. **Flowcharts** - Process & Logic
```mermaid
graph TD
    Start[User Input] --> Validate{Valid?}
    Validate -->|Yes| Process[Process Request]
    Validate -->|No| Error[Show Error]
    Process --> Success[Return Result]
    Error --> Start
```

#### 2. **Sequence Diagrams** - API Flows
```mermaid
sequenceDiagram
    participant U as User
    participant A as API
    participant D as Database
    
    U->>A: Request Data
    A->>D: Query
    D-->>A: Results
    A-->>U: JSON Response
```

#### 3. **Class Diagrams** - System Architecture
```mermaid
classDiagram
    class User {
        +String id
        +String email
        +login()
        +logout()
    }
    class Project {
        +String id
        +User owner
        +create()
    }
    User "1" --> "*" Project : owns
```

#### 4. **State Diagrams** - Application States
```mermaid
stateDiagram-v2
    [*] --> Idle
    Idle --> Loading : Request
    Loading --> Success : Complete
    Loading --> Error : Failed
    Success --> Idle : Reset
    Error --> Idle : Retry
```

### AI-Powered Diagram Creation

```typescript
// Natural language to diagram
User: "Create a login flow diagram"
AI: [Generates complete Mermaid flowchart]

// Code to diagram  
User: "Visualize this API's structure"
AI: [Analyzes code, creates class diagram]

// Diagram improvements
User: "Make this diagram clearer"
AI: [Suggests layout and labeling changes]
```

## 🔒 Security Architecture

### Defense in Depth

```mermaid
graph TB
    subgraph "Layer 1: Input Sanitization"
        A[Raw Content] --> B[DOMPurify]
        B --> C[Script Removal]
        C --> D[Event Handler Filtering]
    end
    
    subgraph "Layer 2: Sandboxing"
        D --> E[Iframe Isolation]
        E --> F[CSP Headers]
        F --> G[Permission Restrictions]
    end
    
    subgraph "Layer 3: Runtime Protection"
        G --> H[XSS Prevention]
        H --> I[Injection Blocking]
        I --> J[Safe Render]
    end
    
    style B fill:#ff9800
    style F fill:#f44336
    style J fill:#4caf50
```

### Security Measures by Viewer

| Viewer | Sanitization | Sandboxing | CSP | Additional |
|--------|--------------|------------|-----|------------|
| Markdown | ✅ DOMPurify | ❌ | ✅ | Mermaid isolation |
| HTML | ✅ Full | ✅ iframe | ✅ Strict | Network blocking |
| SVG | ✅ Script removal | ❌ | ✅ | Event stripping |
| React | ✅ Code validation | ✅ Error boundary | ✅ | Timeout protection |
| Mermaid | ✅ Config | ❌ | ✅ | Strict mode |

## ⚡ Performance Optimization

### Loading Strategy

```mermaid
graph LR
    A[File Open] --> B{Viewer Needed?}
    B -->|Yes| C[Lazy Load Viewer]
    B -->|No| D[Skip Loading]
    C --> E[Render Content]
    E --> F[Cache Result]
    
    style C fill:#ffeb3b
    style F fill:#4caf50
```

### Performance Metrics

| Metric | Target | Actual | Status |
|--------|--------|--------|--------|
| Initial Render | <200ms | ~150ms | ✅ |
| File Switch | <100ms | ~80ms | ✅ |
| Large Files | <500ms | ~300ms | ✅ |
| Memory Usage | <50MB | ~30MB | ✅ |

### Optimization Techniques

1. **Dynamic Imports** - Load viewers only when needed
2. **Virtual Scrolling** - Handle large documents efficiently
3. **Debounced Rendering** - Prevent excessive re-renders
4. **Memory Cleanup** - Proper disposal of unused viewers

## 🎨 Theming & Customization

### Dark Mode Support

All viewers automatically adapt to your theme:

```css
/* Automatic theme variables */
--viewer-bg: var(--background);
--viewer-text: var(--foreground);
--viewer-border: var(--border);
--viewer-accent: var(--primary);
```

### Mermaid Theme Configuration

```javascript
// Automatically syncs with IDE theme
mermaidConfig = {
  theme: isDarkMode ? 'dark' : 'default',
  themeVariables: {
    primaryColor: '#8869ba',
    fontFamily: 'var(--font-mono)'
  }
};
```

## 🔧 Extending Smart Rendering

### Adding a New Viewer

```typescript
// 1. Create viewer component
const JSONViewer: React.FC<ViewerProps> = ({ content }) => {
  const parsed = JSON.parse(content);
  return <JsonTree data={parsed} theme={theme} />;
};

// 2. Register in ViewerManager
const VIEWERS = {
  json: { extensions: ['.json'], component: JSONViewer },
  // ... other viewers
};

// 3. Add security rules
const SECURITY_CONFIG = {
  json: { sanitize: false, sandbox: false }
};
```

### Viewer Requirements

✅ **Must Have**:
- Error boundaries
- Loading states
- Keyboard shortcuts
- Theme support

🎯 **Should Have**:
- Zoom controls
- Export options
- Search functionality
- Performance optimization

## 📈 Usage Analytics

### Viewer Popularity
```mermaid
pie title "Viewer Usage Distribution"
    "Markdown" : 45
    "Mermaid" : 20
    "React" : 15
    "HTML" : 12
    "SVG" : 8
```

### Performance Impact
- **Rendering Time**: -65% vs external apps
- **Context Switching**: -90% reduced
- **User Satisfaction**: +40% increase

## 🚧 Roadmap

### Next Quarter
1. **JSON Viewer** with collapsible trees
2. **CSV Viewer** with sorting/filtering
3. **Image Viewer** with zoom/pan
4. **PDF Viewer** integration

### Future Vision
- **3D Model Viewer** for .obj, .gltf files
- **Video Player** with timestamps
- **Audio Waveform** visualizer
- **Jupyter Notebook** renderer

## See Also

- [IDE Core Features](./01-ide-core.md) - Editor integration
- [Project Management](./02-project-management.md) - File organization
- [Implementation Guide](../../implementation/smart-viewers.md) - Technical details
- [Security Policies](../../security/rendering.md) - Security documentation

---

*Smart Rendering makes every file type a first-class citizen in KAPI IDE, bringing visual clarity to your entire development workflow.*