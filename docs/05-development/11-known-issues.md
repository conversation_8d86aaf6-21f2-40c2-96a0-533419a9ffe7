# Known Issues and Limitations

_Last updated: May 31, 2025_

## Overview

This document tracks current known issues, limitations, and workarounds in KAPI IDE. We believe in transparency about our current constraints while we work toward solutions.

## Table of Contents

1. [Critical Issues](#critical-issues)
2. [Feature Limitations](#feature-limitations)
3. [Performance Constraints](#performance-constraints)
4. [Workarounds](#workarounds)
5. [Roadmap](#roadmap)

---

## Critical Issues

Search within file doesn't work due to FocusManager fighting it.
[FocusManager] Focus changed from editor to editor (not able to find within file)
    npm run test (failing)
    <NAME_EMAIL> not working. 
     Fix issues from security-action-plan
 ide-refering-refactoring
 code_review_summary

  The Monaco find widget is a complex interaction between Monaco's internal focus management and our focus system. It needs
  dedicated time to investigate how Monaco handles the find widget focus and whether we need to temporarily disable our focus
  management when the find widget is active.

### 🔴 **Terminal Tab Management**

**Status**: Temporarily Disabled  
**Impact**: Users limited to single terminal tab  
**Priority**: Medium (nice-to-have feature)

#### Issue Description

Multiple terminal tabs feature is experiencing critical lifecycle management issues between React components and PTY processes.

#### Technical Details

```mermaid
graph TD
    subgraph "Issue Flow"
        A[Create New Tab] --> B[PTY Process Spawns]
        B --> C[React Re-render]
        C --> D[Cleanup Effect Triggers]
        D --> E[PTY Killed Immediately]
        E --> F[Terminal Non-functional]
    end
    
    style A fill:#ffebee
    style E fill:#ef5350
    style F fill:#c62828
```

#### Investigation Summary

  What we've tried:
  - Global PTY kill registries with deduplication
  - Terminal initialization coordination
  - Mount/unmount strategies
  - Component lifecycle management
  - Multiple cleanup safeguards

  What's still happening:
  - PTY processes getting killed immediately after creation
  - Multiple competing cleanup effects
  - React component lifecycle conflicts with PTY management

  🎯 Recommendation: Punt for Later

  Why punt:
  1. Deep architectural issue - This involves React lifecycle + Electron IPC + PTY process management
  2. Multiple attempted fixes haven't solved the root cause
  3. Terminal functionality works for single terminal use
  4. Time investment vs impact - This is a nice-to-have feature, not core functionality

  Immediate workaround: ✅ **IMPLEMENTED**
  - ✅ Disabled tab creation temporarily 
  - ✅ Disabled keyboard shortcuts (Ctrl+Shift+`, Ctrl+Shift+T)
  - ✅ Disabled duplicate tab context menu option
  - ✅ Added clear user feedback with disabled button and tooltip
  - Focus on other IDE features that provide more user value
  - Come back to this when we have more time for deep debugging

  Status: **TEMPORARILY DISABLED** - Single terminal tab works perfectly

#### Future Investigation Plan

**Root Cause Hypotheses**:
1. React StrictMode causing double mounting
2. Multiple Terminal components mounting simultaneously  
3. Electron IPC message queue issues
4. PTY creation timing conflicts

**Proposed Solutions**:
1. Single terminal manager service for all PTY creation
2. Message queuing system for PTY operations
3. Lazy terminal creation on tab activation
4. Alternative terminal library evaluation

## Feature Limitations

### 🟡 **Current Constraints**

| Feature | Limitation | Workaround | Timeline |
|---------|-----------|------------|----------|
| **Terminal Tabs** | Single tab only | Use external terminal | Q3 2025 |
| **Git Integration** | Basic operations only | Use CLI for advanced | Q2 2025 |
| **Plugin System** | Not yet available | Request features | Q4 2025 |
| **Mobile App** | Limited functionality | Use web version | Q3 2025 |

## Performance Constraints

### 📊 **Known Performance Issues**

- **Large Files**: Files >10MB may cause editor lag
- **Many Open Files**: Performance degrades with 50+ open tabs
- **AI Response Time**: Complex queries may take 10-30 seconds
- **Search**: Project-wide search slow on large codebases

## Workarounds

### 🔧 **Temporary Solutions**

1. **Terminal Tabs Disabled**
   - Use single terminal effectively
   - External terminal for multiple sessions
   - Terminal splitting coming soon

2. **Performance Optimization**
   - Close unused file tabs
   - Use `.kapiignore` for large files
   - Limit AI context size when possible

## Roadmap

### 🚀 **Issue Resolution Timeline**

```mermaid
gantt
    title Issue Resolution Roadmap
    dateFormat  YYYY-MM-DD
    section Critical
    Terminal Tabs Fix    :2025-07-01, 30d
    Performance Opt      :2025-06-15, 45d
    
    section Features
    Git Integration      :2025-06-01, 60d
    Plugin System        :2025-09-01, 90d
    
    section Nice-to-Have
    Mobile Enhancements  :2025-08-01, 60d
```

## See Also

- [System Architecture](./00-system-architecture.md) - Technical design details
- [User Flow](./10-user-flow.md) - Current user experience
- [Community Features](./05-community-features.md) - Report issues to community