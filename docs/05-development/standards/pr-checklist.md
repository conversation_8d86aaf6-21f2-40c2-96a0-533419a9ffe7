
# ✅ Pull Request Checklist (For All Developers)

## 🔤 1. Branch Naming
Use clear, kebab-case branch names:
- `feature/add-login-screen`
- `bugfix/fix-terminal-resize`
- `refactor/ai-panel-cleanup`
- `doc/update-readme`

🧠 Rule of thumb: `type/what-you’re-doing`

---

## 📝 2. Pull Request Title & Description
- **Title** should be short & clear:  
  `Fix crash on empty chat message`
- **Description** should include:
  - 🧩 What changed
  - 🎯 Why it changed
  - 🧪 Any test steps (manual or CI)

---

## 🔍 3. Code Quality
- Code is **small and focused** (<300 lines preferred)
- All major functions have comments or docstrings
- Follow style guide / linter (e.g., <PERSON>SL<PERSON>, <PERSON>uff, Prettier)
- No debug `console.log` or `print` left in

---

## 🧪 4. Testing
- ✅ Local tests run cleanly
- ✅ CI/CD (GitHub Actions) is passing
- ✅ Manual testing for UI/edge cases

---

## 📎 5. Link to Issues (if any)
- Include `Closes #123` or `Related to #456`
- Helps track what's being solved

---

## 🗣 6. Review Collaboration
- Tag reviewer(s): `@balajivis please review`
- Respond to feedback with comments or new commits
- Don’t merge your own PR without approval (unless hotfix)

---

## 🧹 7. After Merge
- ✅ Delete the branch if merged
- 📓 Update changelog or documentation if needed
