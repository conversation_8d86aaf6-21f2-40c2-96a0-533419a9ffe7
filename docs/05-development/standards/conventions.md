# Kapi Browser IDE Design Conventions
The Kapi Browser IDE is designed with the "backwards build" philosophy in mind, which emphasizes creating software in this order:
This document outlines the design conventions for the Kapi Browser IDE, ensuring consistency across the application.
- Use type safe tsx files and not jsx files. 
- In backend manage python dependencies using Poetry.
- The FastAPI server has a sqladmin as admin interface.
- Prioritise short, simple code. Don't create complex abstractions.
- Keep documentation as concise and simple as possible. Project is in early stages and will be updated frequently. Keep it relevant.
- For the browser_ide Keep a simple common stylesheet with CSS variables for consistent styling.
- Mark temporary files with temp_ prefix and slate it for deletion.
- Run Eslint on frontend and Ruff/mypy on backend
- Mandatory AI written unit tests (poetry run pytest and npm run test)
- <50 lines a function


1. **Documentation First** (`#228be6` Blue)
   - Clear descriptions of intent and functionality
   - Design decisions and architecture documents
   - User stories and requirements
   - Write docstrings for all public modules, classes, and functions
  - Follow Google-style docstring format

2. **Tests Second** (`#40c057` Green)
   - Test cases that validate the requirements
   - Integration tests that ensure components work together
   - Performance and edge case tests

3. **Code Third** (`#fab005` Yellow)
   - Implementation that satisfies documentation and tests
   - Clean, maintainable code
   - Optimization and refinement



This philosophy is reflected in the UI through color-coding, workflow guidance, and component organization. Elements related to documentation use blue accents, testing-related elements use green, and code implementation uses yellow.