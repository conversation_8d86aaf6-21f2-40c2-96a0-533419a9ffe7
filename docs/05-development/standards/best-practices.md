## Best practices

-- Manage branches properly (dont touch the main)

- AI code reviews
- Force comments on every function. Keep it short and concise.
- Mermaid diagram for each PR

PRDs
• App overview
• User flows
• Tech stack & APIs
• Core features
• In-scope vs out-of-scope items

App Flow document:
• Describe every page in your app
• How users move from one to another
• Simple language, no bullets
• Be painfully specific

3. Tech Stack Doc: Tell the AI exactly what to build with.
• All packages & dependencies
• Links to API docs (yes, it can read them)
• Preferred libraries or tools (e.g., Supabase, Stripe, NextAuth)

4. Frontend Guidelines: Give it your design system.
• Fonts
• Color palette
• Spacing & layout rules
• Preferred UI library or framework
• Icon set

5. Backend Structure Doc
• DB schema
• Auth logic
• Storage rules
• Any edge cases

6. Implementation Plan
List 50+ specific steps you’d take if you were coding this manually.

Give this to Cursor or Windsurf, and now they’re just executing, not guessing.

You can create all these docs using ChatGPT.