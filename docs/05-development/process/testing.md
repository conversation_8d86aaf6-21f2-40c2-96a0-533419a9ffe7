# Testing Guide

This document provides information about the testing framework and how to run tests for the project.

## Test Structure

The project's tests are organized in the `backend/tests` directory with the following structure:

- `tests/services/` - Tests for service layer components
- `tests/routers/` - Tests for API routers and endpoints
- `tests/labs/` - Tests for lab features (experimental features)
- `tests/integration/` - Integration tests that test multiple components together

## Running Tests

The project includes a unified test runner script (`run_tests.sh`) that can run all tests or specific test modules.

### Basic Usage

```bash
# Run all tests
./run_tests.sh --all

# Run tests for a specific module
./run_tests.sh --module audio

# Run Nova Sonic tests (default behavior)
./run_tests.sh
# or explicitly
./run_tests.sh --nova-sonic
```

### Command Line Options

The test runner supports the following options:

| Option | Description |
|--------|-------------|
| `--all` | Run all tests in the project |
| `--integration` | Run integration tests |
| `--live` | Run live endpoint tests |
| `--only-live` | Run only live endpoint tests (skip unit tests) |
| `--module NAME` | Run tests for a specific module |
| `--nova-sonic` | Run all Nova Sonic tests (shortcut for `--module nova_sonic`) |
| `--verbose`, `-v` | Show more detailed test output |
| `--coverage` | Generate test coverage report |
| `--list` | List all available test modules |
| `--help` | Show help message |

### Available Test Modules

The following test modules are available:

- `nova_sonic` - Nova Sonic audio service tests
- `audio` - Audio processing tests
- `conversation` - Conversation service tests
- `user` - User service tests
- `project` - Project service tests
- `social` - Social service tests
- `qa` - QA service tests
- `blog` - Blog endpoint tests
- `usage` - Usage endpoint tests

You can list all available modules with:

```bash
./run_tests.sh --list
```

## Test Coverage

To run tests with coverage reporting:

```bash
./run_tests.sh --all --coverage
```

This will generate a coverage report showing which lines of code are covered by tests.

## Writing Tests

### Unit Tests

Unit tests should be placed in the appropriate directory based on what they're testing:

```python
# Example test for a service
# File: tests/services/test_example_service.py

import pytest
from app.services.example_service import ExampleService

def test_example_function():
    service = ExampleService()
    result = service.example_function()
    assert result == expected_value
```

### Integration Tests

Integration tests should test the interaction between multiple components:

```python
# Example integration test
# File: tests/integration/test_example_integration.py

import pytest
from fastapi.testclient import TestClient
from app.main import app

client = TestClient(app)

def test_example_endpoint():
    response = client.get("/api/example")
    assert response.status_code == 200
    assert response.json() == expected_response
```

### Async Tests

For testing async functions, use pytest-asyncio:

```python
# Example async test
# File: tests/services/test_async_service.py

import pytest
import pytest_asyncio
from app.services.async_service import AsyncService

@pytest.mark.asyncio
async def test_async_function():
    service = AsyncService()
    result = await service.async_function()
    assert result == expected_value
```

## Best Practices

1. **Test Isolation**: Each test should be independent and not rely on the state from other tests.
2. **Use Fixtures**: Use pytest fixtures for setup and teardown.
3. **Mock External Dependencies**: Use mocking to isolate the code being tested.
4. **Test Edge Cases**: Include tests for edge cases and error conditions.
5. **Keep Tests Fast**: Tests should run quickly to encourage frequent testing.
6. **Descriptive Test Names**: Use descriptive names that explain what the test is checking.

## Nova Sonic Testing

Nova Sonic tests include:

1. **Unit Tests**:
   - Service tests (`tests/services/test_nova_sonic_service.py`)
   - Router tests (`tests/routers/test_nova_sonic_router.py`)
   - Wrapper tests (`tests/labs/test_nova_sonic_wrapper.py`)

2. **Integration Tests**:
   - End-to-end tests (`tests/integration/test_nova_sonic_integration.py`)

3. **Live Endpoint Tests**:
   - Tests against a running server

To run Nova Sonic tests with live endpoint testing:

```bash
./run_tests.sh --nova-sonic --live
```

## Continuous Integration

Tests are automatically run in the CI pipeline on each pull request and merge to main branches. The CI configuration can be found in the project's CI configuration files.
