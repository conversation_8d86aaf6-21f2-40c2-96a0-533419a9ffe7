# KAPI IDE Development Backlog

_Last updated: May 15, 2025_

## P0: MVP Critical (Must Complete in 2-Week Sprint)

3. Dashboard should reflect real data
1. Move all docs out of /Users/<USER>/Code/kapi-fresh/nodejs_backend/docs to the main docs folder
7. Merge fresh-start to main

### Backend & Infrastructure
-     - Bring tool usage to the voice agent flow
    - Blog tags
      - Test API endpoints
      - Implement API in IDE
      
      - add google analytics
      - Issues in Github actions
      




### Core Agents & Backwards Build
- Implement Documentation & Slides Agent with SVG capabilities
- Implement Code Generation Agent
- Implement Code Quality Agent with "analyze code" and "copy code" features
- Implement minimal Evidence Collection Agent
- Connect agents in backwards build workflow
- Implement critical voice commands


## P1: Important (Next Features After MVP)

### Model Integrations
- Support tool calling
    - new screens for showing mockup

### IDE Enhancements
- Enable canvas drawing
- Support for contextual userinfo (max 1000 tokens as JSON table)
- Merge key backend features
- Implement documentation tools (ref.tools/mcp or perplexity-mcp)

### Voice & Audio Enhancements
- Integrate Google Live Voice

### Platform Improvements
- Integrate search engine
- Setup Clerk waitlist
- Enhance SEO
- Remove broken links
- Remove React admin

## P2: Future Enhancements

### Social Features
- Implement ELO rating system
- Complete leaderboard and karma points
- Finalize user matching
- Develop learning pods

### Documentation Synchronization
- Add endpoints for docs, slides, and code consistency
- Monitor synchronization status
- Notify on inconsistencies

### Cross-Product Integration
- Integrate with Modern AI Pro
- Add workshop templates and learning endpoints
- Implement achievement system endpoints
- Create workshop tracking integration points

### Blog & Content
- Fix blog post backend
- Support blogging features
- Content management system

### External Integrations
- Integrate Heygen
- Explore marketplace integrations (e.g., Github, AWS)
- Review Wolf gaming studios interface
