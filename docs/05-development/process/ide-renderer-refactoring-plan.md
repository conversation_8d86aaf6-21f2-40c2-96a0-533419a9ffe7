# IDE Renderer Refactoring Plan

**Generated**: June 01, 2025  
**Status**: 🔴 Critical - Immediate Action Required  
**Component**: `/new_ide/src/renderer`

## 📊 Priority Matrix

| Priority | Impact | Effort | Timeline | Status |
|----------|---------|---------|----------|---------|
| 🔴 **P0 - Critical** | Blocks Development | High | Week 1 | ⏳ Pending |
| 🟠 **P1 - High** | Major Issues | Medium | Week 2-3 | ⏳ Pending |
| 🟡 **P2 - Medium** | Quality Issues | Low-Med | Week 4-5 | ⏳ Pending |
| 🟢 **P3 - Low** | Nice to Have | Low | Week 6+ | ⏳ Pending |

## 🚨 P0 - Critical Issues (Week 1)

### Immediate Code Fixes

| Issue | File | Action | Owner | Due Date | Status |
|-------|------|--------|-------|----------|---------|
| Remove hardcoded test mode | `App.tsx` | Delete `FORCE_SCREEN = 'ide'` and implement proper env-based testing | TBD | Day 1 | ⏳ |
| Fix AuthProvider duplication | `index.tsx`, `AppProviders.tsx` | Remove AuthProvider from index.tsx, keep only in AppProviders | TBD | Day 1 | ⏳ |
| Remove production test code | `App.tsx` | Remove `window.testHelpers` and all test-related code | TBD | Day 2 | ⏳ |
| Add error boundaries | `App.tsx`, `IDE.tsx` | Implement React error boundaries at app and feature levels | TBD | Day 3 | ⏳ |
| Fix TypeScript `any` types | All files | Replace with proper interfaces (see Type Safety section) | TBD | Day 4-5 | ⏳ |
| **Migrate authentication system** | **Multiple backend files** | **Replace clerkAuthMiddleware with unifiedAuthMiddleware** | **TBD** | **Day 6-7** | **⏳** |
| **Fix Monaco find widget focus** | **Editor.tsx** | **Monaco Cmd+F widget input not clickable/typeable** | **TBD** | **Day 8** | **⏳** |

### Type Safety Fixes

```typescript
// ❌ Current
const [projectConfig, setProjectConfig] = useState<any>(null);
const handleCompleteProjectSetup = (config: any) => { ... }

// ✅ Should be
interface ProjectConfig {
  name: string;
  description: string;
  type: 'react-chatbot' | 'agent' | 'web-app';
  directory: string;
  initialized: boolean;
}
const [projectConfig, setProjectConfig] = useState<ProjectConfig | null>(null);
```

### Authentication System Migration

**Files requiring authentication update** (13 total):

| Priority | File | Current Auth | Target Auth | Endpoints Affected | Status |
|----------|------|-------------|-------------|-------------------|---------|
| 🔴 High | `routes/conversation-tasks/index.ts` | clerkAuthMiddleware | unifiedAuthMiddleware | Chat, code-gen, planning tasks | ⏳ |
| 🔴 High | `routes/projects/project.routes.ts` | clerkAuthMiddleware | unifiedAuthMiddleware | Project CRUD operations | ⏳ |
| 🔴 High | `api/code-analysis/routes.ts` | clerkAuthMiddleware | unifiedAuthMiddleware | Code analysis features | ⏳ |
| 🟠 Medium | `routes/users/index.ts` | clerkAuthMiddleware | unifiedAuthMiddleware | User onboarding | ⏳ |
| 🟠 Medium | `routes/blog/blog.routes.ts` | clerkAuthMiddleware | unifiedAuthMiddleware | Blog CRUD operations | ⏳ |
| 🟠 Medium | `routes/social.routes.ts` | clerkAuthMiddleware | unifiedAuthMiddleware | Social features | ⏳ |
| 🟡 Low | `routes/memory.routes.ts` | clerkAuthMiddleware | unifiedAuthMiddleware | Context management | ⏳ |
| 🟡 Low | `routes/payment.routes.ts` | clerkAuthMiddleware | unifiedAuthMiddleware | Payment processing | ⏳ |
| 🟡 Low | `api/agent/controllers/agent.controller.ts` | verifyClerkToken | unified WebSocket auth | WebSocket agent | ⏳ |
| 🔧 Core | `middleware/auth.middleware.ts` | Clerk wrapper | Integrate with unified | Core auth wrapper | ⏳ |
| 🔧 Core | `middleware/clerk.middleware.ts` | Clerk implementation | Unified integration | Core authentication | ⏳ |

**Migration Pattern:**
```typescript
// ❌ Current
import { clerkAuthMiddleware } from '../../middleware/clerk.middleware';
router.use(clerkAuthMiddleware);

// ✅ Target
import { unifiedAuthMiddleware } from '../../middleware/unified-auth';
router.use(unifiedAuthMiddleware);
```

## 🟠 P1 - High Priority (Week 2-3)

### Component Refactoring Plan

| Component | Current Lines | Target Lines | Subtasks | Effort |
|-----------|---------------|--------------|----------|---------|
| `App.tsx` | 600+ | <200 | 1. Extract Navigation<br>2. Extract Screen Manager<br>3. Extract Auth Logic<br>4. Implement Router | 3 days |
| `Editor.tsx` | 1400+ | <300 | 1. Extract Monaco Manager<br>2. Extract Viewer Logic<br>3. Extract Keyboard Handlers<br>4. Create EditorToolbar | 4 days |
| `FileExplorer.tsx` | 1500+ | <400 | 1. Extract FileTree<br>2. Extract ContextMenu<br>3. Extract FileOperations<br>4. Optimize tree algorithms | 4 days |

### CSS Architecture Migration

| Phase | Current State | Target State | Action Items | Timeline |
|-------|--------------|--------------|--------------|----------|
| 1 | Global CSS files | CSS Modules | • Install css-modules<br>• Create module files<br>• Update imports | 2 days |
| 2 | Inline styles | Styled Components | • Extract inline styles<br>• Create styled components<br>• Type theme | 3 days |
| 3 | Hardcoded colors | Theme System | • Create theme provider<br>• Define tokens<br>• Replace hardcoded values | 2 days |

## 🟡 P2 - Medium Priority (Week 4-5)

### State Management Improvement

```mermaid
graph TD
    A[Current: 12 Contexts] --> B[Proposed: 3 Core Contexts]
    B --> C[AuthContext]
    B --> D[AppStateContext]
    B --> E[UIContext]
    
    D --> F[Editor State]
    D --> G[Project State]
    D --> H[Terminal State]
    
    E --> I[Theme]
    E --> J[Layout]
    E --> K[Modals]
```

### File Structure Reorganization

```
src/renderer/
├── core/                    # Core utilities and setup
│   ├── providers/          # Context providers
│   ├── hooks/             # Shared hooks
│   └── types/             # TypeScript definitions
├── components/             # Reusable UI components
│   ├── common/            # Buttons, Inputs, etc.
│   ├── layout/            # Layout components
│   └── feedback/          # Modals, Toasts, etc.
├── features/              # Feature modules
│   ├── editor/
│   │   ├── components/    # Editor-specific components
│   │   ├── hooks/        # Editor hooks
│   │   ├── services/     # Editor services
│   │   └── index.ts      # Public API
│   ├── explorer/
│   ├── terminal/
│   └── chat/
├── pages/                 # Route pages
├── services/              # API and external services
├── styles/                # Global styles and themes
└── utils/                 # Utility functions
```

## 🟢 P3 - Low Priority (Week 6+)

### Performance Optimizations

| Component | Current Issue | Solution | Metric | Target |
|-----------|--------------|----------|---------|---------|
| FileExplorer | Re-renders on every file change | Implement React.memo + useMemo | Render count | -80% |
| Editor | Large bundle size | Code split Monaco editor | Bundle size | -40% |
| Contexts | Unnecessary re-renders | Context splitting + selectors | Re-render count | -60% |
| Lists | No virtualization | Implement react-window | FPS on scroll | 60fps |

### Documentation Requirements

- [ ] Component documentation with JSDoc
- [ ] Storybook setup for UI components
- [ ] Architecture decision records (ADRs)
- [ ] API documentation
- [ ] Testing guide

## 📈 Success Metrics

| Metric | Current | Target | Measurement |
|--------|---------|---------|-------------|
| Bundle Size | ~8MB | <3MB | Webpack analyzer |
| First Paint | 3.2s | <1s | Lighthouse |
| TypeScript Coverage | ~40% | 100% | TS strict mode |
| Component Size | 500+ lines avg | <200 lines | ESLint rules |
| Test Coverage | 0% | >80% | Jest coverage |

## 🛠️ Tooling Setup

### Required Dependencies

```json
{
  "devDependencies": {
    "@types/react": "^18.0.0",
    "css-modules": "^0.0.0",
    "styled-components": "^6.0.0",
    "@types/styled-components": "^5.1.0",
    "react-error-boundary": "^4.0.0",
    "@tanstack/react-query": "^5.0.0",
    "zustand": "^4.0.0",
    "react-window": "^1.8.0"
  }
}
```

### ESLint Rules to Add

```javascript
{
  "rules": {
    "max-lines": ["error", 300],
    "max-lines-per-function": ["error", 50],
    "@typescript-eslint/no-explicit-any": "error",
    "react/prop-types": "off",
    "@typescript-eslint/explicit-module-boundary-types": "error"
  }
}
```

## 👥 Team Assignments

| Developer | Focus Area | Week 1 Tasks | Week 2-3 Tasks |
|-----------|------------|--------------|----------------|
| Dev 1 | Core Architecture | Fix auth, remove test code | Refactor App.tsx |
| Dev 2 | UI Components | Add error boundaries | CSS architecture |
| Dev 3 | Features | Fix TypeScript types | Refactor Editor.tsx |
| Dev 4 | Performance | - | FileExplorer optimization |

## 📅 Weekly Checkpoints

### Week 1 Checkpoint
- [ ] All P0 issues resolved
- [ ] No hardcoded test values in production
- [ ] Error boundaries implemented
- [ ] Type safety improved (no `any` types)
- [ ] **Authentication system unified (13 files migrated)**
- [ ] **CORS and streaming issues resolved**

### Week 2-3 Checkpoint
- [ ] Large components refactored
- [ ] CSS modules implemented
- [ ] Theme system in place
- [ ] 50% reduction in component sizes

### Week 4-5 Checkpoint
- [ ] State management simplified
- [ ] File structure reorganized
- [ ] Performance metrics improved
- [ ] Documentation started

### Week 6+ Checkpoint
- [ ] All optimizations complete
- [ ] Full documentation
- [ ] 80%+ test coverage
- [ ] Production ready

## 🚀 Getting Started

1. **Create feature branch**: `git checkout -b refactor/ide-renderer`
2. **Set up tooling**: Install required dependencies
3. **Start with P0**: Fix critical issues first
4. **Daily standups**: Track progress on action items
5. **Code reviews**: Mandatory for all changes
6. **Update this doc**: Mark items as ✅ when complete

---

**Note**: This is a living document. Update status and add findings as work progresses.
