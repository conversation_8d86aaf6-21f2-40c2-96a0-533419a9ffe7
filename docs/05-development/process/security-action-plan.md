# Critical Security Action Plan
**Created**: June 1, 2025  
**Status**: EMERGENCY - Immediate Action Required  
**Priority**: P0-P1 Items (1-9)

---

## 🚨 EMERGENCY ACTIONS (Execute Immediately)

### 1. R<PERSON><PERSON><PERSON> EXPOSED GOOGLE CLOUD CREDENTIALS
**Priority**: P0 CRITICAL  
**Estimated Time**: 30 minutes  
**Owner**: DevOps/Security Lead

#### Why This Is Critical
- **Google Cloud service account private key** is exposed in repository
- **Full access** to Google TTS services under project `lofty-scheduler-456614-q0`
- **Active security breach** - credentials are publicly accessible
- **Potential unauthorized billing** and service abuse

#### Action Steps
```bash
# 1. Immediately revoke the service account key
# Go to Google Cloud Console -> IAM & Admin -> Service Accounts
# Find: <EMAIL>
# Delete the key with ID: a6a3cf07d7601494d384e2e06243687f1081f4b7

# 2. Remove from git history
cd /Users/<USER>/Code/kapi-fresh/nodejs_backend
git filter-branch --force --index-filter \
'git rm --cached --ignore-unmatch config/google-tts-key.json' \
--prune-empty --tag-name-filter cat -- --all

# 3. Generate new service account key
# Store in environment variable: GOOGLE_TTS_CREDENTIALS_JSON

# 4. Update application code
# Replace file reference with environment variable
```

#### Success Criteria
- [ ] Old service account key revoked in Google Cloud Console
- [ ] File removed from git history (verify with `git log --all -- config/google-tts-key.json`)
- [ ] New key generated and stored securely
- [ ] Application updated to use environment variable
- [ ] Force push coordinated with team

---

### 2. UPDATE HIGH-RISK DEPENDENCIES
**Priority**: P0 CRITICAL  
**Estimated Time**: 2-3 hours  
**Owner**: Frontend/Backend Leads

#### Why This Is Critical
- **React Router v7.5.1**: 2 HIGH severity vulnerabilities (CVE: data spoofing, DoS)
- **Electron v27.3.11**: 9 major versions behind (critical security patches missing)
- **esbuild vulnerability**: Development server can read responses from any website
- **Active attack vectors** in production dependencies

#### Action Steps

**Frontend (new_ide/):**
```bash
cd new_ide/

# 1. Update React Router (HIGH severity fix)
npm update @remix-run/router react-router react-router-dom

# 2. Update Electron (critical security patches)
npm install electron@latest

# 3. Update esbuild (security fix)
npm update esbuild

# 4. Run security audit
npm audit fix
npm audit --audit-level high

# 5. Test critical functionality
npm run build
npm run test
```

**Backend (nodejs_backend/):**
```bash
cd nodejs_backend/

# 1. Update AI SDK dependencies
npm install @anthropic-ai/sdk@latest
npm install @google/genai@latest
npm install openai@latest

# 2. Run security audit
npm audit fix
npm audit --audit-level high

# 3. Test AI integrations
npm run test
npm run build
```

#### Success Criteria
- [ ] React Router updated to secure version
- [ ] Electron updated to v36+
- [ ] esbuild updated to secure version
- [ ] AI SDKs updated to latest versions
- [ ] `npm audit` shows no HIGH/CRITICAL vulnerabilities
- [ ] All tests passing after updates
- [ ] Application functionality verified

---

## 🔧 IMMEDIATE ACTIONS (Next 1-2 Days)

### 3. ✅ AUTHENTICATION SECURITY - COMPLETED
**Priority**: P0 CRITICAL  
**Status**: ✅ COMPLETED  

#### What Was Fixed
- ✅ Removed automatic admin user creation in development
- ✅ Added explicit environment variable controls (`DEVELOPMENT_AUTH_BYPASS`)
- ✅ Implemented proper WebSocket JWT authentication
- ✅ Added session ownership verification

#### Verification Steps
- [ ] Verify no automatic admin creation in development
- [ ] Test authentication requires proper credentials
- [ ] Confirm WebSocket connections require valid JWT tokens

---

### 4. ✅ SECRET MANAGEMENT - PARTIALLY COMPLETED
**Priority**: P0 CRITICAL  
**Status**: 🟡 PARTIALLY COMPLETED  

#### What Was Fixed
- ✅ Removed hardcoded development secrets from auth middleware
- ✅ Added environment variable enforcement
- ✅ Replaced default JWT secrets with required env vars

#### Remaining Work
```bash
# Remove hardcoded test credentials
# File: new_ide/tests/e2e/utils/test-credentials.ts
# Replace hardcoded password: M03cnEQ5d25x

# File: nodejs_backend/tests/aws-credentials.test.ts
# Move AWS credential tests to use environment variables
```

#### Success Criteria
- [ ] No hardcoded credentials in test files
- [ ] All authentication uses environment variables
- [ ] Production deployment fails if secrets not provided

---

### 5. UPDATE AI DEPENDENCIES
**Priority**: P1 HIGH  
**Estimated Time**: 1-2 hours  
**Owner**: Backend Lead

#### Why This Is Critical
- **@anthropic-ai/sdk v0.17.2**: 35 minor versions behind (Latest: v0.52.0)
- **@google/genai v0.14.1**: Major version behind (Latest: v1.3.0)
- **openai v4.103.0**: Major version behind (Latest: v5.0.1)
- **Breaking changes** may affect AI functionality
- **Security patches** and performance improvements missing

#### Action Steps
```bash
cd nodejs_backend/

# 1. Update Anthropic SDK
npm install @anthropic-ai/sdk@latest

# 2. Update Google GenAI
npm install @google/genai@latest

# 3. Update OpenAI SDK
npm install openai@latest

# 4. Check for breaking changes
# Review changelogs for each SDK
# Update service implementations if needed

# 5. Test AI services
npm run test -- --grep "AI"
npm run test -- --grep "agent"
npm run test -- --grep "conversation"
```

#### Potential Breaking Changes
- **OpenAI v4 → v5**: Response format changes
- **Google GenAI**: API endpoint changes
- **Anthropic SDK**: Token handling updates

#### Success Criteria
- [ ] All AI SDKs updated to latest versions
- [ ] AI service tests passing
- [ ] Agent functionality verified
- [ ] Conversation flows working
- [ ] No regression in AI features

---

### 6. FIX BROKEN TESTS
**Priority**: P1 HIGH  
**Estimated Time**: 2-3 hours  
**Owner**: QA Lead

#### Why This Is Critical
- **All test suites failing** due to NODE_ENV assignment error
- **No automated testing** means potential bugs go undetected
- **CI/CD pipeline broken** - can't verify code quality
- **Security testing impossible** without working test infrastructure

#### Action Steps

**Fix NODE_ENV Error:**
```typescript
// tests/setup.ts - Replace line 5
// OLD: process.env.NODE_ENV = 'test';
// NEW: Object.defineProperty(process.env, 'NODE_ENV', { value: 'test' });

// Alternative approach:
beforeAll(() => {
  if (!process.env.NODE_ENV) {
    Object.defineProperty(process.env, 'NODE_ENV', { value: 'test' });
  }
});
```

**Remove Hardcoded Test Credentials:**
```typescript
// new_ide/tests/e2e/utils/test-credentials.ts
// Replace hardcoded credentials with environment variables
export const testCredentials = {
  email: process.env.TEST_USER_EMAIL || '<EMAIL>',
  password: process.env.TEST_USER_PASSWORD || 'generated-test-password'
};
```

**Verify Test Execution:**
```bash
cd nodejs_backend/
npm run test

cd ../new_ide/
npm run test
```

#### Success Criteria
- [ ] NODE_ENV assignment error resolved
- [ ] All test suites can execute
- [ ] No hardcoded credentials in test files
- [ ] Basic API endpoint tests added
- [ ] Test coverage report generated

---

## 🔧 HIGH PRIORITY ACTIONS (Next 1-2 Weeks)

### 7. PERFORMANCE OPTIMIZATION
**Priority**: P1 HIGH  
**Estimated Time**: 1 week  
**Owner**: Backend Lead

#### Why This Is Critical
- **No caching layer** - every request hits database
- **N+1 query patterns** causing performance bottlenecks
- **WebSocket memory leaks** in session management
- **Scalability issues** will impact user experience

#### Action Steps

**Implement Redis Caching:**
```bash
# Install Redis dependencies
npm install redis @types/redis

# Add caching to key services
# - User sessions
# - AI model responses
# - Frequent database queries
```

**Add Database Indexes:**
```sql
-- High-priority indexes for performance
CREATE INDEX idx_conversations_user_id ON conversations(user_id);
CREATE INDEX idx_messages_conversation_id ON messages(conversation_id);
CREATE INDEX idx_model_usage_user_id ON model_usage(user_id);
CREATE INDEX idx_model_usage_created_at ON model_usage(created_at);
```

**Fix N+1 Queries:**
```typescript
// Add proper includes to Prisma queries
const conversations = await prisma.conversations.findMany({
  include: {
    messages: true,
    user: true
  }
});
```

#### Success Criteria
- [ ] Redis caching implemented for sessions
- [ ] Database indexes added for critical queries
- [ ] N+1 queries identified and fixed
- [ ] WebSocket session cleanup implemented
- [ ] Performance benchmarks improved by 50%+

---

### 8. SECURITY HARDENING
**Priority**: P1 HIGH  
**Estimated Time**: 3-4 days  
**Owner**: Security Lead

#### Why This Is Critical
- **Error messages expose stack traces** - information disclosure
- **CORS allows all origins** - potential XSS attacks
- **No input sanitization** - XSS vulnerability
- **Rate limiting missing** - DoS vulnerability

#### Action Steps

**Fix Error Handling:**
```typescript
// src/middleware/error.middleware.ts
export const errorHandler = (err: Error, req: Request, res: Response, next: NextFunction) => {
  const isDevelopment = process.env.NODE_ENV === 'development';
  
  res.status(status).json({
    success: false,
    message: isDevelopment ? err.message : 'Internal server error',
    // Remove stack trace exposure
    ...(isDevelopment && { stack: err.stack })
  });
};
```

**Configure Secure CORS:**
```typescript
// src/app.ts
app.use(cors({
  origin: process.env.CORS_ORIGINS?.split(',') || ['http://localhost:3001'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));
```

**Add Input Sanitization:**
```bash
npm install express-validator helmet express-rate-limit
```

#### Success Criteria
- [ ] Error messages sanitized (no stack traces in production)
- [ ] CORS configured with specific allowed origins
- [ ] Input validation added to all endpoints
- [ ] Rate limiting implemented
- [ ] Security headers added (helmet)

---

### 9. CODE QUALITY IMPROVEMENTS
**Priority**: P1 HIGH  
**Estimated Time**: 2-3 days  
**Owner**: Development Team

#### Why This Is Critical
- **Inconsistent error handling** across services
- **Type safety violations** with `any` types
- **Async/await patterns** inconsistent - potential race conditions
- **Code maintainability** issues affecting development velocity

#### Action Steps

**Standardize Error Handling:**
```typescript
// Create consistent Result pattern
type Result<T> = {
  success: true;
  data: T;
} | {
  success: false;
  error: string;
};

// Update all service methods to use Result pattern
async getUser(id: number): Promise<Result<User>> {
  try {
    const user = await this.prisma.user.findUnique({ where: { id } });
    if (!user) {
      return { success: false, error: 'User not found' };
    }
    return { success: true, data: user };
  } catch (error) {
    return { success: false, error: 'Database error' };
  }
}
```

**Fix Type Safety:**
```typescript
// Replace any types with proper TypeScript types
// src/services/base.service.ts
import { PrismaClient } from '@prisma/client';

protected get modelClient(): PrismaClient {
  return this.prisma;
}
```

**Standardize Async Patterns:**
```typescript
// Use Promise.all for independent operations
const [session, user, permissions] = await Promise.all([
  this.sessionService.getSession(sessionId),
  this.userService.getUser(userId),
  this.authService.getPermissions(userId)
]);
```

#### Success Criteria
- [ ] All services use consistent Result pattern
- [ ] No `any` types in critical code paths
- [ ] Consistent async/await patterns
- [ ] ESLint rules enforced
- [ ] Code review checklist updated

---

## Execution Timeline

### Week 1 (Days 1-7)
**Day 1 (EMERGENCY):**
- [ ] Item 1: Revoke Google credentials
- [ ] Item 2: Update high-risk dependencies

**Days 2-3:**
- [ ] Item 4: Complete secret management cleanup
- [ ] Item 5: Update AI dependencies
- [ ] Item 6: Fix broken tests

**Days 4-7:**
- [ ] Item 7: Begin performance optimization (Redis setup)
- [ ] Item 8: Security hardening (error handling, CORS)

### Week 2 (Days 8-14)
- [ ] Item 7: Complete performance optimization
- [ ] Item 8: Complete security hardening
- [ ] Item 9: Code quality improvements

## Risk Assessment

| Action | Risk Level | Mitigation |
|--------|------------|------------|
| Credential Revocation | Low | Coordinate with team, have new credentials ready |
| Dependency Updates | Medium | Test thoroughly, have rollback plan |
| AI SDK Updates | High | Breaking changes possible, extensive testing needed |
| Database Changes | Medium | Run migrations in staging first |
| Error Handling Changes | Low | Gradual rollout, monitor logs |

## Success Metrics

- [ ] Security score improved from 2.0/10 to 7.0/10
- [ ] No HIGH/CRITICAL vulnerabilities in `npm audit`
- [ ] All tests passing
- [ ] Performance improved by 50%+
- [ ] Zero exposed credentials or secrets
- [ ] Production deployment ready

## Emergency Contacts

- **Security Issues**: [Security Lead]
- **Infrastructure**: [DevOps Lead]  
- **Dependencies**: [Frontend/Backend Leads]
- **Testing**: [QA Lead]

---

**Note**: This plan focuses on P0-P1 items (1-9) that require immediate to short-term attention. Items 10+ are medium/long-term priorities to be addressed after these critical issues are resolved.