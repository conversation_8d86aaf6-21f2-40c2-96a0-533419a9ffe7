<?xml version="1.0" encoding="utf-8"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" d2Version="0.6.9" preserveAspectRatio="xMinYMin meet" viewBox="0 0 1179 518"><svg class="d2-svg" width="1179" height="518" viewBox="-176 -101 1179 518"><style type="text/css"><![CDATA[
.d2-4174224679 .text {
	font-family: "d2-4174224679-font-regular";
}
@font-face {
	font-family: d2-4174224679-font-regular;
	src: url("data:application/font-woff;base64,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");
}
.text-underline {
	text-decoration: underline;
}
.d2-4174224679 .text-bold {
	font-family: "d2-4174224679-font-bold";
}
@font-face {
	font-family: d2-4174224679-font-bold;
	src: url("data:application/font-woff;base64,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");
}
.d2-4174224679 .text-italic {
	font-family: "d2-4174224679-font-italic";
}
@font-face {
	font-family: d2-4174224679-font-italic;
	src: url("data:application/font-woff;base64,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");
}]]></style><style type="text/css"><![CDATA[.shape {
  shape-rendering: geometricPrecision;
  stroke-linejoin: round;
}
.connection {
  stroke-linecap: round;
  stroke-linejoin: round;
}
.blend {
  mix-blend-mode: multiply;
  opacity: 0.5;
}

		.d2-4174224679 .fill-N1{fill:#0A0F25;}
		.d2-4174224679 .fill-N2{fill:#676C7E;}
		.d2-4174224679 .fill-N3{fill:#9499AB;}
		.d2-4174224679 .fill-N4{fill:#CFD2DD;}
		.d2-4174224679 .fill-N5{fill:#DEE1EB;}
		.d2-4174224679 .fill-N6{fill:#EEF1F8;}
		.d2-4174224679 .fill-N7{fill:#FFFFFF;}
		.d2-4174224679 .fill-B1{fill:#0D32B2;}
		.d2-4174224679 .fill-B2{fill:#0D32B2;}
		.d2-4174224679 .fill-B3{fill:#E3E9FD;}
		.d2-4174224679 .fill-B4{fill:#E3E9FD;}
		.d2-4174224679 .fill-B5{fill:#EDF0FD;}
		.d2-4174224679 .fill-B6{fill:#F7F8FE;}
		.d2-4174224679 .fill-AA2{fill:#4A6FF3;}
		.d2-4174224679 .fill-AA4{fill:#EDF0FD;}
		.d2-4174224679 .fill-AA5{fill:#F7F8FE;}
		.d2-4174224679 .fill-AB4{fill:#EDF0FD;}
		.d2-4174224679 .fill-AB5{fill:#F7F8FE;}
		.d2-4174224679 .stroke-N1{stroke:#0A0F25;}
		.d2-4174224679 .stroke-N2{stroke:#676C7E;}
		.d2-4174224679 .stroke-N3{stroke:#9499AB;}
		.d2-4174224679 .stroke-N4{stroke:#CFD2DD;}
		.d2-4174224679 .stroke-N5{stroke:#DEE1EB;}
		.d2-4174224679 .stroke-N6{stroke:#EEF1F8;}
		.d2-4174224679 .stroke-N7{stroke:#FFFFFF;}
		.d2-4174224679 .stroke-B1{stroke:#0D32B2;}
		.d2-4174224679 .stroke-B2{stroke:#0D32B2;}
		.d2-4174224679 .stroke-B3{stroke:#E3E9FD;}
		.d2-4174224679 .stroke-B4{stroke:#E3E9FD;}
		.d2-4174224679 .stroke-B5{stroke:#EDF0FD;}
		.d2-4174224679 .stroke-B6{stroke:#F7F8FE;}
		.d2-4174224679 .stroke-AA2{stroke:#4A6FF3;}
		.d2-4174224679 .stroke-AA4{stroke:#EDF0FD;}
		.d2-4174224679 .stroke-AA5{stroke:#F7F8FE;}
		.d2-4174224679 .stroke-AB4{stroke:#EDF0FD;}
		.d2-4174224679 .stroke-AB5{stroke:#F7F8FE;}
		.d2-4174224679 .background-color-N1{background-color:#0A0F25;}
		.d2-4174224679 .background-color-N2{background-color:#676C7E;}
		.d2-4174224679 .background-color-N3{background-color:#9499AB;}
		.d2-4174224679 .background-color-N4{background-color:#CFD2DD;}
		.d2-4174224679 .background-color-N5{background-color:#DEE1EB;}
		.d2-4174224679 .background-color-N6{background-color:#EEF1F8;}
		.d2-4174224679 .background-color-N7{background-color:#FFFFFF;}
		.d2-4174224679 .background-color-B1{background-color:#0D32B2;}
		.d2-4174224679 .background-color-B2{background-color:#0D32B2;}
		.d2-4174224679 .background-color-B3{background-color:#E3E9FD;}
		.d2-4174224679 .background-color-B4{background-color:#E3E9FD;}
		.d2-4174224679 .background-color-B5{background-color:#EDF0FD;}
		.d2-4174224679 .background-color-B6{background-color:#F7F8FE;}
		.d2-4174224679 .background-color-AA2{background-color:#4A6FF3;}
		.d2-4174224679 .background-color-AA4{background-color:#EDF0FD;}
		.d2-4174224679 .background-color-AA5{background-color:#F7F8FE;}
		.d2-4174224679 .background-color-AB4{background-color:#EDF0FD;}
		.d2-4174224679 .background-color-AB5{background-color:#F7F8FE;}
		.d2-4174224679 .color-N1{color:#0A0F25;}
		.d2-4174224679 .color-N2{color:#676C7E;}
		.d2-4174224679 .color-N3{color:#9499AB;}
		.d2-4174224679 .color-N4{color:#CFD2DD;}
		.d2-4174224679 .color-N5{color:#DEE1EB;}
		.d2-4174224679 .color-N6{color:#EEF1F8;}
		.d2-4174224679 .color-N7{color:#FFFFFF;}
		.d2-4174224679 .color-B1{color:#0D32B2;}
		.d2-4174224679 .color-B2{color:#0D32B2;}
		.d2-4174224679 .color-B3{color:#E3E9FD;}
		.d2-4174224679 .color-B4{color:#E3E9FD;}
		.d2-4174224679 .color-B5{color:#EDF0FD;}
		.d2-4174224679 .color-B6{color:#F7F8FE;}
		.d2-4174224679 .color-AA2{color:#4A6FF3;}
		.d2-4174224679 .color-AA4{color:#EDF0FD;}
		.d2-4174224679 .color-AA5{color:#F7F8FE;}
		.d2-4174224679 .color-AB4{color:#EDF0FD;}
		.d2-4174224679 .color-AB5{color:#F7F8FE;}.appendix text.text{fill:#0A0F25}.md{--color-fg-default:#0A0F25;--color-fg-muted:#676C7E;--color-fg-subtle:#9499AB;--color-canvas-default:#FFFFFF;--color-canvas-subtle:#EEF1F8;--color-border-default:#0D32B2;--color-border-muted:#0D32B2;--color-neutral-muted:#EEF1F8;--color-accent-fg:#0D32B2;--color-accent-emphasis:#0D32B2;--color-attention-subtle:#676C7E;--color-danger-fg:red;}.sketch-overlay-B1{fill:url(#streaks-darker-d2-4174224679);mix-blend-mode:lighten}.sketch-overlay-B2{fill:url(#streaks-darker-d2-4174224679);mix-blend-mode:lighten}.sketch-overlay-B3{fill:url(#streaks-bright-d2-4174224679);mix-blend-mode:darken}.sketch-overlay-B4{fill:url(#streaks-bright-d2-4174224679);mix-blend-mode:darken}.sketch-overlay-B5{fill:url(#streaks-bright-d2-4174224679);mix-blend-mode:darken}.sketch-overlay-B6{fill:url(#streaks-bright-d2-4174224679);mix-blend-mode:darken}.sketch-overlay-AA2{fill:url(#streaks-dark-d2-4174224679);mix-blend-mode:overlay}.sketch-overlay-AA4{fill:url(#streaks-bright-d2-4174224679);mix-blend-mode:darken}.sketch-overlay-AA5{fill:url(#streaks-bright-d2-4174224679);mix-blend-mode:darken}.sketch-overlay-AB4{fill:url(#streaks-bright-d2-4174224679);mix-blend-mode:darken}.sketch-overlay-AB5{fill:url(#streaks-bright-d2-4174224679);mix-blend-mode:darken}.sketch-overlay-N1{fill:url(#streaks-darker-d2-4174224679);mix-blend-mode:lighten}.sketch-overlay-N2{fill:url(#streaks-dark-d2-4174224679);mix-blend-mode:overlay}.sketch-overlay-N3{fill:url(#streaks-normal-d2-4174224679);mix-blend-mode:color-burn}.sketch-overlay-N4{fill:url(#streaks-normal-d2-4174224679);mix-blend-mode:color-burn}.sketch-overlay-N5{fill:url(#streaks-bright-d2-4174224679);mix-blend-mode:darken}.sketch-overlay-N6{fill:url(#streaks-bright-d2-4174224679);mix-blend-mode:darken}.sketch-overlay-N7{fill:url(#streaks-bright-d2-4174224679);mix-blend-mode:darken}.light-code{display: block}.dark-code{display: none}]]></style><style type="text/css">.md em,
.md dfn {
  font-family: "d2-4174224679-font-italic";
}

.md b,
.md strong {
  font-family: "d2-4174224679-font-bold";
}

.md code,
.md kbd,
.md pre,
.md samp {
  font-family: "d2-4174224679-font-mono";
  font-size: 1em;
}

.md {
  tab-size: 4;
}

/* variables are provided in d2renderers/d2svg/d2svg.go */

.md {
  -ms-text-size-adjust: 100%;
  -webkit-text-size-adjust: 100%;
  margin: 0;
  color: var(--color-fg-default);
  background-color: transparent; /* we don't want to define the background color */
  font-family: "d2-4174224679-font-regular";
  font-size: 16px;
  line-height: 1.5;
  word-wrap: break-word;
}

.md details,
.md figcaption,
.md figure {
  display: block;
}

.md summary {
  display: list-item;
}

.md [hidden] {
  display: none !important;
}

.md a {
  background-color: transparent;
  color: var(--color-accent-fg);
  text-decoration: none;
}

.md a:active,
.md a:hover {
  outline-width: 0;
}

.md abbr[title] {
  border-bottom: none;
  text-decoration: underline dotted;
}

.md dfn {
  font-style: italic;
}

.md h1 {
  margin: 0.67em 0;
  padding-bottom: 0.3em;
  font-size: 2em;
  border-bottom: 1px solid var(--color-border-muted);
}

.md mark {
  background-color: var(--color-attention-subtle);
  color: var(--color-text-primary);
}

.md small {
  font-size: 90%;
}

.md sub,
.md sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

.md sub {
  bottom: -0.25em;
}

.md sup {
  top: -0.5em;
}

.md img {
  border-style: none;
  max-width: 100%;
  box-sizing: content-box;
  background-color: var(--color-canvas-default);
}

.md figure {
  margin: 1em 40px;
}

.md hr {
  box-sizing: content-box;
  overflow: hidden;
  background: transparent;
  border-bottom: 1px solid var(--color-border-muted);
  height: 0.25em;
  padding: 0;
  margin: 24px 0;
  background-color: var(--color-border-default);
  border: 0;
}

.md input {
  font: inherit;
  margin: 0;
  overflow: visible;
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
}

.md [type="button"],
.md [type="reset"],
.md [type="submit"] {
  -webkit-appearance: button;
}

.md [type="button"]::-moz-focus-inner,
.md [type="reset"]::-moz-focus-inner,
.md [type="submit"]::-moz-focus-inner {
  border-style: none;
  padding: 0;
}

.md [type="button"]:-moz-focusring,
.md [type="reset"]:-moz-focusring,
.md [type="submit"]:-moz-focusring {
  outline: 1px dotted ButtonText;
}

.md [type="checkbox"],
.md [type="radio"] {
  box-sizing: border-box;
  padding: 0;
}

.md [type="number"]::-webkit-inner-spin-button,
.md [type="number"]::-webkit-outer-spin-button {
  height: auto;
}

.md [type="search"] {
  -webkit-appearance: textfield;
  outline-offset: -2px;
}

.md [type="search"]::-webkit-search-cancel-button,
.md [type="search"]::-webkit-search-decoration {
  -webkit-appearance: none;
}

.md ::-webkit-input-placeholder {
  color: inherit;
  opacity: 0.54;
}

.md ::-webkit-file-upload-button {
  -webkit-appearance: button;
  font: inherit;
}

.md a:hover {
  text-decoration: underline;
}

.md hr::before {
  display: table;
  content: "";
}

.md hr::after {
  display: table;
  clear: both;
  content: "";
}

.md table {
  border-spacing: 0;
  border-collapse: collapse;
  display: block;
  width: max-content;
  max-width: 100%;
  overflow: auto;
}

.md td,
.md th {
  padding: 0;
}

.md details summary {
  cursor: pointer;
}

.md details:not([open]) > *:not(summary) {
  display: none !important;
}

.md kbd {
  display: inline-block;
  padding: 3px 5px;
  color: var(--color-fg-default);
  vertical-align: middle;
  background-color: var(--color-canvas-subtle);
  border: solid 1px var(--color-neutral-muted);
  border-bottom-color: var(--color-neutral-muted);
  border-radius: 6px;
  box-shadow: inset 0 -1px 0 var(--color-neutral-muted);
}

.md h1,
.md h2,
.md h3,
.md h4,
.md h5,
.md h6 {
  margin-top: 24px;
  margin-bottom: 16px;
  font-weight: 400;
  line-height: 1.25;
  font-family: "font-semibold";
}

.md h2 {
  padding-bottom: 0.3em;
  font-size: 1.5em;
  border-bottom: 1px solid var(--color-border-muted);
}

.md h3 {
  font-size: 1.25em;
}

.md h4 {
  font-size: 1em;
}

.md h5 {
  font-size: 0.875em;
}

.md h6 {
  font-size: 0.85em;
  color: var(--color-fg-muted);
}

.md p {
  margin-top: 0;
  margin-bottom: 10px;
}

.md blockquote {
  margin: 0;
  padding: 0 1em;
  color: var(--color-fg-muted);
  border-left: 0.25em solid var(--color-border-default);
}

.md ul,
.md ol {
  margin-top: 0;
  margin-bottom: 0;
  padding-left: 2em;
}

.md ol ol,
.md ul ol {
  list-style-type: lower-roman;
}

.md ul ul ol,
.md ul ol ol,
.md ol ul ol,
.md ol ol ol {
  list-style-type: lower-alpha;
}

.md dd {
  margin-left: 0;
}

.md pre {
  margin-top: 0;
  margin-bottom: 0;
  word-wrap: normal;
}

.md ::placeholder {
  color: var(--color-fg-subtle);
  opacity: 1;
}

.md input::-webkit-outer-spin-button,
.md input::-webkit-inner-spin-button {
  margin: 0;
  -webkit-appearance: none;
  appearance: none;
}

.md::before {
  display: table;
  content: "";
}

.md::after {
  display: table;
  clear: both;
  content: "";
}

.md > *:first-child {
  margin-top: 0 !important;
}

.md > *:last-child {
  margin-bottom: 0 !important;
}

.md a:not([href]) {
  color: inherit;
  text-decoration: none;
}

.md .absent {
  color: var(--color-danger-fg);
}

.md .anchor {
  float: left;
  padding-right: 4px;
  margin-left: -20px;
  line-height: 1;
}

.md .anchor:focus {
  outline: none;
}

.md p,
.md blockquote,
.md ul,
.md ol,
.md dl,
.md table,
.md pre,
.md details {
  margin-top: 0;
  margin-bottom: 16px;
}

.md blockquote > :first-child {
  margin-top: 0;
}

.md blockquote > :last-child {
  margin-bottom: 0;
}

.md sup > a::before {
  content: "[";
}

.md sup > a::after {
  content: "]";
}

.md h1:hover .anchor,
.md h2:hover .anchor,
.md h3:hover .anchor,
.md h4:hover .anchor,
.md h5:hover .anchor,
.md h6:hover .anchor {
  text-decoration: none;
}

.md h1 tt,
.md h1 code,
.md h2 tt,
.md h2 code,
.md h3 tt,
.md h3 code,
.md h4 tt,
.md h4 code,
.md h5 tt,
.md h5 code,
.md h6 tt,
.md h6 code {
  padding: 0 0.2em;
  font-size: inherit;
}

.md ul.no-list,
.md ol.no-list {
  padding: 0;
  list-style-type: none;
}

.md ol[type="1"] {
  list-style-type: decimal;
}

.md ol[type="a"] {
  list-style-type: lower-alpha;
}

.md ol[type="i"] {
  list-style-type: lower-roman;
}

.md div > ol:not([type]) {
  list-style-type: decimal;
}

.md ul ul,
.md ul ol,
.md ol ol,
.md ol ul {
  margin-top: 0;
  margin-bottom: 0;
}

.md li > p {
  margin-top: 16px;
}

.md li + li {
  margin-top: 0.25em;
}

.md dl {
  padding: 0;
}

.md dl dt {
  padding: 0;
  margin-top: 16px;
  font-size: 1em;
  font-style: italic;
  font-family: "font-semibold";
}

.md dl dd {
  padding: 0 16px;
  margin-bottom: 16px;
}

.md table th {
  font-family: "font-semibold";
}

.md table th,
.md table td {
  padding: 6px 13px;
  border: 1px solid var(--color-border-default);
}

.md table tr {
  background-color: var(--color-canvas-default);
  border-top: 1px solid var(--color-border-muted);
}

.md table tr:nth-child(2n) {
  background-color: var(--color-canvas-subtle);
}

.md table img {
  background-color: transparent;
}

.md img[align="right"] {
  padding-left: 20px;
}

.md img[align="left"] {
  padding-right: 20px;
}

.md span.frame {
  display: block;
  overflow: hidden;
}

.md span.frame > span {
  display: block;
  float: left;
  width: auto;
  padding: 7px;
  margin: 13px 0 0;
  overflow: hidden;
  border: 1px solid var(--color-border-default);
}

.md span.frame span img {
  display: block;
  float: left;
}

.md span.frame span span {
  display: block;
  padding: 5px 0 0;
  clear: both;
  color: var(--color-fg-default);
}

.md span.align-center {
  display: block;
  overflow: hidden;
  clear: both;
}

.md span.align-center > span {
  display: block;
  margin: 13px auto 0;
  overflow: hidden;
  text-align: center;
}

.md span.align-center span img {
  margin: 0 auto;
  text-align: center;
}

.md span.align-right {
  display: block;
  overflow: hidden;
  clear: both;
}

.md span.align-right > span {
  display: block;
  margin: 13px 0 0;
  overflow: hidden;
  text-align: right;
}

.md span.align-right span img {
  margin: 0;
  text-align: right;
}

.md span.float-left {
  display: block;
  float: left;
  margin-right: 13px;
  overflow: hidden;
}

.md span.float-left span {
  margin: 13px 0 0;
}

.md span.float-right {
  display: block;
  float: right;
  margin-left: 13px;
  overflow: hidden;
}

.md span.float-right > span {
  display: block;
  margin: 13px auto 0;
  overflow: hidden;
  text-align: right;
}

.md code,
.md tt {
  padding: 0.2em 0.4em;
  margin: 0;
  font-size: 85%;
  background-color: var(--color-neutral-muted);
  border-radius: 6px;
}

.md code br,
.md tt br {
  display: none;
}

.md del code {
  text-decoration: inherit;
}

.md pre code {
  font-size: 100%;
}

.md pre > code {
  padding: 0;
  margin: 0;
  word-break: normal;
  white-space: pre;
  background: transparent;
  border: 0;
}

.md .highlight {
  margin-bottom: 16px;
}

.md .highlight pre {
  margin-bottom: 0;
  word-break: normal;
}

.md .highlight pre,
.md pre {
  padding: 16px;
  overflow: auto;
  font-size: 85%;
  line-height: 1.45;
  background-color: var(--color-canvas-subtle);
  border-radius: 6px;
}

.md pre code,
.md pre tt {
  display: inline;
  max-width: auto;
  padding: 0;
  margin: 0;
  overflow: visible;
  line-height: inherit;
  word-wrap: normal;
  background-color: transparent;
  border: 0;
}

.md .csv-data td,
.md .csv-data th {
  padding: 5px;
  overflow: hidden;
  font-size: 12px;
  line-height: 1;
  text-align: left;
  white-space: nowrap;
}

.md .csv-data .blob-num {
  padding: 10px 8px 9px;
  text-align: right;
  background: var(--color-canvas-default);
  border: 0;
}

.md .csv-data tr {
  border-top: 0;
}

.md .csv-data th {
  font-family: "font-semibold";
  background: var(--color-canvas-subtle);
  border-top: 0;
}

.md .footnotes {
  font-size: 12px;
  color: var(--color-fg-muted);
  border-top: 1px solid var(--color-border-default);
}

.md .footnotes ol {
  padding-left: 16px;
}

.md .footnotes li {
  position: relative;
}

.md .footnotes li:target::before {
  position: absolute;
  top: -8px;
  right: -8px;
  bottom: -8px;
  left: -24px;
  pointer-events: none;
  content: "";
  border: 2px solid var(--color-accent-emphasis);
  border-radius: 6px;
}

.md .footnotes li:target {
  color: var(--color-fg-default);
}

.md .task-list-item {
  list-style-type: none;
}

.md .task-list-item label {
  font-weight: 400;
}

.md .task-list-item.enabled label {
  cursor: pointer;
}

.md .task-list-item + .task-list-item {
  margin-top: 3px;
}

.md .task-list-item .handle {
  display: none;
}

.md .task-list-item-checkbox {
  margin: 0 0.2em 0.25em -1.6em;
  vertical-align: middle;
}

.md .contains-task-list:dir(rtl) .task-list-item-checkbox {
  margin: 0 -1.6em 0.25em 0.2em;
}
</style><style type="text/css"><![CDATA[@keyframes d2Transition-d2-4174224679-0 {
		0%, 0.000000% {
				opacity: 0;
		}
		0.000000%, 7.139286% {
				opacity: 1;
		}
		7.142857%, 100% {
				opacity: 0;
		}
}@keyframes d2Transition-d2-4174224679-1 {
		0%, 7.139286% {
				opacity: 0;
		}
		7.142857%, 14.282143% {
				opacity: 1;
		}
		14.285714%, 100% {
				opacity: 0;
		}
}@keyframes d2Transition-d2-4174224679-2 {
		0%, 14.282143% {
				opacity: 0;
		}
		14.285714%, 21.425000% {
				opacity: 1;
		}
		21.428571%, 100% {
				opacity: 0;
		}
}@keyframes d2Transition-d2-4174224679-3 {
		0%, 21.425000% {
				opacity: 0;
		}
		21.428571%, 28.567857% {
				opacity: 1;
		}
		28.571429%, 100% {
				opacity: 0;
		}
}@keyframes d2Transition-d2-4174224679-4 {
		0%, 28.567857% {
				opacity: 0;
		}
		28.571429%, 35.710714% {
				opacity: 1;
		}
		35.714286%, 100% {
				opacity: 0;
		}
}@keyframes d2Transition-d2-4174224679-5 {
		0%, 35.710714% {
				opacity: 0;
		}
		35.714286%, 42.853571% {
				opacity: 1;
		}
		42.857143%, 100% {
				opacity: 0;
		}
}@keyframes d2Transition-d2-4174224679-6 {
		0%, 42.853571% {
				opacity: 0;
		}
		42.857143%, 49.996429% {
				opacity: 1;
		}
		50.000000%, 100% {
				opacity: 0;
		}
}@keyframes d2Transition-d2-4174224679-7 {
		0%, 49.996429% {
				opacity: 0;
		}
		50.000000%, 57.139286% {
				opacity: 1;
		}
		57.142857%, 100% {
				opacity: 0;
		}
}@keyframes d2Transition-d2-4174224679-8 {
		0%, 57.139286% {
				opacity: 0;
		}
		57.142857%, 64.282143% {
				opacity: 1;
		}
		64.285714%, 100% {
				opacity: 0;
		}
}@keyframes d2Transition-d2-4174224679-9 {
		0%, 64.282143% {
				opacity: 0;
		}
		64.285714%, 71.425000% {
				opacity: 1;
		}
		71.428571%, 100% {
				opacity: 0;
		}
}@keyframes d2Transition-d2-4174224679-10 {
		0%, 71.425000% {
				opacity: 0;
		}
		71.428571%, 78.567857% {
				opacity: 1;
		}
		78.571429%, 100% {
				opacity: 0;
		}
}@keyframes d2Transition-d2-4174224679-11 {
		0%, 78.567857% {
				opacity: 0;
		}
		78.571429%, 85.710714% {
				opacity: 1;
		}
		85.714286%, 100% {
				opacity: 0;
		}
}@keyframes d2Transition-d2-4174224679-12 {
		0%, 85.710714% {
				opacity: 0;
		}
		85.714286%, 92.853571% {
				opacity: 1;
		}
		92.857143%, 100% {
				opacity: 0;
		}
}@keyframes d2Transition-d2-4174224679-13 {
		0%, 92.853571% {
				opacity: 0;
		}
		92.857143%, 100.000000% {
				opacity: 1;
		}
}]]></style><g style="animation: d2Transition-d2-4174224679-0 28000ms infinite" class="d2-4174224679 " width="352" height="225" viewBox="-176 -81 352 225"><rect x="-176.000000" y="-81.000000" width="352.000000" height="225.000000" rx="0.000000" fill="#FFFFFF" class=" fill-N7" stroke-width="0" /><g class="dGl0bGU="><g class="shape" ></g><text x="0.000000" y="38.000000" fill="#0A0F25" class="text text-underline fill-N1" style="text-anchor:middle;font-size:18px">Bug Finding Process</text></g><mask id="d2-4174224679" maskUnits="userSpaceOnUse" x="-176" y="-81" width="352" height="225">
<rect x="-176" y="-81" width="352" height="225" fill="white"></rect>
<rect x="-75.000000" y="20.000000" width="150" height="23" fill="rgba(0,0,0,0.75)"></rect>
</mask></g><g style="animation: d2Transition-d2-4174224679-1 28000ms infinite" class="d2-4174224679 " width="352" height="311" viewBox="-135 -101 352 311"><rect x="-135.000000" y="-101.000000" width="352.000000" height="311.000000" rx="0.000000" fill="#FFFFFF" class=" fill-N7" stroke-width="0" /><g class="dGl0bGU="><g class="shape" ></g><text x="41.000000" y="104.000000" fill="#0A0F25" class="text text-underline fill-N1" style="text-anchor:middle;font-size:18px">Bug Finding Process</text></g><g class="c3RhcnQ="><g class="shape" ><rect x="0.000000" y="0.000000" width="81.000000" height="66.000000" rx="5.000000" stroke="#0D32B2" fill="#87CEEB" class=" stroke-B1" style="stroke-width:2;" /></g><text x="40.500000" y="38.500000" fill="#0A0F25" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">Code</text></g><mask id="d2-3952805057" maskUnits="userSpaceOnUse" x="-135" y="-101" width="352" height="311">
<rect x="-135" y="-101" width="352" height="311" fill="white"></rect>
<rect x="-34.000000" y="86.000000" width="150" height="23" fill="rgba(0,0,0,0.75)"></rect>
<rect x="22.500000" y="22.500000" width="36" height="21" fill="rgba(0,0,0,0.75)"></rect>
</mask></g><g style="animation: d2Transition-d2-4174224679-2 28000ms infinite" class="d2-4174224679 " width="352" height="437" viewBox="-127 -101 352 437"><rect x="-127.000000" y="-101.000000" width="352.000000" height="437.000000" rx="0.000000" fill="#FFFFFF" class=" fill-N7" stroke-width="0" /><g class="dGl0bGU="><g class="shape" ></g><text x="49.000000" y="230.000000" fill="#0A0F25" class="text text-underline fill-N1" style="text-anchor:middle;font-size:18px">Bug Finding Process</text></g><g class="c3RhcnQ="><g class="shape" ><rect x="9.000000" y="0.000000" width="81.000000" height="66.000000" rx="5.000000" stroke="#0D32B2" fill="#87CEEB" class=" stroke-B1" style="stroke-width:2;" /></g><text x="49.500000" y="38.500000" fill="#0A0F25" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">Code</text></g><g class="cHJvY2Vzcw=="><g class="shape" ><rect x="0.000000" y="126.000000" width="98.000000" height="66.000000" rx="5.000000" stroke="#0D32B2" fill="#90EE90" class=" stroke-B1" style="stroke-width:2;" /></g><text x="49.000000" y="164.500000" fill="#0A0F25" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">Testing</text></g><mask id="d2-352764321" maskUnits="userSpaceOnUse" x="-127" y="-101" width="352" height="437">
<rect x="-127" y="-101" width="352" height="437" fill="white"></rect>
<rect x="-26.000000" y="212.000000" width="150" height="23" fill="rgba(0,0,0,0.75)"></rect>
<rect x="31.500000" y="22.500000" width="36" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="22.500000" y="148.500000" width="53" height="21" fill="rgba(0,0,0,0.75)"></rect>
</mask></g><g style="animation: d2Transition-d2-4174224679-3 28000ms infinite" class="d2-4174224679 " width="539" height="311" viewBox="-101 -101 539 311"><rect x="-101.000000" y="-101.000000" width="539.000000" height="311.000000" rx="0.000000" fill="#FFFFFF" class=" fill-N7" stroke-width="0" /><g class="dGl0bGU="><g class="shape" ></g><text x="168.000000" y="104.000000" fill="#0A0F25" class="text text-underline fill-N1" style="text-anchor:middle;font-size:18px">Bug Finding Process</text></g><g class="c3RhcnQ="><g class="shape" ><rect x="0.000000" y="0.000000" width="81.000000" height="66.000000" rx="5.000000" stroke="#0D32B2" fill="#87CEEB" class=" stroke-B1" style="stroke-width:2;" /></g><text x="40.500000" y="38.500000" fill="#0A0F25" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">Code</text></g><g class="cHJvY2Vzcw=="><g class="shape" ><rect x="239.000000" y="0.000000" width="98.000000" height="66.000000" rx="5.000000" stroke="#0D32B2" fill="#90EE90" class=" stroke-B1" style="stroke-width:2;" /></g><text x="288.000000" y="38.500000" fill="#0A0F25" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">Testing</text></g><g class="KHN0YXJ0IC0mZ3Q7IHByb2Nlc3MpWzBd"><marker id="mk-d2-1985150803-2347144802" markerWidth="10.000000" markerHeight="12.000000" refX="7.000000" refY="6.000000" viewBox="0.000000 0.000000 10.000000 12.000000" orient="auto" markerUnits="userSpaceOnUse"> <polygon points="0.000000,0.000000 10.000000,6.000000 0.000000,12.000000" fill="#333333" class="connection" stroke-width="2" /> </marker><path d="M 83.000000 33.000000 C 144.199997 33.000000 175.800003 33.000000 235.000000 33.000000" stroke="#333333" fill="none" class="connection" style="stroke-width:2;" marker-end="url(#mk-d2-1985150803-2347144802)" mask="url(#d2-1985150803)" /><text x="160.000000" y="39.000000" fill="#676C7E" class="text-italic fill-N2" style="text-anchor:middle;font-size:16px">run tests</text></g><mask id="d2-1985150803" maskUnits="userSpaceOnUse" x="-101" y="-101" width="539" height="311">
<rect x="-101" y="-101" width="539" height="311" fill="white"></rect>
<rect x="93.000000" y="86.000000" width="150" height="23" fill="rgba(0,0,0,0.75)"></rect>
<rect x="22.500000" y="22.500000" width="36" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="261.500000" y="22.500000" width="53" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="131.000000" y="23.000000" width="58" height="21" fill="black"></rect>
</mask></g><g style="animation: d2Transition-d2-4174224679-4 28000ms infinite" class="d2-4174224679 " width="589" height="437" viewBox="-101 -101 589 437"><rect x="-101.000000" y="-101.000000" width="589.000000" height="437.000000" rx="0.000000" fill="#FFFFFF" class=" fill-N7" stroke-width="0" /><g class="dGl0bGU="><g class="shape" ></g><text x="193.000000" y="230.000000" fill="#0A0F25" class="text text-underline fill-N1" style="text-anchor:middle;font-size:18px">Bug Finding Process</text></g><g class="c3RhcnQ="><g class="shape" ><rect x="25.000000" y="0.000000" width="81.000000" height="66.000000" rx="5.000000" stroke="#0D32B2" fill="#87CEEB" class=" stroke-B1" style="stroke-width:2;" /></g><text x="65.500000" y="38.500000" fill="#0A0F25" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">Code</text></g><g class="cHJvY2Vzcw=="><g class="shape" ><rect x="289.000000" y="0.000000" width="98.000000" height="66.000000" rx="5.000000" stroke="#0D32B2" fill="#90EE90" class=" stroke-B1" style="stroke-width:2;" /></g><text x="338.000000" y="38.500000" fill="#0A0F25" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">Testing</text></g><g class="ZW5k"><g class="shape" ><rect x="0.000000" y="126.000000" width="131.000000" height="66.000000" rx="5.000000" stroke="#0D32B2" fill="#FFB6C1" class=" stroke-B1" style="stroke-width:2;" /></g><text x="65.500000" y="164.500000" fill="#0A0F25" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">Deployment</text></g><g class="KHN0YXJ0IC0mZ3Q7IHByb2Nlc3MpWzBd"><marker id="mk-d2-4094302736-2347144802" markerWidth="10.000000" markerHeight="12.000000" refX="7.000000" refY="6.000000" viewBox="0.000000 0.000000 10.000000 12.000000" orient="auto" markerUnits="userSpaceOnUse"> <polygon points="0.000000,0.000000 10.000000,6.000000 0.000000,12.000000" fill="#333333" class="connection" stroke-width="2" /> </marker><path d="M 108.000000 33.000000 C 189.199997 33.000000 225.800003 33.000000 285.000000 33.000000" stroke="#333333" fill="none" class="connection" style="stroke-width:2;" marker-end="url(#mk-d2-4094302736-2347144802)" mask="url(#d2-4094302736)" /><text x="198.000000" y="39.000000" fill="#676C7E" class="text-italic fill-N2" style="text-anchor:middle;font-size:16px">run tests</text></g><mask id="d2-4094302736" maskUnits="userSpaceOnUse" x="-101" y="-101" width="589" height="437">
<rect x="-101" y="-101" width="589" height="437" fill="white"></rect>
<rect x="118.000000" y="212.000000" width="150" height="23" fill="rgba(0,0,0,0.75)"></rect>
<rect x="47.500000" y="22.500000" width="36" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="311.500000" y="22.500000" width="53" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="22.500000" y="148.500000" width="86" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="169.000000" y="23.000000" width="58" height="21" fill="black"></rect>
</mask></g><g style="animation: d2Transition-d2-4174224679-5 28000ms infinite" class="d2-4174224679 " width="802" height="311" viewBox="-101 -101 802 311"><rect x="-101.000000" y="-101.000000" width="802.000000" height="311.000000" rx="0.000000" fill="#FFFFFF" class=" fill-N7" stroke-width="0" /><g class="dGl0bGU="><g class="shape" ></g><text x="300.000000" y="104.000000" fill="#0A0F25" class="text text-underline fill-N1" style="text-anchor:middle;font-size:18px">Bug Finding Process</text></g><g class="c3RhcnQ="><g class="shape" ><rect x="0.000000" y="0.000000" width="81.000000" height="66.000000" rx="5.000000" stroke="#0D32B2" fill="#87CEEB" class=" stroke-B1" style="stroke-width:2;" /></g><text x="40.500000" y="38.500000" fill="#0A0F25" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">Code</text></g><g class="cHJvY2Vzcw=="><g class="shape" ><rect x="239.000000" y="0.000000" width="98.000000" height="66.000000" rx="5.000000" stroke="#0D32B2" fill="#90EE90" class=" stroke-B1" style="stroke-width:2;" /></g><text x="288.000000" y="38.500000" fill="#0A0F25" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">Testing</text></g><g class="ZW5k"><g class="shape" ><rect x="469.000000" y="0.000000" width="131.000000" height="66.000000" rx="5.000000" stroke="#0D32B2" fill="#FFB6C1" class=" stroke-B1" style="stroke-width:2;" /></g><text x="534.500000" y="38.500000" fill="#0A0F25" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">Deployment</text></g><g class="KHN0YXJ0IC0mZ3Q7IHByb2Nlc3MpWzBd"><marker id="mk-d2-427808452-2347144802" markerWidth="10.000000" markerHeight="12.000000" refX="7.000000" refY="6.000000" viewBox="0.000000 0.000000 10.000000 12.000000" orient="auto" markerUnits="userSpaceOnUse"> <polygon points="0.000000,0.000000 10.000000,6.000000 0.000000,12.000000" fill="#333333" class="connection" stroke-width="2" /> </marker><path d="M 83.000000 33.000000 C 144.199997 33.000000 175.800003 33.000000 235.000000 33.000000" stroke="#333333" fill="none" class="connection" style="stroke-width:2;" marker-end="url(#mk-d2-427808452-2347144802)" mask="url(#d2-427808452)" /><text x="160.000000" y="39.000000" fill="#676C7E" class="text-italic fill-N2" style="text-anchor:middle;font-size:16px">run tests</text></g><g class="KHByb2Nlc3MgLSZndDsgZW5kKVswXQ=="><path d="M 339.000000 33.000000 C 389.799988 33.000000 416.200012 33.000000 465.000000 33.000000" stroke="#333333" fill="none" class="connection" style="stroke-width:2;" marker-end="url(#mk-d2-427808452-2347144802)" mask="url(#d2-427808452)" /><text x="403.000000" y="39.000000" fill="#676C7E" class="text-italic fill-N2" style="text-anchor:middle;font-size:16px">pass</text></g><mask id="d2-427808452" maskUnits="userSpaceOnUse" x="-101" y="-101" width="802" height="311">
<rect x="-101" y="-101" width="802" height="311" fill="white"></rect>
<rect x="225.000000" y="86.000000" width="150" height="23" fill="rgba(0,0,0,0.75)"></rect>
<rect x="22.500000" y="22.500000" width="36" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="261.500000" y="22.500000" width="53" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="491.500000" y="22.500000" width="86" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="131.000000" y="23.000000" width="58" height="21" fill="black"></rect>
<rect x="387.000000" y="23.000000" width="32" height="21" fill="black"></rect>
</mask></g><g style="animation: d2Transition-d2-4174224679-6 28000ms infinite" class="d2-4174224679 " width="802" height="311" viewBox="-101 -101 802 311"><rect x="-101.000000" y="-101.000000" width="802.000000" height="311.000000" rx="0.000000" fill="#FFFFFF" class=" fill-N7" stroke-width="0" /><g class="dGl0bGU="><g class="shape" ></g><text x="300.000000" y="104.000000" fill="#0A0F25" class="text text-underline fill-N1" style="text-anchor:middle;font-size:18px">Bug Finding Process</text></g><g class="c3RhcnQ="><g class="shape" ><rect x="0.000000" y="0.000000" width="81.000000" height="66.000000" rx="5.000000" stroke="#0D32B2" fill="#87CEEB" class=" stroke-B1" style="stroke-width:2;" /></g><text x="40.500000" y="38.500000" fill="#0A0F25" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">Code</text></g><g class="cHJvY2Vzcw=="><g class="shape" ><rect x="239.000000" y="0.000000" width="98.000000" height="66.000000" rx="5.000000" stroke="#0D32B2" fill="#FF6347" class=" stroke-B1" style="stroke-width:2;" /></g><text x="288.000000" y="38.500000" fill="#0A0F25" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">Testing</text></g><g class="ZW5k"><g class="shape" ><rect x="469.000000" y="0.000000" width="131.000000" height="66.000000" rx="5.000000" stroke="#0D32B2" fill="#FFB6C1" class=" stroke-B1" style="stroke-width:2;" /></g><text x="534.500000" y="38.500000" fill="#0A0F25" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">Deployment</text></g><g class="KHN0YXJ0IC0mZ3Q7IHByb2Nlc3MpWzBd"><marker id="mk-d2-3555845150-2347144802" markerWidth="10.000000" markerHeight="12.000000" refX="7.000000" refY="6.000000" viewBox="0.000000 0.000000 10.000000 12.000000" orient="auto" markerUnits="userSpaceOnUse"> <polygon points="0.000000,0.000000 10.000000,6.000000 0.000000,12.000000" fill="#333333" class="connection" stroke-width="2" /> </marker><path d="M 83.000000 33.000000 C 144.199997 33.000000 175.800003 33.000000 235.000000 33.000000" stroke="#333333" fill="none" class="connection" style="stroke-width:2;" marker-end="url(#mk-d2-3555845150-2347144802)" mask="url(#d2-3555845150)" /><text x="160.000000" y="39.000000" fill="#676C7E" class="text-italic fill-N2" style="text-anchor:middle;font-size:16px">run tests</text></g><g class="KHByb2Nlc3MgLSZndDsgZW5kKVswXQ=="><path d="M 339.000000 33.000000 C 389.799988 33.000000 416.200012 33.000000 465.000000 33.000000" stroke="#333333" fill="none" class="connection" style="stroke-width:2;stroke-dasharray:10.000000,9.865639;" marker-end="url(#mk-d2-3555845150-2347144802)" mask="url(#d2-3555845150)" /><text x="403.000000" y="39.000000" fill="#676C7E" class="text-italic fill-N2" style="text-anchor:middle;font-size:16px">pass</text></g><mask id="d2-3555845150" maskUnits="userSpaceOnUse" x="-101" y="-101" width="802" height="311">
<rect x="-101" y="-101" width="802" height="311" fill="white"></rect>
<rect x="225.000000" y="86.000000" width="150" height="23" fill="rgba(0,0,0,0.75)"></rect>
<rect x="22.500000" y="22.500000" width="36" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="261.500000" y="22.500000" width="53" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="491.500000" y="22.500000" width="86" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="131.000000" y="23.000000" width="58" height="21" fill="black"></rect>
<rect x="387.000000" y="23.000000" width="32" height="21" fill="black"></rect>
</mask></g><g style="animation: d2Transition-d2-4174224679-7 28000ms infinite" class="d2-4174224679 " width="846" height="437" viewBox="-101 -101 846 437"><rect x="-101.000000" y="-101.000000" width="846.000000" height="437.000000" rx="0.000000" fill="#FFFFFF" class=" fill-N7" stroke-width="0" /><g class="dGl0bGU="><g class="shape" ></g><text x="322.000000" y="230.000000" fill="#0A0F25" class="text text-underline fill-N1" style="text-anchor:middle;font-size:18px">Bug Finding Process</text></g><g class="c3RhcnQ="><g class="shape" ><rect x="22.000000" y="0.000000" width="81.000000" height="66.000000" rx="5.000000" stroke="#0D32B2" fill="#87CEEB" class=" stroke-B1" style="stroke-width:2;" /></g><text x="62.500000" y="38.500000" fill="#0A0F25" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">Code</text></g><g class="cHJvY2Vzcw=="><g class="shape" ><rect x="283.000000" y="0.000000" width="98.000000" height="66.000000" rx="5.000000" stroke="#0D32B2" fill="#FF6347" class=" stroke-B1" style="stroke-width:2;" /></g><text x="332.000000" y="38.500000" fill="#0A0F25" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">Testing</text></g><g class="ZXJyb3I="><g class="shape" ><rect x="0.000000" y="126.000000" width="125.000000" height="66.000000" rx="5.000000" stroke="#0D32B2" fill="#FF6347" class=" stroke-B1" style="stroke-width:2;" /></g><text x="62.500000" y="164.500000" fill="#0A0F25" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">Bug Found!</text></g><g class="ZW5k"><g class="shape" ><rect x="513.000000" y="0.000000" width="131.000000" height="66.000000" rx="5.000000" stroke="#0D32B2" fill="#FFB6C1" class=" stroke-B1" style="stroke-width:2;" /></g><text x="578.500000" y="38.500000" fill="#0A0F25" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">Deployment</text></g><g class="KHN0YXJ0IC0mZ3Q7IHByb2Nlc3MpWzBd"><marker id="mk-d2-1288160822-2347144802" markerWidth="10.000000" markerHeight="12.000000" refX="7.000000" refY="6.000000" viewBox="0.000000 0.000000 10.000000 12.000000" orient="auto" markerUnits="userSpaceOnUse"> <polygon points="0.000000,0.000000 10.000000,6.000000 0.000000,12.000000" fill="#333333" class="connection" stroke-width="2" /> </marker><path d="M 105.000000 33.000000 C 183.800003 33.000000 219.800003 33.000000 279.000000 33.000000" stroke="#333333" fill="none" class="connection" style="stroke-width:2;" marker-end="url(#mk-d2-1288160822-2347144802)" mask="url(#d2-1288160822)" /><text x="193.000000" y="39.000000" fill="#676C7E" class="text-italic fill-N2" style="text-anchor:middle;font-size:16px">run tests</text></g><g class="KHByb2Nlc3MgLSZndDsgZW5kKVswXQ=="><path d="M 383.000000 33.000000 C 433.799988 33.000000 460.200012 33.000000 509.000000 33.000000" stroke="#333333" fill="none" class="connection" style="stroke-width:2;stroke-dasharray:10.000000,9.865639;" marker-end="url(#mk-d2-1288160822-2347144802)" mask="url(#d2-1288160822)" /><text x="447.000000" y="39.000000" fill="#676C7E" class="text-italic fill-N2" style="text-anchor:middle;font-size:16px">pass</text></g><mask id="d2-1288160822" maskUnits="userSpaceOnUse" x="-101" y="-101" width="846" height="437">
<rect x="-101" y="-101" width="846" height="437" fill="white"></rect>
<rect x="247.000000" y="212.000000" width="150" height="23" fill="rgba(0,0,0,0.75)"></rect>
<rect x="44.500000" y="22.500000" width="36" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="305.500000" y="22.500000" width="53" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="22.500000" y="148.500000" width="80" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="535.500000" y="22.500000" width="86" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="164.000000" y="23.000000" width="58" height="21" fill="black"></rect>
<rect x="431.000000" y="23.000000" width="32" height="21" fill="black"></rect>
</mask></g><g style="animation: d2Transition-d2-4174224679-8 28000ms infinite" class="d2-4174224679 " width="802" height="437" viewBox="-101 -101 802 437"><rect x="-101.000000" y="-101.000000" width="802.000000" height="437.000000" rx="0.000000" fill="#FFFFFF" class=" fill-N7" stroke-width="0" /><g class="dGl0bGU="><g class="shape" ></g><text x="300.000000" y="230.000000" fill="#0A0F25" class="text text-underline fill-N1" style="text-anchor:middle;font-size:18px">Bug Finding Process</text></g><g class="c3RhcnQ="><g class="shape" ><rect x="0.000000" y="63.000000" width="81.000000" height="66.000000" rx="5.000000" stroke="#0D32B2" fill="#87CEEB" class=" stroke-B1" style="stroke-width:2;" /></g><text x="40.500000" y="101.500000" fill="#0A0F25" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">Code</text></g><g class="cHJvY2Vzcw=="><g class="shape" ><rect x="239.000000" y="63.000000" width="98.000000" height="66.000000" rx="5.000000" stroke="#0D32B2" fill="#FF6347" class=" stroke-B1" style="stroke-width:2;" /></g><text x="288.000000" y="101.500000" fill="#0A0F25" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">Testing</text></g><g class="ZXJyb3I="><g class="shape" ><rect x="472.000000" y="0.000000" width="125.000000" height="66.000000" rx="5.000000" stroke="#0D32B2" fill="#FF6347" class=" stroke-B1" style="stroke-width:2;" /></g><text x="534.500000" y="38.500000" fill="#0A0F25" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">Bug Found!</text></g><g class="ZW5k"><g class="shape" ><rect x="469.000000" y="126.000000" width="131.000000" height="66.000000" rx="5.000000" stroke="#0D32B2" fill="#FFB6C1" class=" stroke-B1" style="stroke-width:2;" /></g><text x="534.500000" y="164.500000" fill="#0A0F25" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">Deployment</text></g><g class="KHN0YXJ0IC0mZ3Q7IHByb2Nlc3MpWzBd"><marker id="mk-d2-595738330-2347144802" markerWidth="10.000000" markerHeight="12.000000" refX="7.000000" refY="6.000000" viewBox="0.000000 0.000000 10.000000 12.000000" orient="auto" markerUnits="userSpaceOnUse"> <polygon points="0.000000,0.000000 10.000000,6.000000 0.000000,12.000000" fill="#333333" class="connection" stroke-width="2" /> </marker><path d="M 83.000000 96.000000 C 144.199997 96.000000 175.800003 96.000000 235.000000 96.000000" stroke="#333333" fill="none" class="connection" style="stroke-width:2;" marker-end="url(#mk-d2-595738330-2347144802)" mask="url(#d2-595738330)" /><text x="160.000000" y="102.000000" fill="#676C7E" class="text-italic fill-N2" style="text-anchor:middle;font-size:16px">run tests</text></g><g class="KHByb2Nlc3MgLSZndDsgZXJyb3IpWzBd"><marker id="mk-d2-595738330-2277482313" markerWidth="13.000000" markerHeight="16.000000" refX="8.500000" refY="8.000000" viewBox="0.000000 0.000000 13.000000 16.000000" orient="auto" markerUnits="userSpaceOnUse"> <polygon points="0.000000,0.000000 13.000000,8.000000 0.000000,16.000000" fill="#FF0000" class="connection" stroke-width="3" /> </marker><path d="M 339.194739 67.802869 C 389.799988 40.200001 416.799988 33.000000 466.500000 33.000000" stroke="#FF0000" fill="none" class="connection" style="stroke-width:3;" marker-end="url(#mk-d2-595738330-2277482313)" mask="url(#d2-595738330)" /><text x="400.500000" y="43.000000" fill="#676C7E" class="text-italic fill-N2" style="text-anchor:middle;font-size:16px">fail</text></g><g class="KHByb2Nlc3MgLSZndDsgZW5kKVswXQ=="><path d="M 338.755791 123.957705 C 389.799988 151.800003 416.200012 159.000000 465.000000 159.000000" stroke="#333333" fill="none" class="connection" style="stroke-width:2;stroke-dasharray:10.000000,9.865639;" marker-end="url(#mk-d2-595738330-2347144802)" mask="url(#d2-595738330)" /><text x="399.000000" y="160.000000" fill="#676C7E" class="text-italic fill-N2" style="text-anchor:middle;font-size:16px">pass</text></g><mask id="d2-595738330" maskUnits="userSpaceOnUse" x="-101" y="-101" width="802" height="437">
<rect x="-101" y="-101" width="802" height="437" fill="white"></rect>
<rect x="225.000000" y="212.000000" width="150" height="23" fill="rgba(0,0,0,0.75)"></rect>
<rect x="22.500000" y="85.500000" width="36" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="261.500000" y="85.500000" width="53" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="494.500000" y="22.500000" width="80" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="491.500000" y="148.500000" width="86" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="131.000000" y="86.000000" width="58" height="21" fill="black"></rect>
<rect x="390.000000" y="27.000000" width="21" height="21" fill="black"></rect>
<rect x="383.000000" y="144.000000" width="32" height="21" fill="black"></rect>
</mask></g><g style="animation: d2Transition-d2-4174224679-9 28000ms infinite" class="d2-4174224679 " width="849" height="500" viewBox="-101 -101 849 500"><rect x="-101.000000" y="-101.000000" width="849.000000" height="500.000000" rx="0.000000" fill="#FFFFFF" class=" fill-N7" stroke-width="0" /><g class="dGl0bGU="><g class="shape" ></g><text x="323.000000" y="293.000000" fill="#0A0F25" class="text text-underline fill-N1" style="text-anchor:middle;font-size:18px">Bug Finding Process</text></g><g class="c3RhcnQ="><g class="shape" ><rect x="24.000000" y="63.000000" width="81.000000" height="66.000000" rx="5.000000" stroke="#0D32B2" fill="#87CEEB" class=" stroke-B1" style="stroke-width:2;" /></g><text x="64.500000" y="101.500000" fill="#0A0F25" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">Code</text></g><g class="cHJvY2Vzcw=="><g class="shape" ><rect x="286.000000" y="63.000000" width="98.000000" height="66.000000" rx="5.000000" stroke="#0D32B2" fill="#FF6347" class=" stroke-B1" style="stroke-width:2;" /></g><text x="335.000000" y="101.500000" fill="#0A0F25" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">Testing</text></g><g class="ZXJyb3I="><g class="shape" ><rect x="519.000000" y="0.000000" width="125.000000" height="66.000000" rx="5.000000" stroke="#0D32B2" fill="#FF6347" class=" stroke-B1" style="stroke-width:2;" /></g><text x="581.500000" y="38.500000" fill="#0A0F25" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">Bug Found!</text></g><g class="cmVjb3Zlcnk="><g class="shape" ><rect x="0.000000" y="189.000000" width="128.000000" height="66.000000" rx="5.000000" stroke="#0D32B2" fill="#FFD700" class=" stroke-B1" style="stroke-width:2;" /></g><text x="64.000000" y="227.500000" fill="#0A0F25" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">Debug &amp; Fix</text></g><g class="ZW5k"><g class="shape" ><rect x="516.000000" y="126.000000" width="131.000000" height="66.000000" rx="5.000000" stroke="#0D32B2" fill="#FFB6C1" class=" stroke-B1" style="stroke-width:2;" /></g><text x="581.500000" y="164.500000" fill="#0A0F25" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">Deployment</text></g><g class="KHN0YXJ0IC0mZ3Q7IHByb2Nlc3MpWzBd"><marker id="mk-d2-1473808653-2347144802" markerWidth="10.000000" markerHeight="12.000000" refX="7.000000" refY="6.000000" viewBox="0.000000 0.000000 10.000000 12.000000" orient="auto" markerUnits="userSpaceOnUse"> <polygon points="0.000000,0.000000 10.000000,6.000000 0.000000,12.000000" fill="#333333" class="connection" stroke-width="2" /> </marker><path d="M 107.500000 96.000000 C 186.699997 96.000000 222.800003 96.000000 282.000000 96.000000" stroke="#333333" fill="none" class="connection" style="stroke-width:2;" marker-end="url(#mk-d2-1473808653-2347144802)" mask="url(#d2-1473808653)" /><text x="196.000000" y="102.000000" fill="#676C7E" class="text-italic fill-N2" style="text-anchor:middle;font-size:16px">run tests</text></g><g class="KHByb2Nlc3MgLSZndDsgZXJyb3IpWzBd"><marker id="mk-d2-1473808653-2277482313" markerWidth="13.000000" markerHeight="16.000000" refX="8.500000" refY="8.000000" viewBox="0.000000 0.000000 13.000000 16.000000" orient="auto" markerUnits="userSpaceOnUse"> <polygon points="0.000000,0.000000 13.000000,8.000000 0.000000,16.000000" fill="#FF0000" class="connection" stroke-width="3" /> </marker><path d="M 386.194739 67.802869 C 436.799988 40.200001 463.799988 33.000000 513.500000 33.000000" stroke="#FF0000" fill="none" class="connection" style="stroke-width:3;" marker-end="url(#mk-d2-1473808653-2277482313)" mask="url(#d2-1473808653)" /><text x="447.500000" y="43.000000" fill="#676C7E" class="text-italic fill-N2" style="text-anchor:middle;font-size:16px">fail</text></g><g class="KHByb2Nlc3MgLSZndDsgZW5kKVswXQ=="><path d="M 385.755791 123.957705 C 436.799988 151.800003 463.200012 159.000000 512.000000 159.000000" stroke="#333333" fill="none" class="connection" style="stroke-width:2;stroke-dasharray:10.000000,9.865639;" marker-end="url(#mk-d2-1473808653-2347144802)" mask="url(#d2-1473808653)" /><text x="446.000000" y="160.000000" fill="#676C7E" class="text-italic fill-N2" style="text-anchor:middle;font-size:16px">pass</text></g><mask id="d2-1473808653" maskUnits="userSpaceOnUse" x="-101" y="-101" width="849" height="500">
<rect x="-101" y="-101" width="849" height="500" fill="white"></rect>
<rect x="248.000000" y="275.000000" width="150" height="23" fill="rgba(0,0,0,0.75)"></rect>
<rect x="46.500000" y="85.500000" width="36" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="308.500000" y="85.500000" width="53" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="541.500000" y="22.500000" width="80" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="22.500000" y="211.500000" width="83" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="538.500000" y="148.500000" width="86" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="167.000000" y="86.000000" width="58" height="21" fill="black"></rect>
<rect x="437.000000" y="27.000000" width="21" height="21" fill="black"></rect>
<rect x="430.000000" y="144.000000" width="32" height="21" fill="black"></rect>
</mask></g><g style="animation: d2Transition-d2-4174224679-10 28000ms infinite" class="d2-4174224679 " width="1082" height="437" viewBox="-101 -101 1082 437"><rect x="-101.000000" y="-101.000000" width="1082.000000" height="437.000000" rx="0.000000" fill="#FFFFFF" class=" fill-N7" stroke-width="0" /><g class="dGl0bGU="><g class="shape" ></g><text x="440.000000" y="230.000000" fill="#0A0F25" class="text text-underline fill-N1" style="text-anchor:middle;font-size:18px">Bug Finding Process</text></g><g class="c3RhcnQ="><g class="shape" ><rect x="0.000000" y="63.000000" width="81.000000" height="66.000000" rx="5.000000" stroke="#0D32B2" fill="#87CEEB" class=" stroke-B1" style="stroke-width:2;" /></g><text x="40.500000" y="101.500000" fill="#0A0F25" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">Code</text></g><g class="cHJvY2Vzcw=="><g class="shape" ><rect x="239.000000" y="63.000000" width="98.000000" height="66.000000" rx="5.000000" stroke="#0D32B2" fill="#FF6347" class=" stroke-B1" style="stroke-width:2;" /></g><text x="288.000000" y="101.500000" fill="#0A0F25" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">Testing</text></g><g class="ZXJyb3I="><g class="shape" ><rect x="472.000000" y="0.000000" width="125.000000" height="66.000000" rx="5.000000" stroke="#0D32B2" fill="#FF6347" class=" stroke-B1" style="stroke-width:2;" /></g><text x="534.500000" y="38.500000" fill="#0A0F25" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">Bug Found!</text></g><g class="cmVjb3Zlcnk="><g class="shape" ><rect x="752.000000" y="0.000000" width="128.000000" height="66.000000" rx="5.000000" stroke="#0D32B2" fill="#FFD700" class=" stroke-B1" style="stroke-width:2;" /></g><text x="816.000000" y="38.500000" fill="#0A0F25" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">Debug &amp; Fix</text></g><g class="ZW5k"><g class="shape" ><rect x="469.000000" y="126.000000" width="131.000000" height="66.000000" rx="5.000000" stroke="#0D32B2" fill="#FFB6C1" class=" stroke-B1" style="stroke-width:2;" /></g><text x="534.500000" y="164.500000" fill="#0A0F25" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">Deployment</text></g><g class="KHN0YXJ0IC0mZ3Q7IHByb2Nlc3MpWzBd"><marker id="mk-d2-453183414-2347144802" markerWidth="10.000000" markerHeight="12.000000" refX="7.000000" refY="6.000000" viewBox="0.000000 0.000000 10.000000 12.000000" orient="auto" markerUnits="userSpaceOnUse"> <polygon points="0.000000,0.000000 10.000000,6.000000 0.000000,12.000000" fill="#333333" class="connection" stroke-width="2" /> </marker><path d="M 83.000000 96.000000 C 144.199997 96.000000 175.800003 96.000000 235.000000 96.000000" stroke="#333333" fill="none" class="connection" style="stroke-width:2;" marker-end="url(#mk-d2-453183414-2347144802)" mask="url(#d2-453183414)" /><text x="160.000000" y="102.000000" fill="#676C7E" class="text-italic fill-N2" style="text-anchor:middle;font-size:16px">run tests</text></g><g class="KHByb2Nlc3MgLSZndDsgZXJyb3IpWzBd"><marker id="mk-d2-453183414-2277482313" markerWidth="13.000000" markerHeight="16.000000" refX="8.500000" refY="8.000000" viewBox="0.000000 0.000000 13.000000 16.000000" orient="auto" markerUnits="userSpaceOnUse"> <polygon points="0.000000,0.000000 13.000000,8.000000 0.000000,16.000000" fill="#FF0000" class="connection" stroke-width="3" /> </marker><path d="M 339.194739 67.802869 C 389.799988 40.200001 416.799988 33.000000 466.500000 33.000000" stroke="#FF0000" fill="none" class="connection" style="stroke-width:3;" marker-end="url(#mk-d2-453183414-2277482313)" mask="url(#d2-453183414)" /><text x="400.500000" y="43.000000" fill="#676C7E" class="text-italic fill-N2" style="text-anchor:middle;font-size:16px">fail</text></g><g class="KGVycm9yIC0mZ3Q7IHJlY292ZXJ5KVswXQ=="><marker id="mk-d2-453183414-1719471275" markerWidth="10.000000" markerHeight="12.000000" refX="7.000000" refY="6.000000" viewBox="0.000000 0.000000 10.000000 12.000000" orient="auto" markerUnits="userSpaceOnUse"> <polygon points="0.000000,0.000000 10.000000,6.000000 0.000000,12.000000" fill="#FFD700" class="connection" stroke-width="2" /> </marker><path d="M 599.000000 33.000000 C 660.200012 33.000000 691.200012 33.000000 748.000000 33.000000" stroke="#FFD700" fill="none" class="connection" style="stroke-width:2;" marker-end="url(#mk-d2-453183414-1719471275)" mask="url(#d2-453183414)" /><text x="675.000000" y="39.000000" fill="#676C7E" class="text-italic fill-N2" style="text-anchor:middle;font-size:16px">analyze</text></g><g class="KHByb2Nlc3MgLSZndDsgZW5kKVswXQ=="><path d="M 338.755791 123.957705 C 389.799988 151.800003 416.200012 159.000000 465.000000 159.000000" stroke="#333333" fill="none" class="connection" style="stroke-width:2;stroke-dasharray:10.000000,9.865639;" marker-end="url(#mk-d2-453183414-2347144802)" mask="url(#d2-453183414)" /><text x="399.000000" y="160.000000" fill="#676C7E" class="text-italic fill-N2" style="text-anchor:middle;font-size:16px">pass</text></g><mask id="d2-453183414" maskUnits="userSpaceOnUse" x="-101" y="-101" width="1082" height="437">
<rect x="-101" y="-101" width="1082" height="437" fill="white"></rect>
<rect x="365.000000" y="212.000000" width="150" height="23" fill="rgba(0,0,0,0.75)"></rect>
<rect x="22.500000" y="85.500000" width="36" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="261.500000" y="85.500000" width="53" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="494.500000" y="22.500000" width="80" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="774.500000" y="22.500000" width="83" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="491.500000" y="148.500000" width="86" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="131.000000" y="86.000000" width="58" height="21" fill="black"></rect>
<rect x="390.000000" y="27.000000" width="21" height="21" fill="black"></rect>
<rect x="649.000000" y="23.000000" width="52" height="21" fill="black"></rect>
<rect x="383.000000" y="144.000000" width="32" height="21" fill="black"></rect>
</mask></g><g style="animation: d2Transition-d2-4174224679-11 28000ms infinite" class="d2-4174224679 " width="1104" height="477" viewBox="-101 -101 1104 477"><rect x="-101.000000" y="-101.000000" width="1104.000000" height="477.000000" rx="0.000000" fill="#FFFFFF" class=" fill-N7" stroke-width="0" /><g class="dGl0bGU="><g class="shape" ></g><text x="451.000000" y="270.000000" fill="#0A0F25" class="text text-underline fill-N1" style="text-anchor:middle;font-size:18px">Bug Finding Process</text></g><g class="c3RhcnQ="><g class="shape" ><rect x="0.000000" y="100.000000" width="81.000000" height="66.000000" rx="5.000000" stroke="#0D32B2" fill="#87CEEB" class=" stroke-B1" style="stroke-width:2;" /></g><text x="40.500000" y="138.500000" fill="#0A0F25" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">Code</text></g><g class="cHJvY2Vzcw=="><g class="shape" ><rect x="239.000000" y="63.000000" width="98.000000" height="66.000000" rx="5.000000" stroke="#0D32B2" fill="#FF6347" class=" stroke-B1" style="stroke-width:2;" /></g><text x="288.000000" y="101.500000" fill="#0A0F25" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">Testing</text></g><g class="ZXJyb3I="><g class="shape" ><rect x="494.000000" y="0.000000" width="125.000000" height="66.000000" rx="5.000000" stroke="#0D32B2" fill="#FF6347" class=" stroke-B1" style="stroke-width:2;" /></g><text x="556.500000" y="38.500000" fill="#0A0F25" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">Bug Found!</text></g><g class="cmVjb3Zlcnk="><g class="shape" ><rect x="774.000000" y="100.000000" width="128.000000" height="66.000000" rx="5.000000" stroke="#0D32B2" fill="#FFD700" class=" stroke-B1" style="stroke-width:2;" /></g><text x="838.000000" y="138.500000" fill="#0A0F25" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">Debug &amp; Fix</text></g><g class="ZW5k"><g class="shape" ><rect x="491.000000" y="126.000000" width="131.000000" height="66.000000" rx="5.000000" stroke="#0D32B2" fill="#FFB6C1" class=" stroke-B1" style="stroke-width:2;" /></g><text x="556.500000" y="164.500000" fill="#0A0F25" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">Deployment</text></g><g class="KHN0YXJ0IC0mZ3Q7IHByb2Nlc3MpWzBd"><marker id="mk-d2-2021760935-2347144802" markerWidth="10.000000" markerHeight="12.000000" refX="7.000000" refY="6.000000" viewBox="0.000000 0.000000 10.000000 12.000000" orient="auto" markerUnits="userSpaceOnUse"> <polygon points="0.000000,0.000000 10.000000,6.000000 0.000000,12.000000" fill="#333333" class="connection" stroke-width="2" /> </marker><path d="M 82.913641 119.418641 C 144.199997 100.800003 175.800003 96.000000 235.000000 96.000000" stroke="#333333" fill="none" class="connection" style="stroke-width:2;" marker-end="url(#mk-d2-2021760935-2347144802)" mask="url(#d2-2021760935)" /><text x="159.000000" y="104.000000" fill="#676C7E" class="text-italic fill-N2" style="text-anchor:middle;font-size:16px">run tests</text></g><g class="KHByb2Nlc3MgLSZndDsgZXJyb3IpWzBd"><marker id="mk-d2-2021760935-2277482313" markerWidth="13.000000" markerHeight="16.000000" refX="8.500000" refY="8.000000" viewBox="0.000000 0.000000 13.000000 16.000000" orient="auto" markerUnits="userSpaceOnUse"> <polygon points="0.000000,0.000000 13.000000,8.000000 0.000000,16.000000" fill="#FF0000" class="connection" stroke-width="3" /> </marker><path d="M 339.230245 70.870395 C 398.600006 40.799999 430.000000 33.000000 488.500000 33.000000" stroke="#FF0000" fill="none" class="connection" style="stroke-width:3;" marker-end="url(#mk-d2-2021760935-2277482313)" mask="url(#d2-2021760935)" /><text x="411.500000" y="43.000000" fill="#676C7E" class="text-italic fill-N2" style="text-anchor:middle;font-size:16px">fail</text></g><g class="KGVycm9yIC0mZ3Q7IHJlY292ZXJ5KVswXQ=="><marker id="mk-d2-2021760935-1719471275" markerWidth="10.000000" markerHeight="12.000000" refX="7.000000" refY="6.000000" viewBox="0.000000 0.000000 10.000000 12.000000" orient="auto" markerUnits="userSpaceOnUse"> <polygon points="0.000000,0.000000 10.000000,6.000000 0.000000,12.000000" fill="#FFD700" class="connection" stroke-width="2" /> </marker><path d="M 621.000000 33.000000 C 682.200012 33.000000 716.713013 46.299999 788.306588 97.182759" stroke="#FFD700" fill="none" class="connection" style="stroke-width:2;" marker-end="url(#mk-d2-2021760935-1719471275)" mask="url(#d2-2021760935)" /><text x="713.000000" y="50.000000" fill="#676C7E" class="text-italic fill-N2" style="text-anchor:middle;font-size:16px">analyze</text></g><g class="KHJlY292ZXJ5IC0mZ3Q7IHN0YXJ0KVswXQ=="><marker id="mk-d2-2021760935-1627577928" markerWidth="10.000000" markerHeight="12.000000" refX="7.000000" refY="6.000000" viewBox="0.000000 0.000000 10.000000 12.000000" orient="auto" markerUnits="userSpaceOnUse"> <polygon points="0.000000,0.000000 10.000000,6.000000 0.000000,12.000000" fill="#4169E1" class="connection" stroke-width="2" /> </marker><path d="M 788.934397 167.655228 C 716.513000 218.899994 669.700012 232.000000 627.250000 232.000000 C 584.799988 232.000000 528.000000 232.000000 485.250000 232.000000 C 442.500000 232.000000 388.799988 232.000000 351.000000 232.000000 C 313.200012 232.000000 144.199997 218.800003 84.069698 168.564558" stroke="#4169E1" fill="none" class="connection" style="stroke-width:2;" marker-end="url(#mk-d2-2021760935-1627577928)" mask="url(#d2-2021760935)" /><text x="435.000000" y="238.000000" fill="#676C7E" class="text-italic fill-N2" style="text-anchor:middle;font-size:16px">refactor</text></g><g class="KHByb2Nlc3MgLSZndDsgZW5kKVswXQ=="><path d="M 338.784196 120.903684 C 398.600006 151.199997 429.399994 159.000000 487.000000 159.000000" stroke="#333333" fill="none" class="connection" style="stroke-width:2;stroke-dasharray:10.000000,9.865639;" marker-end="url(#mk-d2-2021760935-2347144802)" mask="url(#d2-2021760935)" /><text x="410.000000" y="160.000000" fill="#676C7E" class="text-italic fill-N2" style="text-anchor:middle;font-size:16px">pass</text></g><mask id="d2-2021760935" maskUnits="userSpaceOnUse" x="-101" y="-101" width="1104" height="477">
<rect x="-101" y="-101" width="1104" height="477" fill="white"></rect>
<rect x="376.000000" y="252.000000" width="150" height="23" fill="rgba(0,0,0,0.75)"></rect>
<rect x="22.500000" y="122.500000" width="36" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="261.500000" y="85.500000" width="53" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="516.500000" y="22.500000" width="80" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="796.500000" y="122.500000" width="83" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="513.500000" y="148.500000" width="86" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="130.000000" y="88.000000" width="58" height="21" fill="black"></rect>
<rect x="401.000000" y="27.000000" width="21" height="21" fill="black"></rect>
<rect x="687.000000" y="34.000000" width="52" height="21" fill="black"></rect>
<rect x="408.000000" y="222.000000" width="54" height="21" fill="black"></rect>
<rect x="394.000000" y="144.000000" width="32" height="21" fill="black"></rect>
</mask></g><g style="animation: d2Transition-d2-4174224679-12 28000ms infinite" class="d2-4174224679 " width="1104" height="518" viewBox="-101 -101 1104 518"><rect x="-101.000000" y="-101.000000" width="1104.000000" height="518.000000" rx="0.000000" fill="#FFFFFF" class=" fill-N7" stroke-width="0" /><g class="dGl0bGU="><g class="shape" ></g><text x="451.000000" y="311.000000" fill="#0A0F25" class="text text-underline fill-N1" style="text-anchor:middle;font-size:18px">Bug Finding Process</text></g><g class="c3RhcnQ="><g class="shape" ><rect x="0.000000" y="183.000000" width="81.000000" height="66.000000" rx="5.000000" stroke="#0D32B2" fill="#87CEEB" class=" stroke-B1" style="stroke-width:2;" /></g><text x="40.500000" y="221.500000" fill="#0A0F25" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">Code</text></g><g class="cHJvY2Vzcw=="><g class="shape" ><rect x="239.000000" y="126.000000" width="98.000000" height="66.000000" rx="5.000000" stroke="#0D32B2" fill="#FF6347" class=" stroke-B1" style="stroke-width:2;" /></g><text x="288.000000" y="164.500000" fill="#0A0F25" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">Testing</text></g><g class="ZXJyb3I="><g class="shape" ><rect x="494.000000" y="0.000000" width="125.000000" height="66.000000" rx="5.000000" stroke="#0D32B2" fill="#FF6347" class=" stroke-B1" style="stroke-width:2;" /></g><text x="556.500000" y="38.500000" fill="#0A0F25" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">Bug Found!</text></g><g class="cmVjb3Zlcnk="><g class="shape" ><rect x="774.000000" y="188.000000" width="128.000000" height="66.000000" rx="5.000000" stroke="#0D32B2" fill="#FFD700" class=" stroke-B1" style="stroke-width:2;" /></g><text x="838.000000" y="226.500000" fill="#0A0F25" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">Debug &amp; Fix</text></g><g class="ZW5k"><g class="shape" ><rect x="491.000000" y="126.000000" width="131.000000" height="66.000000" rx="5.000000" stroke="#0D32B2" fill="#FFB6C1" class=" stroke-B1" style="stroke-width:2;" /></g><text x="556.500000" y="164.500000" fill="#0A0F25" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">Deployment</text></g><g class="KHN0YXJ0IC0mZ3Q7IHByb2Nlc3MpWzBd"><marker id="mk-d2-2783362041-2347144802" markerWidth="10.000000" markerHeight="12.000000" refX="7.000000" refY="6.000000" viewBox="0.000000 0.000000 10.000000 12.000000" orient="auto" markerUnits="userSpaceOnUse"> <polygon points="0.000000,0.000000 10.000000,6.000000 0.000000,12.000000" fill="#333333" class="connection" stroke-width="2" /> </marker><path d="M 82.802334 196.133055 C 144.199997 166.600006 175.800003 159.000000 235.000000 159.000000" stroke="#333333" fill="none" class="connection" style="stroke-width:2;" marker-end="url(#mk-d2-2783362041-2347144802)" mask="url(#d2-2783362041)" /><text x="157.000000" y="169.000000" fill="#676C7E" class="text-italic fill-N2" style="text-anchor:middle;font-size:16px">run tests</text></g><g class="KHByb2Nlc3MgLSZndDsgZXJyb3IpWzBd"><marker id="mk-d2-2783362041-2277482313" markerWidth="13.000000" markerHeight="16.000000" refX="8.500000" refY="8.000000" viewBox="0.000000 0.000000 13.000000 16.000000" orient="auto" markerUnits="userSpaceOnUse"> <polygon points="0.000000,0.000000 13.000000,8.000000 0.000000,16.000000" fill="#FF0000" class="connection" stroke-width="3" /> </marker><path d="M 322.767755 124.232221 C 395.399994 51.598999 430.000000 33.000000 488.500000 33.000000" stroke="#FF0000" fill="none" class="connection" style="stroke-width:3;" marker-end="url(#mk-d2-2783362041-2277482313)" mask="url(#d2-2783362041)" /><text x="394.500000" y="58.000000" fill="#676C7E" class="text-italic fill-N2" style="text-anchor:middle;font-size:16px">fail</text></g><g class="KGVycm9yIC0mZ3Q7IHJlY292ZXJ5KVswXQ=="><marker id="mk-d2-2783362041-1719471275" markerWidth="10.000000" markerHeight="12.000000" refX="7.000000" refY="6.000000" viewBox="0.000000 0.000000 10.000000 12.000000" orient="auto" markerUnits="userSpaceOnUse"> <polygon points="0.000000,0.000000 10.000000,6.000000 0.000000,12.000000" fill="#FFD700" class="connection" stroke-width="2" /> </marker><path d="M 621.000000 33.000000 C 682.200012 33.000000 721.000000 64.000000 810.616612 184.787608" stroke="#FFD700" fill="none" class="connection" style="stroke-width:2;" marker-end="url(#mk-d2-2783362041-1719471275)" mask="url(#d2-2783362041)" /><text x="733.000000" y="86.000000" fill="#676C7E" class="text-italic fill-N2" style="text-anchor:middle;font-size:16px">analyze</text></g><g class="KHJlY292ZXJ5IC0mZ3Q7IHN0YXJ0KVswXQ=="><marker id="mk-d2-2783362041-1627577928" markerWidth="10.000000" markerHeight="12.000000" refX="7.000000" refY="6.000000" viewBox="0.000000 0.000000 10.000000 12.000000" orient="auto" markerUnits="userSpaceOnUse"> <polygon points="0.000000,0.000000 10.000000,6.000000 0.000000,12.000000" fill="#4169E1" class="connection" stroke-width="2" /> </marker><path d="M 772.123314 245.691411 C 713.200012 267.399994 669.700012 273.000000 627.250000 273.000000 C 584.799988 273.000000 528.000000 273.000000 485.250000 273.000000 C 442.500000 273.000000 388.799988 273.000000 351.000000 273.000000 C 313.200012 273.000000 144.199997 265.399994 84.604667 236.733890" stroke="#4169E1" fill="none" class="connection" style="stroke-width:2;" marker-end="url(#mk-d2-2783362041-1627577928)" mask="url(#d2-2783362041)" /><text x="426.000000" y="279.000000" fill="#676C7E" class="text-italic fill-N2" style="text-anchor:middle;font-size:16px">refactor</text></g><g class="KHJlY292ZXJ5IC0mZ3Q7IHByb2Nlc3MpWzBd"><path d="M 772.024474 230.811925 C 713.200012 240.100006 669.700012 242.500000 627.250000 242.500000 C 584.799988 242.500000 398.600006 232.300003 340.334849 193.708796" stroke="#333333" fill="none" class="connection" style="stroke-width:2;" marker-end="url(#mk-d2-2783362041-2347144802)" mask="url(#d2-2783362041)" /><text x="549.500000" y="246.000000" fill="#676C7E" class="text-italic fill-N2" style="text-anchor:middle;font-size:16px">retest</text></g><g class="KHByb2Nlc3MgLSZndDsgZW5kKVswXQ=="><path d="M 339.000000 159.000000 C 398.600006 159.000000 429.399994 159.000000 487.000000 159.000000" stroke="#333333" fill="none" class="connection" style="stroke-width:2;stroke-dasharray:10.000000,9.865639;" marker-end="url(#mk-d2-2783362041-2347144802)" mask="url(#d2-2783362041)" /><text x="414.000000" y="165.000000" fill="#676C7E" class="text-italic fill-N2" style="text-anchor:middle;font-size:16px">pass</text></g><mask id="d2-2783362041" maskUnits="userSpaceOnUse" x="-101" y="-101" width="1104" height="518">
<rect x="-101" y="-101" width="1104" height="518" fill="white"></rect>
<rect x="376.000000" y="293.000000" width="150" height="23" fill="rgba(0,0,0,0.75)"></rect>
<rect x="22.500000" y="205.500000" width="36" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="261.500000" y="148.500000" width="53" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="516.500000" y="22.500000" width="80" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="796.500000" y="210.500000" width="83" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="513.500000" y="148.500000" width="86" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="128.000000" y="153.000000" width="58" height="21" fill="black"></rect>
<rect x="384.000000" y="42.000000" width="21" height="21" fill="black"></rect>
<rect x="707.000000" y="70.000000" width="52" height="21" fill="black"></rect>
<rect x="399.000000" y="263.000000" width="54" height="21" fill="black"></rect>
<rect x="530.000000" y="230.000000" width="39" height="21" fill="black"></rect>
<rect x="398.000000" y="149.000000" width="32" height="21" fill="black"></rect>
</mask></g><g style="animation: d2Transition-d2-4174224679-13 28000ms infinite" class="d2-4174224679 " width="1104" height="518" viewBox="-101 -101 1104 518"><rect x="-101.000000" y="-101.000000" width="1104.000000" height="518.000000" rx="0.000000" fill="#FFFFFF" class=" fill-N7" stroke-width="0" /><g class="dGl0bGU="><g class="shape" ></g><text x="451.000000" y="311.000000" fill="#0A0F25" class="text text-underline fill-N1" style="text-anchor:middle;font-size:18px">Bug Finding Process</text></g><g class="c3RhcnQ="><g class="shape" ><rect x="0.000000" y="183.000000" width="81.000000" height="66.000000" rx="5.000000" stroke="#0D32B2" fill="#87CEEB" class=" stroke-B1" style="stroke-width:2;" /></g><text x="40.500000" y="221.500000" fill="#0A0F25" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">Code</text></g><g class="cHJvY2Vzcw=="><g class="shape" ><rect x="239.000000" y="126.000000" width="98.000000" height="66.000000" rx="5.000000" stroke="#0D32B2" fill="#90EE90" class=" stroke-B1" style="stroke-width:2;" /></g><text x="288.000000" y="164.500000" fill="#0A0F25" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">Testing</text></g><g class="ZXJyb3I="><g class="shape" ><rect x="494.000000" y="0.000000" width="125.000000" height="66.000000" rx="5.000000" stroke="#0D32B2" fill="#FF6347" class=" stroke-B1" style="stroke-width:2;" /></g><text x="556.500000" y="38.500000" fill="#0A0F25" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">Bug Found!</text></g><g class="cmVjb3Zlcnk="><g class="shape" ><rect x="774.000000" y="188.000000" width="128.000000" height="66.000000" rx="5.000000" stroke="#0D32B2" fill="#FFD700" class=" stroke-B1" style="stroke-width:2;" /></g><text x="838.000000" y="226.500000" fill="#0A0F25" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">Debug &amp; Fix</text></g><g class="ZW5k"><g class="shape" ><rect x="491.000000" y="126.000000" width="131.000000" height="66.000000" rx="5.000000" stroke="#0D32B2" fill="#FFB6C1" class=" stroke-B1" style="stroke-width:2;" /></g><text x="556.500000" y="164.500000" fill="#0A0F25" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">Deployment</text></g><g class="KHN0YXJ0IC0mZ3Q7IHByb2Nlc3MpWzBd"><marker id="mk-d2-405767020-2347144802" markerWidth="10.000000" markerHeight="12.000000" refX="7.000000" refY="6.000000" viewBox="0.000000 0.000000 10.000000 12.000000" orient="auto" markerUnits="userSpaceOnUse"> <polygon points="0.000000,0.000000 10.000000,6.000000 0.000000,12.000000" fill="#333333" class="connection" stroke-width="2" /> </marker><path d="M 82.802334 196.133055 C 144.199997 166.600006 175.800003 159.000000 235.000000 159.000000" stroke="#333333" fill="none" class="connection" style="stroke-width:2;" marker-end="url(#mk-d2-405767020-2347144802)" mask="url(#d2-405767020)" /><text x="157.000000" y="169.000000" fill="#676C7E" class="text-italic fill-N2" style="text-anchor:middle;font-size:16px">run tests</text></g><g class="KHByb2Nlc3MgLSZndDsgZXJyb3IpWzBd"><marker id="mk-d2-405767020-2277482313" markerWidth="13.000000" markerHeight="16.000000" refX="8.500000" refY="8.000000" viewBox="0.000000 0.000000 13.000000 16.000000" orient="auto" markerUnits="userSpaceOnUse"> <polygon points="0.000000,0.000000 13.000000,8.000000 0.000000,16.000000" fill="#FF0000" class="connection" stroke-width="3" /> </marker><path d="M 322.767755 124.232221 C 395.399994 51.598999 430.000000 33.000000 488.500000 33.000000" stroke="#FF0000" fill="none" class="connection" style="stroke-width:3;stroke-dasharray:15.000000,14.583620;" marker-end="url(#mk-d2-405767020-2277482313)" mask="url(#d2-405767020)" /><text x="394.500000" y="58.000000" fill="#676C7E" class="text-italic fill-N2" style="text-anchor:middle;font-size:16px">fail</text></g><g class="KGVycm9yIC0mZ3Q7IHJlY292ZXJ5KVswXQ=="><marker id="mk-d2-405767020-1719471275" markerWidth="10.000000" markerHeight="12.000000" refX="7.000000" refY="6.000000" viewBox="0.000000 0.000000 10.000000 12.000000" orient="auto" markerUnits="userSpaceOnUse"> <polygon points="0.000000,0.000000 10.000000,6.000000 0.000000,12.000000" fill="#FFD700" class="connection" stroke-width="2" /> </marker><path d="M 621.000000 33.000000 C 682.200012 33.000000 721.000000 64.000000 810.616612 184.787608" stroke="#FFD700" fill="none" class="connection" style="stroke-width:2;" marker-end="url(#mk-d2-405767020-1719471275)" mask="url(#d2-405767020)" /><text x="733.000000" y="86.000000" fill="#676C7E" class="text-italic fill-N2" style="text-anchor:middle;font-size:16px">analyze</text></g><g class="KHJlY292ZXJ5IC0mZ3Q7IHN0YXJ0KVswXQ=="><marker id="mk-d2-405767020-1627577928" markerWidth="10.000000" markerHeight="12.000000" refX="7.000000" refY="6.000000" viewBox="0.000000 0.000000 10.000000 12.000000" orient="auto" markerUnits="userSpaceOnUse"> <polygon points="0.000000,0.000000 10.000000,6.000000 0.000000,12.000000" fill="#4169E1" class="connection" stroke-width="2" /> </marker><path d="M 772.123314 245.691411 C 713.200012 267.399994 669.700012 273.000000 627.250000 273.000000 C 584.799988 273.000000 528.000000 273.000000 485.250000 273.000000 C 442.500000 273.000000 388.799988 273.000000 351.000000 273.000000 C 313.200012 273.000000 144.199997 265.399994 84.604667 236.733890" stroke="#4169E1" fill="none" class="connection" style="stroke-width:2;" marker-end="url(#mk-d2-405767020-1627577928)" mask="url(#d2-405767020)" /><text x="426.000000" y="279.000000" fill="#676C7E" class="text-italic fill-N2" style="text-anchor:middle;font-size:16px">refactor</text></g><g class="KHJlY292ZXJ5IC0mZ3Q7IHByb2Nlc3MpWzBd"><path d="M 772.024474 230.811925 C 713.200012 240.100006 669.700012 242.500000 627.250000 242.500000 C 584.799988 242.500000 398.600006 232.300003 340.334849 193.708796" stroke="#333333" fill="none" class="connection" style="stroke-width:2;" marker-end="url(#mk-d2-405767020-2347144802)" mask="url(#d2-405767020)" /><text x="549.500000" y="246.000000" fill="#676C7E" class="text-italic fill-N2" style="text-anchor:middle;font-size:16px">retest</text></g><g class="KHByb2Nlc3MgLSZndDsgZW5kKVswXQ=="><marker id="mk-d2-405767020-1191028724" markerWidth="13.000000" markerHeight="16.000000" refX="8.500000" refY="8.000000" viewBox="0.000000 0.000000 13.000000 16.000000" orient="auto" markerUnits="userSpaceOnUse"> <polygon points="0.000000,0.000000 13.000000,8.000000 0.000000,16.000000" fill="#32CD32" class="connection" stroke-width="3" /> </marker><path d="M 339.500000 159.000000 C 398.600006 159.000000 429.399994 159.000000 485.500000 159.000000" stroke="#32CD32" fill="none" class="connection" style="stroke-width:3;" marker-end="url(#mk-d2-405767020-1191028724)" mask="url(#d2-405767020)" /><text x="414.000000" y="165.000000" fill="#676C7E" class="text-italic fill-N2" style="text-anchor:middle;font-size:16px">pass</text></g><mask id="d2-405767020" maskUnits="userSpaceOnUse" x="-101" y="-101" width="1104" height="518">
<rect x="-101" y="-101" width="1104" height="518" fill="white"></rect>
<rect x="376.000000" y="293.000000" width="150" height="23" fill="rgba(0,0,0,0.75)"></rect>
<rect x="22.500000" y="205.500000" width="36" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="261.500000" y="148.500000" width="53" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="516.500000" y="22.500000" width="80" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="796.500000" y="210.500000" width="83" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="513.500000" y="148.500000" width="86" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="128.000000" y="153.000000" width="58" height="21" fill="black"></rect>
<rect x="384.000000" y="42.000000" width="21" height="21" fill="black"></rect>
<rect x="707.000000" y="70.000000" width="52" height="21" fill="black"></rect>
<rect x="399.000000" y="263.000000" width="54" height="21" fill="black"></rect>
<rect x="530.000000" y="230.000000" width="39" height="21" fill="black"></rect>
<rect x="398.000000" y="149.000000" width="32" height="21" fill="black"></rect>
</mask></g></svg></svg>