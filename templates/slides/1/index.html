<!doctype html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">

  <title>Nosuits - AI Coding at Scale for Developers</title>

  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/reveal.js/4.5.0/reveal.min.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/reveal.js/4.5.0/theme/night.min.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/reveal.js/4.5.0/plugin/highlight/monokai.min.css">
  <style>
    :root {
      --background-color: #121212;
      --main-color: #f5f5f5;
      --accent-color: #00c4b4;
      --graph-human: #9370DB;
      --graph-ai: #32CD32;
      --graph-hybrid: #FFA500;
    }
    
    .reveal {
      font-family: 'Inter', sans-serif;
    }
    
    .reveal h1, .reveal h2, .reveal h3 {
      font-family: 'Inter', sans-serif;
      color: var(--accent-color);
      text-transform: none;
    }
    
    .reveal pre code {
      padding: 20px;
      border-radius: 5px;
      background-color: #1e1e1e;
    }
    
    .reveal section img {
      border: none;
      box-shadow: none;
    }
    
    .container {
      display: flex;
      justify-content: space-between;
    }
    
    .column {
      flex: 1;
      padding: 0 10px;
    }
    
    .text-accent {
      color: var(--accent-color);
    }
    
    .text-human {
      color: var(--graph-human);
    }
    
    .text-ai {
      color: var(--graph-ai);
    }
    
    .text-hybrid {
      color: var(--graph-hybrid);
    }
    
    .chart-container {
      width: 100%;
      height: 400px;
      margin: 0 auto;
    }
    
    .day-label {
      font-size: 1.5em;
      font-weight: bold;
      margin-bottom: 0.5em;
    }
    
    .subtitle {
      font-size: 0.8em;
      color: #888;
    }
    
    .card {
      background-color: rgba(30, 30, 30, 0.7);
      border-radius: 10px;
      padding: 20px;
      margin: 10px 0;
    }
    
    .footer {
      font-size: 0.5em;
      position: absolute;
      bottom: 10px;
      right: 10px;
      color: #666;
    }
    
    .metrics {
      display: flex;
      justify-content: space-around;
      text-align: center;
      margin-top: 20px;
    }
    
    .metric {
      flex: 1;
    }
    
    .metric-value {
      font-size: 2.5em;
      font-weight: bold;
    }
    
    .metric-label {
      font-size: 0.8em;
      color: #888;
    }
    
    .feature-list li {
      margin-bottom: 0.8em;
    }
    
    .logo {
      font-size: 2.5em;
      font-weight: bold;
      color: var(--accent-color);
      letter-spacing: -1px;
    }
    
    .logo span {
      color: var(--main-color);
    }

    .graph-container {
      height: 400px;
      width: 100%;
      position: relative;
    }

    .graph-bg {
      position: absolute;
      width: 100%;
      height: 100%;
      background-color: rgba(255, 255, 255, 0.03);
      border-left: 1px solid rgba(255, 255, 255, 0.1);
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .graph-grid {
      position: absolute;
      width: 100%;
      height: 100%;
      display: grid;
      grid-template-columns: repeat(6, 1fr);
      grid-template-rows: repeat(5, 1fr);
    }

    .grid-line {
      border-right: 1px dashed rgba(255, 255, 255, 0.1);
      height: 100%;
    }

    .grid-line-horizontal {
      border-top: 1px dashed rgba(255, 255, 255, 0.1);
      width: 100%;
    }

    .graph-line {
      position: absolute;
      width: 3px;
      height: 3px;
      border-radius: 50%;
    }

    .human-line {
      background-color: var(--graph-human);
      box-shadow: 0 0 5px var(--graph-human);
    }

    .ai-line {
      background-color: var(--graph-ai);
      box-shadow: 0 0 5px var(--graph-ai);
    }

    .hybrid-line {
      background-color: var(--graph-hybrid);
      box-shadow: 0 0 5px var(--graph-hybrid);
    }

    .graph-labels {
      display: flex;
      justify-content: space-between;
      margin-top: 5px;
    }

    .graph-legend {
      display: flex;
      justify-content: center;
      gap: 20px;
      margin-top: 10px;
    }

    .legend-item {
      display: flex;
      align-items: center;
      gap: 5px;
    }

    .legend-color {
      width: 12px;
      height: 12px;
      border-radius: 50%;
    }

    .observation-list {
      text-align: left;
      list-style-type: none;
      padding-left: 0;
    }
    
    .observation-list li {
      margin-bottom: 15px;
      padding-left: 25px;
      position: relative;
    }
    
    .observation-list li::before {
      content: "•";
      position: absolute;
      left: 5px;
      font-size: 1.5em;
    }
    
    .observation-list li:nth-child(1)::before {
      color: var(--graph-human);
    }
    
    .observation-list li:nth-child(2)::before {
      color: var(--graph-ai);
    }
    
    .observation-list li:nth-child(3)::before {
      color: var(--graph-hybrid);
    }

    .demo-terminal {
      background-color: #1a1a1a;
      border-radius: 8px;
      padding: 15px;
      font-family: monospace;
      text-align: left;
      height: 200px;
      overflow: auto;
      position: relative;
    }

    .terminal-prompt::before {
      content: "> ";
      color: #00c4b4;
    }

    .terminal-output {
      color: #a0a0a0;
      margin-left: 15px;
    }

    .terminal-success {
      color: #32CD32;
    }

    .terminal-error {
      color: #ff6b6b;
    }

    .terminal-command {
      color: white;
    }

    .terminal-cursor {
      display: inline-block;
      width: 8px;
      height: 15px;
      background-color: white;
      animation: blink 1s infinite;
      vertical-align: middle;
    }

    @keyframes blink {
      0%, 100% { opacity: 1; }
      50% { opacity: 0; }
    }
  </style>
</head>
<body>
  <div class="reveal">
    <div class="slides">
      <!-- SLIDE 1: Title -->
      <section>
        <div class="logo">No<span>suits</span></div>
        <h3>Vibe Coding at Scale for Developers</h3>
        <p class="subtitle">Built by developers, for developers</p>
        <div class="footer">Hackathon Demo 2025</div>
      </section>

      <!-- SLIDE 2: The Problem -->
      <section>
        <h2 style="font-size: 1.5em; margin-bottom: 10px;">The Vibe Coding Problem -- Test</h2>
        <div class="container" style="align-items: flex-start;">
          <div class="column" style="flex: 1.2;">
            <p class="day-label text-accent" style="font-size: 0.9em; margin: 0 0 3px 0; font-weight: bold;">Day 1</p>
            <div class="card" style="padding: 8px; margin-bottom: 8px;">
              <p style="font-size: 0.65em; margin: 0; line-height: 1.3;">Subscribe to a $20/mo hobby-grade AI coding tool. Feel invincible as you see the AI pouring real looking code.</p>
            </div>
            
            <p class="day-label text-accent" style="font-size: 0.9em; margin: 0 0 3px 0; font-weight: bold;">Day 30</p>
            <div class="card" style="padding: 8px; margin-bottom: 8px;">
              <p style="font-size: 0.65em; margin: 0; line-height: 1.3;">Get frustrated as the day 1's improvements are not keeping up.</p>
            </div>
            
            <p class="day-label text-accent" style="font-size: 0.9em; margin: 0 0 3px 0; font-weight: bold;">Day 60</p>
            <div class="card" style="padding: 8px; margin-bottom: 8px;">
              <p style="font-size: 0.65em; margin: 0; line-height: 1.3;">Post that you tried vibe coding and it doesn't work.</p>
            </div>
          </div>
          <div class="column" style="flex: 1;">
            <img src="dev.jpg" alt="Frustrated developer meme" style="max-width: 95%; height: auto; margin-bottom: 5px;" />
            <div style="padding: 0 3px;">
              <p style="font-size: 0.65em; margin-bottom: 5px; line-height: 1.3;"><em>"You invest in a bicycle for an F1 race and complain that locomotion sucks."</em></p>
              <p class="text-accent" style="font-size: 0.65em; margin: 0; line-height: 1.3;">You need the right tools, processes and people to get the concept to work.</p>
            </div>
          </div>
        </div>
      </section>

      <!-- SLIDE 3: The Reality Graph -->
      <section>
        <h2 style="font-size: 1.8em;">Vibe Coding Cost Over Time</h2>
        <h3 style="font-size: 1.4em;">Human vs. AI vs. Human+AI</h3>
        
        <div style="text-align: center; margin-bottom: 20px;">
          <img src="benefit.jpeg" alt="Development Cost Graph" style="max-width: 80%; margin: 0 auto;" />
        </div>
        
        <div class="graph-legend" style="font-size: 0.9em;">
          <div class="legend-item">
            <div class="legend-color" style="background-color: var(--graph-human);"></div>
            <span class="text-human">Human Only</span>
          </div>
          <div class="legend-item">
            <div class="legend-color" style="background-color: var(--graph-ai);"></div>
            <span class="text-ai">AI Only</span>
          </div>
          <div class="legend-item">
            <div class="legend-color" style="background-color: var(--graph-hybrid);"></div>
            <span class="text-hybrid">Human + AI</span>
          </div>
        </div>
        
        <ul class="observation-list" style="font-size: 0.85em;">
          <li><span class="text-human">Human Only:</span> High initial cost (learning curve), gradual increase with complexity</li>
          <li><span class="text-ai">AI Only:</span> Low initial cost, steep increase with project complexity</li>
          <li><span class="text-hybrid">Human + AI:</span> Moderate initial cost, minimal increase with complexity</li>
        </ul>
      </section>

            <!-- SLIDE 4.5: Active Vibe Coding Usage -->
            <section>
              <h2 style="font-size: 1.8em; margin-bottom: 15px;">Our Vibe Coding Journey</h2>
              <h3 style="font-size: 1.2em; color: #aaa; margin-bottom: 20px;">Real-world adoption stats</h3>
              
              <div style="text-align: center; margin-bottom: 15px;">
                <img src="usage.jpeg" alt="Vibe Coding Usage Statistics" style="max-width: 85%; margin: 0 auto; border-radius: 10px; box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);" />
              </div>
              
              <div class="metrics" style="margin-top: 20px;">
                <div class="metric">
                  <div class="metric-value" style="font-size: 2em; color: var(--accent-color);">78<span style="font-size: 0.5em;">%</span></div>
                  <div class="metric-label" style="font-size: 0.85em;">Productivity Gain</div>
                </div>
                <div class="metric">
                  <div class="metric-value" style="font-size: 2em; color: var(--accent-color);">5.2<span style="font-size: 0.5em;">×</span></div>
                  <div class="metric-label" style="font-size: 0.85em;">Deployment Speed</div>
                </div>
                <div class="metric">
                  <div class="metric-value" style="font-size: 2em; color: var(--accent-color);">12<span style="font-size: 0.5em;">K</span></div>
                  <div class="metric-label" style="font-size: 0.85em;">Daily AI Interactions</div>
                </div>
              </div>
            </section>
            
      <!-- SLIDE 4: The Law -->
      <section>
        <h2 style="font-size: 1.8em; margin-bottom: 10px;">The Vibe Coding Law</h2>
        <div class="card" style="font-size: 1.3em; text-align: center; margin-bottom: 20px; padding: 15px;">
          <p>Y = 5X - 2</p>
          <p class="subtitle" style="font-size: 0.6em; margin-top: 5px;">Where X is your developers and Y is the outcome with vibe coding</p>
        </div>
        
        <div class="container" style="margin-top: 10px;">
          <div class="column" style="flex: 3;">
            <ul style="list-style-type: none; text-align: left; font-size: 0.9em; margin: 0; padding: 0;">
              <li style="margin-bottom: 8px;">✅ <strong>1 excellent developer with AI</strong> = powerful trinity</li>
              <li style="margin-bottom: 8px;">✅ <strong>10 excellent devs in tandem with AI</strong> = an armada</li>
              <li style="margin-bottom: 8px;">⚠️ <strong>10 mediocre devs with AI</strong> = chaos of 50 mediocres</li>
              <li style="margin-bottom: 8px;">❌ <strong>0 devs with AI</strong> = -2 (the 2 you need to hire now)</li>
            </ul>
          </div>
          <div class="column" style="flex: 2;">
            <div class="card" style="height: auto; padding: 15px; display: flex; align-items: center; justify-content: center;">
              <p style="font-size: 1em; font-style: italic; margin: 0;">"Amplify the signal and not the noise."</p>
            </div>
          </div>
        </div>
      </section>



      <!-- SLIDE 5: Introduction to Nosuits -->
      <section>
        <h2>Introducing Nosuits</h2>
        <h3>AI Coding at Scale for Developers</h3>
        
        <div class="container">
          <div class="column">
            <p>Nosuits is a developer-centric platform that enables efficient AI-powered coding at scale:</p>
            <ul class="feature-list">
              <li>Codebase understanding & style adaptation</li>
              <li>Code quality metrics & improvement</li>
              <li>Automated test generation</li>
              <li>Intelligent documentation</li>
              <li>Structured code generation</li>
            </ul>
          </div>
          <div class="column">
            <div class="demo-terminal">
              <p class="terminal-prompt"><span class="terminal-command">nosuits scan ./src</span></p>
              <p class="terminal-output">[INFO] Starting Nosuits repository scan...</p>
              <p class="terminal-output">[INFO] Analyzing project structure...</p>
              <p class="terminal-output">[INFO] Found 24 JavaScript files, 18 React components, 8 test files</p>
              <p class="terminal-output">[ANALYSIS] Code quality scan complete:</p>
              <p class="terminal-output">├─ Overall Score: 72/100</p>
              <p class="terminal-output">├─ Lines of Code: 4,256</p>
              <p class="terminal-output">├─ Comment Ratio: 18%</p>
              <p class="terminal-output">├─ Complexity Score: Medium (23.4)</p>
              <p class="terminal-output">└─ Test Coverage: 64%</p>
              <p class="terminal-prompt"><span class="terminal-cursor"></span></p>
            </div>
          </div>
        </div>
      </section>

            <!-- SLIDE 10: Advanced Tools -->
            <section>
              <h2 style="font-size: 1.8em;"> Technology Stack</h2>
              <h3 style="font-size: 1.2em; margin-bottom: 20px;">Powerful Tools Under the Hood</h3>
      
              <div class="container" style="margin-top: 15px;">
                <div class="column">
                  <div class="card" style="padding: 15px; margin-bottom: 15px; background: rgba(0, 196, 180, 0.05);">
                    <h3 style="font-size: 1.1em; margin-bottom: 5px;">🧠 Weaviate Vector Database</h3>
                    <p style="font-size: 0.85em; margin: 0 0 8px 0;">Intelligent code context management</p>
                    <ul style="font-size: 0.8em; text-align: left; margin: 0; padding-left: 20px;">
                      <li>Semantic code search & retrieval</li>
                      <li>Fast similarity matching</li>
                      <li>Context-aware code understanding</li>
                      <li>Pattern recognition across repositories</li>
                    </ul>
                  </div>
                  <div class="card" style="padding: 15px; background: rgba(0, 196, 180, 0.05);">
                    <h3 style="font-size: 1.1em; margin-bottom: 5px;">📊 Real-time Analytics</h3>
                    <p style="font-size: 0.85em; margin: 0 0 8px 0;">Monitoring & insights</p>
                    <ul style="font-size: 0.8em; text-align: left; margin: 0; padding-left: 20px;">
                      <li>Developer productivity tracking</li>
                      <li>AI assistance effectiveness</li>
                      <li>Code quality trends over time</li>
                      <li>Integration with CI/CD pipelines</li>
                    </ul>
                  </div>
                </div>
                <div class="column">
                  <div class="card" style="padding: 15px; margin-bottom: 15px; background: rgba(0, 196, 180, 0.05);">
                    <h3 style="font-size: 1.1em; margin-bottom: 5px;">🎯 AIMon for Code Evaluation</h3>
                    <p style="font-size: 0.85em; margin: 0 0 8px 0;">Automated code quality scoring</p>
                    <ul style="font-size: 0.8em; text-align: left; margin: 0; padding-left: 20px;">
                      <li>Multi-dimensional code quality metrics</li>
                      <li>LLM-based code reviews</li>
                      <li>Performance bottleneck detection</li>
                      <li>Security vulnerability scanning</li>
                    </ul>
                  </div>
                  <div class="card" style="padding: 15px; background: rgba(0, 196, 180, 0.05);">
                    <h3 style="font-size: 1.1em; margin-bottom: 5px;">🔗 Integration Ecosystem</h3>
                    <p style="font-size: 0.85em; margin: 0 0 8px 0;">Works with your existing tools</p>
                    <ul style="font-size: 0.8em; text-align: left; margin: 0; padding-left: 20px;">
                      <li>GitHub, GitLab, Bitbucket</li>
                      <li>VSCode, JetBrains IDEs</li>
                      <li>Jira, Linear, Asana</li>
                      <li>Slack, Discord, Teams</li>
                    </ul>
                  </div>
                </div>
              </div>
            </section>

      <!-- SLIDE 6: The Dashboard -->
      <section data-transition="slide">
        <h2>Nosuits Dashboard</h2>
        <p>All the insights you need in one place</p>
        
        <img src="/api/placeholder/800/400" alt="Nosuits Dashboard" />
        
        <div class="metrics">
          <div class="metric">
            <div class="metric-value">72<span style="font-size: 0.5em;">/100</span></div>
            <div class="metric-label">Code Quality</div>
          </div>
          <div class="metric">
            <div class="metric-value">64<span style="font-size: 0.5em;">%</span></div>
            <div class="metric-label">Test Coverage</div>
          </div>
          <div class="metric">
            <div class="metric-value">47<span style="font-size: 0.5em;">%</span></div>
            <div class="metric-label">Documentation</div>
          </div>
          <div class="metric">
            <div class="metric-value">12</div>
            <div class="metric-label">Issues Found</div>
          </div>
        </div>
      </section>

      <!-- SLIDE 7: Key Features -->
      <section>
        <h2>Key Features</h2>
        
        <div class="container">
          <div class="column">
            <div class="card">
              <h3>🧠 Codebase Analysis</h3>
              <p>Smart scanning to understand code style, patterns, and structure</p>
            </div>
            <div class="card">
              <h3>🧪 Test Generation</h3>
              <p>Automatically create comprehensive test suites for your code</p>
            </div>
          </div>
          <div class="column">
            <div class="card">
              <h3>📊 Quality Metrics</h3>
              <p>Detailed code quality assessment with actionable insights</p>
            </div>
            <div class="card">
              <h3>📝 Documentation</h3>
              <p>Generate and maintain documentation that stays in sync with code</p>
            </div>
          </div>
        </div>
      </section>

      <!-- SLIDE 8: Demo -->
      <section>
        <h2>See Nosuits in Action</h2>
        
        <div class="demo-terminal" style="height: 300px;">
          <p class="terminal-prompt"><span class="terminal-command">nosuits generate tests --for=src/components/UserDashboard.jsx</span></p>
          <p class="terminal-output">[INFO] Analyzing component: UserDashboard.jsx</p>
          <p class="terminal-output">[INFO] Identifying testable behaviors...</p>
          <p class="terminal-output">[INFO] Detecting props, state, and side effects...</p>
          <p class="terminal-output">[THINKING] Generating comprehensive test suite...</p>
          <p class="terminal-output">- Creating rendering tests</p>
          <p class="terminal-output">- Creating data fetching tests</p>
          <p class="terminal-output">- Creating error state tests</p>
          <p class="terminal-output">- Creating loading state tests</p>
          <p class="terminal-output">[GENERATING] Writing tests to src/components/__tests__/UserDashboard.test.js</p>
          <p class="terminal-output">[SUCCESS] Generated 12 test cases:</p>
          <p class="terminal-output">├─ 3 Rendering tests</p>
          <p class="terminal-output">├─ 4 Data interaction tests</p>
          <p class="terminal-output">├─ 3 Error handling tests</p>
          <p class="terminal-output">└─ 2 State management tests</p>
          <p class="terminal-output">[INFO] Test coverage for this component: 94%</p>
          <p class="terminal-prompt"><span class="terminal-cursor"></span></p>
        </div>
      </section>

      <!-- SLIDE 9: Built for Developers -->
      <section>
        <h2 style="font-size: 1.8em;">Built for Developers</h2>
        <h3 style="font-size: 1.3em;">Not for Business Suits</h3>
        
        <div class="container" style="margin-top: 15px;">
          <div class="column">
            <div class="card" style="padding: 15px; margin-bottom: 15px;">
              <h3 style="font-size: 1.1em; margin-bottom: 8px;">💻 Works with Your Tools</h3>
              <p style="font-size: 0.85em; margin: 0;">Integrates with your existing IDE and command line workflow</p>
            </div>
            <div class="card" style="padding: 15px;">
              <h3 style="font-size: 1.1em; margin-bottom: 8px;">🔄 Preserves Your Style</h3>
              <p style="font-size: 0.85em; margin: 0;">Adapts to your codebase patterns, not generic code dumps</p>
            </div>
          </div>
          <div class="column">
            <div class="card" style="padding: 15px; margin-bottom: 15px;">
              <h3 style="font-size: 1.1em; margin-bottom: 8px;">🛠️ Developer-First UX</h3>
              <p style="font-size: 0.85em; margin: 0;">Created by developers who understand real coding challenges</p>
            </div>
            <div class="card" style="padding: 15px;">
              <h3 style="font-size: 1.1em; margin-bottom: 8px;">⚡ Performance Focus</h3>
              <p style="font-size: 0.85em; margin: 0;">Designed for efficiency in large, complex codebases</p>
            </div>
          </div>
        </div>
      </section>



      <!-- SLIDE 11: Call to Action -->
      <section>
        <h1 style="font-size: 2em;">Ready to scale your AI coding?</h1>
        <div class="logo" style="margin: 25px 0; font-size: 2.2em;">No<span>suits</span></div>
        <p style="font-size: 1em;">AI coding for real developers</p>
        <div class="card" style="width: 60%; margin: 25px auto; padding: 15px;">
          <h3 style="font-size: 1.2em; margin-bottom: 10px;">Get early access at</h3>
          <p style="font-size: 1.3em; margin: 0;">nosuits.dev</p>
        </div>
      </section>
    </div>
  </div>

  <script src="https://cdnjs.cloudflare.com/ajax/libs/reveal.js/4.5.0/reveal.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/reveal.js/4.5.0/plugin/markdown/markdown.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/reveal.js/4.5.0/plugin/highlight/highlight.js"></script>
  <script>
    // Initialize Reveal.js
    Reveal.initialize({
      hash: true,
      slideNumber: true,
      transition: 'slide',
      controls: true,
      progress: true,
      center: true,
      touch: true,
      overview: true,
      help: true,
      plugins: [ RevealMarkdown, RevealHighlight ]
    });

    // Add manual fullscreen handler
    document.addEventListener('keydown', function(event) {
      // Handle 'F' key press
      if (event.key === 'f' || event.key === 'F') {
        toggleFullScreen();
      }
    });

    // Function to toggle fullscreen
    function toggleFullScreen() {
      if (!document.fullscreenElement) {
        // Enter fullscreen
        if (document.documentElement.requestFullscreen) {
          document.documentElement.requestFullscreen();
        } else if (document.documentElement.webkitRequestFullscreen) { /* Safari */
          document.documentElement.webkitRequestFullscreen();
        } else if (document.documentElement.msRequestFullscreen) { /* IE11 */
          document.documentElement.msRequestFullscreen();
        }
      } else {
        // Exit fullscreen
        if (document.exitFullscreen) {
          document.exitFullscreen();
        } else if (document.webkitExitFullscreen) { /* Safari */
          document.webkitExitFullscreen();
        } else if (document.msExitFullscreen) { /* IE11 */
          document.msExitFullscreen();
        }
      }
    }

    // Add fullscreen button
    const fullscreenButton = document.createElement('div');
    fullscreenButton.className = 'fullscreen-button';
    fullscreenButton.innerHTML = '⛶';
    fullscreenButton.title = 'Toggle Fullscreen (F)';
    fullscreenButton.style.cssText = 'position: fixed; bottom: 20px; left: 20px; z-index: 999; cursor: pointer; font-size: 24px; color: #00c4b4; background: rgba(0,0,0,0.2); width: 40px; height: 40px; border-radius: 5px; display: flex; align-items: center; justify-content: center; transition: background 0.3s ease;';
    
    fullscreenButton.addEventListener('mouseover', () => {
      fullscreenButton.style.background = 'rgba(0,196,180,0.3)';
    });
    
    fullscreenButton.addEventListener('mouseout', () => {
      fullscreenButton.style.background = 'rgba(0,0,0,0.2)';
    });
    
    fullscreenButton.addEventListener('click', () => {
      toggleFullScreen();
    });
    
    document.body.appendChild(fullscreenButton);
  </script>
</body>
</html>