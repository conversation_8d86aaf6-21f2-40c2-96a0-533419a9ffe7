<!doctype html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
  <title>{{PRODUCT_NAME}} - Product Presentation</title>
  
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/reveal.js/4.5.0/reveal.min.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/reveal.js/4.5.0/theme/night.min.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/reveal.js/4.5.0/plugin/highlight/monokai.min.css">
  
  <style>
    :root {
      --background-color: #121212;
      --main-color: #f5f5f5;
      --accent-color: #00c4b4;
      --secondary-color: #9370DB;
    }
    
    .reveal {
      font-family: 'Inter', sans-serif;
    }
    
    .reveal h1, .reveal h2, .reveal h3 {
      font-family: 'Inter', sans-serif;
      color: var(--accent-color);
      text-transform: none;
    }
    
    .card {
      background-color: rgba(30, 30, 30, 0.7);
      border-radius: 10px;
      padding: 20px;
      margin: 10px 0;
    }
    
    .text-accent {
      color: var(--accent-color);
    }
    
    .container {
      display: flex;
      justify-content: space-between;
    }
    
    .column {
      flex: 1;
      padding: 0 10px;
    }
    
    .metrics {
      display: flex;
      justify-content: space-around;
      text-align: center;
      margin-top: 20px;
    }
    
    .metric {
      flex: 1;
    }
    
    .metric-value {
      font-size: 2.5em;
      font-weight: bold;
      color: var(--accent-color);
    }
    
    .metric-label {
      font-size: 0.8em;
      color: #888;
    }
    
    .logo {
      font-size: 2.5em;
      font-weight: bold;
      color: var(--accent-color);
      letter-spacing: -1px;
    }
    
    .subtitle {
      font-size: 0.8em;
      color: #888;
    }
    
    .footer {
      font-size: 0.5em;
      position: absolute;
      bottom: 10px;
      right: 10px;
      color: #666;
    }
  </style>
</head>
<body>
  <div class="reveal">
    <div class="slides">
      <!-- SLIDE 1: Title -->
      <section>
        <div class="logo">{{PRODUCT_NAME}}</div>
        <h3>{{PRODUCT_DESCRIPTION}}</h3>
        <p class="subtitle">{{TARGET_AUDIENCE}}</p>
        <div class="footer">Generated by KAPI IDE</div>
      </section>

      <!-- SLIDE 2: Problem Statement -->
      <section>
        <h2>The Problem</h2>
        <div class="card">
          <h3>{{PROBLEM_TITLE}}</h3>
          <p>{{PROBLEM_DESCRIPTION}}</p>
        </div>
        <div class="container">
          <div class="column">
            <h4>Current Challenges:</h4>
            <ul>
              {{PROBLEM_POINTS}}
            </ul>
          </div>
          <div class="column">
            <div class="card">
              <p><em>"{{PROBLEM_QUOTE}}"</em></p>
            </div>
          </div>
        </div>
      </section>

      <!-- SLIDE 3: Solution -->
      <section>
        <h2>Our Solution</h2>
        <div class="container">
          <div class="column">
            <div class="card">
              <h3>{{SOLUTION_TITLE}}</h3>
              <p>{{SOLUTION_DESCRIPTION}}</p>
            </div>
          </div>
          <div class="column">
            <h4>Key Features:</h4>
            <ul>
              {{FEATURE_LIST}}
            </ul>
          </div>
        </div>
      </section>

      <!-- SLIDE 4: Target Market -->
      <section>
        <h2>Target Market</h2>
        <div class="container">
          <div class="column">
            <div class="card">
              <h3>Primary Audience</h3>
              <p>{{TARGET_AUDIENCE}}</p>
            </div>
            <div class="card">
              <h3>Market Size</h3>
              <p>{{MARKET_SIZE}}</p>
            </div>
          </div>
          <div class="column">
            <div class="card">
              <h3>Use Cases</h3>
              <ul>
                {{USE_CASES}}
              </ul>
            </div>
          </div>
        </div>
      </section>

      <!-- SLIDE 5: Key Features -->
      <section>
        <h2>Key Features</h2>
        <div class="container">
          <div class="column">
            {{FEATURE_CARDS_LEFT}}
          </div>
          <div class="column">
            {{FEATURE_CARDS_RIGHT}}
          </div>
        </div>
      </section>

      <!-- SLIDE 6: Benefits -->
      <section>
        <h2>Benefits</h2>
        <div class="metrics">
          {{BENEFIT_METRICS}}
        </div>
        <div class="container" style="margin-top: 30px;">
          <div class="column">
            <h4>For Users:</h4>
            <ul>
              {{USER_BENEFITS}}
            </ul>
          </div>
          <div class="column">
            <h4>For Business:</h4>
            <ul>
              {{BUSINESS_BENEFITS}}
            </ul>
          </div>
        </div>
      </section>

      <!-- SLIDE 7: Technology -->
      <section>
        <h2>Technology Stack</h2>
        <div class="container">
          <div class="column">
            <div class="card">
              <h3>Frontend</h3>
              <p>{{TECH_FRONTEND}}</p>
            </div>
            <div class="card">
              <h3>Backend</h3>
              <p>{{TECH_BACKEND}}</p>
            </div>
          </div>
          <div class="column">
            <div class="card">
              <h3>Database</h3>
              <p>{{TECH_DATABASE}}</p>
            </div>
            <div class="card">
              <h3>Infrastructure</h3>
              <p>{{TECH_INFRASTRUCTURE}}</p>
            </div>
          </div>
        </div>
      </section>

      <!-- SLIDE 8: Roadmap -->
      <section>
        <h2>Development Roadmap</h2>
        <div class="container">
          <div class="column">
            <div class="card">
              <h3>Phase 1 - MVP</h3>
              <p>{{ROADMAP_PHASE1}}</p>
            </div>
          </div>
          <div class="column">
            <div class="card">
              <h3>Phase 2 - Growth</h3>
              <p>{{ROADMAP_PHASE2}}</p>
            </div>
          </div>
        </div>
        <div class="card" style="margin-top: 20px;">
          <h3>Next Steps</h3>
          <p>{{NEXT_STEPS}}</p>
        </div>
      </section>

      <!-- SLIDE 9: Call to Action -->
      <section>
        <h1>Ready to Build {{PRODUCT_NAME}}?</h1>
        <div class="logo" style="margin: 25px 0;">{{PRODUCT_NAME}}</div>
        <p>{{CTA_DESCRIPTION}}</p>
        <div class="card" style="width: 60%; margin: 25px auto;">
          <h3>{{CTA_TITLE}}</h3>
          <p style="font-size: 1.3em;">{{CTA_ACTION}}</p>
        </div>
      </section>
    </div>
  </div>

  <script src="https://cdnjs.cloudflare.com/ajax/libs/reveal.js/4.5.0/reveal.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/reveal.js/4.5.0/plugin/highlight/highlight.js"></script>
  <script>
    Reveal.initialize({
      hash: true,
      slideNumber: true,
      transition: 'slide',
      controls: true,
      progress: true,
      center: true,
      touch: true,
      plugins: [ RevealHighlight ]
    });

    // Fullscreen toggle
    document.addEventListener('keydown', function(event) {
      if (event.key === 'f' || event.key === 'F') {
        if (!document.fullscreenElement) {
          document.documentElement.requestFullscreen();
        } else {
          document.exitFullscreen();
        }
      }
    });
  </script>
</body>
</html>